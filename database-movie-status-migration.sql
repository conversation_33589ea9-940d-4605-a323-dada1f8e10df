-- 影片状态管理数据库迁移
-- 为现有的movies表添加状态管理字段

-- 1. 添加影片状态字段
ALTER TABLE movies ADD COLUMN IF NOT EXISTS status VARCHAR(20) DEFAULT 'draft';
ALTER TABLE movies ADD COLUMN IF NOT EXISTS video_urls JSONB DEFAULT '{}';
ALTER TABLE movies ADD COLUMN IF NOT EXISTS processing_priority INTEGER DEFAULT 5;
ALTER TABLE movies ADD COLUMN IF NOT EXISTS published_at TIMESTAMP NULL;
ALTER TABLE movies ADD COLUMN IF NOT EXISTS processing_started_at TIMESTAMP NULL;
ALTER TABLE movies ADD COLUMN IF NOT EXISTS processing_completed_at TIMESTAMP NULL;

-- 2. 创建状态枚举约束
ALTER TABLE movies ADD CONSTRAINT movies_status_check 
CHECK (status IN ('draft', 'processing', 'processed', 'published', 'failed'));

-- 3. 添加索引优化查询
CREATE INDEX IF NOT EXISTS idx_movies_status ON movies(status);
CREATE INDEX IF NOT EXISTS idx_movies_published_at ON movies(published_at);
CREATE INDEX IF NOT EXISTS idx_movies_processing_priority ON movies(processing_priority);

-- 4. 更新现有数据（假设现有影片都是已发布状态）
UPDATE movies SET status = 'published', published_at = created_at WHERE status = 'draft';

-- 5. 创建影片处理队列视图
CREATE OR REPLACE VIEW movie_processing_queue AS
SELECT 
    m.id,
    m.movie_id,
    m.title,
    m.status,
    m.processing_priority,
    m.created_at,
    vpt.task_uuid,
    vpt.progress,
    vpt.status as task_status,
    vpt.error_message
FROM movies m
LEFT JOIN video_processing_tasks vpt ON m.id = vpt.movie_id
WHERE m.status IN ('draft', 'processing', 'processed')
ORDER BY m.processing_priority DESC, m.created_at ASC;

-- 6. 创建已发布影片视图（用户前端使用）
CREATE OR REPLACE VIEW published_movies AS
SELECT 
    m.*,
    CASE 
        WHEN m.video_urls IS NOT NULL AND jsonb_array_length(m.video_urls->'qualities') > 0 
        THEN true 
        ELSE false 
    END as has_video_urls
FROM movies m
WHERE m.status = 'published' 
    AND m.published_at IS NOT NULL
ORDER BY m.published_at DESC;

-- 7. 添加触发器：自动更新时间戳
CREATE OR REPLACE FUNCTION update_movie_timestamps()
RETURNS TRIGGER AS $$
BEGIN
    -- 当状态变为processing时，设置processing_started_at
    IF OLD.status != 'processing' AND NEW.status = 'processing' THEN
        NEW.processing_started_at = NOW();
    END IF;
    
    -- 当状态变为processed时，设置processing_completed_at
    IF OLD.status != 'processed' AND NEW.status = 'processed' THEN
        NEW.processing_completed_at = NOW();
    END IF;
    
    -- 当状态变为published时，设置published_at
    IF OLD.status != 'published' AND NEW.status = 'published' THEN
        NEW.published_at = NOW();
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER movie_status_timestamp_trigger
    BEFORE UPDATE ON movies
    FOR EACH ROW
    EXECUTE FUNCTION update_movie_timestamps();

-- 8. 创建统计视图
CREATE OR REPLACE VIEW movie_status_stats AS
SELECT 
    status,
    COUNT(*) as count,
    COUNT(*) * 100.0 / SUM(COUNT(*)) OVER() as percentage
FROM movies
GROUP BY status;

COMMENT ON TABLE movies IS '影片表 - 包含状态管理';
COMMENT ON COLUMN movies.status IS '影片状态: draft(草稿), processing(处理中), processed(已处理), published(已发布), failed(失败)';
COMMENT ON COLUMN movies.video_urls IS '处理后的视频链接JSON: {qualities: [{resolution, url, bitrate}], thumbnails: []}';
COMMENT ON COLUMN movies.processing_priority IS '处理优先级: 1-10, 10为最高优先级';
