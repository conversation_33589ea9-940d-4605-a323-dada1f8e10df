# 依赖目录
node_modules/
*/node_modules/
javflix/node_modules/
javbus-api/node_modules/
javflix-api/node_modules/
admin-panel/node_modules/
**/node_modules/

# 日志文件
*.log
logs/
npm-debug.log*

# 运行时数据
.env
.env.local
.env.development
.env.test
.env.production

# 编辑器文件
.idea/
.vscode/
*.swp
*.swo

# 操作系统文件
.DS_Store
Thumbs.db

# 构建输出
dist/
build/
out/
coverage/

# 其他
.cache/
.tmp/

# 下载目录
magnet-downloader/downloads/

# 视频处理文件 - 排除大文件和临时文件
video-processor/processing/
video-processor/video-processor
video-processor/*.exe
video-processor/*.bin

# 视频文件和媒体文件
*.mp4
*.avi
*.mkv
*.mov
*.wmv
*.flv
*.webm
*.m4v
*.3gp
*.ts
*.m3u8

# 图片文件（缩略图等）
processing/*/thumbnails/
*/thumbnails/
thumb_*.jpg
thumb_*.png
thumb_*.jpeg

# 临时和缓存文件
*.tmp
*.temp
.torrent.db*
*.torrent