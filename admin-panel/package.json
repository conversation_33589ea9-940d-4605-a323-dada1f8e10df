{"name": "admin-panel", "version": "1.0.0", "description": "JAVFLIX.TV管理后台", "main": "index.js", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint --ext .js,.vue src", "start": "npm run dev"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@vueuse/core": "^9.13.0", "axios": "^1.4.0", "echarts": "^5.6.0", "element-plus": "^2.3.8", "socket.io-client": "^4.8.1", "vue": "^3.3.4", "vue-router": "^4.2.4", "vuex": "^4.1.0"}, "devDependencies": {"@vitejs/plugin-vue": "^4.2.3", "eslint": "^8.46.0", "prettier": "^3.0.1", "vite": "^4.4.9"}}