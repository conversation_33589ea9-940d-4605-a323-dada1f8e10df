<template>
  <div class="dashboard-container">
    <div class="page-header">
      <h2>系统概览</h2>
    </div>
    
    <!-- 数据统计卡片 -->
    <el-row :gutter="20">
      <el-col :span="6">
        <el-card shadow="hover" class="stat-card">
          <div class="stat-card-inner">
            <div class="stat-icon video-icon">
              <i class="el-icon-video-camera"></i>
            </div>
            <div class="stat-content">
              <div class="stat-title">视频总数</div>
              <div class="stat-value">{{ stats.videoCount }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card shadow="hover" class="stat-card">
          <div class="stat-card-inner">
            <div class="stat-icon star-icon">
              <i class="el-icon-user"></i>
            </div>
            <div class="stat-content">
              <div class="stat-title">演员总数</div>
              <div class="stat-value">{{ stats.starCount }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card shadow="hover" class="stat-card">
          <div class="stat-card-inner">
            <div class="stat-icon user-icon">
              <i class="el-icon-user-solid"></i>
            </div>
            <div class="stat-content">
              <div class="stat-title">用户总数</div>
              <div class="stat-value">{{ stats.userCount }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card shadow="hover" class="stat-card">
          <div class="stat-card-inner">
            <div class="stat-icon magnet-icon">
              <i class="el-icon-download"></i>
            </div>
            <div class="stat-content">
              <div class="stat-title">磁力链接数</div>
              <div class="stat-value">{{ stats.magnetCount }}</div>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :span="6" style="margin-top: 20px;">
        <el-card shadow="hover" class="stat-card">
          <div class="stat-card-inner">
            <div class="stat-icon genre-icon">
              <i class="el-icon-collection-tag"></i>
            </div>
            <div class="stat-content">
              <div class="stat-title">标签总数</div>
              <div class="stat-value">{{ stats.genreCount }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
    
    <!-- 最近添加的视频 -->
    <el-card class="box-card" style="margin-top: 20px;">
      <template #header>
        <div class="card-header">
          <span>最近添加的视频</span>
          <el-button class="button" type="text" @click="goToVideoList">查看全部</el-button>
        </div>
      </template>
      <el-table :data="recentVideos" style="width: 100%" v-loading="loadingVideos">
        <el-table-column prop="movie_id" label="影片ID" width="120" />
        <el-table-column prop="title" label="影片标题" />
        <el-table-column prop="release_date" label="发行日期" width="120" />
        <el-table-column label="操作" width="150">
          <template #default="scope">
            <el-button size="small" @click="goToVideoDetail(scope.row.id)">查看</el-button>
            <el-button size="small" type="primary" @click="goToVideoEdit(scope.row.id)">编辑</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
    
    <!-- 热门演员 -->
    <el-card class="box-card" style="margin-top: 20px;">
      <template #header>
        <div class="card-header">
          <span>热门演员</span>
          <el-button class="button" type="text" @click="goToStarList">查看全部</el-button>
        </div>
      </template>
      <div class="star-list">
        <div v-for="star in popularStars" :key="star.id" class="star-item">
          <el-avatar :size="80" :src="star.image_url || 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png'" />
          <div class="star-name">{{ star.name }}</div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { getRecentVideos } from '../api/video'
import { getMovieStats, getStarStats, getUserStats, getMagnetStats, getGenreStats } from '../api/stats' // 导入统计API

export default {
  name: 'Dashboard',
  setup() {
    const router = useRouter()
    const loadingVideos = ref(false)
    const recentVideos = ref([]) // 确保 recentVideos 初始化为数组
    
    const stats = reactive({
      videoCount: 0,
      starCount: 0,
      userCount: 0,
      magnetCount: 0,
      genreCount: 0
    })
    
    const popularStars = ref([])
    
    // 加载最近视频
    const loadRecentVideos = async () => {
      loadingVideos.value = true
      try {
        console.log('开始加载最近视频...');
        const response = await getRecentVideos({ limit: 5 })
        console.log('最近视频API响应:', response);

        // 处理不同的响应格式
        if (Array.isArray(response)) {
          recentVideos.value = response;
        } else if (response?.items && Array.isArray(response.items)) {
          recentVideos.value = response.items;
        } else if (response?.data && Array.isArray(response.data)) {
          recentVideos.value = response.data;
        } else {
          console.warn('未知的响应格式:', response);
          recentVideos.value = [];
        }

        console.log('最近视频数据:', recentVideos.value);
      } catch (error) {
        console.error('加载最近视频失败:', error)
        recentVideos.value = [] // 出错时设置为空数组
      } finally {
        loadingVideos.value = false
      }
    }
    
    // 加载统计数据
    const loadStats = async () => {
      try {
        console.log('开始加载统计数据...');

        // 并行获取所有统计数据
        const [movieResponse, starResponse, userResponse, genreResponse] = await Promise.allSettled([
          getMovieStats(),
          getStarStats(),
          getUserStats(),
          getGenreStats()
        ]);

        // 处理影片统计数据
        if (movieResponse.status === 'fulfilled') {
          const movieData = movieResponse.value;
          stats.videoCount = movieData?.totalMovies ||
                           movieData?.data?.totalMovies ||
                           0;
          console.log('影片统计数据:', movieData);
        } else {
          console.warn('获取影片统计失败:', movieResponse.reason);
          stats.videoCount = 0;
        }

        // 处理演员统计数据
        if (starResponse.status === 'fulfilled') {
          const starData = starResponse.value;
          stats.starCount = starData?.totalStars ||
                          starData?.data?.totalStars ||
                          0;
          console.log('演员统计数据:', starData);
        } else {
          console.warn('获取演员统计失败:', starResponse.reason);
          stats.starCount = 0;
        }

        // 处理用户统计数据
        if (userResponse.status === 'fulfilled') {
          const userData = userResponse.value;
          stats.userCount = userData?.totalUsers ||
                          userData?.data?.totalUsers ||
                          userData?.count ||
                          0;
          console.log('用户统计数据:', userData);
        } else {
          console.warn('获取用户统计失败:', userResponse.reason);
          stats.userCount = 0;
        }

        // 处理类型统计数据
        if (genreResponse.status === 'fulfilled') {
          const genreData = genreResponse.value;
          stats.genreCount = genreData?.totalGenres ||
                           genreData?.data?.totalGenres ||
                           genreData?.count ||
                           0;
          console.log('类型统计数据:', genreData);
        } else {
          console.warn('获取类型统计失败:', genreResponse.reason);
          stats.genreCount = 0;
        }

        // 单独处理磁力链接统计
        try {
          const magnetResponse = await getMagnetStats();
          stats.magnetCount = magnetResponse?.totalMagnets ||
                              magnetResponse?.data?.totalMagnets ||
                              magnetResponse?.count ||
                              0;
          console.log('磁力链接统计数据:', magnetResponse);
        } catch (magnetError) {
          console.warn('获取磁力链接统计失败:', magnetError);
          stats.magnetCount = 0;
        }

        console.log('统计数据加载完成:', stats);
      } catch (error) {
        console.error('加载统计数据失败:', error);
        // 如果出现意外错误，将所有统计设为 0
        stats.videoCount = 0;
        stats.starCount = 0;
        stats.userCount = 0;
        stats.magnetCount = 0;
        stats.genreCount = 0;
      }
    }
    
    const goToVideoList = () => {
      router.push('/videos')
    }
    
    const goToVideoDetail = (id) => {
      router.push(`/videos/detail/${id}`)
    }
    
    const goToVideoEdit = (id) => {
      router.push(`/videos/edit/${id}`)
    }
    
    const goToStarList = () => {
      router.push('/stars')
    }
    
    onMounted(() => {
      loadRecentVideos()
      loadStats()
    })
    
    return {
      stats,
      recentVideos,
      popularStars,
      loadingVideos,
      goToVideoList,
      goToVideoDetail,
      goToVideoEdit,
      goToStarList
    }
  }
}
</script>

<style scoped>
.dashboard-container {
  padding: 20px 0;
}

.stat-card {
  height: 120px;
}

.stat-card-inner {
  display: flex;
  align-items: center;
  height: 100%;
}

.stat-icon {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 20px;
}

.stat-icon i {
  font-size: 40px;
  color: white;
}

.video-icon {
  background-color: #409EFF;
}

.star-icon {
  background-color: #67C23A;
}

.user-icon {
  background-color: #E6A23C;
}

.magnet-icon {
  background-color: #F56C6C;
}

.stat-content {
  display: flex;
  flex-direction: column;
}

.stat-title {
  font-size: 16px;
  color: #909399;
}

.stat-value {
  font-size: 28px;
  font-weight: bold;
  color: #303133;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.star-list {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
}

.star-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100px;
}

.star-name {
  margin-top: 10px;
  text-align: center;
}

.stat-icon.genre-icon {
  background-color: #67C23A; /* Element Plus success color */
}
.stat-icon.genre-icon i {
  color: white;
}
</style>