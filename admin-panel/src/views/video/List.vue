<template>
  <div class="video-list-container">
    <!-- 页面标题和操作按钮 -->
    <div class="page-header">
      <h2>视频管理</h2>
      <div>
        <el-button type="primary" @click="goToCreate">添加视频</el-button>
        <el-button type="success" @click="goToImport">从JavBus导入</el-button>
      </div>
    </div>
    
    <!-- 搜索和过滤 -->
    <el-card shadow="never" style="margin-bottom: 20px;">
      <div class="filter-container">
        <el-input
          v-model="queryParams.keyword"
          placeholder="请输入视频ID或标题"
          style="width: 200px; margin-right: 10px;"
          clearable
          @keyup.enter="handleSearch"
        />
        <el-button type="primary" @click="handleSearch">搜索</el-button>
        <el-button @click="resetSearch">重置</el-button>
      </div>
    </el-card>
    
    <!-- 视频列表 -->
    <el-card shadow="never">
      <el-table
        :data="videoList"
        style="width: 100%"
        v-loading="loading"
        border
      >
        <el-table-column prop="movie_id" label="影片ID" width="120" />
        <el-table-column label="封面" width="100">
          <template #default="scope">
            <el-image
              style="width: 60px; height: 80px"
              :src="scope.row.image_url"
              fit="cover"
              :preview-src-list="[scope.row.image_url]"
            >
              <template #error>
                <div class="image-error">
                  <i class="el-icon-picture-outline"></i>
                </div>
              </template>
            </el-image>
          </template>
        </el-table-column>
        <el-table-column prop="title" label="标题" show-overflow-tooltip />
        <el-table-column prop="release_date" label="发行日期" width="100" />
        <el-table-column prop="duration" label="时长" width="80" />
        <el-table-column label="演员" show-overflow-tooltip>
          <template #default="scope">
            <span>{{ formatStars(scope.row.stars) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="磁链数" width="80">
          <template #default="scope">
            <el-tag>{{ scope.row.magnet_count || 0 }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="250">
          <template #default="scope">
            <el-button size="small" @click="goToDetail(scope.row.id)">查看</el-button>
            <el-button 
              size="small"
              type="primary"
              @click="goToEdit(scope.row.id)"
            >
              编辑
            </el-button>
            <el-button
              size="small"
              type="danger"
              @click="handleDelete(scope.row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          background
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          :page-size="queryParams.limit"
          :page-sizes="[10, 20, 50, 100]"
          :current-page="queryParams.page"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getVideoList, deleteVideo } from '../../api/video'

export default {
  name: 'VideoList',
  setup() {
    const router = useRouter()
    const loading = ref(false)
    const videoList = ref([])
    const total = ref(0)
    
    // 查询参数
    const queryParams = reactive({
      page: 1,
      limit: 10,
      keyword: '',
      sort: 'release_date',
      order: 'desc'
    })
    
    // 获取视频列表
    const getVideos = async () => {
      loading.value = true
      try {
        console.log('获取视频列表，参数:', queryParams);
        const data = await getVideoList(queryParams)
        console.log('视频列表API响应:', data);

        // 处理不同的响应格式
        if (Array.isArray(data)) {
          videoList.value = data;
          total.value = data.length;
        } else if (data?.items && Array.isArray(data.items)) {
          videoList.value = data.items;
          total.value = data.pagination?.totalItems || data.total || data.items.length;
        } else if (data?.data && Array.isArray(data.data)) {
          videoList.value = data.data;
          total.value = data.pagination?.totalItems || data.total || data.data.length;
        } else {
          console.warn('未知的响应格式:', data);
          videoList.value = [];
          total.value = 0;
        }

        console.log('处理后的视频列表:', videoList.value);
        console.log('总数:', total.value);
      } catch (error) {
        console.error('获取视频列表失败:', error)
        ElMessage.error('获取视频列表失败: ' + (error.message || '未知错误'))
        videoList.value = [];
        total.value = 0;
      } finally {
        loading.value = false
      }
    }
    
    // 格式化演员列表
    const formatStars = (stars) => {
      if (!stars || !stars.length) return '无演员信息'
      return stars.map(star => star.name).join(', ')
    }
    
    // 搜索
    const handleSearch = () => {
      queryParams.page = 1
      getVideos()
    }
    
    // 重置搜索
    const resetSearch = () => {
      queryParams.keyword = ''
      queryParams.page = 1
      getVideos()
    }
    
    // 改变每页显示数量
    const handleSizeChange = (val) => {
      queryParams.limit = val
      getVideos()
    }
    
    // 改变页码
    const handleCurrentChange = (val) => {
      queryParams.page = val
      getVideos()
    }
    
    // 删除视频
    const handleDelete = (row) => {
      ElMessageBox.confirm(
        `确定要删除《${row.title}》吗？此操作不可恢复！`,
        '警告',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )
        .then(async () => {
          try {
            await deleteVideo(row.id)
            ElMessage.success('删除成功')
            getVideos()
          } catch (error) {
            console.error('删除失败:', error)
            ElMessage.error('删除失败')
          }
        })
        .catch(() => {
          ElMessage.info('已取消删除')
        })
    }
    
    // 跳转到详情
    const goToDetail = (id) => {
      router.push(`/videos/detail/${id}`)
    }
    
    // 跳转到编辑
    const goToEdit = (id) => {
      router.push(`/videos/edit/${id}`)
    }
    
    // 跳转到创建
    const goToCreate = () => {
      router.push('/videos/create')
    }
    
    // 跳转到导入
    const goToImport = () => {
      router.push('/import')
    }
    
    onMounted(() => {
      getVideos()
    })
    
    return {
      loading,
      videoList,
      total,
      queryParams,
      formatStars,
      handleSearch,
      resetSearch,
      handleSizeChange,
      handleCurrentChange,
      handleDelete,
      goToDetail,
      goToEdit,
      goToCreate,
      goToImport
    }
  }
}
</script>

<style scoped>
.video-list-container {
  padding: 20px 0;
}

.filter-container {
  display: flex;
  align-items: center;
}

.image-error {
  width: 60px;
  height: 80px;
  background-color: #f5f7fa;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #909399;
}
</style> 