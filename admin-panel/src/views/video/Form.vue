<template>
  <div class="video-form-container">
    <div class="page-header">
      <h2>{{ isEdit ? '编辑视频' : '添加视频' }}</h2>
      <div>
        <el-button @click="goBack">返回</el-button>
        <el-button type="primary" @click="saveVideo" :loading="loading">保存</el-button>
      </div>
    </div>

    <el-card shadow="never">
      <el-form 
        ref="videoFormRef" 
        :model="videoForm" 
        :rules="rules" 
        label-width="120px"
        v-loading="formLoading"
      >
        <el-row :gutter="20">
          <el-col :span="16">
            <!-- 基本信息 -->
            <el-form-item label="影片ID" prop="movie_id">
              <el-input v-model="videoForm.movie_id" placeholder="请输入影片ID" />
            </el-form-item>

            <el-form-item label="标题" prop="title">
              <el-input v-model="videoForm.title" placeholder="请输入标题" />
            </el-form-item>

            <el-form-item label="发行日期" prop="release_date">
              <el-date-picker
                v-model="videoForm.release_date"
                type="date"
                placeholder="选择日期"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>

            <el-form-item label="时长" prop="duration">
              <el-input v-model="videoForm.duration" placeholder="例如：120分钟" />
            </el-form-item>

            <el-form-item label="导演">
              <el-input v-model="videoForm.director" placeholder="请输入导演名称" />
            </el-form-item>

            <el-form-item label="制作商">
              <el-input v-model="videoForm.maker" placeholder="请输入制作商名称" />
            </el-form-item>

            <el-form-item label="发行商">
              <el-input v-model="videoForm.label" placeholder="请输入发行商名称" />
            </el-form-item>

            <el-form-item label="描述">
              <el-input 
                v-model="videoForm.description" 
                type="textarea" 
                rows="4"
                placeholder="请输入影片描述" 
              />
            </el-form-item>
          </el-col>

          <el-col :span="8">
            <!-- 封面图 -->
            <el-form-item label="封面图">
              <div class="upload-container">
                <el-image
                  v-if="videoForm.image_url"
                  :src="videoForm.image_url"
                  style="width: 100%; max-width: 200px; max-height: 280px"
                  fit="cover"
                >
                  <template #error>
                    <div class="image-error">
                      <i class="el-icon-picture-outline"></i>
                    </div>
                  </template>
                </el-image>
                <div v-else class="image-placeholder">
                  <i class="el-icon-picture-outline"></i>
                  <span>暂无封面</span>
                </div>

                <div class="upload-actions">
                  <el-button size="small" type="primary">上传图片</el-button>
                  <el-button 
                    size="small" 
                    type="danger" 
                    @click="videoForm.image_url = ''"
                    v-if="videoForm.image_url"
                  >
                    删除
                  </el-button>
                </div>
                <el-input 
                  v-model="videoForm.image_url" 
                  placeholder="或输入图片URL"
                  style="margin-top: 10px"
                />
              </div>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 类型选择 -->
        <el-form-item label="类型">
          <el-select
            v-model="videoForm.genres"
            multiple
            filterable
            placeholder="请选择类型"
            style="width: 100%"
          >
            <el-option
              v-for="item in genreOptions"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>

        <!-- 演员选择 -->
        <el-form-item label="演员">
          <el-select
            v-model="videoForm.stars"
            multiple
            filterable
            placeholder="请选择演员"
            style="width: 100%"
          >
            <el-option
              v-for="item in starOptions"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>

        <!-- 磁力链接 -->
        <el-form-item label="磁力链接">
          <div class="magnets-container">
            <div v-for="(magnet, index) in videoForm.magnets" :key="index" class="magnet-item">
              <el-input
                v-model="magnet.link"
                placeholder="磁力链接"
                style="margin-bottom: 10px"
              />
              <div class="magnet-item-row">
                <el-input
                  v-model="magnet.title"
                  placeholder="标题"
                  style="width: 60%; margin-right: 10px"
                />
                <el-input
                  v-model="magnet.size"
                  placeholder="大小"
                  style="width: 20%; margin-right: 10px"
                />
                <el-button 
                  type="danger" 
                  size="small" 
                  icon="el-icon-delete"
                  @click="removeMagnet(index)"
                />
              </div>
              <div class="magnet-item-row" style="margin-top: 10px">
                <el-checkbox v-model="magnet.is_hd">高清</el-checkbox>
                <el-checkbox v-model="magnet.has_subtitle">字幕</el-checkbox>
              </div>
            </div>

            <el-button type="primary" plain @click="addMagnet">添加磁力链接</el-button>
          </div>
        </el-form-item>

        <!-- 样品图片 -->
        <el-form-item label="样品图片">
          <div class="samples-container">
            <div v-for="(sample, index) in videoForm.samples" :key="index" class="sample-item">
              <el-image
                :src="sample.img"
                style="width: 160px; height: 120px; margin-right: 10px"
                fit="cover"
              >
                <template #error>
                  <div class="sample-error">
                    <i class="el-icon-picture-outline"></i>
                  </div>
                </template>
              </el-image>
              <el-button 
                type="danger" 
                size="small" 
                icon="el-icon-delete"
                @click="removeSample(index)"
              />
            </div>

            <div class="sample-add">
              <el-input 
                v-model="newSampleUrl" 
                placeholder="输入样品图片URL"
                style="width: 300px; margin-right: 10px"
              />
              <el-button type="primary" @click="addSample">添加</el-button>
            </div>
          </div>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { getVideoDetail, createVideo, updateVideo } from '../../api/video'

export default {
  name: 'VideoForm',
  setup() {
    const route = useRoute()
    const router = useRouter()
    const videoFormRef = ref(null)
    const loading = ref(false)
    const formLoading = ref(false)
    const newSampleUrl = ref('')

    // 获取当前是编辑还是添加
    const isEdit = computed(() => {
      return route.name === 'VideoEdit'
    })

    // 表单数据
    const videoForm = reactive({
      id: '',
      movie_id: '',
      title: '',
      image_url: '',
      release_date: '',
      duration: '',
      description: '',
      director: '',
      maker: '',
      label: '',
      genres: [],
      stars: [],
      magnets: [],
      samples: []
    })

    // 表单验证规则
    const rules = {
      movie_id: [
        { required: true, message: '请输入影片ID', trigger: 'blur' }
      ],
      title: [
        { required: true, message: '请输入影片标题', trigger: 'blur' }
      ],
      release_date: [
        { required: true, message: '请选择发行日期', trigger: 'change' }
      ]
    }

    // 类型选项，实际项目中应该从API获取
    const genreOptions = ref([
      { id: 1, name: '类型1' },
      { id: 2, name: '类型2' },
      { id: 3, name: '类型3' },
      { id: 4, name: '类型4' }
    ])

    // 演员选项，实际项目中应该从API获取
    const starOptions = ref([
      { id: 1, name: '演员1' },
      { id: 2, name: '演员2' },
      { id: 3, name: '演员3' },
      { id: 4, name: '演员4' }
    ])

    // 添加磁力链接
    const addMagnet = () => {
      videoForm.magnets.push({
        link: '',
        title: '',
        size: '',
        is_hd: false,
        has_subtitle: false
      })
    }

    // 移除磁力链接
    const removeMagnet = (index) => {
      videoForm.magnets.splice(index, 1)
    }

    // 添加样品图片
    const addSample = () => {
      if (!newSampleUrl.value) {
        ElMessage.warning('请输入样品图片URL')
        return
      }

      videoForm.samples.push({
        img: newSampleUrl.value,
        size: { width: 800, height: 600 }
      })

      newSampleUrl.value = ''
    }

    // 移除样品图片
    const removeSample = (index) => {
      videoForm.samples.splice(index, 1)
    }

    // 加载视频详情（编辑模式）
    const loadVideoDetail = async (id) => {
      formLoading.value = true
      try {
        const data = await getVideoDetail(id)
        
        // 填充表单数据
        Object.assign(videoForm, {
          id: data.id,
          movie_id: data.movie_id,
          title: data.title,
          image_url: data.image_url,
          release_date: data.release_date,
          duration: data.duration,
          description: data.description,
          director: data.director?.name || '',
          maker: data.maker?.name || '',
          label: data.label?.name || '',
          genres: data.genres?.map(g => g.id) || [],
          stars: data.stars?.map(s => s.id) || [],
          magnets: data.magnets || [],
          samples: data.samples || []
        })
      } catch (error) {
        console.error('加载视频详情失败:', error)
        ElMessage.error('加载视频详情失败')
      } finally {
        formLoading.value = false
      }
    }

    // 保存视频
    const saveVideo = async () => {
      try {
        await videoFormRef.value.validate()
        
        loading.value = true
        
        // 格式化提交数据
        const formData = {
          ...videoForm,
          // 处理演员和类型数据结构
          genres: videoForm.genres.map(id => ({ id })),
          stars: videoForm.stars.map(id => ({ id })),
          director: videoForm.director ? { name: videoForm.director } : null,
          maker: videoForm.maker ? { name: videoForm.maker } : null,
          label: videoForm.label ? { name: videoForm.label } : null
        }
        
        if (isEdit.value) {
          // 编辑模式
          await updateVideo(videoForm.id, formData)
          ElMessage.success('更新成功')
        } else {
          // 添加模式
          await createVideo(formData)
          ElMessage.success('添加成功')
        }
        
        // 返回列表页
        goBack()
      } catch (error) {
        if (error.message) {
          console.error('保存视频失败:', error)
          ElMessage.error('保存失败: ' + error.message)
        }
      } finally {
        loading.value = false
      }
    }

    // 返回列表页
    const goBack = () => {
      router.push('/videos')
    }

    onMounted(() => {
      // 如果是编辑模式，加载视频详情
      if (isEdit.value && route.params.id) {
        loadVideoDetail(route.params.id)
      } else {
        // 添加模式，初始化空的磁力链接
        addMagnet()
      }
      
      // 实际项目中，这里应该加载类型和演员列表
      // loadGenres()
      // loadStars()
    })

    return {
      videoFormRef,
      videoForm,
      loading,
      formLoading,
      isEdit,
      rules,
      genreOptions,
      starOptions,
      newSampleUrl,
      addMagnet,
      removeMagnet,
      addSample,
      removeSample,
      saveVideo,
      goBack
    }
  }
}
</script>

<style scoped>
.video-form-container {
  padding: 20px 0;
}

.upload-container {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.image-placeholder {
  width: 200px;
  height: 280px;
  background-color: #f5f7fa;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: #909399;
}

.image-placeholder i {
  font-size: 40px;
  margin-bottom: 10px;
}

.upload-actions {
  margin-top: 10px;
  display: flex;
  justify-content: center;
  gap: 10px;
}

.magnet-item {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 15px;
  margin-bottom: 15px;
}

.magnet-item-row {
  display: flex;
  align-items: center;
}

.samples-container {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  margin-bottom: 15px;
}

.sample-item {
  position: relative;
}

.sample-item .el-button {
  position: absolute;
  top: 5px;
  right: 15px;
}

.sample-error {
  width: 160px;
  height: 120px;
  background-color: #f5f7fa;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #909399;
}

.sample-add {
  display: flex;
  align-items: center;
}
</style> 