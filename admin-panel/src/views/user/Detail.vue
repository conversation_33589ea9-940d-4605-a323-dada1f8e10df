<template>
  <div class="user-detail-container">
    <div class="page-header">
      <h2>用户详情</h2>
      <el-button type="primary" @click="goToEdit">编辑用户</el-button>
    </div>
    
    <el-card class="info-card" v-loading="loading">
      <el-descriptions title="基本信息" border>
        <el-descriptions-item label="ID">{{ userInfo.id }}</el-descriptions-item>
        <el-descriptions-item label="用户名">{{ userInfo.username }}</el-descriptions-item>
        <el-descriptions-item label="邮箱">{{ userInfo.email }}</el-descriptions-item>
        <el-descriptions-item label="角色">
          <el-tag :type="getRoleTagType(userInfo.role)">
            {{ formatRole(userInfo.role) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag :type="userInfo.status === 'active' ? 'success' : 'danger'">
            {{ userInfo.status === 'active' ? '正常' : '禁用' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="注册时间">{{ formatDate(userInfo.created_at) }}</el-descriptions-item>
        <el-descriptions-item label="最后登录">{{ formatDate(userInfo.last_login) }}</el-descriptions-item>
        <el-descriptions-item label="收藏数量">{{ userInfo.favorites_count || 0 }}</el-descriptions-item>
        <el-descriptions-item label="观看历史">{{ userInfo.history_count || 0 }}</el-descriptions-item>
      </el-descriptions>
    </el-card>
    
    <!-- 用户收藏列表 -->
    <el-card class="favorites-card" style="margin-top: 20px;">
      <template #header>
        <div class="card-header">
          <span>收藏的视频</span>
        </div>
      </template>
      <el-table :data="favorites" border style="width: 100%" v-loading="loadingFavorites">
        <el-table-column prop="movie_id" label="影片ID" width="120" />
        <el-table-column prop="title" label="影片标题" />
        <el-table-column prop="added_date" label="收藏时间" width="180">
          <template #default="scope">
            {{ formatDate(scope.row.added_date) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150">
          <template #default="scope">
            <el-button size="small" @click="goToMovieDetail(scope.row.movie_id)">查看视频</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination-container" v-if="favorites.length > 0">
        <el-pagination
          background
          layout="prev, pager, next"
          :total="totalFavorites"
          :page-size="favoritesPageSize"
          :current-page="favoritesCurrentPage"
          @current-change="handleFavoritesPageChange"
        />
      </div>
      <div v-else class="no-data">暂无收藏记录</div>
    </el-card>
    
    <!-- 用户观看历史 -->
    <el-card class="history-card" style="margin-top: 20px;">
      <template #header>
        <div class="card-header">
          <span>观看历史</span>
        </div>
      </template>
      <el-table :data="history" border style="width: 100%" v-loading="loadingHistory">
        <el-table-column prop="movie_id" label="影片ID" width="120" />
        <el-table-column prop="title" label="影片标题" />
        <el-table-column prop="view_date" label="观看时间" width="180">
          <template #default="scope">
            {{ formatDate(scope.row.view_date) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150">
          <template #default="scope">
            <el-button size="small" @click="goToMovieDetail(scope.row.movie_id)">查看视频</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination-container" v-if="history.length > 0">
        <el-pagination
          background
          layout="prev, pager, next"
          :total="totalHistory"
          :page-size="historyPageSize"
          :current-page="historyCurrentPage"
          @current-change="handleHistoryPageChange"
        />
      </div>
      <div v-else class="no-data">暂无观看记录</div>
    </el-card>
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { getUserDetail } from '../../api/user'

export default {
  name: 'UserDetail',
  setup() {
    const router = useRouter()
    const route = useRoute()
    const loading = ref(false)
    const loadingFavorites = ref(false)
    const loadingHistory = ref(false)
    const userId = route.params.id
    
    // 用户基本信息
    const userInfo = reactive({
      id: '',
      username: '',
      email: '',
      role: '',
      status: 'active',
      created_at: '',
      last_login: '',
      favorites_count: 0,
      history_count: 0
    })
    
    // 收藏列表数据
    const favorites = ref([])
    const totalFavorites = ref(0)
    const favoritesPageSize = ref(5)
    const favoritesCurrentPage = ref(1)
    
    // 历史记录数据
    const history = ref([])
    const totalHistory = ref(0)
    const historyPageSize = ref(5)
    const historyCurrentPage = ref(1)
    
    // 获取用户详情
    const loadUserInfo = async () => {
      loading.value = true
      try {
        const response = await getUserDetail(userId)
        Object.assign(userInfo, response)
        
        // 加载收藏和历史记录
        loadFavorites()
        loadHistory()
      } catch (error) {
        console.error('加载用户信息失败:', error)
        ElMessage.error('加载用户信息失败')
      } finally {
        loading.value = false
      }
    }
    
    // 加载用户收藏
    const loadFavorites = async () => {
      loadingFavorites.value = true
      try {
        // 这里应该有一个API获取用户收藏
        // const response = await getUserFavorites(userId, {
        //   page: favoritesCurrentPage.value,
        //   limit: favoritesPageSize.value
        // })
        // favorites.value = response.items || []
        // totalFavorites.value = response.total || 0
        
        // 模拟数据
        setTimeout(() => {
          favorites.value = [
            { movie_id: 'ABC-123', title: '模拟收藏视频1', added_date: '2025-04-15T10:30:00Z' },
            { movie_id: 'DEF-456', title: '模拟收藏视频2', added_date: '2025-04-10T14:20:00Z' }
          ]
          totalFavorites.value = 2
          loadingFavorites.value = false
        }, 500)
      } catch (error) {
        console.error('加载用户收藏失败:', error)
        ElMessage.error('加载用户收藏失败')
        favorites.value = []
        totalFavorites.value = 0
        loadingFavorites.value = false
      }
    }
    
    // 加载用户历史
    const loadHistory = async () => {
      loadingHistory.value = true
      try {
        // 这里应该有一个API获取用户历史
        // const response = await getUserHistory(userId, {
        //   page: historyCurrentPage.value,
        //   limit: historyPageSize.value
        // })
        // history.value = response.items || []
        // totalHistory.value = response.total || 0
        
        // 模拟数据
        setTimeout(() => {
          history.value = [
            { movie_id: 'GHI-789', title: '模拟观看历史1', view_date: '2025-05-20T16:45:00Z' },
            { movie_id: 'JKL-012', title: '模拟观看历史2', view_date: '2025-05-19T09:15:00Z' },
            { movie_id: 'MNO-345', title: '模拟观看历史3', view_date: '2025-05-15T22:30:00Z' }
          ]
          totalHistory.value = 3
          loadingHistory.value = false
        }, 800)
      } catch (error) {
        console.error('加载用户历史失败:', error)
        ElMessage.error('加载用户历史失败')
        history.value = []
        totalHistory.value = 0
        loadingHistory.value = false
      }
    }
    
    // 格式化日期
    const formatDate = (dateString) => {
      if (!dateString) return '—'
      const date = new Date(dateString)
      return date.toLocaleString()
    }
    
    // 格式化角色
    const formatRole = (role) => {
      const roleMap = {
        'user': '普通用户',
        'vip': 'VIP用户',
        'admin': '管理员'
      }
      return roleMap[role] || role
    }
    
    // 获取角色标签类型
    const getRoleTagType = (role) => {
      const typeMap = {
        'user': '',
        'vip': 'success',
        'admin': 'warning'
      }
      return typeMap[role] || 'info'
    }
    
    // 分页处理
    const handleFavoritesPageChange = (page) => {
      favoritesCurrentPage.value = page
      loadFavorites()
    }
    
    const handleHistoryPageChange = (page) => {
      historyCurrentPage.value = page
      loadHistory()
    }
    
    // 导航功能
    const goToEdit = () => {
      router.push(`/users/edit/${userId}`)
    }
    
    const goToMovieDetail = (id) => {
      router.push(`/videos/detail/${id}`)
    }
    
    onMounted(() => {
      loadUserInfo()
    })
    
    return {
      userInfo,
      loading,
      favorites,
      totalFavorites,
      favoritesPageSize,
      favoritesCurrentPage,
      loadingFavorites,
      history,
      totalHistory,
      historyPageSize,
      historyCurrentPage,
      loadingHistory,
      formatDate,
      formatRole,
      getRoleTagType,
      handleFavoritesPageChange,
      handleHistoryPageChange,
      goToEdit,
      goToMovieDetail
    }
  }
}
</script>

<style scoped>
.user-detail-container {
  padding: 20px 0;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.info-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.no-data {
  text-align: center;
  padding: 30px;
  color: #909399;
}
</style>
