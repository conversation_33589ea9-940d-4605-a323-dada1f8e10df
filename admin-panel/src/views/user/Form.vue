<template>
  <div class="user-form-container">
    <div class="page-header">
      <h2>{{ isEdit ? '编辑用户' : '添加用户' }}</h2>
    </div>
    
    <el-card class="form-card">
      <el-form 
        ref="formRef" 
        :model="form" 
        :rules="rules" 
        label-width="100px" 
        v-loading="loading"
      >
        <el-form-item label="用户名" prop="username">
          <el-input v-model="form.username" placeholder="请输入用户名" :disabled="isEdit" />
        </el-form-item>
        
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="form.email" placeholder="请输入邮箱" />
        </el-form-item>
        
        <el-form-item label="密码" prop="password" v-if="!isEdit">
          <el-input v-model="form.password" type="password" placeholder="请输入密码" show-password />
        </el-form-item>
        
        <el-form-item label="确认密码" prop="confirmPassword" v-if="!isEdit">
          <el-input v-model="form.confirmPassword" type="password" placeholder="请确认密码" show-password />
        </el-form-item>
        
        <el-form-item label="角色" prop="role">
          <el-select v-model="form.role" placeholder="请选择角色">
            <el-option label="普通用户" value="user" />
            <el-option label="VIP用户" value="vip" />
            <el-option label="管理员" value="admin" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="状态" prop="status">
          <el-select v-model="form.status" placeholder="请选择状态">
            <el-option label="正常" value="active" />
            <el-option label="禁用" value="disabled" />
          </el-select>
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="submitForm">保存</el-button>
          <el-button @click="goBack">取消</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { getUserDetail, updateUser, createUser } from '../../api/user'

export default {
  name: 'UserForm',
  setup() {
    const router = useRouter()
    const route = useRoute()
    const formRef = ref(null)
    const loading = ref(false)
    
    // 计算当前是否为编辑模式
    const isEdit = computed(() => route.path.includes('edit'))
    
    // 表单数据
    const form = reactive({
      username: '',
      email: '',
      password: '',
      confirmPassword: '',
      role: 'user',
      status: 'active'
    })
    
    // 表单验证规则
    const rules = {
      username: [
        { required: true, message: '请输入用户名', trigger: 'blur' },
        { min: 3, max: 20, message: '长度在3到20个字符之间', trigger: 'blur' }
      ],
      email: [
        { required: true, message: '请输入邮箱', trigger: 'blur' },
        { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
      ],
      password: [
        { required: !isEdit.value, message: '请输入密码', trigger: 'blur' },
        { min: 6, message: '密码长度至少为6个字符', trigger: 'blur' }
      ],
      confirmPassword: [
        { required: !isEdit.value, message: '请确认密码', trigger: 'blur' },
        {
          validator: (rule, value, callback) => {
            if (value !== form.password) {
              callback(new Error('两次输入的密码不一致'))
            } else {
              callback()
            }
          },
          trigger: 'blur'
        }
      ],
      role: [
        { required: true, message: '请选择角色', trigger: 'change' }
      ],
      status: [
        { required: true, message: '请选择状态', trigger: 'change' }
      ]
    }
    
    // 如果是编辑模式，加载用户信息
    const loadUserInfo = async (id) => {
      loading.value = true
      try {
        const response = await getUserDetail(id)
        // 清除密码相关字段
        delete form.password
        delete form.confirmPassword
        
        // 使用API返回的数据更新表单
        Object.assign(form, response)
      } catch (error) {
        console.error('加载用户信息失败:', error)
        ElMessage.error('加载用户信息失败')
      } finally {
        loading.value = false
      }
    }
    
    // 提交表单
    const submitForm = async () => {
      if (!formRef.value) return
      
      await formRef.value.validate(async (valid) => {
        if (!valid) {
          ElMessage.error('请正确填写表单！')
          return false
        }
        
        // 移除确认密码字段，后端API不需要
        const submitData = { ...form }
        delete submitData.confirmPassword
        
        loading.value = true
        try {
          if (isEdit.value) {
            const id = route.params.id
            await updateUser(id, submitData)
            ElMessage.success('更新用户信息成功')
          } else {
            await createUser(submitData)
            ElMessage.success('添加用户成功')
          }
          goBack()
        } catch (error) {
          console.error('保存用户信息失败:', error)
          ElMessage.error('保存用户信息失败')
        } finally {
          loading.value = false
        }
      })
    }
    
    // 返回列表页
    const goBack = () => {
      router.push('/users')
    }
    
    onMounted(() => {
      if (isEdit.value && route.params.id) {
        loadUserInfo(route.params.id)
      }
    })
    
    return {
      formRef,
      form,
      rules,
      loading,
      isEdit,
      submitForm,
      goBack
    }
  }
}
</script>

<style scoped>
.user-form-container {
  padding: 20px 0;
}

.page-header {
  margin-bottom: 20px;
}

.form-card {
  max-width: 700px;
}
</style>
