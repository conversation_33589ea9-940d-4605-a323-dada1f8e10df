<template>
  <div class="video-processing-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1 class="page-title">
        <el-icon class="title-icon"><VideoPlay /></el-icon>
        视频处理监控
      </h1>
      <p class="page-description">实时监控磁力下载和视频处理进度</p>
    </div>

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :span="6">
        <el-card class="stat-card processing-card" shadow="hover">
          <div class="stat-content">
            <div class="stat-icon processing">
              <el-icon><Loading /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ stats.processing }}</div>
              <div class="stat-label">处理中</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stat-card completed-card" shadow="hover">
          <div class="stat-content">
            <div class="stat-icon completed">
              <el-icon><Check /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ stats.completed }}</div>
              <div class="stat-label">已完成</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stat-card failed-card" shadow="hover">
          <div class="stat-content">
            <div class="stat-icon failed">
              <el-icon><Close /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ stats.failed }}</div>
              <div class="stat-label">失败</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stat-card total-card" shadow="hover">
          <div class="stat-content">
            <div class="stat-icon total">
              <el-icon><DataBoard /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ stats.total }}</div>
              <div class="stat-label">总任务</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 处理任务列表 -->
    <el-card class="task-list-card" shadow="never">
      <template #header>
        <div class="card-header">
          <span class="card-title">
            <el-icon><List /></el-icon>
            处理任务列表
          </span>
          <div class="card-actions">
            <el-button 
              type="primary" 
              :icon="Refresh" 
              @click="refreshTasks"
              :loading="loading"
              size="small"
            >
              刷新
            </el-button>
          </div>
        </div>
      </template>

      <!-- 任务列表 -->
      <div class="task-list">
        <div
          v-for="task in tasks"
          :key="task.task_uuid"
          class="task-item"
          :class="getTaskStatusClass(task.status)"
        >
          <!-- 任务头部 -->
          <div class="task-header">
            <div class="task-info">
              <div class="task-title">
                <el-icon class="task-icon">
                  <VideoPlay v-if="task.status === 'processing'" />
                  <Loading v-else-if="task.status === 'downloading'" />
                  <Check v-else-if="task.status === 'completed'" />
                  <Close v-else-if="task.status === 'failed'" />
                  <Clock v-else />
                </el-icon>
                <span>{{ getMovieTitle(task.movie_id) }}</span>
              </div>
              <div class="task-meta">
                <span class="task-id">任务ID: {{ task.task_uuid.slice(0, 8) }}...</span>
                <span class="task-time">{{ formatTime(task.created_at) }}</span>
              </div>
            </div>
            <div class="task-status">
              <el-tag 
                :type="getStatusTagType(task.status)"
                effect="dark"
                size="large"
              >
                {{ getStatusText(task.status) }}
              </el-tag>
            </div>
          </div>

          <!-- 进度条 -->
          <div class="task-progress" v-if="task.status === 'processing' || task.status === 'downloading'">
            <el-progress 
              :percentage="task.progress || 0"
              :status="getProgressStatus(task.status)"
              :stroke-width="8"
              :show-text="true"
              :format="(percentage) => `${percentage}% - ${task.current_step || '处理中'}`"
            />
          </div>

          <!-- 任务详情 -->
          <div class="task-details" v-if="task.expanded">
            <el-row :gutter="20">
              <el-col :span="12">
                <div class="detail-item">
                  <span class="detail-label">磁力链接:</span>
                  <span class="detail-value magnet-link">{{ task.magnet_link }}</span>
                </div>
                <div class="detail-item">
                  <span class="detail-label">开始时间:</span>
                  <span class="detail-value">{{ formatTime(task.started_at) }}</span>
                </div>
              </el-col>
              <el-col :span="12">
                <div class="detail-item">
                  <span class="detail-label">当前步骤:</span>
                  <span class="detail-value">{{ task.current_step || '等待中' }}</span>
                </div>
                <div class="detail-item" v-if="task.error_message">
                  <span class="detail-label">错误信息:</span>
                  <span class="detail-value error-message">{{ task.error_message }}</span>
                </div>
              </el-col>
            </el-row>
          </div>

          <!-- 操作按钮 -->
          <div class="task-actions">
            <el-button 
              type="text" 
              @click="toggleTaskDetails(task)"
              size="small"
            >
              {{ task.expanded ? '收起' : '详情' }}
            </el-button>
            <el-button
              type="text"
              @click="retryTask(task)"
              v-if="task.status === 'failed'"
              size="small"
            >
              重试
            </el-button>
            <el-button
              type="text"
              @click="cancelTask(task)"
              v-if="task.status === 'processing' || task.status === 'downloading'"
              size="small"
              style="color: #f56c6c;"
            >
              取消并删除
            </el-button>
          </div>
        </div>

        <!-- 空状态 -->
        <div v-if="tasks.length === 0 && !loading" class="empty-state">
          <el-empty description="暂无处理任务" />
        </div>

        <!-- 加载状态 -->
        <div v-if="loading" class="loading-state">
          <el-skeleton :rows="3" animated />
        </div>
      </div>
    </el-card>
  </div>
</template>

<script>
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  VideoPlay,
  Loading,
  Check,
  Close,
  Clock,
  List,
  Refresh,
  DataBoard
} from '@element-plus/icons-vue'
import {
  getVideoTasks,
  getTaskStats,
  retryTask as retryTaskAPI,
  cancelTask as cancelTaskAPI
} from '../api/video-processing'

export default {
  name: 'VideoProcessing',
  components: {
    VideoPlay,
    Loading, 
    Check,
    Close,
    Clock,
    List,
    Refresh,
    DataBoard
  },
  setup() {
    const loading = ref(false)
    const socket = ref(null)
    
    // 统计数据
    const stats = reactive({
      processing: 0,
      completed: 0,
      failed: 0,
      total: 0
    })
    
    // 任务列表
    const tasks = ref([])

    // 电影信息缓存
    const movieCache = ref({})
    
    // 初始化WebSocket连接
    const initWebSocket = () => {
      const wsUrl = 'ws://localhost:8080/ws'
      socket.value = new WebSocket(wsUrl)

      socket.value.onopen = () => {
        console.log('WebSocket连接已建立')
        ElMessage.success('实时监控连接成功')
      }

      socket.value.onmessage = (event) => {
        try {
          const message = JSON.parse(event.data)
          handleWebSocketMessage(message)
        } catch (error) {
          console.error('解析WebSocket消息失败:', error)
        }
      }

      socket.value.onclose = () => {
        console.log('WebSocket连接已关闭')
        ElMessage.warning('实时监控连接断开')
        // 5秒后尝试重连
        setTimeout(() => {
          if (socket.value?.readyState === WebSocket.CLOSED) {
            initWebSocket()
          }
        }, 5000)
      }

      socket.value.onerror = (error) => {
        console.error('WebSocket错误:', error)
        ElMessage.error('实时监控连接错误')
      }
    }

    // 处理WebSocket消息
    const handleWebSocketMessage = (message) => {
      console.log('收到WebSocket消息:', message)

      // 处理Go服务器发送的消息格式
      if (message.taskId) {
        // 检查是否是进度检查消息（包含completed和total）
        if (message.completed !== undefined && message.total !== undefined) {
          // 这是进度检查消息，使用真实的进度数据
          const realProgress = Math.round((message.completed / message.total) * 100)
          const taskData = {
            taskUuid: message.taskId,
            progress: realProgress,
            status: message.status || 'downloading',
            message: `下载进度: ${realProgress}% (${formatBytes(message.completed)}/${formatBytes(message.total)})`,
            completed: message.completed,
            total: message.total,
            isProgressUpdate: true
          }
          updateTaskProgress(taskData)
        } else if (message.msg && message.msg.includes('MB/s')) {
          // 这是Go服务器的速度更新消息，现在Go服务器的速度计算已经修复，直接使用
          const taskData = {
            taskUuid: message.taskId,
            status: message.status || 'downloading',
            message: message.msg,
            isSpeedUpdate: true
          }
          updateTaskProgress(taskData)
        } else {
          // 其他状态消息
          const taskData = {
            taskUuid: message.taskId,
            progress: message.progress || 0,
            status: message.status,
            message: message.msg || message.currentStep || '',
            isStatusUpdate: true
          }
          updateTaskProgress(taskData)
        }
        return
      }

      // 处理原有的消息格式（向后兼容）
      switch (message.type) {
        case 'video-task-progress':
          updateTaskProgress(message.data)
          break
        case 'video-task-completed':
          updateTaskStatus(message.data.taskUuid, 'completed', message.data)
          break
        case 'video-task-failed':
          updateTaskStatus(message.data.taskUuid, 'failed', message.data)
          break
        case 'video-task-started':
          addNewTask(message.data)
          break
        default:
          console.log('未知消息类型:', message.type || '无类型')
      }
    }
    
    // 格式化字节数
    const formatBytes = (bytes) => {
      if (bytes === 0) return '0 B'
      const k = 1024
      const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    }

    // 更新任务进度
    const updateTaskProgress = (data) => {
      const task = tasks.value.find(t => t.task_uuid === data.taskUuid)
      if (task) {
        // 根据消息类型更新不同的字段
        if (data.isProgressUpdate) {
          // 进度更新：更新进度条和详细信息
          task.progress = data.progress
          task.current_step = data.message
          if (data.completed !== undefined) task.completed = data.completed
          if (data.total !== undefined) task.total = data.total
        } else if (data.isSpeedUpdate) {
          // 速度更新：只更新当前步骤的速度显示
          task.current_step = data.message
        } else {
          // 其他更新：更新进度、状态和消息
          if (data.progress !== undefined) {
            task.progress = data.progress
          }
          task.current_step = data.message
        }

        // 总是更新状态和时间戳
        if (data.status) {
          task.status = data.status
        }
        task.updated_at = new Date().toISOString()

        console.log('更新任务进度:', {
          taskUuid: data.taskUuid,
          progress: task.progress,
          status: task.status,
          message: data.message,
          type: data.isProgressUpdate ? 'progress' : data.isSpeedUpdate ? 'speed' : 'status'
        })
      } else {
        console.log('未找到任务:', data.taskUuid, '当前任务列表:', tasks.value.map(t => t.task_uuid))
      }
    }
    
    // 更新任务状态
    const updateTaskStatus = (taskUuid, status, data) => {
      const task = tasks.value.find(t => t.taskUuid === taskUuid)
      if (task) {
        task.status = status
        task.progress = status === 'completed' ? 100 : task.progress
        task.errorMessage = data.errorMessage
        task.completedAt = status === 'completed' ? new Date().toISOString() : null
        task.updatedAt = new Date().toISOString()
      }
      updateStats()
    }
    
    // 添加新任务
    const addNewTask = (data) => {
      const existingTask = tasks.value.find(t => t.taskUuid === data.taskUuid)
      if (!existingTask) {
        tasks.value.unshift({
          ...data,
          expanded: false,
          createdAt: new Date().toISOString()
        })
        updateStats()
      }
    }
    
    // 更新统计数据
    const updateStats = () => {
      stats.processing = tasks.value.filter(t => 
        t.status === 'processing' || t.status === 'downloading'
      ).length
      stats.completed = tasks.value.filter(t => t.status === 'completed').length
      stats.failed = tasks.value.filter(t => t.status === 'failed').length
      stats.total = tasks.value.length
    }
    
    // 获取电影标题
    const getMovieTitle = (movieId) => {
      if (movieCache.value[movieId]) {
        return movieCache.value[movieId].title
      }
      // 如果没有缓存，尝试从任务中获取电影标题
      const task = tasks.value.find(t => t.movie_id === movieId)
      if (task && task.movie_title) {
        return task.movie_title
      }
      return `影片 ${movieId}`
    }
    
    // 获取任务状态样式类
    const getTaskStatusClass = (status) => {
      return `task-${status}`
    }
    
    // 获取状态标签类型
    const getStatusTagType = (status) => {
      const statusMap = {
        'pending': 'info',
        'downloading': 'warning', 
        'processing': 'primary',
        'completed': 'success',
        'failed': 'danger'
      }
      return statusMap[status] || 'info'
    }
    
    // 获取状态文本
    const getStatusText = (status) => {
      const statusMap = {
        'pending': '等待中',
        'downloading': '下载中',
        'processing': '处理中', 
        'completed': '已完成',
        'failed': '失败'
      }
      return statusMap[status] || '未知'
    }
    
    // 获取进度条状态
    const getProgressStatus = (status) => {
      if (status === 'failed') return 'exception'
      if (status === 'completed') return 'success'
      return null
    }
    
    // 格式化时间
    const formatTime = (timeStr) => {
      if (!timeStr) return '-'
      const date = new Date(timeStr)
      return date.toLocaleString('zh-CN')
    }
    
    // 切换任务详情
    const toggleTaskDetails = (task) => {
      task.expanded = !task.expanded
    }
    
    // 重试任务
    const retryTask = async (task) => {
      try {
        await retryTaskAPI(task.task_uuid)
        ElMessage.success('任务重试请求已提交')
        // 更新任务状态
        task.status = 'pending'
        task.progress = 0
        task.error_message = null
        updateStats()
      } catch (error) {
        ElMessage.error('重试失败: ' + error.message)
      }
    }

    // 取消任务
    const cancelTask = async (task) => {
      try {
        // 显示确认对话框
        await ElMessageBox.confirm(
          '取消任务将会删除该任务及相关的磁力链接和电影数据，此操作不可恢复。确定要继续吗？',
          '确认取消任务',
          {
            confirmButtonText: '确定删除',
            cancelButtonText: '取消',
            type: 'warning',
            confirmButtonClass: 'el-button--danger'
          }
        )

        await cancelTaskAPI(task.task_uuid)
        ElMessage.success('任务已取消并删除相关数据')

        // 从任务列表中移除该任务
        const index = tasks.value.findIndex(t => t.task_uuid === task.task_uuid)
        if (index > -1) {
          tasks.value.splice(index, 1)
        }

        updateStats()
      } catch (error) {
        if (error !== 'cancel') {
          ElMessage.error('取消失败: ' + error.message)
        }
      }
    }

    // 刷新任务列表
    const refreshTasks = async () => {
      loading.value = true
      try {
        const response = await getVideoTasks()
        console.log('getVideoTasks响应:', response)
        // 由于request.js已经处理了success响应，直接使用response
        tasks.value = response || []

        // 获取统计数据
        const statsResponse = await getTaskStats()
        console.log('getTaskStats响应:', statsResponse)
        Object.assign(stats, statsResponse || {})

        updateStats()
      } catch (error) {
        console.error('刷新任务失败:', error)
        ElMessage.error('刷新失败: ' + error.message)
      } finally {
        loading.value = false
      }
    }
    
    // 组件挂载
    onMounted(() => {
      initWebSocket()
      refreshTasks()
    })
    
    // 组件卸载
    onUnmounted(() => {
      if (socket.value && socket.value.readyState === WebSocket.OPEN) {
        socket.value.close()
      }
    })
    
    return {
      loading,
      stats,
      tasks,
      getMovieTitle,
      getTaskStatusClass,
      getStatusTagType,
      getStatusText,
      getProgressStatus,
      formatTime,
      toggleTaskDetails,
      retryTask,
      cancelTask,
      refreshTasks
    }
  }
}
</script>

<style scoped>
.video-processing-container {
  padding: 20px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: calc(100vh - 120px);
}

/* 页面标题 */
.page-header {
  margin-bottom: 30px;
  text-align: center;
}

.page-title {
  font-size: 28px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 10px 0;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
}

.title-icon {
  font-size: 32px;
  color: #409eff;
}

.page-description {
  color: #7f8c8d;
  font-size: 16px;
  margin: 0;
}

/* 统计卡片 */
.stats-row {
  margin-bottom: 30px;
}

.stat-card {
  border-radius: 12px;
  border: none;
  transition: all 0.3s ease;
  overflow: hidden;
  position: relative;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #409eff, #67c23a);
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.processing-card::before { background: linear-gradient(90deg, #409eff, #5dade2); }
.completed-card::before { background: linear-gradient(90deg, #67c23a, #58d68d); }
.failed-card::before { background: linear-gradient(90deg, #f56c6c, #ec7063); }
.total-card::before { background: linear-gradient(90deg, #e6a23c, #f7dc6f); }

.stat-content {
  display: flex;
  align-items: center;
  padding: 20px;
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20px;
  font-size: 24px;
  color: white;
}

.stat-icon.processing { background: linear-gradient(135deg, #409eff, #5dade2); }
.stat-icon.completed { background: linear-gradient(135deg, #67c23a, #58d68d); }
.stat-icon.failed { background: linear-gradient(135deg, #f56c6c, #ec7063); }
.stat-icon.total { background: linear-gradient(135deg, #e6a23c, #f7dc6f); }

.stat-number {
  font-size: 32px;
  font-weight: 700;
  color: #2c3e50;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: #7f8c8d;
  margin-top: 5px;
}

/* 任务列表卡片 */
.task-list-card {
  border-radius: 12px;
  border: none;
  box-shadow: 0 4px 20px rgba(0,0,0,0.08);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 任务项 */
.task-item {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  margin-bottom: 16px;
  padding: 20px;
  background: white;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.task-item::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background: #e4e7ed;
  transition: all 0.3s ease;
}

.task-item:hover {
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
  transform: translateY(-2px);
}

.task-pending::before { background: #909399; }
.task-downloading::before { background: #e6a23c; }
.task-processing::before { background: #409eff; }
.task-completed::before { background: #67c23a; }
.task-failed::before { background: #f56c6c; }

.task-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 15px;
}

.task-info {
  flex: 1;
}

.task-title {
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.task-icon {
  font-size: 18px;
}

.task-meta {
  display: flex;
  gap: 20px;
  font-size: 12px;
  color: #7f8c8d;
}

.task-progress {
  margin-bottom: 15px;
}

.task-details {
  background: #f8f9fa;
  border-radius: 6px;
  padding: 15px;
  margin-bottom: 15px;
}

.detail-item {
  display: flex;
  margin-bottom: 8px;
}

.detail-item:last-child {
  margin-bottom: 0;
}

.detail-label {
  font-weight: 600;
  color: #606266;
  width: 80px;
  flex-shrink: 0;
}

.detail-value {
  color: #303133;
  flex: 1;
}

.magnet-link {
  font-family: monospace;
  font-size: 12px;
  word-break: break-all;
}

.error-message {
  color: #f56c6c;
}

.task-actions {
  display: flex;
  gap: 10px;
  justify-content: flex-end;
}

/* 空状态和加载状态 */
.empty-state,
.loading-state {
  padding: 40px;
  text-align: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .video-processing-container {
    padding: 10px;
  }

  .stats-row .el-col {
    margin-bottom: 15px;
  }

  .task-header {
    flex-direction: column;
    gap: 10px;
  }

  .task-meta {
    flex-direction: column;
    gap: 5px;
  }

  .detail-item {
    flex-direction: column;
  }

  .detail-label {
    width: auto;
    margin-bottom: 5px;
  }
}

/* 动画效果 */
@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.7; }
  100% { opacity: 1; }
}

.task-downloading .task-icon,
.task-processing .task-icon {
  animation: pulse 2s infinite;
}

/* 进度条自定义样式 */
:deep(.el-progress-bar__outer) {
  border-radius: 10px;
  overflow: hidden;
}

:deep(.el-progress-bar__inner) {
  border-radius: 10px;
  background: linear-gradient(90deg, #409eff, #67c23a);
}

/* 标签样式 */
:deep(.el-tag) {
  border-radius: 20px;
  padding: 8px 16px;
  font-weight: 500;
}
</style>
