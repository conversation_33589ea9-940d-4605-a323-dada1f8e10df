<template>
  <div class="genre-form-container">
    <div class="page-header">
      <h2>{{ isEdit ? '编辑类型' : '添加类型' }}</h2>
    </div>
    
    <el-card class="form-card">
      <el-form 
        ref="formRef" 
        :model="form" 
        :rules="rules" 
        label-width="100px" 
        v-loading="loading"
      >
        <el-form-item label="类型名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入类型名称" />
        </el-form-item>
        
        <el-form-item label="描述" prop="description">
          <el-input 
            v-model="form.description" 
            type="textarea" 
            :rows="4" 
            placeholder="请输入类型描述（可选）" 
          />
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="submitForm">保存</el-button>
          <el-button @click="goBack">取消</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { getGenreById, createGenre, updateGenre } from '../../api/genre'

export default {
  name: 'GenreForm',
  setup() {
    const router = useRouter()
    const route = useRoute()
    const formRef = ref(null)
    const loading = ref(false)
    
    // 计算当前是否为编辑模式
    const isEdit = computed(() => route.path.includes('edit'))
    
    // 表单数据
    const form = reactive({
      name: '',
      description: ''
    })
    
    // 表单验证规则
    const rules = {
      name: [
        { required: true, message: '请输入类型名称', trigger: 'blur' },
        { min: 1, max: 50, message: '长度在1到50个字符之间', trigger: 'blur' }
      ]
    }
    
    // 如果是编辑模式，加载类型信息
    const loadGenreInfo = async (id) => {
      loading.value = true
      try {
        const response = await getGenreById(id)
        Object.assign(form, response)
      } catch (error) {
        console.error('加载类型信息失败:', error)
        ElMessage.error('加载类型信息失败')
      } finally {
        loading.value = false
      }
    }
    
    // 提交表单
    const submitForm = async () => {
      if (!formRef.value) return
      
      await formRef.value.validate(async (valid) => {
        if (!valid) {
          ElMessage.error('请正确填写表单！')
          return false
        }
        
        loading.value = true
        try {
          if (isEdit.value) {
            const id = route.params.id
            await updateGenre(id, form)
            ElMessage.success('更新类型信息成功')
          } else {
            await createGenre(form)
            ElMessage.success('添加类型成功')
          }
          goBack()
        } catch (error) {
          console.error('保存类型信息失败:', error)
          ElMessage.error('保存类型信息失败')
        } finally {
          loading.value = false
        }
      })
    }
    
    // 返回列表页
    const goBack = () => {
      router.push('/genres')
    }
    
    onMounted(() => {
      if (isEdit.value && route.params.id) {
        loadGenreInfo(route.params.id)
      }
    })
    
    return {
      formRef,
      form,
      rules,
      loading,
      isEdit,
      submitForm,
      goBack
    }
  }
}
</script>

<style scoped>
.genre-form-container {
  padding: 20px 0;
}

.page-header {
  margin-bottom: 20px;
}

.form-card {
  max-width: 700px;
}
</style>
