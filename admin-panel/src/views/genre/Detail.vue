<template>
  <div class="genre-detail-container">
    <div class="page-header">
      <h2>类型详情</h2>
      <el-button type="primary" @click="goToEdit">编辑类型</el-button>
    </div>
    
    <el-card class="info-card" v-loading="loading">
      <el-descriptions title="基本信息" border>
        <el-descriptions-item label="ID">{{ genreInfo.id }}</el-descriptions-item>
        <el-descriptions-item label="类型名称">{{ genreInfo.name }}</el-descriptions-item>
        <el-descriptions-item label="相关影片数">{{ genreInfo.movie_count || 0 }}</el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ formatDate(genreInfo.created_at) }}</el-descriptions-item>
        <el-descriptions-item label="更新时间">{{ formatDate(genreInfo.updated_at) }}</el-descriptions-item>
      </el-descriptions>
      
      <div class="description-section" v-if="genreInfo.description">
        <h3>描述</h3>
        <p>{{ genreInfo.description }}</p>
      </div>
    </el-card>
    
    <!-- 相关影片列表 -->
    <el-card class="movies-card" style="margin-top: 20px;">
      <template #header>
        <div class="card-header">
          <span>相关影片</span>
        </div>
      </template>
      <el-table :data="relatedMovies" border style="width: 100%" v-loading="loadingMovies">
        <el-table-column prop="movie_id" label="影片ID" width="120" />
        <el-table-column prop="title" label="标题" />
        <el-table-column prop="release_date" label="发行日期" width="180">
          <template #default="scope">
            {{ formatDate(scope.row.release_date) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150">
          <template #default="scope">
            <el-button size="small" @click="goToMovieDetail(scope.row.id)">查看</el-button>
            <el-button size="small" type="primary" @click="goToMovieEdit(scope.row.id)">编辑</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination-container" v-if="relatedMovies.length > 0">
        <el-pagination
          background
          layout="prev, pager, next"
          :total="totalMovies"
          :page-size="pageSize"
          :current-page="currentPage"
          @current-change="handleCurrentChange"
        />
      </div>
      <div v-else class="no-data">暂无相关影片</div>
    </el-card>
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { getGenreById } from '../../api/genre'
import { getMoviesByGenre } from '../../api/genre'

export default {
  name: 'GenreDetail',
  setup() {
    const router = useRouter()
    const route = useRoute()
    const loading = ref(false)
    const loadingMovies = ref(false)
    const genreId = route.params.id
    
    // 类型基本信息
    const genreInfo = reactive({
      id: '',
      name: '',
      description: '',
      movie_count: 0,
      created_at: '',
      updated_at: ''
    })
    
    // 相关影片列表
    const relatedMovies = ref([])
    const totalMovies = ref(0)
    const currentPage = ref(1)
    const pageSize = ref(10)
    
    // 获取类型详情
    const loadGenreInfo = async () => {
      loading.value = true
      try {
        const response = await getGenreById(genreId)
        Object.assign(genreInfo, response)
        
        // 加载相关影片
        loadRelatedMovies()
      } catch (error) {
        console.error('加载类型信息失败:', error)
        ElMessage.error('加载类型信息失败')
      } finally {
        loading.value = false
      }
    }
    
    // 加载相关影片
    const loadRelatedMovies = async () => {
      loadingMovies.value = true
      try {
        const response = await getMoviesByGenre(genreId, {
          page: currentPage.value,
          limit: pageSize.value
        })
        relatedMovies.value = response.items || []
        totalMovies.value = response.total || 0
      } catch (error) {
        console.error('加载相关影片失败:', error)
        ElMessage.error('加载相关影片失败')
        relatedMovies.value = []
        totalMovies.value = 0
      } finally {
        loadingMovies.value = false
      }
    }
    
    // 格式化日期
    const formatDate = (dateString) => {
      if (!dateString) return '—'
      const date = new Date(dateString)
      return date.toLocaleString()
    }
    
    // 分页事件
    const handleCurrentChange = (page) => {
      currentPage.value = page
      loadRelatedMovies()
    }
    
    // 导航功能
    const goToEdit = () => {
      router.push(`/genres/edit/${genreId}`)
    }
    
    const goToMovieDetail = (id) => {
      router.push(`/videos/detail/${id}`)
    }
    
    const goToMovieEdit = (id) => {
      router.push(`/videos/edit/${id}`)
    }
    
    onMounted(() => {
      loadGenreInfo()
    })
    
    return {
      genreInfo,
      loading,
      relatedMovies,
      totalMovies,
      currentPage,
      pageSize,
      loadingMovies,
      formatDate,
      handleCurrentChange,
      goToEdit,
      goToMovieDetail,
      goToMovieEdit
    }
  }
}
</script>

<style scoped>
.genre-detail-container {
  padding: 20px 0;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.info-card {
  margin-bottom: 20px;
}

.description-section {
  margin-top: 20px;
}

.description-section h3 {
  margin-bottom: 10px;
  font-weight: 500;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.no-data {
  text-align: center;
  padding: 30px;
  color: #909399;
}
</style>
