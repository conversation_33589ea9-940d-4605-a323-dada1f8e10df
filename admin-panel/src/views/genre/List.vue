<template>
  <div class="genre-list-container">
    <div class="page-header">
      <h2>标签管理</h2>
      <el-button type="primary" @click="handleCreate">添加标签</el-button>
    </div>

    <!-- 搜索表单 -->
    <el-card class="filter-container">
      <el-form :inline="true" :model="queryParams" class="search-form">
        <el-form-item label="标签名称">
          <el-input v-model="queryParams.name" placeholder="请输入标签名称" clearable></el-input>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="queryParams.status" placeholder="选择状态" clearable>
            <el-option label="启用" value="active"></el-option>
            <el-option label="禁用" value="disabled"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 标签数据表格 -->
    <el-table
      v-loading="loading"
      :data="genreList"
      border
      style="width: 100%; margin-top: 20px;"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55"></el-table-column>
      <el-table-column prop="id" label="ID" width="80"></el-table-column>
      <el-table-column prop="name" label="标签名称" width="150"></el-table-column>
      <el-table-column prop="description" label="描述"></el-table-column>
      <el-table-column prop="videoCount" label="影片数量" width="100"></el-table-column>
      <el-table-column prop="createdAt" label="创建时间" width="180">
        <template #default="scope">
          {{ formatDate(scope.row.createdAt) }}
        </template>
      </el-table-column>
      <el-table-column prop="status" label="状态" width="100">
        <template #default="scope">
          <el-tag :type="scope.row.status === 'active' ? 'success' : 'danger'">
            {{ scope.row.status === 'active' ? '启用' : '禁用' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="250">
        <template #default="scope">
          <el-button size="small" type="success" @click="handleView(scope.row)">查看</el-button>
          <el-button size="small" type="primary" @click="handleEdit(scope.row)">编辑</el-button>
          <el-button 
            size="small" 
            :type="scope.row.status === 'active' ? 'warning' : 'success'"
            @click="handleToggleStatus(scope.row)"
          >
            {{ scope.row.status === 'active' ? '禁用' : '启用' }}
          </el-button>
          <el-button size="small" type="danger" @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <el-pagination
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :current-page="queryParams.page"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="queryParams.limit"
      layout="total, sizes, prev, pager, next, jumper"
      :total="total"
      class="pagination"
    ></el-pagination>
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getGenreList, deleteGenre, updateGenreStatus } from '@/api/genre'

export default {
  name: 'GenreList',
  setup() {
    const router = useRouter()
    const loading = ref(false)
    const genreList = ref([])
    const total = ref(0)
    const selectedRows = ref([])
    
    const queryParams = reactive({
      page: 1,
      limit: 10,
      name: '',
      status: ''
    })
    
    // 获取标签列表数据
    const fetchGenreList = async () => {
      loading.value = true
      try {
        const response = await getGenreList(queryParams)
        // 根据实际API响应结构调整数据提取方式
        genreList.value = Array.isArray(response) ? response : (response.data || [])
        total.value = response.total || response.count || genreList.value.length || 0
      } catch (error) {
        console.error('获取标签列表失败:', error)
        ElMessage.error('获取标签列表失败')
      } finally {
        loading.value = false
      }
    }
    
    // 处理查询
    const handleSearch = () => {
      queryParams.page = 1
      fetchGenreList()
    }
    
    // 重置查询条件
    const resetQuery = () => {
      queryParams.name = ''
      queryParams.status = ''
      handleSearch()
    }
    
    // 处理分页大小变化
    const handleSizeChange = (size) => {
      queryParams.limit = size
      fetchGenreList()
    }
    
    // 处理当前页变化
    const handleCurrentChange = (page) => {
      queryParams.page = page
      fetchGenreList()
    }
    
    // 处理多选变化
    const handleSelectionChange = (rows) => {
      selectedRows.value = rows
    }
    
    // 格式化日期
    const formatDate = (dateString) => {
      if (!dateString) return '-'
      const date = new Date(dateString)
      return date.toLocaleString()
    }
    
    // 查看标签详情
    const handleView = (row) => {
      router.push(`/genres/detail/${row.id}`)
    }
    
    // 创建标签
    const handleCreate = () => {
      router.push('/genres/create')
    }
    
    // 编辑标签
    const handleEdit = (row) => {
      router.push(`/genres/edit/${row.id}`)
    }
    
    // 切换标签状态
    const handleToggleStatus = (row) => {
      const statusText = row.status === 'active' ? '禁用' : '启用'
      ElMessageBox.confirm(`确认要${statusText}该标签吗?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          const newStatus = row.status === 'active' ? 'disabled' : 'active'
          await updateGenreStatus(row.id, { status: newStatus })
          ElMessage.success(`${statusText}标签成功`)
          // 更新本地数据
          row.status = newStatus
        } catch (error) {
          console.error(`${statusText}标签失败:`, error)
          ElMessage.error(`${statusText}标签失败`)
        }
      }).catch(() => {
        // 取消操作
      })
    }
    
    // 删除标签
    const handleDelete = (row) => {
      ElMessageBox.confirm('确认删除该标签吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          await deleteGenre(row.id)
          ElMessage.success('删除标签成功')
          fetchGenreList() // 重新加载数据
        } catch (error) {
          console.error('删除标签失败:', error)
          ElMessage.error('删除标签失败')
        }
      }).catch(() => {
        // 取消操作
      })
    }
    
    onMounted(() => {
      fetchGenreList()
    })
    
    return {
      loading,
      genreList,
      total,
      queryParams,
      selectedRows,
      formatDate,
      handleSearch,
      resetQuery,
      handleSizeChange,
      handleCurrentChange,
      handleSelectionChange,
      handleView,
      handleCreate,
      handleEdit,
      handleToggleStatus,
      handleDelete
    }
  }
}
</script>

<style scoped>
.genre-list-container {
  padding: 20px 0;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.filter-container {
  margin-bottom: 20px;
}

.search-form {
  display: flex;
  flex-wrap: wrap;
}

.pagination {
  margin-top: 20px;
  text-align: right;
}
</style>