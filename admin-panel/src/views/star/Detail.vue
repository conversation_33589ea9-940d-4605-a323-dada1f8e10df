<template>
  <div class="star-detail-container">
    <div class="page-header">
      <h2>演员详情</h2>
      <el-button type="primary" @click="goToEdit">编辑演员</el-button>
    </div>
    
    <el-row :gutter="20" v-loading="loading">
      <el-col :span="6">
        <el-card class="info-card">
          <div class="star-avatar">
            <el-avatar 
              :size="150" 
              :src="starInfo.image_url || 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png'" 
            />
          </div>
          <div class="star-name">{{ starInfo.name }}</div>
          <div class="star-id">ID: {{ starInfo.star_id }}</div>
        </el-card>
      </el-col>
      
      <el-col :span="18">
        <el-card class="details-card">
          <el-descriptions title="基本信息" border>
            <el-descriptions-item label="ID">{{ starInfo.id }}</el-descriptions-item>
            <el-descriptions-item label="演员ID">{{ starInfo.star_id }}</el-descriptions-item>
            <el-descriptions-item label="演员姓名">{{ starInfo.name }}</el-descriptions-item>
            <el-descriptions-item label="作品数量">{{ starInfo.movie_count }}</el-descriptions-item>
            <el-descriptions-item label="创建时间">{{ formatDate(starInfo.created_at) }}</el-descriptions-item>
            <el-descriptions-item label="更新时间">{{ formatDate(starInfo.updated_at) }}</el-descriptions-item>
          </el-descriptions>
        </el-card>
        
        <!-- 演员作品列表 -->
        <el-card class="movies-card" style="margin-top: 20px;">
          <template #header>
            <div class="card-header">
              <span>相关作品</span>
            </div>
          </template>
          <el-table :data="relatedMovies" border style="width: 100%" v-loading="loadingMovies">
            <el-table-column prop="movie_id" label="影片ID" width="120" />
            <el-table-column prop="title" label="标题" />
            <el-table-column prop="release_date" label="发行日期" width="120" />
            <el-table-column label="操作" width="150">
              <template #default="scope">
                <el-button size="small" @click="goToMovieDetail(scope.row.id)">查看</el-button>
              </template>
            </el-table-column>
          </el-table>
          <div class="pagination-container" v-if="relatedMovies.length > 0">
            <el-pagination
              background
              layout="prev, pager, next"
              :total="totalMovies"
              :page-size="pageSize"
              :current-page="currentPage"
              @current-change="handleCurrentChange"
            />
          </div>
          <div v-else class="no-data">暂无相关作品</div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { getStarById } from '../../api/star'
import { getMoviesByStar } from '../../api/video'

export default {
  name: 'StarDetail',
  setup() {
    const router = useRouter()
    const route = useRoute()
    const loading = ref(false)
    const loadingMovies = ref(false)
    const starId = route.params.id
    
    const starInfo = reactive({
      id: '',
      star_id: '',
      name: '',
      image_url: '',
      movie_count: 0,
      created_at: '',
      updated_at: ''
    })
    
    const relatedMovies = ref([])
    const totalMovies = ref(0)
    const currentPage = ref(1)
    const pageSize = ref(10)
    
    // 加载演员信息
    const loadStarInfo = async () => {
      loading.value = true
      try {
        const response = await getStarById(starId)
        Object.assign(starInfo, response)
        loadRelatedMovies()
      } catch (error) {
        console.error('加载演员信息失败:', error)
        ElMessage.error('加载演员信息失败')
      } finally {
        loading.value = false
      }
    }
    
    // 加载相关作品
    const loadRelatedMovies = async () => {
      loadingMovies.value = true
      try {
        const response = await getMoviesByStar(starId, {
          page: currentPage.value,
          limit: pageSize.value
        })
        relatedMovies.value = response.items || []
        totalMovies.value = response.total || 0
      } catch (error) {
        console.error('加载相关作品失败:', error)
        ElMessage.error('加载相关作品失败')
        relatedMovies.value = []
        totalMovies.value = 0
      } finally {
        loadingMovies.value = false
      }
    }
    
    // 格式化日期
    const formatDate = (dateString) => {
      if (!dateString) return '—'
      const date = new Date(dateString)
      return date.toLocaleString()
    }
    
    // 分页事件
    const handleCurrentChange = (page) => {
      currentPage.value = page
      loadRelatedMovies()
    }
    
    // 导航功能
    const goToEdit = () => {
      router.push(`/stars/edit/${starId}`)
    }
    
    const goToMovieDetail = (id) => {
      router.push(`/videos/detail/${id}`)
    }
    
    onMounted(() => {
      loadStarInfo()
    })
    
    return {
      starInfo,
      loading,
      loadingMovies,
      relatedMovies,
      totalMovies,
      currentPage,
      pageSize,
      formatDate,
      handleCurrentChange,
      goToEdit,
      goToMovieDetail
    }
  }
}
</script>

<style scoped>
.star-detail-container {
  padding: 20px 0;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.info-card {
  text-align: center;
  padding: 20px;
}

.star-avatar {
  margin-bottom: 20px;
}

.star-name {
  font-size: 20px;
  font-weight: bold;
  margin-bottom: 10px;
}

.star-id {
  color: #909399;
}

.details-card, .movies-card {
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.no-data {
  text-align: center;
  padding: 30px;
  color: #909399;
}
</style>
