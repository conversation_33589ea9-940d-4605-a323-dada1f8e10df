<template>
  <div class="star-form-container">
    <div class="page-header">
      <h2>{{ isEdit ? '编辑演员' : '添加演员' }}</h2>
    </div>
    
    <el-card class="form-card">
      <el-form 
        ref="formRef" 
        :model="form" 
        :rules="rules" 
        label-width="100px" 
        v-loading="loading"
      >
        <el-form-item label="演员ID" prop="star_id">
          <el-input v-model="form.star_id" placeholder="请输入演员ID" :disabled="isEdit" />
        </el-form-item>
        
        <el-form-item label="演员姓名" prop="name">
          <el-input v-model="form.name" placeholder="请输入演员姓名" />
        </el-form-item>
        
        <el-form-item label="头像">
          <el-upload
            class="avatar-uploader"
            action="/api/upload/image"
            :show-file-list="false"
            :on-success="handleAvatarSuccess"
            :before-upload="beforeAvatarUpload"
          >
            <img v-if="form.image_url" :src="form.image_url" class="avatar" />
            <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
          </el-upload>
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="submitForm">保存</el-button>
          <el-button @click="goBack">取消</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import { getStarById, createStar, updateStar } from '../../api/star'

export default {
  name: 'StarForm',
  components: {
    Plus
  },
  setup() {
    const router = useRouter()
    const route = useRoute()
    const formRef = ref(null)
    const loading = ref(false)
    
    // 计算当前是否为编辑模式
    const isEdit = computed(() => route.path.includes('edit'))
    
    // 表单数据
    const form = reactive({
      star_id: '',
      name: '',
      image_url: ''
    })
    
    // 表单验证规则
    const rules = {
      star_id: [
        { required: true, message: '请输入演员ID', trigger: 'blur' },
        { min: 2, max: 20, message: '长度在2到20个字符之间', trigger: 'blur' }
      ],
      name: [
        { required: true, message: '请输入演员姓名', trigger: 'blur' },
        { min: 1, max: 50, message: '长度在1到50个字符之间', trigger: 'blur' }
      ]
    }
    
    // 如果是编辑模式，加载演员信息
    const loadStarInfo = async (id) => {
      loading.value = true
      try {
        const response = await getStarById(id)
        Object.assign(form, response)
      } catch (error) {
        console.error('加载演员信息失败:', error)
        ElMessage.error('加载演员信息失败')
      } finally {
        loading.value = false
      }
    }
    
    // 头像上传前的验证
    const beforeAvatarUpload = (file) => {
      const isImage = file.type.startsWith('image/')
      const isLt2M = file.size / 1024 / 1024 < 2
      
      if (!isImage) {
        ElMessage.error('头像必须是图片格式！')
        return false
      }
      if (!isLt2M) {
        ElMessage.error('头像大小不能超过2MB！')
        return false
      }
      return true
    }
    
    // 头像上传成功的回调
    const handleAvatarSuccess = (res) => {
      if (res.success) {
        form.image_url = res.data.url
      } else {
        ElMessage.error('上传头像失败：' + res.message)
      }
    }
    
    // 提交表单
    const submitForm = async () => {
      if (!formRef.value) return
      
      await formRef.value.validate(async (valid) => {
        if (!valid) {
          ElMessage.error('请正确填写表单！')
          return false
        }
        
        loading.value = true
        try {
          if (isEdit.value) {
            const id = route.params.id
            await updateStar(id, form)
            ElMessage.success('更新演员信息成功')
          } else {
            await createStar(form)
            ElMessage.success('添加演员成功')
          }
          goBack()
        } catch (error) {
          console.error('保存演员信息失败:', error)
          ElMessage.error('保存演员信息失败')
        } finally {
          loading.value = false
        }
      })
    }
    
    // 返回列表页
    const goBack = () => {
      router.push('/stars')
    }
    
    onMounted(() => {
      if (isEdit.value && route.params.id) {
        loadStarInfo(route.params.id)
      }
    })
    
    return {
      formRef,
      form,
      rules,
      loading,
      isEdit,
      beforeAvatarUpload,
      handleAvatarSuccess,
      submitForm,
      goBack
    }
  }
}
</script>

<style scoped>
.star-form-container {
  padding: 20px 0;
}

.page-header {
  margin-bottom: 20px;
}

.form-card {
  max-width: 700px;
}

.avatar-uploader {
  text-align: center;
}

.avatar-uploader .el-upload {
  border: 1px dashed var(--el-border-color);
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: var(--el-transition-duration-fast);
}

.avatar-uploader .el-upload:hover {
  border-color: var(--el-color-primary);
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 120px;
  height: 120px;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
}

.avatar {
  width: 120px;
  height: 120px;
  display: block;
}
</style>
