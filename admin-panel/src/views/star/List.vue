<template>
  <div class="star-container">
    <div class="page-header">
      <h2>演员管理</h2>
      <el-button type="primary" @click="goToCreate">添加演员</el-button>
    </div>
    
    <!-- 搜索框 -->
    <el-card class="search-card">
      <el-form :inline="true" :model="searchForm" class="search-form">
        <el-form-item label="演员姓名">
          <el-input v-model="searchForm.name" placeholder="请输入演员姓名" clearable />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>
    
    <!-- 数据表格 -->
    <el-card class="table-card">
      <el-table :data="starList" border style="width: 100%" v-loading="loading">
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="star_id" label="演员ID" width="120" />
        <el-table-column label="头像" width="100">
          <template #default="scope">
            <el-avatar :size="50" :src="scope.row.image_url || 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png'" />
          </template>
        </el-table-column>
        <el-table-column prop="name" label="姓名" />
        <el-table-column prop="movie_count" label="作品数量" width="100" />
        <el-table-column label="操作" width="200">
          <template #default="scope">
            <el-button size="small" @click="goToDetail(scope.row.id)">查看</el-button>
            <el-button size="small" type="primary" @click="goToEdit(scope.row.id)">编辑</el-button>
            <el-button size="small" type="danger" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          background
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          :page-size="pageSize"
          :current-page="currentPage"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
    
    <!-- 删除确认对话框 -->
    <el-dialog
      title="确认删除"
      v-model="deleteDialogVisible"
      width="30%"
    >
      <span>确定要删除演员 {{ starToDelete?.name }} 吗？</span>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="deleteDialogVisible = false">取消</el-button>
          <el-button type="danger" @click="confirmDelete">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getStarsList, deleteStar } from '../../api/star'

export default {
  name: 'StarList',
  setup() {
    const router = useRouter()
    const loading = ref(false)
    const starList = ref([])
    const total = ref(0)
    const currentPage = ref(1)
    const pageSize = ref(10)
    const deleteDialogVisible = ref(false)
    const starToDelete = ref(null)
    
    const searchForm = reactive({
      name: '',
    })
    
    // 加载演员列表
    const loadStarList = async () => {
      loading.value = true
      try {
        console.log('加载演员列表，参数:', {
          page: currentPage.value,
          limit: pageSize.value,
          name: searchForm.name || undefined
        });

        const response = await getStarsList({
          page: currentPage.value,
          limit: pageSize.value,
          name: searchForm.name || undefined
        })

        console.log('演员列表API响应:', response);

        // 处理不同的响应格式
        if (Array.isArray(response)) {
          starList.value = response;
          total.value = response.length;
        } else if (response?.items && Array.isArray(response.items)) {
          starList.value = response.items;
          total.value = response.pagination?.totalItems || response.total || response.items.length;
        } else if (response?.data && Array.isArray(response.data)) {
          starList.value = response.data;
          total.value = response.pagination?.totalItems || response.total || response.data.length;
        } else {
          console.warn('未知的响应格式:', response);
          starList.value = [];
          total.value = 0;
        }

        console.log('处理后的演员列表:', starList.value);
        console.log('总数:', total.value);
      } catch (error) {
        console.error('加载演员列表失败:', error)
        ElMessage.error('加载演员列表失败: ' + (error.message || '未知错误'))
        starList.value = []
        total.value = 0
      } finally {
        loading.value = false
      }
    }
    
    // 搜索事件
    const handleSearch = () => {
      currentPage.value = 1
      loadStarList()
    }
    
    // 重置搜索条件
    const resetSearch = () => {
      searchForm.name = ''
      currentPage.value = 1
      loadStarList()
    }
    
    // 分页事件
    const handleSizeChange = (size) => {
      pageSize.value = size
      loadStarList()
    }
    
    const handleCurrentChange = (page) => {
      currentPage.value = page
      loadStarList()
    }
    
    // 删除演员
    const handleDelete = (star) => {
      starToDelete.value = star
      deleteDialogVisible.value = true
    }
    
    const confirmDelete = async () => {
      try {
        await deleteStar(starToDelete.value.id)
        ElMessage.success(`删除演员 ${starToDelete.value.name} 成功`)
        deleteDialogVisible.value = false
        loadStarList() // 重新加载列表
      } catch (error) {
        console.error('删除演员失败:', error)
        ElMessage.error(`删除演员 ${starToDelete.value.name} 失败`)
      }
    }
    
    // 导航功能
    const goToDetail = (id) => {
      router.push(`/stars/detail/${id}`)
    }
    
    const goToEdit = (id) => {
      router.push(`/stars/edit/${id}`)
    }
    
    const goToCreate = () => {
      router.push('/stars/create')
    }
    
    onMounted(() => {
      loadStarList()
    })
    
    return {
      loading,
      starList,
      total,
      currentPage,
      pageSize,
      searchForm,
      deleteDialogVisible,
      starToDelete,
      handleSearch,
      resetSearch,
      handleSizeChange,
      handleCurrentChange,
      handleDelete,
      confirmDelete,
      goToDetail,
      goToEdit,
      goToCreate
    }
  }
}
</script>

<style scoped>
.star-container {
  padding: 20px 0;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.search-card {
  margin-bottom: 20px;
}

.table-card {
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}
</style>
