<template>
  <div class="not-found">
    <div class="error-container">
      <h1>404</h1>
      <h2>页面未找到</h2>
      <p>抱歉，您访问的页面不存在。</p>
      <el-button type="primary" @click="goHome">返回首页</el-button>
    </div>
  </div>
</template>

<script>
import { useRouter } from 'vue-router'

export default {
  name: 'NotFound',
  setup() {
    const router = useRouter()
    
    const goHome = () => {
      router.push('/')
    }
    
    return {
      goHome
    }
  }
}
</script>

<style scoped>
.not-found {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  min-height: 500px;
}

.error-container {
  text-align: center;
}

h1 {
  font-size: 100px;
  color: #409EFF;
  margin: 0;
}

h2 {
  font-size: 30px;
  margin: 10px 0;
}

p {
  font-size: 16px;
  color: #909399;
  margin-bottom: 30px;
}
</style> 