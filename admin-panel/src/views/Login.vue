<template>
  <div class="login-container">
    <el-card class="login-card">
      <div class="title">
        <h2>JAVFLIX.TV 管理后台</h2>
      </div>
      <el-form
        ref="loginFormRef"
        :model="loginForm"
        :rules="loginRules"
        class="login-form"
      >
        <el-form-item prop="email">
          <el-input
            v-model="loginForm.email"
            placeholder="邮箱"
            type="text"
          />
        </el-form-item>
        
        <el-form-item prop="password">
          <el-input
            v-model="loginForm.password"
            placeholder="密码"
            type="password"
          />
        </el-form-item>
        
        <el-form-item>
          <el-button
            :loading="loading"
            type="primary"
            class="login-button"
            @click="handleLogin"
          >
            登录
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script>
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { useStore } from 'vuex'
import { ElMessage } from 'element-plus'
import { login } from '../api/user'

export default {
  name: 'Login',
  setup() {
    const router = useRouter()
    const store = useStore()
    const loginFormRef = ref(null)
    const loading = ref(false)
    
    const loginForm = reactive({
      email: '',
      password: ''
    })
    
    const loginRules = {
      email: [
        { required: true, message: '请输入邮箱', trigger: 'blur' },
        { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
      ],
      password: [
        { required: true, message: '请输入密码', trigger: 'blur' },
        { min: 6, message: '密码长度不能少于6个字符', trigger: 'blur' }
      ]
    }
    
    const handleLogin = async () => {
      if (!loginFormRef.value) return
      
      try {
        await loginFormRef.value.validate()
        loading.value = true
        
        try {
          // 模拟API登录成功，不进行实际API调用
          setTimeout(() => {
            // 假设获取到了用户数据
            const mockUserData = {
              token: 'mock-token-123456',
              user: {
                id: 1,
                username: loginForm.email.split('@')[0],
                email: loginForm.email,
                role: 'admin'
              }
            }
            
            // 保存到Vuex
            store.dispatch('login', {
              token: mockUserData.token,
              user: mockUserData.user
            })
            
            ElMessage.success('登录成功')
            router.push('/')
            loading.value = false
          }, 1000) // 1秒后登录成功
        } catch (error) {
          console.error('登录失败:', error)
          ElMessage.error('登录失败')
          loading.value = false
        }
      } catch (error) {
        console.error('表单验证失败:', error)
      }
    }
    
    return {
      loginForm,
      loginRules,
      loading,
      loginFormRef,
      handleLogin
    }
  }
}
</script>

<style scoped>
.login-container {
  height: 100%;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background: #f3f3f3;
}

.login-card {
  width: 400px;
  padding: 20px;
}

.title {
  text-align: center;
  margin-bottom: 30px;
}

.login-button {
  width: 100%;
}
</style> 