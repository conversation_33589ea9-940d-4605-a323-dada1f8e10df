<template>
  <div class="movie-processing">
    <!-- 页面标题和统计 -->
    <div class="page-header">
      <h1>影片处理管理</h1>
      <div class="stats-cards">
        <el-row :gutter="20">
          <el-col :span="4" v-for="(stat, key) in stats" :key="key">
            <el-card class="stat-card" :class="key">
              <div class="stat-content">
                <div class="stat-number">{{ stat }}</div>
                <div class="stat-label">{{ getStatusLabel(key) }}</div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>
    </div>

    <!-- 操作工具栏 -->
    <el-card class="toolbar-card">
      <div class="toolbar">
        <div class="left-actions">
          <el-button 
            type="primary" 
            @click="startProcessing"
            :disabled="selectedMovies.length === 0"
            :loading="processing"
          >
            <i class="el-icon-video-play"></i>
            开始处理 ({{ selectedMovies.length }})
          </el-button>
          
          <el-button 
            type="success" 
            @click="publishMovies"
            :disabled="selectedProcessedMovies.length === 0"
          >
            <i class="el-icon-upload"></i>
            发布影片 ({{ selectedProcessedMovies.length }})
          </el-button>
          
          <el-button @click="refreshData">
            <i class="el-icon-refresh"></i>
            刷新
          </el-button>
        </div>
        
        <div class="right-actions">
          <el-select v-model="statusFilter" @change="loadMovies" placeholder="状态筛选">
            <el-option label="全部" value=""></el-option>
            <el-option label="草稿" value="draft"></el-option>
            <el-option label="处理中" value="processing"></el-option>
            <el-option label="已处理" value="processed"></el-option>
            <el-option label="失败" value="failed"></el-option>
          </el-select>
        </div>
      </div>
    </el-card>

    <!-- 影片列表 -->
    <el-card class="movie-list-card">
      <el-table 
        :data="movies" 
        v-loading="loading"
        @selection-change="handleSelectionChange"
        row-key="id"
      >
        <el-table-column type="selection" width="55"></el-table-column>
        
        <el-table-column prop="movie_id" label="影片编号" width="120">
          <template #default="scope">
            <el-tag size="small">{{ scope.row.movie_id }}</el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="title" label="标题" min-width="200">
          <template #default="scope">
            <div class="movie-info">
              <img 
                :src="scope.row.image_url" 
                class="movie-thumb"
                @error="handleImageError"
              />
              <div class="movie-details">
                <div class="movie-title">{{ scope.row.title }}</div>
                <div class="movie-meta">
                  磁力链接: {{ scope.row.magnet_count }}个
                </div>
              </div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="status" label="状态" width="120">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.status)">
              {{ getStatusLabel(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="处理进度" width="200">
          <template #default="scope">
            <div v-if="scope.row.status === 'processing'">
              <el-progress 
                :percentage="scope.row.progress || 0"
                :status="scope.row.task_status === 'failed' ? 'exception' : null"
              />
              <div class="progress-text">
                {{ scope.row.task_status || '等待中' }}
              </div>
            </div>
            <div v-else-if="scope.row.status === 'processed'">
              <el-tag type="success">处理完成</el-tag>
            </div>
            <div v-else-if="scope.row.status === 'failed'">
              <el-tag type="danger">处理失败</el-tag>
              <div class="error-text">{{ scope.row.error_message }}</div>
            </div>
            <div v-else>
              <span class="text-muted">未开始</span>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="processing_priority" label="优先级" width="100">
          <template #default="scope">
            <el-input-number
              v-model="scope.row.processing_priority"
              :min="1"
              :max="10"
              size="small"
              @change="updatePriority(scope.row.id, scope.row.processing_priority)"
            />
          </template>
        </el-table-column>
        
        <el-table-column prop="created_at" label="创建时间" width="160">
          <template #default="scope">
            {{ formatTime(scope.row.created_at) }}
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <el-button 
              size="small" 
              type="primary"
              v-if="scope.row.status === 'draft' || scope.row.status === 'failed'"
              @click="startSingleProcessing(scope.row.id)"
            >
              开始处理
            </el-button>
            
            <el-button 
              size="small" 
              type="success"
              v-if="scope.row.status === 'processed'"
              @click="publishSingle(scope.row.id)"
            >
              发布
            </el-button>
            
            <el-button 
              size="small" 
              type="warning"
              v-if="scope.row.status === 'processing'"
              @click="cancelProcessing(scope.row.id)"
            >
              取消
            </el-button>
            
            <el-button 
              size="small"
              @click="viewDetails(scope.row)"
            >
              详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
        />
      </div>
    </el-card>

    <!-- 处理配置对话框 -->
    <el-dialog 
      title="处理配置" 
      v-model="showConfigDialog"
      width="600px"
    >
      <el-form :model="processingConfig" label-width="120px">
        <el-form-item label="优先级">
          <el-slider
            v-model="processingConfig.priority"
            :min="1"
            :max="10"
            show-stops
            show-input
          />
          <div class="form-help">1为最低优先级，10为最高优先级</div>
        </el-form-item>
        
        <el-form-item label="水印设置">
          <el-switch v-model="processingConfig.watermark.enabled" />
          <div v-if="processingConfig.watermark.enabled" class="watermark-config">
            <el-input 
              v-model="processingConfig.watermark.text" 
              placeholder="水印文字"
              style="margin-top: 10px;"
            />
          </div>
        </el-form-item>
        
        <el-form-item label="视频质量">
          <el-checkbox-group v-model="selectedQualities">
            <el-checkbox label="1080p">1080p (5000k)</el-checkbox>
            <el-checkbox label="720p">720p (3000k)</el-checkbox>
            <el-checkbox label="480p">480p (1500k)</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showConfigDialog = false">取消</el-button>
        <el-button type="primary" @click="confirmProcessing" :loading="processing">
          开始处理
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { io } from 'socket.io-client';

export default {
  name: 'MovieProcessing',
  data() {
    return {
      movies: [],
      stats: {
        draft: 0,
        processing: 0,
        processed: 0,
        published: 0,
        failed: 0
      },
      selectedMovies: [],
      loading: false,
      processing: false,
      currentPage: 1,
      pageSize: 20,
      total: 0,
      statusFilter: '',
      showConfigDialog: false,
      processingConfig: {
        priority: 5,
        watermark: {
          enabled: true,
          text: 'JAVFLIX.TV'
        }
      },
      selectedQualities: ['1080p', '720p', '480p'],
      socket: null
    };
  },
  
  computed: {
    selectedProcessedMovies() {
      return this.selectedMovies.filter(movie => movie.status === 'processed');
    }
  },
  
  mounted() {
    this.loadMovies();
    this.loadStats();
    this.initWebSocket();
  },
  
  beforeUnmount() {
    if (this.socket) {
      this.socket.disconnect();
    }
  },
  
  methods: {
    async loadMovies() {
      this.loading = true;
      try {
        const response = await this.$http.get('/api/movie-processing/pending', {
          params: {
            page: this.currentPage,
            limit: this.pageSize,
            status: this.statusFilter
          }
        });
        
        this.movies = response.data.data.movies;
        this.total = response.data.data.pagination.total;
      } catch (error) {
        this.$message.error('加载影片列表失败');
      } finally {
        this.loading = false;
      }
    },
    
    async loadStats() {
      try {
        const response = await this.$http.get('/api/movie-processing/stats');
        this.stats = response.data.data;
      } catch (error) {
        console.error('加载统计失败:', error);
      }
    },
    
    initWebSocket() {
      this.socket = io(process.env.VUE_APP_API_URL);
      
      // 监听处理进度更新
      this.socket.on('video-task-progress', (data) => {
        this.updateMovieProgress(data);
      });
      
      // 监听处理完成
      this.socket.on('movie-processing-completed', (data) => {
        this.onProcessingCompleted(data);
      });
      
      // 监听处理失败
      this.socket.on('movie-processing-failed', (data) => {
        this.onProcessingFailed(data);
      });
    },
    
    updateMovieProgress(data) {
      const movie = this.movies.find(m => m.id === data.movieId);
      if (movie) {
        movie.progress = data.progress;
        movie.task_status = data.status;
      }
    },
    
    onProcessingCompleted(data) {
      const movie = this.movies.find(m => m.id === data.movieId);
      if (movie) {
        movie.status = 'processed';
        movie.progress = 100;
      }
      
      this.$notify({
        title: '处理完成',
        message: `影片《${data.title}》处理完成，可以发布了！`,
        type: 'success'
      });
      
      this.loadStats();
    },
    
    onProcessingFailed(data) {
      const movie = this.movies.find(m => m.id === data.movieId);
      if (movie) {
        movie.status = 'failed';
        movie.error_message = data.error;
      }
      
      this.$notify({
        title: '处理失败',
        message: `影片处理失败: ${data.error}`,
        type: 'error'
      });
      
      this.loadStats();
    },
    
    handleSelectionChange(selection) {
      this.selectedMovies = selection;
    },
    
    startProcessing() {
      if (this.selectedMovies.length === 0) {
        this.$message.warning('请选择要处理的影片');
        return;
      }
      
      this.showConfigDialog = true;
    },
    
    async confirmProcessing() {
      this.processing = true;
      
      try {
        const movieIds = this.selectedMovies.map(m => m.id);
        const config = {
          priority: this.processingConfig.priority,
          watermark: this.processingConfig.watermark,
          slice: {
            qualities: this.selectedQualities.map(q => {
              const qualityMap = {
                '1080p': { resolution: '1080p', bitrate: '5000k' },
                '720p': { resolution: '720p', bitrate: '3000k' },
                '480p': { resolution: '480p', bitrate: '1500k' }
              };
              return qualityMap[q];
            })
          }
        };
        
        const response = await this.$http.post('/api/movie-processing/start', {
          movieIds,
          config
        });
        
        this.$message.success(response.data.message);
        this.showConfigDialog = false;
        this.loadMovies();
        this.loadStats();
        
      } catch (error) {
        this.$message.error('启动处理失败');
      } finally {
        this.processing = false;
      }
    },
    
    async publishMovies() {
      if (this.selectedProcessedMovies.length === 0) {
        this.$message.warning('请选择已处理完成的影片');
        return;
      }
      
      try {
        await this.$confirm('确定要发布选中的影片吗？发布后用户就可以观看了。', '确认发布');
        
        const movieIds = this.selectedProcessedMovies.map(m => m.id);
        const response = await this.$http.post('/api/movie-processing/publish', {
          movieIds
        });
        
        this.$message.success(response.data.message);
        this.loadMovies();
        this.loadStats();
        
      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error('发布失败');
        }
      }
    },
    
    getStatusType(status) {
      const statusMap = {
        'draft': 'info',
        'processing': 'warning',
        'processed': 'success',
        'published': '',
        'failed': 'danger'
      };
      return statusMap[status] || 'info';
    },
    
    getStatusLabel(status) {
      const statusMap = {
        'draft': '草稿',
        'processing': '处理中',
        'processed': '已处理',
        'published': '已发布',
        'failed': '失败'
      };
      return statusMap[status] || status;
    },
    
    formatTime(time) {
      return new Date(time).toLocaleString();
    },
    
    refreshData() {
      this.loadMovies();
      this.loadStats();
    },
    
    handleSizeChange(size) {
      this.pageSize = size;
      this.loadMovies();
    },
    
    handleCurrentChange(page) {
      this.currentPage = page;
      this.loadMovies();
    },

    async updatePriority(movieId, priority) {
      try {
        await this.$http.put('/api/movie-processing/priority', {
          movieId,
          priority
        });
        this.$message.success('优先级更新成功');
      } catch (error) {
        this.$message.error('优先级更新失败');
      }
    },

    async startSingleProcessing(movieId) {
      this.selectedMovies = this.movies.filter(m => m.id === movieId);
      this.showConfigDialog = true;
    },

    async publishSingle(movieId) {
      try {
        await this.$http.post('/api/movie-processing/publish', {
          movieIds: [movieId]
        });
        this.$message.success('影片发布成功');
        this.loadMovies();
        this.loadStats();
      } catch (error) {
        this.$message.error('影片发布失败');
      }
    },

    async cancelProcessing(movieId) {
      try {
        await this.$confirm('确定要取消处理吗？', '确认取消');
        await this.$http.delete(`/api/movie-processing/cancel/${movieId}`);
        this.$message.success('处理已取消');
        this.loadMovies();
      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error('取消失败');
        }
      }
    },

    viewDetails(movie) {
      // TODO: 实现详情查看
      this.$message.info('详情功能开发中...');
    },

    handleImageError(event) {
      event.target.src = '/placeholder-image.jpg';
    }
  }
};
</script>

<style scoped>
.movie-processing {
  padding: 20px;
}

.page-header h1 {
  margin-bottom: 20px;
  color: #303133;
}

.stats-cards {
  margin-bottom: 20px;
}

.stat-card {
  text-align: center;
  cursor: pointer;
  transition: all 0.3s;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.stat-content {
  padding: 10px;
}

.stat-number {
  font-size: 24px;
  font-weight: bold;
  color: #409EFF;
}

.stat-label {
  font-size: 14px;
  color: #909399;
  margin-top: 5px;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.movie-info {
  display: flex;
  align-items: center;
}

.movie-thumb {
  width: 40px;
  height: 60px;
  object-fit: cover;
  border-radius: 4px;
  margin-right: 10px;
}

.movie-details {
  flex: 1;
}

.movie-title {
  font-weight: bold;
  margin-bottom: 5px;
}

.movie-meta {
  font-size: 12px;
  color: #909399;
}

.progress-text {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
}

.error-text {
  font-size: 12px;
  color: #F56C6C;
  margin-top: 5px;
}

.pagination-wrapper {
  margin-top: 20px;
  text-align: center;
}

.watermark-config {
  margin-top: 10px;
}

.form-help {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
}
</style>
