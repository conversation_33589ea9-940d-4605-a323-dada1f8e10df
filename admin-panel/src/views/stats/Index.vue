<template>
  <div class="stats-container">
    <div class="page-header">
      <h2>数据统计</h2>
    </div>
    
    <!-- 基本统计信息卡片 -->
    <el-row :gutter="20">
      <el-col :span="6">
        <el-card shadow="hover" class="stat-card">
          <div class="stat-card-inner">
            <div class="stat-icon video-icon">
              <i class="el-icon-video-camera"></i>
            </div>
            <div class="stat-content">
              <div class="stat-title">视频总数</div>
              <div class="stat-value">{{ stats.videoCount }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card shadow="hover" class="stat-card">
          <div class="stat-card-inner">
            <div class="stat-icon star-icon">
              <i class="el-icon-user"></i>
            </div>
            <div class="stat-content">
              <div class="stat-title">演员总数</div>
              <div class="stat-value">{{ stats.starCount }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card shadow="hover" class="stat-card">
          <div class="stat-card-inner">
            <div class="stat-icon user-icon">
              <i class="el-icon-user-solid"></i>
            </div>
            <div class="stat-content">
              <div class="stat-title">用户总数</div>
              <div class="stat-value">{{ stats.userCount }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card shadow="hover" class="stat-card">
          <div class="stat-card-inner">
            <div class="stat-icon magnet-icon">
              <i class="el-icon-download"></i>
            </div>
            <div class="stat-content">
              <div class="stat-title">磁力链接数</div>
              <div class="stat-value">{{ stats.magnetCount }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
    
    <!-- 视频数据趋势图 -->
    <el-card class="chart-card">
      <template #header>
        <div class="card-header">
          <span>视频数据趋势</span>
          <el-radio-group v-model="timeRange" size="small" @change="loadChartData">
            <el-radio-button label="week">最近一周</el-radio-button>
            <el-radio-button label="month">最近一月</el-radio-button>
            <el-radio-button label="year">最近一年</el-radio-button>
          </el-radio-group>
        </div>
      </template>
      <div class="chart-container" ref="videoChartRef"></div>
    </el-card>
    
    <!-- 用户活动统计 -->
    <el-card class="chart-card">
      <template #header>
        <div class="card-header">
          <span>用户活动统计</span>
        </div>
      </template>
      <div class="chart-container" ref="userChartRef"></div>
    </el-card>
    
    <!-- 标签分布统计 -->
    <el-row :gutter="20">
      <el-col :span="12">
        <el-card class="chart-card">
          <template #header>
            <div class="card-header">
              <span>热门标签分布</span>
            </div>
          </template>
          <div class="chart-container" ref="genreChartRef"></div>
        </el-card>
      </el-col>
      
      <el-col :span="12">
        <el-card class="chart-card">
          <template #header>
            <div class="card-header">
              <span>热门演员分布</span>
            </div>
          </template>
          <div class="chart-container" ref="starChartRef"></div>
        </el-card>
      </el-col>
    </el-row>
    
    <!-- 磁力统计 -->
    <el-card class="chart-card">
      <template #header>
        <div class="card-header">
          <span>磁力链接统计</span>
        </div>
      </template>
      <el-row :gutter="20">
        <el-col :span="12">
          <div class="chart-container" ref="magnetQualityChartRef"></div>
        </el-col>
        <el-col :span="12">
          <div class="chart-container" ref="magnetSubtitleChartRef"></div>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import * as echarts from 'echarts'
import { 
  getMovieStats, 
  getStarStats, 
  getUserStats, 
  getMagnetStats, 
  getGenreStats 
} from '../../api/stats'

export default {
  name: 'StatsIndex',
  setup() {
    // 图表实例
    let videoChart = null
    let userChart = null
    let genreChart = null
    let starChart = null
    let magnetQualityChart = null
    let magnetSubtitleChart = null
    
    // 图表Dom引用
    const videoChartRef = ref(null)
    const userChartRef = ref(null)
    const genreChartRef = ref(null)
    const starChartRef = ref(null)
    const magnetQualityChartRef = ref(null)
    const magnetSubtitleChartRef = ref(null)
    
    // 时间范围选择
    const timeRange = ref('month')
    
    // 基本统计数据
    const stats = reactive({
      videoCount: 0,
      starCount: 0,
      userCount: 0,
      magnetCount: 0,
      genreCount: 0
    })
    
    // 加载基本统计数据
    const loadStats = async () => {
      try {
        // 首先处理重要的统计数据，这些API可能会成功
        const [movieResponse, starResponse, userResponse, genreResponse] = await Promise.all([
          getMovieStats(),
          getStarStats(),
          getUserStats(), 
          getGenreStats()
        ]);
        
        // 提取视频总数
        stats.videoCount = movieResponse?.pagination?.totalItems || 0;
        
        // 提取演员总数
        stats.starCount = starResponse?.totalStars || starResponse?.count || 0;
        
        // 提取用户总数
        stats.userCount = userResponse?.count || 0;
        
        // 提取标签总数
        stats.genreCount = genreResponse?.totalGenres || genreResponse?.count || 0;

        // 单独处理磁力链接统计，因为这个可能会失败
        try {
          const magnetResponse = await getMagnetStats();
          stats.magnetCount = magnetResponse?.totalMagnets || magnetResponse?.count || 0;
        } catch (magnetError) {
          console.warn('获取磁力链接统计失败:', magnetError);
          stats.magnetCount = 0;
        }
      } catch (error) {
        console.error('加载统计数据失败:', error);
      }
    }
    
    // 初始化图表
    const initCharts = () => {
      // 视频数据趋势图
      videoChart = echarts.init(videoChartRef.value)
      videoChart.setOption({
        title: {
          text: '视频上传趋势'
        },
        tooltip: {
          trigger: 'axis'
        },
        legend: {
          data: ['视频数量']
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            name: '视频数量',
            type: 'line',
            smooth: true,
            data: [10, 13, 15, 18, 20, 25, 30]
          }
        ]
      });
      
      // 用户活动统计图
      userChart = echarts.init(userChartRef.value)
      userChart.setOption({
        title: {
          text: '用户活动统计'
        },
        tooltip: {
          trigger: 'axis'
        },
        legend: {
          data: ['新注册用户', '活跃用户']
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            name: '新注册用户',
            type: 'line',
            smooth: true,
            data: [5, 8, 10, 12, 15, 20, 25]
          },
          {
            name: '活跃用户',
            type: 'line',
            smooth: true,
            data: [50, 55, 60, 58, 65, 80, 95]
          }
        ]
      });
      
      // 标签分布统计图
      genreChart = echarts.init(genreChartRef.value)
      genreChart.setOption({
        title: {
          text: '热门标签分布',
          left: 'center'
        },
        tooltip: {
          trigger: 'item'
        },
        legend: {
          orient: 'vertical',
          left: 'left'
        },
        series: [
          {
            name: '标签分布',
            type: 'pie',
            radius: '50%',
            data: [
              { value: 148, name: '剧情' },
              { value: 120, name: '喜剧' },
              { value: 100, name: '动作' },
              { value: 80, name: '爱情' },
              { value: 60, name: '科幻' }
            ],
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            }
          }
        ]
      });
      
      // 演员分布统计图
      starChart = echarts.init(starChartRef.value)
      starChart.setOption({
        title: {
          text: '热门演员分布',
          left: 'center'
        },
        tooltip: {
          trigger: 'item'
        },
        legend: {
          orient: 'vertical',
          left: 'left'
        },
        series: [
          {
            name: '演员作品数',
            type: 'pie',
            radius: '50%',
            data: [
              { value: 25, name: '演员A' },
              { value: 20, name: '演员B' },
              { value: 18, name: '演员C' },
              { value: 15, name: '演员D' },
              { value: 12, name: '演员E' }
            ],
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            }
          }
        ]
      });
      
      // 磁力质量分布图
      magnetQualityChart = echarts.init(magnetQualityChartRef.value)
      magnetQualityChart.setOption({
        title: {
          text: '高清与标清分布',
          left: 'center'
        },
        tooltip: {
          trigger: 'item'
        },
        legend: {
          orient: 'vertical',
          left: 'left'
        },
        series: [
          {
            name: '磁力质量',
            type: 'pie',
            radius: '50%',
            data: [
              { value: 60, name: '高清' },
              { value: 40, name: '标清' }
            ],
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            }
          }
        ]
      });
      
      // 磁力字幕分布图
      magnetSubtitleChart = echarts.init(magnetSubtitleChartRef.value)
      magnetSubtitleChart.setOption({
        title: {
          text: '字幕分布',
          left: 'center'
        },
        tooltip: {
          trigger: 'item'
        },
        legend: {
          orient: 'vertical',
          left: 'left'
        },
        series: [
          {
            name: '字幕分布',
            type: 'pie',
            radius: '50%',
            data: [
              { value: 70, name: '有字幕' },
              { value: 30, name: '无字幕' }
            ],
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            }
          }
        ]
      });
    }
    
    // 加载图表数据
    const loadChartData = () => {
      // 根据实际API调整数据获取逻辑
      // 这里使用示例数据
    }
    
    // 窗口大小变化时重绘图表
    const handleResize = () => {
      videoChart && videoChart.resize();
      userChart && userChart.resize();
      genreChart && genreChart.resize();
      starChart && starChart.resize();
      magnetQualityChart && magnetQualityChart.resize();
      magnetSubtitleChart && magnetSubtitleChart.resize();
    }
    
    onMounted(() => {
      loadStats();
      // 确保DOM已经渲染
      setTimeout(() => {
        initCharts();
        loadChartData();
        
        // 监听窗口大小变化
        window.addEventListener('resize', handleResize);
      }, 300);
    })
    
    // 组件卸载时清理资源
    onUnmounted(() => {
      // 移除窗口大小变化监听器
      window.removeEventListener('resize', handleResize);
      
      // 销毁图表实例以避免内存泄漏
      videoChart && videoChart.dispose();
      userChart && userChart.dispose();
      genreChart && genreChart.dispose();
      starChart && starChart.dispose();
      magnetQualityChart && magnetQualityChart.dispose();
      magnetSubtitleChart && magnetSubtitleChart.dispose();
    })
    
    return {
      stats,
      timeRange,
      videoChartRef,
      userChartRef,
      genreChartRef,
      starChartRef,
      magnetQualityChartRef,
      magnetSubtitleChartRef,
      loadChartData
    }
  }
}
</script>

<style scoped>
.stats-container {
  padding: 20px 0;
}

.page-header {
  margin-bottom: 20px;
}

.stat-card {
  height: 120px;
  margin-bottom: 20px;
}

.stat-card-inner {
  display: flex;
  align-items: center;
  height: 100%;
}

.stat-icon {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 20px;
}

.stat-icon i {
  font-size: 40px;
  color: white;
}

.video-icon {
  background-color: #409EFF;
}

.star-icon {
  background-color: #67C23A;
}

.user-icon {
  background-color: #E6A23C;
}

.magnet-icon {
  background-color: #F56C6C;
}

.stat-content {
  display: flex;
  flex-direction: column;
}

.stat-title {
  font-size: 16px;
  color: #909399;
}

.stat-value {
  font-size: 28px;
  font-weight: bold;
  color: #303133;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chart-card {
  margin-bottom: 20px;
}

.chart-container {
  height: 400px;
}
</style>
