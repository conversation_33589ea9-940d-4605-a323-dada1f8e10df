import request from './request'

// 获取视频列表
export function getVideoList(params) {
  return request({
    url: '/db/movies',
    method: 'get',
    params
  })
}

// 获取视频详情
export function getVideoDetail(id) {
  return request({
    url: `/db/movies/${id}`,
    method: 'get'
  })
}

// 创建视频
export function createVideo(data) {
  return request({
    url: '/admin/movies',
    method: 'post',
    data
  })
}

// 更新视频
export function updateVideo(id, data) {
  return request({
    url: `/admin/movies/${id}`,
    method: 'put',
    data
  })
}

// 删除视频
export function deleteVideo(id) {
  return request({
    url: `/admin/movies/${id}`,
    method: 'delete'
  })
}

// 获取视频磁力链接
export function getVideoMagnets(id) {
  return request({
    url: `/db/movies/${id}/magnets`,
    method: 'get'
  })
}

// 搜索视频
export function searchVideos(params) {
  return request({
    url: '/db/movies/search',
    method: 'get',
    params
  })
}

// 获取热门视频
export function getPopularVideos(params) {
  return request({
    url: '/db/movies/popular',
    method: 'get',
    params
  })
}

// 获取最新视频
export function getRecentVideos(params) {
  return request({
    url: '/db/movies/recent',
    method: 'get',
    params
  })
}

// 根据演员ID获取相关作品
export function getMoviesByStar(starId, params) {
  return request({
    url: `/db/stars/${starId}/movies`,
    method: 'get',
    params
  })
} 