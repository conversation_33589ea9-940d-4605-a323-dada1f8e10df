import request from './request'

// 从JavBus导入影片
export function importMoviesFromJavBus(params) {
  return request({
    url: '/javbus-admin/import-movies',
    method: 'get',
    params
  })
}

// 保存影片数据到数据库（已包含本地图片下载功能）
export function saveMovies(data) {
  return request({
    url: '/javbus-admin/save-movies',
    method: 'post',
    data
  })
}

// 获取导入任务状态
export function getImportStatus() {
  return request({
    url: '/javbus-admin/import-status',
    method: 'get'
  })
}

// 获取已导入的影片列表
export function getImportedMovies(params) {
  return request({
    url: '/javbus-admin/imported-movies',
    method: 'get',
    params
  })
}

// 获取JavBus影片列表
export function getJavBusMovies(params) {
  return request({
    url: '/javbus/movies',
    method: 'get',
    params
  })
}

// 获取JavBus影片详情
export function getJavBusMovieDetail(id) {
  return request({
    url: `/javbus/movies/${id}`,
    method: 'get'
  })
}

// 搜索JavBus影片
export function searchJavBusMovies(params) {
  return request({
    url: '/javbus/movies/search',
    method: 'get',
    params
  })
}

// 下载影片图片到本地
export function downloadMovieImages(data) {
  return request({
    url: '/javbus-admin/download-images',
    method: 'post',
    data
  })
} 