import request from './request'

// 获取演员列表
export function getStarsList(params) {
  return request({
    url: '/db/stars',
    method: 'get',
    params
  })
}

// 获取热门演员
export function getPopularStars(params) {
  return request({
    url: '/db/stars/popular',
    method: 'get',
    params
  })
}

// 根据ID获取演员信息
export function getStarById(id) {
  return request({
    url: `/db/stars/${id}`,
    method: 'get'
  })
}

// 创建演员
export function createStar(data) {
  return request({
    url: '/db/stars',
    method: 'post',
    data
  })
}

// 更新演员信息
export function updateStar(id, data) {
  return request({
    url: `/db/stars/${id}`,
    method: 'put',
    data
  })
}

// 删除演员
export function deleteStar(id) {
  return request({
    url: `/db/stars/${id}`,
    method: 'delete'
  })
}

// 搜索演员
export function searchStars(params) {
  return request({
    url: '/db/stars/search',
    method: 'get',
    params
  })
}