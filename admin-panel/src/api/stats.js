import request from './request';

// 获取影片统计信息
export function getMovieStats() {
  return request({
    url: '/db/stats/movies',
    method: 'get'
  });
}

// 获取演员统计信息
export function getStarStats() {
  return request({
    url: '/db/stats/stars',
    method: 'get'
  });
}

// 获取类型统计信息
export function getGenreStats() {
  return request({
    url: '/db/stats/genres',
    method: 'get'
  });
}

// 获取磁力链接统计信息
export function getMagnetStats() {
  return request({
    url: '/db/stats/magnets',
    method: 'get'
  });
}

// 获取用户统计信息 - 使用实际的用户数据
export function getUserStats() {
  return request({
    url: '/users/stats',
    method: 'get'
  });
}
