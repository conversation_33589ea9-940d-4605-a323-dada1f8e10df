import request from './request'

// 获取类型列表
export function getGenreList(params) {
  return request({
    url: '/db/genres',
    method: 'get',
    params
  })
}

// 获取热门类型
export function getPopularGenres(params) {
  return request({
    url: '/db/genres/popular',
    method: 'get',
    params
  })
}

// 根据ID获取类型信息
export function getGenreById(id) {
  return request({
    url: `/db/genres/${id}`,
    method: 'get'
  })
}

// 创建类型
export function createGenre(data) {
  return request({
    url: '/db/genres',
    method: 'post',
    data
  })
}

// 更新类型信息
export function updateGenre(id, data) {
  return request({
    url: `/db/genres/${id}`,
    method: 'put',
    data
  })
}

// 删除类型
export function deleteGenre(id) {
  return request({
    url: `/db/genres/${id}`,
    method: 'delete'
  })
}

// 更新标签状态
export function updateGenreStatus(id, data) {
  return request({
    url: `/db/genres/${id}/status`,
    method: 'patch',
    data
  })
}

// 获取类型下的影片
export function getMoviesByGenre(genreId, params) {
  return request({
    url: `/db/movies/genre/${genreId}`,
    method: 'get',
    params
  })
}