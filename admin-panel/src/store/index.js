import { createStore } from 'vuex'

export default createStore({
  state: {
    token: localStorage.getItem('admin_token') || '',
    user: JSON.parse(localStorage.getItem('admin_user') || '{}'),
    sidebarCollapsed: false
  },
  mutations: {
    SET_TOKEN(state, token) {
      state.token = token
      localStorage.setItem('admin_token', token)
    },
    SET_USER(state, user) {
      state.user = user
      localStorage.setItem('admin_user', JSON.stringify(user))
    },
    CLEAR_AUTH(state) {
      state.token = ''
      state.user = {}
      localStorage.removeItem('admin_token')
      localStorage.removeItem('admin_user')
    },
    TOGGLE_SIDEBAR(state) {
      state.sidebarCollapsed = !state.sidebarCollapsed
    }
  },
  actions: {
    login({ commit }, { token, user }) {
      commit('SET_TOKEN', token)
      commit('SET_USER', user)
    },
    logout({ commit }) {
      commit('C<PERSON>AR_AUTH')
    }
  },
  getters: {
    isAuthenticated: state => !!state.token,
    username: state => state.user.username || '',
    sidebarCollapsed: state => state.sidebarCollapsed
  }
}) 