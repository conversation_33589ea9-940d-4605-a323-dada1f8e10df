<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>清除认证状态</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .btn {
            background: #ff4444;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 10px 5px;
        }
        .btn:hover {
            background: #cc3333;
        }
        .success {
            color: #28a745;
            font-weight: bold;
        }
        .info {
            background: #e9ecef;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>清除认证状态工具</h1>
        
        <div class="info">
            <p><strong>说明：</strong>由于修复了认证系统，需要清除旧的无效token并重新登录。</p>
            <p>点击下面的按钮将清除所有本地存储的认证信息。</p>
        </div>
        
        <div>
            <button class="btn" onclick="clearAuth()">清除认证状态</button>
            <button class="btn" onclick="goToLogin()" style="background: #007bff;">前往登录页面</button>
        </div>
        
        <div id="result" style="margin-top: 20px;"></div>
        
        <div class="info" style="margin-top: 30px;">
            <h3>步骤说明：</h3>
            <ol>
                <li>点击"清除认证状态"按钮</li>
                <li>点击"前往登录页面"或直接访问网站</li>
                <li>重新登录以获取正确的JWT token</li>
                <li>现在点赞功能应该可以正常工作了</li>
            </ol>
        </div>
    </div>

    <script>
        function clearAuth() {
            try {
                // 清除localStorage
                localStorage.removeItem('auth_token');
                localStorage.removeItem('current_user');
                localStorage.removeItem('auth_timestamp');
                
                // 清除所有认证相关的sessionStorage
                sessionStorage.clear();
                
                // 清除cookies
                document.cookie = 'auth_token=; Path=/; Expires=Thu, 01 Jan 1970 00:00:01 GMT;';
                document.cookie = 'auth_state=; Path=/; Expires=Thu, 01 Jan 1970 00:00:01 GMT;';
                
                // 显示成功消息
                document.getElementById('result').innerHTML = 
                    '<div class="success">✅ 认证状态已清除！现在可以重新登录了。</div>';
                
                console.log('认证状态已清除');
            } catch (error) {
                document.getElementById('result').innerHTML = 
                    '<div style="color: red;">❌ 清除过程中出现错误: ' + error.message + '</div>';
                console.error('清除认证状态时出错:', error);
            }
        }
        
        function goToLogin() {
            // 判断当前是否在localhost:3000
            if (window.location.hostname === 'localhost' && window.location.port === '3000') {
                window.location.href = '/en-US/auth/login';
            } else {
                alert('请手动访问 http://localhost:3000/en-US/auth/login 进行登录');
            }
        }
        
        // 页面加载时检查当前认证状态
        window.onload = function() {
            const token = localStorage.getItem('auth_token');
            const user = localStorage.getItem('current_user');
            
            if (token || user) {
                document.getElementById('result').innerHTML = 
                    '<div style="color: orange;">⚠️ 检测到旧的认证数据，建议清除后重新登录。</div>';
            } else {
                document.getElementById('result').innerHTML = 
                    '<div class="success">✅ 当前没有认证数据，可以直接登录。</div>';
            }
        };
    </script>
</body>
</html> 