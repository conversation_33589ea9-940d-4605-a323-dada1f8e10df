const { Pool } = require('pg');
require('dotenv').config();

const pool = new Pool({
  host: process.env.PGHOST || 'localhost',
  port: process.env.PGPORT || 5432,
  database: process.env.PGDATABASE || 'javflix',
  user: process.env.PGUSER || 'longgedemacminim4',
  password: process.env.PGPASSWORD || '',
});

async function cleanupTestTasks() {
  const client = await pool.connect();
  
  try {
    console.log('🔄 开始清理测试任务...');
    
    // 删除所有测试任务
    const testTaskIds = [
      'test-12345',
      'real-test-12345'
    ];
    
    for (const taskId of testTaskIds) {
      // 删除视频处理任务记录
      const deleteResult = await client.query(
        'DELETE FROM video_processing_tasks WHERE task_uuid = $1',
        [taskId]
      );
      
      if (deleteResult.rowCount > 0) {
        console.log(`✅ 删除任务: ${taskId}`);
      } else {
        console.log(`⚠️  任务不存在: ${taskId}`);
      }
    }
    
    // 清理Redis队列中的测试任务（如果需要的话）
    console.log('📋 清理完成');
    
    // 显示剩余的任务
    const remainingTasks = await client.query(`
      SELECT task_uuid, movie_id, status, created_at 
      FROM video_processing_tasks 
      ORDER BY created_at DESC 
      LIMIT 10
    `);
    
    console.log('\n📊 剩余任务:');
    if (remainingTasks.rows.length === 0) {
      console.log('  无任务');
    } else {
      remainingTasks.rows.forEach(task => {
        console.log(`  - ${task.task_uuid} (影片${task.movie_id}) - ${task.status} - ${task.created_at}`);
      });
    }
    
  } catch (error) {
    console.error('❌ 清理失败:', error);
    throw error;
  } finally {
    client.release();
    await pool.end();
  }
}

// 运行脚本
cleanupTestTasks()
  .then(() => {
    console.log('✅ 清理完成');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ 清理失败:', error);
    process.exit(1);
  });
