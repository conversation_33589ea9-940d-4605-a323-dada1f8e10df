<template>
  <el-container>
    <!-- 左侧导航 -->
    <el-aside :width="sidebarCollapsed ? '64px' : '220px'" class="sidebar">
      <div class="logo">
        <router-link to="/">
          <h1 v-if="!sidebarCollapsed">JAVFLIX管理</h1>
          <h1 v-else>J</h1>
        </router-link>
      </div>
      <el-menu
        :collapse="sidebarCollapsed"
        :default-active="activeMenu"
        background-color="#304156"
        text-color="#bfcbd9"
        active-text-color="#409EFF"
        :collapse-transition="false"
        router
      >
        <el-menu-item index="/dashboard">
          <el-icon><i class="el-icon-s-home"></i></el-icon>
          <template #title>控制面板</template>
        </el-menu-item>
        
        <el-sub-menu index="/videos">
          <template #title>
            <el-icon><i class="el-icon-video-camera"></i></el-icon>
            <span>视频管理</span>
          </template>
          <el-menu-item index="/videos">视频列表</el-menu-item>
          <el-menu-item index="/videos/create">添加视频</el-menu-item>
        </el-sub-menu>
        
        <el-menu-item index="/stars">
          <el-icon><i class="el-icon-user"></i></el-icon>
          <template #title>演员管理</template>
        </el-menu-item>
        
        <el-menu-item index="/genres">
          <el-icon><i class="el-icon-collection-tag"></i></el-icon>
          <template #title>类型管理</template>
        </el-menu-item>
        
        <el-menu-item index="/users">
          <el-icon><i class="el-icon-user-solid"></i></el-icon>
          <template #title>用户管理</template>
        </el-menu-item>
        
        <el-menu-item index="/import">
          <el-icon><i class="el-icon-download"></i></el-icon>
          <template #title>数据导入</template>
        </el-menu-item>

        <el-menu-item index="/video-processing">
          <el-icon><i class="el-icon-video-play"></i></el-icon>
          <template #title>视频处理监控</template>
        </el-menu-item>
        
        <el-menu-item index="/stats">
          <el-icon><i class="el-icon-data-analysis"></i></el-icon>
          <template #title>数据统计</template>
        </el-menu-item>
      </el-menu>
    </el-aside>
    
    <el-container>
      <!-- 顶部导航 -->
      <el-header class="header">
        <div class="left">
          <div class="toggle" @click="toggleSidebar">
            <i class="el-icon-s-fold" v-if="!sidebarCollapsed"></i>
            <i class="el-icon-s-unfold" v-else></i>
          </div>
          <breadcrumb />
        </div>
        <div class="right">
          <el-dropdown trigger="click">
            <div class="user-info">
              <span>{{ username }}</span>
              <i class="el-icon-arrow-down"></i>
            </div>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item @click="logout">退出登录</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </el-header>
      
      <!-- 主内容区 -->
      <el-main class="container-main">
        <router-view />
      </el-main>
    </el-container>
  </el-container>
</template>

<script>
import { computed, h } from 'vue'
import { useStore } from 'vuex'
import { useRouter, useRoute } from 'vue-router'

export default {
  name: 'Layout',
  components: {
    Breadcrumb: {
      setup() {
        return () => h('div', '路径')
      }
    }
  },
  setup() {
    const store = useStore()
    const router = useRouter()
    const route = useRoute()
    
    const sidebarCollapsed = computed(() => store.getters.sidebarCollapsed)
    const username = computed(() => store.getters.username || '管理员')
    
    // 获取当前激活菜单
    const activeMenu = computed(() => {
      const { path } = route
      return path
    })
    
    // 折叠/展开侧边栏
    const toggleSidebar = () => {
      store.commit('TOGGLE_SIDEBAR')
    }
    
    // 退出登录
    const logout = () => {
      store.dispatch('logout')
      router.push('/login')
    }
    
    return {
      sidebarCollapsed,
      username,
      activeMenu,
      toggleSidebar,
      logout
    }
  }
}
</script>

<style scoped>
.sidebar {
  height: 100%;
  background-color: #304156;
  transition: width 0.3s;
  overflow-x: hidden;
}

.sidebar .logo {
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-bottom: 1px solid #1f2d3d;
}

.sidebar .logo h1 {
  color: #fff;
  font-size: 18px;
  margin: 0;
  white-space: nowrap;
}

.sidebar .logo a {
  text-decoration: none;
}

.header {
  height: 60px;
  background-color: #fff;
  border-bottom: 1px solid #e6e6e6;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
}

.header .left {
  display: flex;
  align-items: center;
}

.header .toggle {
  font-size: 20px;
  color: #5a5e66;
  cursor: pointer;
  margin-right: 20px;
}

.header .right .user-info {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.header .right .user-info span {
  margin-right: 10px;
  color: #606266;
}
</style> 