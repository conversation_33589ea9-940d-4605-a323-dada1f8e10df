import request from './request'

// 获取视频处理任务列表
export function getVideoTasks(params) {
  return request({
    url: '/video-processing/tasks',
    method: 'get',
    params
  })
}

// 获取单个任务详情
export function getTaskDetail(taskId) {
  return request({
    url: `/video-processing/tasks/${taskId}`,
    method: 'get'
  })
}

// 创建新的视频处理任务
export function createVideoTask(data) {
  return request({
    url: '/video-processing/tasks',
    method: 'post',
    data
  })
}

// 重试失败的任务
export function retryTask(taskId) {
  return request({
    url: `/video-processing/tasks/${taskId}/retry`,
    method: 'post'
  })
}

// 取消正在进行的任务
export function cancelTask(taskId) {
  return request({
    url: `/video-processing/tasks/${taskId}/cancel`,
    method: 'post'
  })
}

// 删除任务
export function deleteTask(taskId) {
  return request({
    url: `/video-processing/tasks/${taskId}`,
    method: 'delete'
  })
}

// 获取任务统计信息
export function getTaskStats() {
  return request({
    url: '/video-processing/stats',
    method: 'get'
  })
}

// 获取系统状态
export function getSystemStatus() {
  return request({
    url: '/video-processing/system-status',
    method: 'get'
  })
}

// 批量操作任务
export function batchOperateTasks(data) {
  return request({
    url: '/video-processing/tasks/batch',
    method: 'post',
    data
  })
}

// 获取任务日志
export function getTaskLogs(taskId, params) {
  return request({
    url: `/video-processing/tasks/${taskId}/logs`,
    method: 'get',
    params
  })
}

// 清理已完成的任务
export function cleanupCompletedTasks() {
  return request({
    url: '/video-processing/tasks/cleanup',
    method: 'post'
  })
}

// 获取处理队列状态
export function getQueueStatus() {
  return request({
    url: '/video-processing/queue-status',
    method: 'get'
  })
}

// 暂停/恢复处理队列
export function toggleQueue(action) {
  return request({
    url: `/video-processing/queue/${action}`,
    method: 'post'
  })
}

// 获取实时进度
export function getTaskProgress(taskId) {
  return request({
    url: `/video-processing/tasks/${taskId}/progress`,
    method: 'get'
  })
}
