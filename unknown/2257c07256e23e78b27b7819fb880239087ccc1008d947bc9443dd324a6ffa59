const { Pool } = require('pg');
require('dotenv').config();

const pool = new Pool({
  host: process.env.PGHOST || 'localhost',
  port: process.env.PGPORT || 5432,
  database: process.env.PGDATABASE || 'javflix',
  user: process.env.PGUSER || 'longgedemacminim4',
  password: process.env.PGPASSWORD || '',
});

async function createVideoProcessingTable() {
  const client = await pool.connect();
  
  try {
    console.log('🔄 开始创建视频处理任务表...');
    
    // 创建视频处理任务表
    await client.query(`
      CREATE TABLE IF NOT EXISTS video_processing_tasks (
        id SERIAL PRIMARY KEY,
        task_uuid VARCHAR(255) UNIQUE NOT NULL,
        movie_id INTEGER REFERENCES movies(id) ON DELETE CASCADE,
        magnet_link TEXT NOT NULL,
        status VARCHAR(20) DEFAULT 'pending',
        progress INTEGER DEFAULT 0,
        current_step VARCHAR(255),
        error_message TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        started_at TIMESTAMP,
        completed_at TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        
        -- 处理结果信息
        download_path TEXT,
        output_path TEXT,
        video_url TEXT,
        thumbnail_url TEXT,
        duration INTEGER,
        file_size BIGINT,
        
        -- 处理配置
        watermark_text VARCHAR(255) DEFAULT 'JAVFLIX.TV',
        watermark_position VARCHAR(20) DEFAULT 'bottom-right',
        quality VARCHAR(20) DEFAULT 'high',
        
        -- 元数据
        metadata JSONB DEFAULT '{}'::jsonb
      );
    `);
    console.log('✅ video_processing_tasks表创建成功');

    // 创建索引
    const indexes = [
      'CREATE INDEX IF NOT EXISTS idx_video_tasks_uuid ON video_processing_tasks(task_uuid);',
      'CREATE INDEX IF NOT EXISTS idx_video_tasks_movie_id ON video_processing_tasks(movie_id);',
      'CREATE INDEX IF NOT EXISTS idx_video_tasks_status ON video_processing_tasks(status);',
      'CREATE INDEX IF NOT EXISTS idx_video_tasks_created_at ON video_processing_tasks(created_at);',
      'CREATE INDEX IF NOT EXISTS idx_video_tasks_updated_at ON video_processing_tasks(updated_at);'
    ];

    for (const indexQuery of indexes) {
      await client.query(indexQuery);
    }
    console.log('✅ 所有索引创建成功');

    // 创建更新时间触发器
    await client.query(`
      CREATE OR REPLACE FUNCTION update_video_processing_tasks_updated_at()
      RETURNS TRIGGER AS $$
      BEGIN
        NEW.updated_at = CURRENT_TIMESTAMP;
        RETURN NEW;
      END;
      $$ language 'plpgsql';
    `);

    await client.query(`
      DROP TRIGGER IF EXISTS update_video_processing_tasks_updated_at_trigger ON video_processing_tasks;
      CREATE TRIGGER update_video_processing_tasks_updated_at_trigger
        BEFORE UPDATE ON video_processing_tasks
        FOR EACH ROW
        EXECUTE FUNCTION update_video_processing_tasks_updated_at();
    `);
    console.log('✅ 更新时间触发器创建成功');

    // 验证表是否创建成功
    const result = await client.query(`
      SELECT column_name, data_type, is_nullable, column_default
      FROM information_schema.columns 
      WHERE table_name = 'video_processing_tasks'
      ORDER BY ordinal_position;
    `);

    console.log('\n📊 video_processing_tasks表结构:');
    result.rows.forEach(row => {
      console.log(`  - ${row.column_name}: ${row.data_type} ${row.is_nullable === 'NO' ? 'NOT NULL' : ''} ${row.column_default ? `DEFAULT ${row.column_default}` : ''}`);
    });

    console.log('\n🎉 视频处理任务表创建完成！');
    
  } catch (error) {
    console.error('❌ 创建表时出错:', error);
    throw error;
  } finally {
    client.release();
    await pool.end();
  }
}

// 运行脚本
createVideoProcessingTable()
  .then(() => {
    console.log('✅ 脚本执行完成');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ 脚本执行失败:', error);
    process.exit(1);
  });
