const { Pool } = require('pg');
const Redis = require('redis');

// 数据库连接配置
const pool = new Pool({
  host: process.env.PGHOST || 'localhost',
  port: parseInt(process.env.PGPORT || '5432'),
  database: process.env.PGDATABASE || 'javflix',
  user: process.env.PGUSER || 'longgedemacminim4',
  password: process.env.PGPASSWORD || '',
});

// Redis连接
const redisClient = Redis.createClient();

async function syncStatsToRedis() {
  try {
    console.log('🚀 开始同步统计数据到Redis...');
    
    // 连接Redis
    await redisClient.connect();
    console.log('✅ Redis连接成功');
    
    // 从PostgreSQL获取所有视频的观看次数
    const query = `
      SELECT id, movie_id, view_count 
      FROM movies 
      WHERE view_count > 0 
      ORDER BY view_count DESC
    `;
    
    const result = await pool.query(query);
    console.log(`📊 找到 ${result.rows.length} 个有观看记录的视频`);
    
    let syncCount = 0;
    
    // 同步每个视频的统计数据到Redis
    for (const row of result.rows) {
      const { id, movie_id, view_count } = row;
      
      // 设置Redis中的统计数据
      const redisKey = `video_stats:${id}`;
      await redisClient.hSet(redisKey, {
        'views': view_count.toString(),
        'likes': '0',
        'favorites': '0',
        'updatedAt': Date.now().toString()
      });
      
      syncCount++;
      
      if (syncCount % 10 === 0) {
        console.log(`📈 已同步 ${syncCount}/${result.rows.length} 个视频...`);
      }
    }
    
    console.log(`🎉 同步完成！总共同步了 ${syncCount} 个视频的统计数据`);
    
    // 验证一些关键视频的数据
    const testVideoIds = ['19', '23', '27', '134', '150'];
    console.log('\n🔍 验证同步结果：');
    
    for (const id of testVideoIds) {
      const stats = await redisClient.hGetAll(`video_stats:${id}`);
      if (Object.keys(stats).length > 0) {
        console.log(`视频ID ${id}: views=${stats.views}, likes=${stats.likes}, favorites=${stats.favorites}`);
      } else {
        console.log(`视频ID ${id}: 没有数据`);
      }
    }
    
  } catch (error) {
    console.error('❌ 同步失败:', error);
  } finally {
    // 关闭连接
    await redisClient.quit();
    await pool.end();
    console.log('🔌 连接已关闭');
  }
}

// 运行同步
syncStatsToRedis(); 