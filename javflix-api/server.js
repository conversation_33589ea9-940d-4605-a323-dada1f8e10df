const app = require('./src/app');
const { connectDB } = require('./src/config/db');
const { createServer } = require('http');
const { initializeSocketService } = require('./dist/services/SocketService');

const PORT = process.env.PORT || 4000;

async function startServer() {
  try {
    console.log('🔄 正在初始化服务...');
    
    // 连接数据库并获取连接池
    const pool = await connectDB();
    
    // 将数据库连接池添加到app
    app.set('pool', pool);
    
    // 创建HTTP服务器
    const server = createServer(app);
    
    // 初始化Socket.IO服务
    const socketService = initializeSocketService(server);
    console.log('🔌 Socket.IO服务初始化完成');
    
    // 将socketService实例添加到app中，供其他模块使用
    app.set('socketService', socketService);
    
    // 启动服务器
    server.listen(PORT, () => {
      console.log(`🚀 服务器运行在端口 ${PORT}`);
      console.log(`📡 API地址: http://localhost:${PORT}`);
      console.log(`🔌 Socket.IO服务已启动`);
    });
    
  } catch (error) {
    console.error('❌ 服务器启动失败:', error);
    process.exit(1);
  }
}

startServer(); 