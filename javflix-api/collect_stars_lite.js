const axios = require('axios');
const { Pool } = require('pg');
const fs = require('fs');
const path = require('path');
const https = require('https');
const crypto = require('crypto');

// 数据库连接配置
const pool = new Pool({
  host: process.env.PGHOST || 'localhost',
  port: parseInt(process.env.PGPORT || '5432'),
  database: process.env.PGDATABASE || 'javflix',
  user: process.env.PGUSER || 'longgedemacminim4',
  password: process.env.PGPASSWORD || '',
});

// API配置
const JAVBUS_API_URL = 'http://localhost:3000/api';

// 图片保存路径 - 参照import-javbus-data.js
const IMAGE_BASE_DIR = path.join(__dirname, '../javflix/public/images/javbus');

/**
 * 获取哈希文件名 - 参照import-javbus-data.js
 */
function getHashedFileName(url) {
  const hash = crypto.createHash('md5').update(url).digest('hex');
  const ext = path.extname(url) || '.jpg'; // 默认为jpg
  return `${hash}${ext}`;
}

/**
 * 确保目录存在 - 参照import-javbus-data.js
 */
function ensureDirExists(dirPath) {
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true });
  }
}

/**
 * 下载图片并保存到本地 - 参照import-javbus-data.js
 */
async function downloadImage(url, type = 'actress') {
  if (!url) return null;
  
  console.log(`正在下载图片: ${url}`);
  const imageDirPath = path.join(IMAGE_BASE_DIR, type);
  ensureDirExists(imageDirPath);
  
  const hashedFileName = getHashedFileName(url);
  const filePath = path.join(imageDirPath, hashedFileName);
  const publicPath = `/images/javbus/${type}/${hashedFileName}`;
  
  // 如果文件已存在，直接返回路径
  if (fs.existsSync(filePath)) {
    console.log(`图片已存在，跳过下载: ${publicPath}`);
    return publicPath;
  }
  
  return new Promise((resolve, reject) => {
    const file = fs.createWriteStream(filePath);
    
    https.get(url, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Referer': 'https://www.javbus.com/'
      },
      timeout: 15000 // 15秒超时
    }, (response) => {
      if (response.statusCode !== 200) {
        file.close();
        fs.unlink(filePath, () => {}); // 删除可能部分下载的文件
        console.error(`下载失败，状态码: ${response.statusCode}`);
        resolve(null);
        return;
      }
      
      response.pipe(file);
      
      file.on('finish', () => {
        file.close();
        console.log(`图片下载成功: ${publicPath}`);
        resolve(publicPath);
      });
    }).on('error', (err) => {
      file.close();
      fs.unlink(filePath, () => {});
      console.error('下载图片出错:', err.message);
      resolve(null);
    });
  });
}

// 获取影片中的女优信息
async function getStarsFromMovie(movieId) {
  try {
    console.log(`正在获取影片 ${movieId} 的详情...`);
    const response = await axios.get(`${JAVBUS_API_URL}/movies/${movieId}`);
    const movieDetail = response.data;
    
    if (movieDetail.stars && movieDetail.stars.length > 0) {
      console.log(`影片 ${movieId} 包含 ${movieDetail.stars.length} 位女优`);
      return movieDetail.stars;
    } else {
      console.log(`影片 ${movieId} 没有女优信息`);
      return [];
    }
  } catch (error) {
    console.error(`获取影片 ${movieId} 详情失败:`, error.message);
    return [];
  }
}

// 获取女优详细信息
async function getStarDetail(starId) {
  try {
    console.log(`正在获取女优 ${starId} 的详细信息...`);
    const response = await axios.get(`${JAVBUS_API_URL}/stars/${starId}`);
    return response.data;
  } catch (error) {
    console.error(`获取女优 ${starId} 详情失败:`, error.message);
    return null;
  }
}

// 保存女优到数据库（下载图片并保存本地路径）
async function saveStar(client, starData) {
  try {
    // 检查女优是否已存在
    const existingQuery = 'SELECT id FROM stars WHERE star_id = $1';
    const existingResult = await client.query(existingQuery, [starData.id]);
    
    if (existingResult.rows.length > 0) {
      console.log(`女优 ${starData.name} (${starData.id}) 已存在，跳过`);
      return existingResult.rows[0].id;
    }
    
    // 下载女优图片 - 参照import-javbus-data.js
    let cachedImageUrl = null;
    if (starData.avatar) {
      cachedImageUrl = await downloadImage(starData.avatar, 'actress');
    }
    
    // 插入新女优（使用本地图片路径）
    const insertQuery = `
      INSERT INTO stars (star_id, name, image_url, cached_image_url, created_at, updated_at) 
      VALUES ($1, $2, $3, $4, NOW(), NOW()) 
      RETURNING id
    `;
    
    const insertValues = [
      starData.id,
      starData.name,
      starData.avatar, // 保留原始URL
      cachedImageUrl,  // 使用本地缓存路径
    ];
    
    const result = await client.query(insertQuery, insertValues);
    console.log(`✅ 成功保存女优: ${starData.name} (${starData.id}) - 本地图片: ${cachedImageUrl || '无'}`);
    return result.rows[0].id;
  } catch (error) {
    console.error(`保存女优失败: ${starData.name} (${starData.id})`, error);
    throw error;
  }
}

// 主要采集函数
async function collectStarsFromJavbus() {
  const client = await pool.connect();
  
  try {
    console.log('🚀 开始从JavBus采集女优数据（包含图片下载）...\n');
    
    // 确保图片目录存在
    ensureDirExists(path.join(IMAGE_BASE_DIR, 'actress'));
    
    const uniqueStars = new Map(); // 用于去重
    let totalMoviesProcessed = 0;
    let totalStarsFound = 0;
    let totalStarsSaved = 0;
    let totalImagesDownloaded = 0;
    
    // 处理前5页影片，每页最多处理10部影片
    for (let page = 1; page <= 5; page++) {
      try {
        console.log(`\n📖 正在处理第 ${page} 页影片...`);
        
        // 获取影片列表
        const moviesResponse = await axios.get(`${JAVBUS_API_URL}/movies?page=${page}&magnet=exist`);
        const movies = moviesResponse.data.movies.slice(0, 10); // 只处理前10部
        
        console.log(`第 ${page} 页包含 ${movies.length} 部影片`);
        
        // 处理每部影片
        for (const movie of movies) {
          totalMoviesProcessed++;
          
          // 获取影片中的女优
          const stars = await getStarsFromMovie(movie.id);
          totalStarsFound += stars.length;
          
          // 处理每位女优
          for (const star of stars) {
            // 去重检查
            if (!uniqueStars.has(star.id)) {
              uniqueStars.set(star.id, star);
              
              // 获取女优详细信息
              const starDetail = await getStarDetail(star.id);
              if (starDetail) {
                // 合并基本信息和详细信息
                const fullStarData = {
                  ...star,
                  ...starDetail
                };
                
                // 保存到数据库（包含图片下载）
                await saveStar(client, fullStarData);
                totalStarsSaved++;
                
                if (fullStarData.avatar) {
                  totalImagesDownloaded++;
                }
              }
              
              // 添加延迟避免请求过快
              await new Promise(resolve => setTimeout(resolve, 500));
            }
          }
          
          // 每处理5部影片休息一下
          if (totalMoviesProcessed % 5 === 0) {
            console.log(`\n⏱️  已处理 ${totalMoviesProcessed} 部影片，发现 ${totalStarsFound} 位女优，已保存 ${totalStarsSaved} 位，下载图片 ${totalImagesDownloaded} 张...`);
          }
        }
        
        // 每页之间稍作停顿
        await new Promise(resolve => setTimeout(resolve, 2000));
        
      } catch (error) {
        console.error(`处理第 ${page} 页时出错:`, error.message);
        continue; // 继续处理下一页
      }
    }
    
    // 输出最终统计
    console.log('\n🎉 女优数据采集完成!');
    console.log(`📊 统计信息:`);
    console.log(`   - 处理影片总数: ${totalMoviesProcessed}`);
    console.log(`   - 发现女优总数: ${totalStarsFound}`);
    console.log(`   - 去重后女优数: ${uniqueStars.size}`);
    console.log(`   - 成功保存女优: ${totalStarsSaved}`);
    console.log(`   - 下载图片总数: ${totalImagesDownloaded}`);
    
    // 验证数据库中的最终结果
    const finalCount = await client.query('SELECT COUNT(*) as count FROM stars');
    const withImagesCount = await client.query('SELECT COUNT(*) as count FROM stars WHERE cached_image_url IS NOT NULL');
    console.log(`   - 数据库中女优总数: ${finalCount.rows[0].count}`);
    console.log(`   - 有本地图片的女优: ${withImagesCount.rows[0].count}`);
    
  } catch (error) {
    console.error('采集过程中发生错误:', error);
    throw error;
  } finally {
    client.release();
  }
}

// 主函数
async function main() {
  try {
    await collectStarsFromJavbus();
  } catch (error) {
    console.error('💥 采集失败:', error);
    process.exit(1);
  } finally {
    await pool.end();
  }
}

// 执行主函数
main(); 