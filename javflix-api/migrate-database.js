const { Pool } = require('pg');
const fs = require('fs');
const path = require('path');

// 数据库连接配置
const pool = new Pool({
  host: process.env.PGHOST || 'localhost',
  port: parseInt(process.env.PGPORT || '5432'),
  database: process.env.PGDATABASE || 'javflix',
  user: process.env.PGUSER || 'longgedemacminim4',
  password: process.env.PGPASSWORD || '',
});

async function runMigration() {
  const client = await pool.connect();
  
  try {
    console.log('🔄 开始执行数据库迁移...');
    
    // 读取迁移脚本
    const migrationPath = path.join(__dirname, '../video-processor/migrations/add_download_progress_fields.sql');
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
    
    // 分割SQL语句（按分号分割）
    const statements = migrationSQL
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));
    
    console.log(`📝 找到 ${statements.length} 个SQL语句`);
    
    // 执行每个语句
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i];
      if (statement.trim()) {
        try {
          console.log(`⚡ 执行语句 ${i + 1}/${statements.length}: ${statement.substring(0, 50)}...`);
          await client.query(statement);
          console.log(`✅ 语句 ${i + 1} 执行成功`);
        } catch (error) {
          if (error.message.includes('already exists') || error.message.includes('duplicate column')) {
            console.log(`⚠️  语句 ${i + 1} 跳过（字段已存在）: ${error.message}`);
          } else {
            console.error(`❌ 语句 ${i + 1} 执行失败:`, error.message);
            throw error;
          }
        }
      }
    }
    
    // 验证迁移结果
    console.log('🔍 验证迁移结果...');
    const result = await client.query(`
      SELECT column_name, data_type 
      FROM information_schema.columns 
      WHERE table_name = 'video_processing_tasks' 
      AND column_name IN ('completed_bytes', 'total_bytes', 'download_speed')
      ORDER BY column_name
    `);
    
    console.log('📊 新增字段验证:');
    result.rows.forEach(row => {
      console.log(`  ✅ ${row.column_name}: ${row.data_type}`);
    });
    
    // 检查是否有失败的任务记录
    const taskCheck = await client.query(`
      SELECT task_uuid, status, progress, created_at 
      FROM video_processing_tasks 
      WHERE task_uuid = 'e9ed4c20-a536-4d59-a456-c77b2e45a23f'
    `);
    
    if (taskCheck.rows.length > 0) {
      console.log('🔍 找到问题任务记录:');
      console.log(taskCheck.rows[0]);
    } else {
      console.log('⚠️  未找到任务记录: e9ed4c20-a536-4d59-a456-c77b2e45a23f');
      
      // 创建一个测试任务记录
      console.log('🔧 创建测试任务记录...');
      await client.query(`
        INSERT INTO video_processing_tasks (
          task_uuid, movie_id, status, progress, current_step,
          watermark_text, watermark_position, quality, metadata,
          completed_bytes, total_bytes, download_speed
        ) VALUES (
          'e9ed4c20-a536-4d59-a456-c77b2e45a23f', 
          1, 
          'downloading', 
          0, 
          'initializing',
          'JAVFLIX.TV',
          'bottom-right',
          'high',
          '{}',
          0,
          0,
          0.0
        )
        ON CONFLICT (task_uuid) DO UPDATE SET
          status = EXCLUDED.status,
          updated_at = CURRENT_TIMESTAMP
      `);
      console.log('✅ 测试任务记录已创建');
    }
    
    console.log('🎉 数据库迁移完成！');
    
  } catch (error) {
    console.error('❌ 迁移失败:', error);
    throw error;
  } finally {
    client.release();
    await pool.end();
  }
}

// 执行迁移
runMigration()
  .then(() => {
    console.log('✅ 迁移脚本执行完成');
    process.exit(0);
  })
  .catch(error => {
    console.error('❌ 迁移脚本执行失败:', error);
    process.exit(1);
  });
