// 视频任务服务 - 管理视频处理任务
const { v4: uuidv4 } = require('uuid');
const { pool } = require('../config/db');

class VideoTaskService {
  constructor() {
    // 模拟Go服务器URL（暂时使用模拟）
    this.goServerUrl = process.env.GO_VIDEO_SERVER_URL || 'http://localhost:8080';
    this.serverToken = process.env.GO_SERVER_TOKEN || 'mock-token';
  }

  /**
   * 创建视频处理任务
   */
  async createVideoTask(movieId, config) {
    const client = await pool.connect();
    
    try {
      await client.query('BEGIN');
      
      // 1. 创建任务记录
      const taskUuid = uuidv4();
      const taskResult = await client.query(`
        INSERT INTO video_processing_tasks (
          task_uuid, movie_id, task_type, status, priority,
          magnet_link, watermark_config, slice_config, quality_config
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
        RETURNING *
      `, [
        taskUuid, movieId, 'video_process', 'pending', config.priority || 5,
        config.magnetLink, JSON.stringify(config.watermark),
        JSON.stringify(config.slice), JSON.stringify(config.quality || {})
      ]);

      const task = taskResult.rows[0];

      // 2. 模拟发送任务到Go服务器（暂时跳过实际发送）
      console.log(`📝 创建视频处理任务: ${taskUuid} for movie ${movieId}`);
      console.log(`🔗 磁力链接: ${config.magnetLink}`);
      console.log(`⚙️ 配置: ${JSON.stringify(config, null, 2)}`);
      
      // 模拟任务开始处理
      setTimeout(async () => {
        await this.simulateTaskProgress(taskUuid);
      }, 1000);
      
      await client.query('COMMIT');
      return task;
      
    } catch (error) {
      await client.query('ROLLBACK');
      throw error;
    } finally {
      client.release();
    }
  }

  /**
   * 模拟任务处理进度（用于测试）
   */
  async simulateTaskProgress(taskUuid) {
    const progressSteps = [
      { progress: 10, status: 'downloading', message: '开始下载视频...' },
      { progress: 30, status: 'downloading', message: '下载进行中...' },
      { progress: 50, status: 'processing', message: '添加水印...' },
      { progress: 70, status: 'processing', message: 'HLS切片处理...' },
      { progress: 90, status: 'uploading', message: '上传到CDN...' },
      { progress: 100, status: 'completed', message: '处理完成' }
    ];

    for (let i = 0; i < progressSteps.length; i++) {
      const step = progressSteps[i];
      
      // 等待一段时间模拟处理
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // 更新任务进度
      await pool.query(`
        UPDATE video_processing_tasks 
        SET progress = $1, status = $2, updated_at = NOW()
        WHERE task_uuid = $3
      `, [step.progress, step.status, taskUuid]);

      // 发送WebSocket进度更新（暂时注释掉）
      // const { getIO } = require('./SocketService');
      // const io = getIO();
      // if (io) {
      //   io.emit('video-task-progress', {
      //     taskUuid,
      //     progress: step.progress,
      //     status: step.status,
      //     message: step.message,
      //     timestamp: new Date().toISOString()
      //   });
      // }

      console.log(`📊 任务 ${taskUuid} 进度: ${step.progress}% - ${step.message}`);
    }

    // 模拟处理完成，生成假的视频URL
    const mockVideoUrls = {
      qualities: [
        {
          resolution: "1080p",
          url: "https://3b6dc8a552f1f639304d79328d3a3166.r2.cloudflarestorage.com/javflix/videos/1080p/playlist.m3u8",
          bitrate: "5000k"
        },
        {
          resolution: "720p", 
          url: "https://3b6dc8a552f1f639304d79328d3a3166.r2.cloudflarestorage.com/javflix/videos/720p/playlist.m3u8",
          bitrate: "3000k"
        },
        {
          resolution: "480p",
          url: "https://3b6dc8a552f1f639304d79328d3a3166.r2.cloudflarestorage.com/javflix/videos/480p/playlist.m3u8", 
          bitrate: "1500k"
        }
      ],
      thumbnails: [
        "https://3b6dc8a552f1f639304d79328d3a3166.r2.cloudflarestorage.com/javflix/thumbnails/thumb_001.jpg",
        "https://3b6dc8a552f1f639304d79328d3a3166.r2.cloudflarestorage.com/javflix/thumbnails/thumb_002.jpg"
      ]
    };

    // 获取movie_id用于回调
    const taskResult = await pool.query(
      'SELECT movie_id FROM video_processing_tasks WHERE task_uuid = $1',
      [taskUuid]
    );

    if (taskResult.rows.length > 0) {
      const movieId = taskResult.rows[0].movie_id;
      
      // 调用处理完成回调
      const MovieProcessingService = require('./MovieProcessingService');
      const movieProcessingService = new MovieProcessingService();
      
      await movieProcessingService.onProcessingComplete(taskUuid, {
        success: true,
        videoUrls: mockVideoUrls
      });

      console.log(`✅ 任务 ${taskUuid} 处理完成，影片 ${movieId} 状态已更新为 processed`);
    }
  }

  /**
   * 获取任务状态
   */
  async getTaskStatus(taskUuid) {
    const result = await pool.query(
      'SELECT * FROM video_processing_tasks WHERE task_uuid = $1',
      [taskUuid]
    );
    
    return result.rows[0] || null;
  }

  /**
   * 更新任务进度
   */
  async updateTaskProgress(taskUuid, progress, status, message) {
    await pool.query(`
      UPDATE video_processing_tasks 
      SET progress = $1, status = $2, updated_at = NOW()
      WHERE task_uuid = $3
    `, [progress, status, taskUuid]);

    // 发送WebSocket通知（暂时注释掉）
    // const { getIO } = require('./SocketService');
    // const io = getIO();
    // if (io) {
    //   io.emit('video-task-progress', {
    //     taskUuid,
    //     progress,
    //     status,
    //     message,
    //     timestamp: new Date().toISOString()
    //   });
    // }
  }
}

module.exports = VideoTaskService;