// 影片处理服务 - 管理影片从采集到发布的完整流程
const { pool } = require('../config/db');
const VideoTaskService = require('./VideoTaskService');
// 暂时注释掉SocketService，稍后修复
// const { getIO } = require('./SocketService');

class MovieProcessingService {
  constructor() {
    this.videoTaskService = new VideoTaskService();
  }

  /**
   * 获取待处理影片列表
   */
  async getPendingMovies(page = 1, limit = 20, status = null) {
    const offset = (page - 1) * limit;
    
    let whereClause = "WHERE m.status IN ('draft', 'processing', 'processed')";
    let params = [limit, offset];
    
    if (status) {
      whereClause = "WHERE m.status = $3";
      params.push(status);
    }

    const query = `
      SELECT 
        m.id,
        m.movie_id,
        m.title,
        m.image_url,
        m.status,
        m.processing_priority,
        m.created_at,
        m.processing_started_at,
        m.processing_completed_at,
        vpt.task_uuid,
        vpt.progress,
        vpt.status as task_status,
        vpt.error_message,
        COUNT(mag.id) as magnet_count
      FROM movies m
      LEFT JOIN video_processing_tasks vpt ON m.id = vpt.movie_id
      LEFT JOIN magnets mag ON m.id = mag.movie_id
      ${whereClause}
      GROUP BY m.id, vpt.task_uuid, vpt.progress, vpt.status, vpt.error_message
      ORDER BY m.processing_priority DESC, m.created_at ASC
      LIMIT $1 OFFSET $2
    `;

    const result = await pool.query(query, params);
    
    // 获取总数
    const countQuery = `
      SELECT COUNT(DISTINCT m.id) as total
      FROM movies m
      ${whereClause.replace(/\$1|\$2/g, '').replace('$3', '$1')}
    `;
    const countParams = status ? [status] : [];
    const countResult = await pool.query(countQuery, countParams);

    return {
      movies: result.rows,
      pagination: {
        total: parseInt(countResult.rows[0].total),
        page,
        limit,
        pages: Math.ceil(countResult.rows[0].total / limit)
      }
    };
  }

  /**
   * 开始处理影片
   */
  async startProcessing(movieIds, config = {}) {
    const client = await pool.connect();
    
    try {
      await client.query('BEGIN');
      
      const results = [];
      
      for (const movieId of movieIds) {
        // 1. 检查影片状态
        const movieResult = await client.query(
          'SELECT id, movie_id, title, status FROM movies WHERE id = $1',
          [movieId]
        );
        
        if (movieResult.rows.length === 0) {
          results.push({ movieId, success: false, error: '影片不存在' });
          continue;
        }
        
        const movie = movieResult.rows[0];
        
        if (movie.status !== 'draft' && movie.status !== 'failed') {
          results.push({ 
            movieId, 
            success: false, 
            error: `影片状态为${movie.status}，无法处理` 
          });
          continue;
        }

        // 2. 获取磁力链接
        const magnetResult = await client.query(
          'SELECT link FROM magnets WHERE movie_id = $1 ORDER BY is_hd DESC, size DESC LIMIT 1',
          [movieId]
        );
        
        if (magnetResult.rows.length === 0) {
          results.push({ movieId, success: false, error: '没有找到磁力链接' });
          continue;
        }

        // 3. 更新影片状态为processing
        await client.query(
          'UPDATE movies SET status = $1, processing_priority = $2 WHERE id = $3',
          ['processing', config.priority || 5, movieId]
        );

        // 4. 创建视频处理任务
        try {
          const taskConfig = {
            magnetLink: magnetResult.rows[0].link,
            priority: config.priority || 5,
            watermark: config.watermark || {
              enabled: true,
              text: "JAVFLIX.TV",
              position: "bottom-right",
              opacity: 0.8
            },
            slice: config.slice || {
              qualities: [
                { resolution: "1080p", bitrate: "5000k" },
                { resolution: "720p", bitrate: "3000k" },
                { resolution: "480p", bitrate: "1500k" }
              ],
              segmentDuration: 10,
              generateThumbnails: true
            }
          };

          const task = await this.videoTaskService.createVideoTask(movieId, taskConfig);
          
          results.push({
            movieId,
            success: true,
            taskUuid: task.task_uuid,
            message: '处理任务已创建'
          });

          // 发送WebSocket通知
          this.notifyProcessingStarted(movie, task.task_uuid);
          
        } catch (error) {
          // 如果创建任务失败，回滚影片状态
          await client.query(
            'UPDATE movies SET status = $1 WHERE id = $2',
            ['failed', movieId]
          );
          
          results.push({
            movieId,
            success: false,
            error: `创建处理任务失败: ${error.message}`
          });
        }
      }
      
      await client.query('COMMIT');
      return results;
      
    } catch (error) {
      await client.query('ROLLBACK');
      throw error;
    } finally {
      client.release();
    }
  }

  /**
   * 处理完成回调
   */
  async onProcessingComplete(taskUuid, result) {
    const client = await pool.connect();
    
    try {
      await client.query('BEGIN');
      
      // 1. 获取任务信息
      const taskResult = await client.query(
        'SELECT movie_id FROM video_processing_tasks WHERE task_uuid = $1',
        [taskUuid]
      );
      
      if (taskResult.rows.length === 0) {
        throw new Error('任务不存在');
      }
      
      const movieId = taskResult.rows[0].movie_id;
      
      if (result.success) {
        // 2. 更新影片状态和视频链接
        await client.query(`
          UPDATE movies 
          SET status = $1, video_urls = $2
          WHERE id = $3
        `, ['processed', JSON.stringify(result.videoUrls), movieId]);
        
        // 3. 获取影片信息用于通知
        const movieResult = await client.query(
          'SELECT id, movie_id, title FROM movies WHERE id = $1',
          [movieId]
        );
        
        this.notifyProcessingCompleted(movieResult.rows[0], result);
        
      } else {
        // 处理失败
        await client.query(
          'UPDATE movies SET status = $1 WHERE id = $2',
          ['failed', movieId]
        );
        
        this.notifyProcessingFailed(movieId, result.error);
      }
      
      await client.query('COMMIT');
      
    } catch (error) {
      await client.query('ROLLBACK');
      throw error;
    } finally {
      client.release();
    }
  }

  /**
   * 发布影片
   */
  async publishMovies(movieIds) {
    const client = await pool.connect();
    
    try {
      await client.query('BEGIN');
      
      const results = [];
      
      for (const movieId of movieIds) {
        const result = await client.query(`
          UPDATE movies 
          SET status = 'published'
          WHERE id = $1 AND status = 'processed'
          RETURNING id, movie_id, title
        `, [movieId]);
        
        if (result.rows.length > 0) {
          results.push({
            movieId,
            success: true,
            movie: result.rows[0]
          });
          
          this.notifyMoviePublished(result.rows[0]);
        } else {
          results.push({
            movieId,
            success: false,
            error: '影片状态不正确或不存在'
          });
        }
      }
      
      await client.query('COMMIT');
      return results;
      
    } catch (error) {
      await client.query('ROLLBACK');
      throw error;
    } finally {
      client.release();
    }
  }

  /**
   * 获取处理统计
   */
  async getProcessingStats() {
    const query = `
      SELECT 
        status,
        COUNT(*) as count
      FROM movies
      GROUP BY status
    `;
    
    const result = await pool.query(query);
    
    const stats = {
      draft: 0,
      processing: 0,
      processed: 0,
      published: 0,
      failed: 0
    };
    
    result.rows.forEach(row => {
      stats[row.status] = parseInt(row.count);
    });
    
    return stats;
  }

  // WebSocket通知方法
  notifyProcessingStarted(movie, taskUuid) {
    // 暂时注释掉WebSocket通知，稍后修复
    console.log(`🔔 影片处理开始: ${movie.title} (${taskUuid})`);
    // const io = getIO();
    // io.emit('movie-processing-started', {
    //   movieId: movie.id,
    //   movieCode: movie.movie_id,
    //   title: movie.title,
    //   taskUuid,
    //   timestamp: new Date().toISOString()
    // });
  }

  notifyProcessingCompleted(movie, result) {
    console.log(`✅ 影片处理完成: ${movie.title}`);
    // const io = getIO();
    // io.emit('movie-processing-completed', {
    //   movieId: movie.id,
    //   movieCode: movie.movie_id,
    //   title: movie.title,
    //   videoUrls: result.videoUrls,
    //   timestamp: new Date().toISOString()
    // });
  }

  notifyProcessingFailed(movieId, error) {
    console.log(`❌ 影片处理失败: ${movieId} - ${error}`);
    // const io = getIO();
    // io.emit('movie-processing-failed', {
    //   movieId,
    //   error,
    //   timestamp: new Date().toISOString()
    // });
  }

  notifyMoviePublished(movie) {
    console.log(`🚀 影片已发布: ${movie.title}`);
    // const io = getIO();
    // io.emit('movie-published', {
    //   movieId: movie.id,
    //   movieCode: movie.movie_id,
    //   title: movie.title,
    //   timestamp: new Date().toISOString()
    // });
  }

  /**
   * 开始真实的视频处理（发送到Go服务器）
   */
  async startRealProcessing(movieIds, config = {}) {
    const client = await pool.connect();

    try {
      await client.query('BEGIN');

      const results = [];

      for (const movieId of movieIds) {
        // 1. 检查影片状态
        const movieResult = await client.query(
          'SELECT id, movie_id, title, status FROM movies WHERE id = $1',
          [movieId]
        );

        if (movieResult.rows.length === 0) {
          results.push({ movieId, success: false, error: '影片不存在' });
          continue;
        }

        const movie = movieResult.rows[0];

        if (movie.status !== 'draft' && movie.status !== 'failed') {
          results.push({
            movieId,
            success: false,
            error: `影片状态为${movie.status}，无法处理`
          });
          continue;
        }

        // 2. 获取磁力链接
        const magnetResult = await client.query(
          'SELECT link FROM magnets WHERE movie_id = $1 ORDER BY is_hd DESC, size DESC LIMIT 1',
          [movieId]
        );

        if (magnetResult.rows.length === 0) {
          results.push({ movieId, success: false, error: '没有找到磁力链接' });
          continue;
        }

        // 3. 生成任务UUID
        const { v4: uuidv4 } = require('uuid');
        const taskUuid = uuidv4();

        // 4. 创建视频处理任务记录
        await client.query(`
          INSERT INTO video_processing_tasks (
            task_uuid, movie_id, task_type, status, progress, priority,
            magnet_link, watermark_config, slice_config
          ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
        `, [
          taskUuid, movieId, 'video_processing', 'pending', 0, config.priority || 5,
          magnetResult.rows[0].link,
          JSON.stringify(config.watermark || { enabled: true, text: "JAVFLIX.TV" }),
          JSON.stringify(config.slice || { qualities: ["1080p", "720p"] })
        ]);

        // 5. 更新影片状态为processing
        await client.query(
          'UPDATE movies SET status = $1, processing_started_at = NOW(), processing_priority = $2 WHERE id = $3',
          ['processing', config.priority || 5, movieId]
        );

        // 6. 发送任务到Go服务器
        try {
          await this.sendTaskToGoServer(taskUuid, movie, magnetResult.rows[0].link, config);

          results.push({
            movieId,
            success: true,
            taskUuid,
            message: '真实处理任务已发送到Go服务器'
          });

          console.log(`🚀 真实处理任务已发送: ${movie.title} (${taskUuid})`);

        } catch (error) {
          // 如果发送失败，回滚状态
          await client.query(
            'UPDATE movies SET status = $1 WHERE id = $2',
            ['failed', movieId]
          );

          await client.query(
            'UPDATE video_processing_tasks SET status = $1, error_message = $2 WHERE task_uuid = $3',
            ['failed', error.message, taskUuid]
          );

          results.push({
            movieId,
            success: false,
            error: `发送到Go服务器失败: ${error.message}`
          });
        }
      }

      await client.query('COMMIT');
      return results;

    } catch (error) {
      await client.query('ROLLBACK');
      throw error;
    } finally {
      client.release();
    }
  }

  /**
   * 发送任务到Go服务器
   */
  async sendTaskToGoServer(taskUuid, movie, magnetLink, config) {
    const axios = require('axios');

    const payload = {
      taskUuid,
      movieId: movie.id,
      magnetLink,
      watermarkConfig: config.watermark || {
        enabled: true,
        text: "JAVFLIX.TV",
        position: "bottom-right",
        opacity: 0.8
      },
      sliceConfig: config.slice || {
        qualities: [
          { resolution: "1080p", bitrate: "5000k", scale: "1920:1080" },
          { resolution: "720p", bitrate: "3000k", scale: "1280:720" }
        ],
        segmentDuration: 10,
        generateThumbnails: true
      },
      uploadConfig: {
        provider: "cloudflare_r2",
        bucket: "javflix-videos",
        cdnDomain: "https://cdn.javflix.tv"
      },
      callbackUrl: `${process.env.API_BASE_URL || 'http://localhost:4000'}/api/movie-processing/callback`
    };

    const goServerUrl = process.env.GO_VIDEO_SERVER_URL || 'http://localhost:8080';
    const goServerToken = process.env.GO_SERVER_TOKEN || 'go-video-processor-token-123';

    console.log(`📤 发送任务到Go服务器: ${goServerUrl}/api/v1/process-video`);

    try {
      const response = await axios.post(`${goServerUrl}/api/v1/process-video`, payload, {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${goServerToken}`,
          'X-Request-ID': taskUuid
        },
        timeout: 30000
      });

      console.log('✅ 任务已发送到Go服务器:', response.data);
      return response.data;
    } catch (error) {
      if (error.response) {
        throw new Error(`Go服务器响应错误: ${error.response.status} - ${error.response.data}`);
      } else if (error.request) {
        throw new Error(`无法连接到Go服务器: ${goServerUrl}`);
      } else {
        throw new Error(`发送请求失败: ${error.message}`);
      }
    }
  }

  /**
   * 更新任务进度
   */
  async updateTaskProgress(taskUuid, progress, status, message, completed = null, total = null, downloadSpeed = null) {
    const client = await pool.connect();

    try {
      // 更新任务进度，包括completed、total和downloadSpeed字段（如果提供）
      let updateQuery = `
        UPDATE video_processing_tasks
        SET progress = $1, status = $2, updated_at = CURRENT_TIMESTAMP
      `;
      let params = [progress, status];

      // 如果提供了completed和total，也更新这些字段
      if (completed !== null && total !== null) {
        updateQuery += `, completed_bytes = $${params.length + 1}, total_bytes = $${params.length + 2}`;
        params.push(completed, total);
      }

      // 如果提供了下载速度，也更新该字段
      if (downloadSpeed !== null) {
        updateQuery += `, download_speed = $${params.length + 1}`;
        params.push(downloadSpeed);
      }

      updateQuery += ` WHERE task_uuid = $${params.length + 1}`;
      params.push(taskUuid);

      await client.query(updateQuery, params);

      // 获取任务信息
      const taskResult = await client.query(
        'SELECT movie_id FROM video_processing_tasks WHERE task_uuid = $1',
        [taskUuid]
      );

      if (taskResult.rows.length > 0) {
        const movieId = taskResult.rows[0].movie_id;

        // 获取影片信息
        const movieResult = await client.query(
          'SELECT id, movie_id, title FROM movies WHERE id = $1',
          [movieId]
        );

        if (movieResult.rows.length > 0) {
          const movie = movieResult.rows[0];
          let logMessage = `📊 进度更新: ${movie.title} - ${progress}% (${status}) - ${message}`;

          if (completed !== null && total !== null) {
            const completedMB = (completed / 1024 / 1024).toFixed(1);
            const totalMB = (total / 1024 / 1024).toFixed(1);
            logMessage += ` [${completedMB}MB/${totalMB}MB]`;
          }

          if (downloadSpeed !== null) {
            logMessage += ` [${downloadSpeed.toFixed(1)}MB/s]`;
          }

          console.log(logMessage);

          // 发送WebSocket通知到前端
          this.notifyProgressUpdate(movie, taskUuid, progress, status, message, completed, total, downloadSpeed);
        }
      }

    } finally {
      client.release();
    }
  }

  /**
   * 发送进度更新通知到前端
   */
  notifyProgressUpdate(movie, taskUuid, progress, status, message, completed = null, total = null, downloadSpeed = null) {
    // 这里可以集成WebSocket或Server-Sent Events来实时推送到前端
    // 暂时使用console.log记录
    const progressData = {
      taskUuid,
      movieTitle: movie.title,
      progress,
      status,
      message,
      completed,
      total,
      downloadSpeed,
      timestamp: new Date().toISOString()
    };

    console.log('📡 发送进度更新通知:', progressData);

    // TODO: 实现WebSocket推送到前端
    // if (this.wsServer) {
    //   this.wsServer.broadcast('video-task-progress', progressData);
    // }
  }
}

module.exports = MovieProcessingService;
