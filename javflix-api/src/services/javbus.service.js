const axios = require('axios');
const cheerio = require('cheerio');

// JavBus网站基础URL
const BASE_URL = 'https://www.javbus.com';

// JavBus服务
const javbusService = {
  /**
   * 获取影片列表
   * @param {number} page - 页码
   * @param {string} magnet - 过滤条件，'all'或'exist'
   * @param {string} type - 影片类型，'normal'或'uncensored'
   * @returns {Promise} - 返回影片列表和分页信息
   */
  getMovies: async (page = 1, magnet = 'all', type = 'normal') => {
    try {
      // 根据影片类型选择URL
      let url = BASE_URL;
      if (type === 'uncensored') {
        url = `${BASE_URL}/uncensored/page/${page}`;
      } else {
        url = `${BASE_URL}/page/${page}`;
      }
      
      // 发送请求
      const response = await axios.get(url, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
      });
      
      // 解析HTML
      const $ = cheerio.load(response.data);
      const movies = [];
      
      // 提取电影信息
      $('.movie-box').each((i, element) => {
        const $element = $(element);
        const id = $element.find('date').text().trim();
        const title = $element.find('img').attr('title');
        const img = $element.find('img').attr('src');
        const date = $element.find('date:eq(1)').text().trim();
        
        // 添加到列表
        movies.push({
          id,
          title,
          img,
          date,
          tags: []
        });
      });
      
      // 提取分页信息
      const pagination = {
        currentPage: parseInt(page),
        pages: [],
        hasNextPage: false,
        hasPrevPage: page > 1
      };
      
      $('.pagination li').each((i, element) => {
        const $element = $(element);
        const pageText = $element.text().trim();
        
        if (!isNaN(parseInt(pageText))) {
          pagination.pages.push(parseInt(pageText));
        }
        
        if (pageText === '>') {
          pagination.hasNextPage = true;
          pagination.nextPage = pagination.currentPage + 1;
        }
      });
      
      return {
        movies,
        pagination
      };
    } catch (error) {
      console.error('获取影片列表失败:', error);
      throw error;
    }
  },
  
  /**
   * 搜索影片
   * @param {string} keyword - 搜索关键词
   * @param {number} page - 页码
   * @param {string} magnet - 过滤条件，'all'或'exist'
   * @param {string} type - 影片类型，'normal'或'uncensored'
   * @returns {Promise} - 返回搜索结果和分页信息
   */
  searchMovies: async (keyword, page = 1, magnet = 'all', type = 'normal') => {
    try {
      // 构建搜索URL
      let url = `${BASE_URL}/search/${encodeURIComponent(keyword)}/page/${page}`;
      if (type === 'uncensored') {
        url = `${BASE_URL}/uncensored/search/${encodeURIComponent(keyword)}/page/${page}`;
      }
      
      // 发送请求
      const response = await axios.get(url, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
      });
      
      // 解析HTML
      const $ = cheerio.load(response.data);
      const movies = [];
      
      // 提取电影信息
      $('.movie-box').each((i, element) => {
        const $element = $(element);
        const id = $element.find('date').text().trim();
        const title = $element.find('img').attr('title');
        const img = $element.find('img').attr('src');
        const date = $element.find('date:eq(1)').text().trim();
        
        // 添加到列表
        movies.push({
          id,
          title,
          img,
          date,
          tags: []
        });
      });
      
      // 提取分页信息
      const pagination = {
        currentPage: parseInt(page),
        pages: [],
        hasNextPage: false,
        hasPrevPage: page > 1
      };
      
      $('.pagination li').each((i, element) => {
        const $element = $(element);
        const pageText = $element.text().trim();
        
        if (!isNaN(parseInt(pageText))) {
          pagination.pages.push(parseInt(pageText));
        }
        
        if (pageText === '>') {
          pagination.hasNextPage = true;
          pagination.nextPage = pagination.currentPage + 1;
        }
      });
      
      return {
        movies,
        pagination
      };
    } catch (error) {
      console.error('搜索影片失败:', error);
      throw error;
    }
  },
  
  /**
   * 获取影片详情
   * @param {string} movieId - 影片ID
   * @returns {Promise} - 返回影片详情
   */
  getMovieDetail: async (movieId) => {
    try {
      // 构建URL
      const url = `${BASE_URL}/${movieId}`;
      
      // 发送请求
      const response = await axios.get(url, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
      });
      
      // 解析HTML
      const $ = cheerio.load(response.data);
      
      // 提取影片详情
      const title = $('h3').text().trim();
      const img = $('.bigImage').attr('href');
      const id = $('.info p:contains("識別碼")').next().text().trim();
      const date = $('.info p:contains("發行日期")').next().text().trim();
      const videoLength = $('.info p:contains("長度")').next().text().trim().replace('分鐘', '');
      
      // 提取导演、制作商、发行商、系列
      const director = {
        name: $('.info p:contains("導演")').next().find('a').text().trim() || null,
        id: $('.info p:contains("導演")').next().find('a').attr('href')?.split('/').pop() || null
      };
      
      const producer = {
        name: $('.info p:contains("製作商")').next().find('a').text().trim() || null,
        id: $('.info p:contains("製作商")').next().find('a').attr('href')?.split('/').pop() || null
      };
      
      const publisher = {
        name: $('.info p:contains("發行商")').next().find('a').text().trim() || null,
        id: $('.info p:contains("發行商")').next().find('a').attr('href')?.split('/').pop() || null
      };
      
      const series = {
        name: $('.info p:contains("系列")').next().find('a').text().trim() || null,
        id: $('.info p:contains("系列")').next().find('a').attr('href')?.split('/').pop() || null
      };
      
      // 提取类别
      const genres = [];
      $('.info p:contains("類別")').next().find('a').each((i, element) => {
        const $element = $(element);
        genres.push({
          name: $element.text().trim(),
          id: $element.attr('href')?.split('/').pop() || null
        });
      });
      
      // 提取演员
      const stars = [];
      $('.star-box').each((i, element) => {
        const $element = $(element);
        stars.push({
          name: $element.find('img').attr('title') || $element.find('.star-name').text().trim(),
          id: $element.find('a').attr('href')?.split('/').pop() || null,
          avatar: $element.find('img').attr('src')
        });
      });
      
      // 提取样品图片
      const samples = [];
      $('#sample-waterfall .sample-box').each((i, element) => {
        const $element = $(element);
        samples.push({
          thumbnail: $element.find('img').attr('src'),
          src: $element.attr('href'),
          alt: $element.find('img').attr('title') || ''
        });
      });
      
      // 提取gid和uc (用于获取磁力链接)
      let gid = null;
      let uc = null;
      
      // 尝试从脚本中提取
      $('script').each((i, element) => {
        const script = $(element).html();
        if (script.includes('gid =')) {
          const gidMatch = script.match(/gid\s*=\s*(\d+)/);
          const ucMatch = script.match(/uc\s*=\s*(\d+)/);
          
          if (gidMatch && gidMatch[1]) gid = gidMatch[1];
          if (ucMatch && ucMatch[1]) uc = ucMatch[1];
        }
      });
      
      return {
        id,
        title,
        img,
        date,
        videoLength,
        director,
        producer,
        publisher,
        series,
        genres,
        stars,
        samples,
        gid,
        uc
      };
    } catch (error) {
      console.error('获取影片详情失败:', error);
      throw error;
    }
  },
  
  /**
   * 获取影片磁力链接
   * @param {string} movieId - 影片ID
   * @param {Object} params - 参数，包含gid和uc
   * @returns {Promise} - 返回磁力链接列表
   */
  getMovieMagnets: async (movieId, params) => {
    try {
      const { gid, uc } = params;
      
      if (!gid || !uc) {
        throw new Error('缺少必要参数gid或uc');
      }
      
      // 构建URL
      const url = `${BASE_URL}/ajax/uncledatoolsbyajax.php?gid=${gid}&lang=zh&uc=${uc}`;
      
      // 发送请求
      const response = await axios.get(url, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
          'Referer': `${BASE_URL}/${movieId}`
        }
      });
      
      // 解析HTML
      const $ = cheerio.load(response.data);
      const magnets = [];
      
      // 提取磁力链接
      $('tr').each((i, element) => {
        // 跳过表头
        if (i === 0) return;
        
        const $element = $(element);
        const title = $element.find('a.magnet-name').text().trim();
        const link = $element.find('a.magnet-download').attr('href');
        const size = $element.find('a.magnet-name+td').text().trim();
        const shareDate = $element.find('a.magnet-name+td+td').text().trim();
        const isHD = title.includes('HD') || title.includes('1080') || title.includes('720');
        const hasSubtitle = title.includes('字幕') || title.includes('中文');
        
        // 从磁力链接中提取hash
        let hash = null;
        if (link) {
          const hashMatch = link.match(/magnet:\?xt=urn:btih:([a-zA-Z0-9]+)/);
          if (hashMatch && hashMatch[1]) {
            hash = hashMatch[1].toUpperCase();
          }
        }
        
        if (link) {
          magnets.push({
            title,
            link,
            size,
            shareDate,
            isHD,
            hasSubtitle,
            id: hash
          });
        }
      });
      
      return magnets;
    } catch (error) {
      console.error('获取影片磁力链接失败:', error);
      throw error;
    }
  },
  
  /**
   * 获取演员详情
   * @param {string} starId - 演员ID
   * @returns {Promise} - 返回演员详情
   */
  getStarDetail: async (starId) => {
    try {
      // 构建URL
      const url = `${BASE_URL}/star/${starId}`;
      
      // 发送请求
      const response = await axios.get(url, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
      });
      
      // 解析HTML
      const $ = cheerio.load(response.data);
      
      // 提取演员信息
      const name = $('.avatar-box .photo-info .pb10').text().trim();
      const avatar = $('.avatar-box .photo-frame img').attr('src');
      
      // 提取个人资料
      const birthday = $('.avatar-box .photo-info p:contains("生日")').text().replace('生日:', '').trim() || null;
      const height = $('.avatar-box .photo-info p:contains("身高")').text().replace('身高:', '').trim() || null;
      const bust = $('.avatar-box .photo-info p:contains("胸圍")').text().replace('胸圍:', '').trim() || null;
      const waistline = $('.avatar-box .photo-info p:contains("腰圍")').text().replace('腰圍:', '').trim() || null;
      const hipline = $('.avatar-box .photo-info p:contains("臀圍")').text().replace('臀圍:', '').trim() || null;
      const birthplace = $('.avatar-box .photo-info p:contains("出生地")').text().replace('出生地:', '').trim() || null;
      const hobby = $('.avatar-box .photo-info p:contains("愛好")').text().replace('愛好:', '').trim() || null;
      
      // 计算年龄
      let age = null;
      if (birthday) {
        const birthYear = new Date(birthday).getFullYear();
        const currentYear = new Date().getFullYear();
        age = currentYear - birthYear;
      }
      
      return {
        id: starId,
        name,
        avatar,
        birthday,
        age,
        height,
        bust,
        waistline,
        hipline,
        birthplace,
        hobby
      };
    } catch (error) {
      console.error('获取演员详情失败:', error);
      throw error;
    }
  }
};

module.exports = javbusService; 