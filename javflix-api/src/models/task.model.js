const mongoose = require('mongoose');

const taskSchema = new mongoose.Schema({
  taskType: {
    type: String,
    enum: ['javbus_crawl', 'video_process', 'thumbnail_generation', 'metadata_update'],
    required: [true, '任务类型不能为空']
  },
  status: {
    type: String,
    enum: ['pending', 'running', 'completed', 'failed', 'canceled'],
    default: 'pending'
  },
  progress: {
    type: Number,
    min: 0,
    max: 100,
    default: 0
  },
  config: {
    type: Object,
    default: {}
  },
  result: {
    success: Number, // 成功数量
    failed: Number,  // 失败数量
    total: Number,   // 总数量
    detail: Object   // 详细信息
  },
  error: String,
  logs: [{
    time: {
      type: String,
      required: true
    },
    message: {
      type: String,
      required: true
    },
    level: {
      type: String,
      enum: ['info', 'warning', 'error'],
      default: 'info'
    },
    timestamp: {
      type: Date,
      default: Date.now
    }
  }],
  startTime: Date,
  endTime: Date,
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true
});

// 索引
taskSchema.index({ taskType: 1 });
taskSchema.index({ status: 1 });
taskSchema.index({ createdBy: 1 });

const Task = mongoose.model('Task', taskSchema);

module.exports = Task; 