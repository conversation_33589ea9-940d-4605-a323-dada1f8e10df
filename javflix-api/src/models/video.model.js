const mongoose = require('mongoose');

const videoSchema = new mongoose.Schema({
  code: {
    type: String,
    required: [true, '影片代码不能为空'],
    unique: true,
    trim: true
  },
  title: {
    type: String,
    required: [true, '标题不能为空']
  },
  coverUrl: {
    type: String,
    required: [true, '封面图片不能为空']
  },
  videoUrl: String,
  duration: Number, // 单位：秒
  releaseDate: Date,
  description: String,
  director: String,
  studio: String,
  publisher: String,
  series: String,
  views: {
    type: Number,
    default: 0
  },
  tags: [{
    type: String,
    trim: true
  }],
  actresses: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Actress'
  }],
  javbusId: String,
  gid: String,
  uc: String,
  magnetLinks: [{
    link: String,
    title: String,
    size: String,
    shareDate: Date,
    hasSubtitle: Boolean,
    isHD: Boolean
  }],
  samples: [{
    url: String,
    thumbnailUrl: String
  }]
}, {
  timestamps: true
});

// 添加全文搜索索引
videoSchema.index({ 
  title: 'text',
  code: 'text',
  description: 'text',
  tags: 'text'
}, {
  weights: {
    code: 10,
    title: 5,
    tags: 3,
    description: 1
  }
});

const Video = mongoose.model('Video', videoSchema);

module.exports = Video; 