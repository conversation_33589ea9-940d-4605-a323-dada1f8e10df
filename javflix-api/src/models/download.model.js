const mongoose = require('mongoose');

const downloadSchema = new mongoose.Schema({
  movieId: {
    type: String,
    trim: true
  },
  title: {
    type: String,
    required: [true, '标题不能为空'],
    trim: true
  },
  magnetLink: {
    type: String,
    required: [true, '磁力链接不能为空'],
    trim: true
  },
  magnetInfo: {
    hash: String,
    link: String,
    hasSubtitle: Boolean,
    isHD: Boolean,
    size: String
  },
  status: {
    type: String,
    enum: ['waiting', 'downloading', 'paused', 'completed', 'failed', 'stopped'],
    default: 'waiting'
  },
  progress: {
    type: Number,
    min: 0,
    max: 100,
    default: 0
  },
  speed: {
    type: String,
    default: '0 KB/s'
  },
  savePath: String,
  saveDir: {
    type: String,
    default: 'default'
  },
  customDir: String,
  priority: {
    type: String,
    enum: ['low', 'medium', 'high'],
    default: 'medium'
  },
  autoStart: {
    type: Boolean,
    default: true
  },
  startTime: Date,
  endTime: Date,
  remark: String,
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  logs: [{
    time: {
      type: String,
      required: true
    },
    message: {
      type: String,
      required: true
    },
    timestamp: {
      type: Date,
      default: Date.now
    }
  }],
  error: String,
  fileSize: Number
}, {
  timestamps: true
});

// 索引
downloadSchema.index({ movieId: 1 });
downloadSchema.index({ status: 1 });
downloadSchema.index({ createdBy: 1 });

const Download = mongoose.model('Download', downloadSchema);

module.exports = Download; 