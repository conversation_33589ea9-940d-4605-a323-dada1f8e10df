const mongoose = require('mongoose');

const actressSchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, '姓名不能为空'],
    trim: true
  },
  japaneseName: String,
  imageUrl: {
    type: String,
    required: [true, '头像不能为空']
  },
  birthDate: Date,
  height: Number, // 单位：cm
  measurements: String, // 三围，例如：B88-W59-H89
  cup: String,
  birthplace: String,
  bloodType: String,
  hobby: String,
  javbusId: {
    type: String,
    unique: true,
    sparse: true
  },
  videos: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Video'
  }],
  bio: String,
  slug: {
    type: String,
    unique: true
  },
  active: {
    type: Boolean,
    default: true
  }
}, {
  timestamps: true
});

// 生成slug
actressSchema.pre('save', function(next) {
  if (!this.isModified('name')) {
    return next();
  }

  this.slug = this.name
    .toLowerCase()
    .replace(/[^\w\s-]/g, '')  // 移除非单词和非空格字符
    .replace(/[\s_-]+/g, '-')   // 将空格和下划线替换为连字符
    .replace(/^-+|-+$/g, '');   // 移除开头和结尾的连字符
    
  next();
});

// 添加全文搜索索引
actressSchema.index({
  name: 'text',
  japaneseName: 'text',
  birthplace: 'text'
}, {
  weights: {
    name: 10,
    japaneseName: 5,
    birthplace: 1
  }
});

const Actress = mongoose.model('Actress', actressSchema);

module.exports = Actress; 