const { Pool } = require('pg');

// 创建PostgreSQL连接池
const pool = new Pool({
  host: process.env.PGHOST || 'localhost',
  port: parseInt(process.env.PGPORT || '5432'),
  database: process.env.PGDATABASE || 'javflix',
  user: process.env.PGUSER || 'longgedemacminim4',
  password: process.env.PGPASSWORD || '',
});

// 连接PostgreSQL数据库
const connectDB = async () => {
  try {
    // 测试连接
    const result = await pool.query('SELECT NOW()');
    console.log(`✅ PostgreSQL 连接成功: ${result.rows[0].now}`);
    return pool;
  } catch (error) {
    console.error(`❌ PostgreSQL 连接错误: ${error.message}`);
    process.exit(1);
  }
};

module.exports = { connectDB, pool }; 