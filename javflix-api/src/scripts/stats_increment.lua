-- Redis Lua脚本：原子性视频统计增量操作 - 增强版
-- 实现写时幂等、版本戳和精准同步标记
-- KEYS[1]: video:stats:{videoId} (HASH key)
-- KEYS[2]: modified_videos (ZSET key) 
-- KEYS[3]: video:stats:updates (PUBLISH channel)
-- ARGV[1]: statType (views/likes/favorites)
-- ARGV[2]: delta (增量值，通常为1)
-- ARGV[3]: videoId 
-- ARGV[4]: userId (可选，用于某些统计)
-- ARGV[5]: timestamp (当前时间戳)

local video_stats_key = KEYS[1]
local modified_videos_key = KEYS[2] 
local publish_channel = KEYS[3]
local stat_type = ARGV[1]
local delta = tonumber(ARGV[2])
local video_id = ARGV[3]
local user_id = ARGV[4] or ""
local timestamp = tonumber(ARGV[5])

-- 原子性增加统计值
local new_value = redis.call("HINCRBY", video_stats_key, stat_type, delta)

-- ✅ 更新版本戳和同步标记（写时幂等机制）
redis.call("HSET", video_stats_key, "last_updated", timestamp)
redis.call("HSET", video_stats_key, "needs_sync", "1")
redis.call("HSET", video_stats_key, "version", timestamp)

-- ✅ 记录操作历史（用于审计和幂等性检查）
local operation_key = "video:ops:" .. video_id
redis.call("ZADD", operation_key, timestamp, cjson.encode({
  type = stat_type,
  delta = delta,
  userId = user_id,
  timestamp = timestamp,
  resultValue = new_value
}))

-- 限制操作历史记录数量（保留最近100条）
redis.call("ZREMRANGEBYRANK", operation_key, 0, -101)

-- ✅ 精准标记需要同步的视频（按时间戳排序）
redis.call("ZADD", modified_videos_key, timestamp, video_id)

-- ✅ 设置修改计数器（用于批量同步优化）
local modification_count_key = "video:mod_count:" .. video_id
local mod_count = redis.call("INCR", modification_count_key)
redis.call("EXPIRE", modification_count_key, 3600) -- 1小时过期

-- 确保mod_count是数字
mod_count = tonumber(mod_count) or 1

-- ✅ 如果修改次数较多，提高同步优先级
if mod_count > 10 then
  redis.call("ZADD", "priority_sync_videos", timestamp, video_id)
end

-- 构建增强版发布消息
local message = cjson.encode({
  videoId = video_id,
  statType = stat_type,
  newValue = new_value,
  delta = delta,
  userId = user_id,
  timestamp = timestamp,
  version = timestamp,
  modificationCount = mod_count,
  requiresSync = true
})

-- 发布实时更新消息
redis.call("PUBLISH", publish_channel, message)

-- ✅ 额外发布到融合更新频道（供融合API监听）
redis.call("PUBLISH", "video:fused:updates", cjson.encode({
  videoId = video_id,
  type = "increment",
  statType = stat_type,
  delta = delta,
  timestamp = timestamp
}))

-- 返回包含详细信息的结果
return cjson.encode({
  newValue = new_value,
  timestamp = timestamp,
  version = timestamp,
  modificationCount = mod_count,
  success = true
}) 