const axios = require('axios');
const apiResponse = require('../utils/apiResponse');

// 这里配置javbus-api的基础URL
const JAVBUS_API_BASE_URL = process.env.JAVBUS_API_URL || 'http://localhost:3000/api';

/**
 * 获取影片列表
 */
exports.getMovies = async (req, res) => {
  try {
    // 转发所有查询参数
    const response = await axios.get(`${JAVBUS_API_BASE_URL}/movies`, {
      params: req.query
    });
    return apiResponse.success(res, response.data);
  } catch (error) {
    console.error('获取影片列表失败:', error);
    return apiResponse.error(res, '获取影片列表失败', error.response?.status || 500);
  }
};

/**
 * 搜索影片
 */
exports.searchMovies = async (req, res) => {
  try {
    const response = await axios.get(`${JAVBUS_API_BASE_URL}/movies/search`, {
      params: req.query
    });
    return apiResponse.success(res, response.data);
  } catch (error) {
    console.error('搜索影片失败:', error);
    return apiResponse.error(res, '搜索影片失败', error.response?.status || 500);
  }
};

/**
 * 获取影片详情
 */
exports.getMovieDetail = async (req, res) => {
  try {
    const { movieId } = req.params;
    const response = await axios.get(`${JAVBUS_API_BASE_URL}/movies/${movieId}`);
    return apiResponse.success(res, response.data);
  } catch (error) {
    console.error('获取影片详情失败:', error);
    return apiResponse.error(res, '获取影片详情失败', error.response?.status || 500);
  }
};

/**
 * 获取影片磁力链接
 */
exports.getMovieMagnets = async (req, res) => {
  try {
    const { movieId } = req.params;
    const response = await axios.get(`${JAVBUS_API_BASE_URL}/magnets/${movieId}`, {
      params: req.query
    });
    return apiResponse.success(res, response.data);
  } catch (error) {
    console.error('获取影片磁力链接失败:', error);
    return apiResponse.error(res, '获取影片磁力链接失败', error.response?.status || 500);
  }
};

/**
 * 获取演员详情
 */
exports.getStarDetail = async (req, res) => {
  try {
    const { starId } = req.params;
    const response = await axios.get(`${JAVBUS_API_BASE_URL}/stars/${starId}`, {
      params: req.query
    });
    return apiResponse.success(res, response.data);
  } catch (error) {
    console.error('获取演员详情失败:', error);
    return apiResponse.error(res, '获取演员详情失败', error.response?.status || 500);
  }
};
