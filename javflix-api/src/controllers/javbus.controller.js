const javbusService = require('../services/javbus.service');

// 获取影片列表
exports.getMovies = async (req, res) => {
  try {
    const { page = 1, magnet = 'all', type = 'normal' } = req.query;
    
    const result = await javbusService.getMovies(page, magnet, type);
    
    res.json({
      success: true,
      message: '获取影片列表成功',
      data: result
    });
  } catch (error) {
    console.error('获取影片列表失败:', error);
    res.status(500).json({
      success: false,
      message: '获取影片列表失败',
      error: error.message
    });
  }
};

// 搜索影片
exports.searchMovies = async (req, res) => {
  try {
    const { keyword, page = 1, magnet = 'all', type = 'normal' } = req.query;
    
    if (!keyword) {
      return res.status(400).json({
        success: false,
        message: '搜索关键词不能为空'
      });
    }
    
    const result = await javbusService.searchMovies(keyword, page, magnet, type);
    
    res.json({
      success: true,
      message: '搜索影片成功',
      data: result
    });
  } catch (error) {
    console.error('搜索影片失败:', error);
    res.status(500).json({
      success: false,
      message: '搜索影片失败',
      error: error.message
    });
  }
};

// 获取影片详情
exports.getMovieDetail = async (req, res) => {
  try {
    const { id } = req.params;
    
    const result = await javbusService.getMovieDetail(id);
    
    res.json({
      success: true,
      message: '获取影片详情成功',
      data: result
    });
  } catch (error) {
    console.error('获取影片详情失败:', error);
    res.status(500).json({
      success: false,
      message: '获取影片详情失败',
      error: error.message
    });
  }
};

// 获取影片磁力链接
exports.getMovieMagnets = async (req, res) => {
  try {
    const { id } = req.params;
    const { gid, uc } = req.query;
    
    if (!gid || !uc) {
      return res.status(400).json({
        success: false,
        message: '缺少必要参数gid或uc'
      });
    }
    
    const result = await javbusService.getMovieMagnets(id, { gid, uc });
    
    res.json({
      success: true,
      message: '获取磁力链接成功',
      data: result
    });
  } catch (error) {
    console.error('获取磁力链接失败:', error);
    res.status(500).json({
      success: false,
      message: '获取磁力链接失败',
      error: error.message
    });
  }
};

// 获取演员详情
exports.getStarDetail = async (req, res) => {
  try {
    const { id } = req.params;
    
    const result = await javbusService.getStarDetail(id);
    
    res.json({
      success: true,
      message: '获取演员详情成功',
      data: result
    });
  } catch (error) {
    console.error('获取演员详情失败:', error);
    res.status(500).json({
      success: false,
      message: '获取演员详情失败',
      error: error.message
    });
  }
}; 