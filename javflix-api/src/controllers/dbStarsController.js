const { PrismaClient } = require('../generated/prisma');
const prisma = new PrismaClient();
const { handleApiError } = require('../utils/errorHandler');

// 获取演员列表
exports.getStars = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const skip = (page - 1) * limit;
    const sort = req.query.sort || 'name';
    const order = req.query.order?.toLowerCase() === 'asc' ? 'asc' : 'desc';
    const search = req.query.search; // 添加搜索参数支持

    let orderBy;
    let stars, totalItems;
    
    // 构建搜索条件
    const searchConditions = search ? {
      OR: [
        { name: { contains: search, mode: 'insensitive' } },
        { star_id: { contains: search, mode: 'insensitive' } },
      ],
    } : {};
    
    // 处理人气排序 - 基于作品数量和最近更新时间的综合算法
    if (sort === 'popularity') {
      // 对于人气排序，我们需要获取所有数据然后在内存中排序
      const allStars = await prisma.stars.findMany({
        where: searchConditions, // 添加搜索条件
        include: {
          _count: {
            select: {
              movie_stars: true,
            },
          },
        },
      });

      // 计算人气分数：作品数量 × 0.8 + 随机因子 × 0.2 (模拟用户关注度)
      const starsWithPopularity = allStars.map(star => ({
        ...star,
        popularity_score: star._count.movie_stars * 0.8 + Math.random() * star._count.movie_stars * 0.2
      }));

      // 排序
      starsWithPopularity.sort((a, b) => {
        return order === 'desc' ? b.popularity_score - a.popularity_score : a.popularity_score - b.popularity_score;
      });

      // 分页
      stars = starsWithPopularity.slice(skip, skip + limit);
      totalItems = allStars.length;
    } else if (sort === 'movie_count') {
      // 处理作品数量排序 - 需要特殊处理因为这不是直接的数据库字段
      const allStars = await prisma.stars.findMany({
        where: searchConditions, // 添加搜索条件
        include: {
          _count: {
            select: {
              movie_stars: true,
            },
          },
        },
      });

      // 按作品数量排序
      allStars.sort((a, b) => {
        const countA = a._count.movie_stars;
        const countB = b._count.movie_stars;
        return order === 'desc' ? countB - countA : countA - countB;
      });

      // 分页
      stars = allStars.slice(skip, skip + limit);
      totalItems = allStars.length;
    } else {
      // 普通字段排序
      orderBy = {
        [sort]: order,
      };
      
      stars = await prisma.stars.findMany({
        where: searchConditions, // 添加搜索条件
        skip,
        take: limit,
        orderBy,
        include: {
          _count: {
            select: {
              movie_stars: true,
            },
          },
        },
      });

      totalItems = await prisma.stars.count({
        where: searchConditions, // 添加搜索条件到总数统计
      });
    }

    // 添加影片数量信息到响应
    const formattedStars = stars.map(star => ({
      id: star.id,
      star_id: star.star_id,
      name: star.name,
      image_url: star.image_url,
      cached_image_url: star.cached_image_url, // 添加缓存图片URL
      movie_count: star._count.movie_stars,
    }));

    const totalPages = Math.ceil(totalItems / limit);

    console.log(`[getStars] 搜索: "${search || '无'}", 结果: ${totalItems}条, 页面: ${page}/${totalPages}`);

    return res.status(200).json({
      success: true,
      data: {
        items: formattedStars,
        pagination: {
          currentPage: page,
          totalPages,
          totalItems,
          limit,
        },
      },
      message: search ? '搜索演员成功' : '获取演员列表成功',
      code: 200,
    });
  } catch (error) {
    console.error('[getStars] 错误:', error);
    return handleApiError(res, error);
  }
};

// 搜索演员
exports.searchStars = async (req, res) => {
  try {
    const { keyword } = req.query;
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const skip = (page - 1) * limit;

    if (!keyword) {
      return res.status(400).json({
        success: false,
        message: '搜索关键词不能为空',
        code: 400,
      });
    }

    const stars = await prisma.stars.findMany({
      where: {
        OR: [
          { name: { contains: keyword, mode: 'insensitive' } },
          { star_id: { contains: keyword, mode: 'insensitive' } },
        ],
      },
      skip,
      take: limit,
      include: {
        _count: {
          select: {
            movie_stars: true,
          },
        },
      },
    });

    // 添加影片数量信息到响应
    const formattedStars = stars.map(star => ({
      id: star.id,
      star_id: star.star_id,
      name: star.name,
      image_url: star.image_url,
      movie_count: star._count.movie_stars,
    }));

    const totalItems = await prisma.stars.count({
      where: {
        OR: [
          { name: { contains: keyword, mode: 'insensitive' } },
          { star_id: { contains: keyword, mode: 'insensitive' } },
        ],
      },
    });
    
    const totalPages = Math.ceil(totalItems / limit);

    return res.status(200).json({
      success: true,
      data: {
        items: formattedStars,
        pagination: {
          currentPage: page,
          totalPages,
          totalItems,
          limit,
        },
      },
      message: '搜索演员成功',
      code: 200,
    });
  } catch (error) {
    return handleApiError(res, error);
  }
};

// 获取演员详情
exports.getStarById = async (req, res) => {
  try {
    const { id } = req.params;
    
    // 验证id参数
    if (!id) {
      return res.status(400).json({
        success: false,
        message: '演员ID不能为空',
        code: 400,
      });
    }

    const parsedId = parseInt(id);
    if (isNaN(parsedId)) {
      return res.status(400).json({
        success: false,
        message: '演员ID格式不正确',
        code: 400,
      });
    }

    const star = await prisma.stars.findUnique({
      where: { id: parsedId },
      include: {
        movie_stars: {
          include: {
            movies: {
              include: {
                movie_genres: {
                  include: {
                    genres: true,
                  },
                },
              },
            },
          },
        },
      },
    });

    if (!star) {
      return res.status(404).json({
        success: false,
        message: '演员不存在',
        code: 404,
      });
    }

    // 格式化数据
    const formattedStar = {
      id: star.id,
      star_id: star.star_id,
      name: star.name,
      image_url: star.image_url,
      cached_image_url: star.cached_image_url,
      // 个人基本信息
      birthday: star.birthday,
      age: star.age,
      height: star.height,
      birthplace: star.birthplace,
      debut_date: star.debut_date,
      hobby: star.hobby,
      description: star.description,
      javbus_id: star.javbus_id,
      // 身体测量信息
      bust: star.bust,
      waist: star.waist,
      hip: star.hip,
      bust_size: star.bust_size,
      waist_size: star.waist_size,
      hip_size: star.hip_size,
      cup_size: star.cup_size,
      measurements: star.measurements,
      waistline: star.waistline,
      hipline: star.hipline,
      // 作品信息
      movie_count: star.movie_count || star.movie_stars.length,
      movies: star.movie_stars.map(ms => ({
        id: ms.movies.id,
        movie_id: ms.movies.movie_id,
        title: ms.movies.title,
        image_url: ms.movies.image_url,
        cached_image_url: ms.movies.cached_image_url,
        release_date: ms.movies.release_date,
        duration: ms.movies.duration,
        view_count: ms.movies.view_count || 0,
        genres: ms.movies.movie_genres.map(mg => mg.genres),
      })),
    };

    return res.status(200).json({
      success: true,
      data: formattedStar,
      message: '获取演员详情成功',
      code: 200,
    });
  } catch (error) {
    return handleApiError(res, error);
  }
};

// 获取热门演员
exports.getPopularStars = async (req, res) => {
  try {
    const limit = parseInt(req.query.limit) || 10;

    // 根据出演的电影数量排序
    const stars = await prisma.stars.findMany({
      take: limit,
      include: {
        _count: {
          select: {
            movie_stars: true,
          },
        },
      },
      orderBy: {
        movie_stars: {
          _count: 'desc',
        },
      },
    });

    // 添加影片数量信息到响应
    const formattedStars = stars.map(star => ({
      id: star.id,
      star_id: star.star_id,
      name: star.name,
      image_url: star.image_url,
      movie_count: star._count.movie_stars,
    }));

    return res.status(200).json({
      success: true,
      data: {
        items: formattedStars,
      },
      message: '获取热门演员成功',
      code: 200,
    });
  } catch (error) {
    return handleApiError(res, error);
  }
}; 