// 已发布影片控制器 - 用户前端专用
const { pool } = require('../config/db');
const apiResponse = require('../utils/apiResponse');

/**
 * 获取已发布的影片列表 (用户前端) - 简化版本
 */
exports.getPublishedVideos = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 20,
      sort = 'published_at',
      order = 'DESC',
      search = ''
    } = req.query;

    const offset = (page - 1) * limit;

    // 构建查询条件
    let whereConditions = ["m.status = 'published'"];
    let queryParams = [];
    let paramIndex = 1;

    // 搜索条件
    if (search) {
      whereConditions.push(`(m.title ILIKE $${paramIndex} OR m.movie_id ILIKE $${paramIndex})`);
      queryParams.push(`%${search}%`);
      paramIndex++;
    }

    const whereClause = whereConditions.join(' AND ');

    // 排序字段验证
    const allowedSortFields = ['published_at', 'title', 'release_date', 'view_count'];
    const sortField = allowedSortFields.includes(sort) ? sort : 'published_at';
    const sortOrder = order.toUpperCase() === 'ASC' ? 'ASC' : 'DESC';

    // 主查询 - 简化版本
    const query = `
      SELECT
        m.id,
        m.movie_id,
        m.title,
        m.image_url,
        m.cover_image,
        m.release_date,
        m.duration,
        m.description,
        m.published_at,
        m.video_urls,
        m.view_count as views,
        COUNT(DISTINCT mag.id) as magnet_count
      FROM movies m
      LEFT JOIN magnets mag ON m.id = mag.movie_id
      WHERE ${whereClause}
      GROUP BY m.id
      ORDER BY m.${sortField} ${sortOrder}
      LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
    `;

    queryParams.push(limit, offset);

    const result = await pool.query(query, queryParams);

    // 获取总数
    const countQuery = `
      SELECT COUNT(*) as total
      FROM movies m
      WHERE ${whereClause}
    `;

    const countParams = queryParams.slice(0, -2); // 移除limit和offset参数
    const countResult = await pool.query(countQuery, countParams);

    // 处理视频URL
    const movies = result.rows.map(movie => ({
      ...movie,
      video_urls: movie.video_urls || {},
      has_video: movie.video_urls && Object.keys(movie.video_urls).length > 0,
      actresses: [], // 暂时返回空数组
      genres: [] // 暂时返回空数组
    }));

    return apiResponse.success(res, {
      movies,
      pagination: {
        total: parseInt(countResult.rows[0].total),
        page: parseInt(page),
        limit: parseInt(limit),
        pages: Math.ceil(countResult.rows[0].total / limit)
      }
    });

  } catch (error) {
    console.error('获取已发布影片失败:', error);
    return apiResponse.error(res, '获取影片列表失败');
  }
};

/**
 * 获取单个已发布影片详情 - 简化版本
 */
exports.getPublishedVideoById = async (req, res) => {
  try {
    const { id } = req.params;

    const query = `
      SELECT
        m.*,
        -- 磁力链接
        COALESCE(
          json_agg(
            DISTINCT jsonb_build_object(
              'id', mag.id,
              'link', mag.link,
              'title', mag.title,
              'size', mag.size,
              'is_hd', mag.is_hd,
              'has_subtitle', mag.has_subtitle,
              'share_date', mag.share_date
            )
          ) FILTER (WHERE mag.id IS NOT NULL),
          '[]'
        ) as magnets
      FROM movies m
      LEFT JOIN magnets mag ON m.id = mag.movie_id
      WHERE m.id = $1 AND m.status = 'published'
      GROUP BY m.id
    `;

    const result = await pool.query(query, [id]);

    if (result.rows.length === 0) {
      return apiResponse.notFound(res, '影片不存在或未发布');
    }

    const movie = result.rows[0];

    // 增加观看次数
    await pool.query(
      'UPDATE movies SET view_count = COALESCE(view_count, 0) + 1 WHERE id = $1',
      [id]
    );

    // 处理视频URL
    movie.video_urls = movie.video_urls || {};
    movie.has_video = movie.video_urls && Object.keys(movie.video_urls).length > 0;
    movie.actresses = []; // 暂时返回空数组
    movie.genres = []; // 暂时返回空数组

    return apiResponse.success(res, movie);

  } catch (error) {
    console.error('获取影片详情失败:', error);
    return apiResponse.error(res, '获取影片详情失败');
  }
};

/**
 * 获取热门已发布影片
 */
exports.getPopularPublishedVideos = async (req, res) => {
  try {
    const { limit = 10 } = req.query;
    
    const query = `
      SELECT
        m.id,
        m.movie_id,
        m.title,
        m.image_url,
        m.view_count as views,
        m.published_at,
        0 as actress_count
      FROM movies m
      WHERE m.status = 'published'
        AND m.video_urls IS NOT NULL
        AND jsonb_array_length(COALESCE(m.video_urls->'qualities', '[]'::jsonb)) > 0
      ORDER BY m.view_count DESC, m.published_at DESC
      LIMIT $1
    `;
    
    const result = await pool.query(query, [limit]);
    
    return apiResponse.success(res, result.rows);
    
  } catch (error) {
    console.error('获取热门影片失败:', error);
    return apiResponse.error(res, '获取热门影片失败');
  }
};

/**
 * 获取最新已发布影片
 */
exports.getRecentPublishedVideos = async (req, res) => {
  try {
    const { limit = 10 } = req.query;
    
    const query = `
      SELECT
        m.id,
        m.movie_id,
        m.title,
        m.image_url,
        m.published_at,
        0 as actress_count
      FROM movies m
      WHERE m.status = 'published'
        AND m.video_urls IS NOT NULL
        AND jsonb_array_length(COALESCE(m.video_urls->'qualities', '[]'::jsonb)) > 0
      ORDER BY m.published_at DESC
      LIMIT $1
    `;
    
    const result = await pool.query(query, [limit]);
    
    return apiResponse.success(res, result.rows);
    
  } catch (error) {
    console.error('获取最新影片失败:', error);
    return apiResponse.error(res, '获取最新影片失败');
  }
};

/**
 * 搜索已发布影片
 */
exports.searchPublishedVideos = async (req, res) => {
  try {
    const { 
      q: query = '', 
      page = 1, 
      limit = 20 
    } = req.query;
    
    if (!query.trim()) {
      return apiResponse.error(res, '搜索关键词不能为空');
    }
    
    const offset = (page - 1) * limit;
    
    const searchQuery = `
      SELECT 
        m.id,
        m.movie_id,
        m.title,
        m.image_url,
        m.published_at,
        m.views,
        ts_rank(
          to_tsvector('simple', m.title || ' ' || m.movie_id || ' ' || COALESCE(m.description, '')),
          plainto_tsquery('simple', $1)
        ) as rank
      FROM movies m
      WHERE m.status = 'published'
        AND m.video_urls IS NOT NULL
        AND (
          m.title ILIKE $2 
          OR m.movie_id ILIKE $2
          OR m.description ILIKE $2
        )
      ORDER BY rank DESC, m.published_at DESC
      LIMIT $3 OFFSET $4
    `;
    
    const searchPattern = `%${query}%`;
    const result = await pool.query(searchQuery, [query, searchPattern, limit, offset]);
    
    // 获取搜索结果总数
    const countQuery = `
      SELECT COUNT(*) as total
      FROM movies m
      WHERE m.status = 'published'
        AND m.video_urls IS NOT NULL
        AND (
          m.title ILIKE $1 
          OR m.movie_id ILIKE $1
          OR m.description ILIKE $1
        )
    `;
    
    const countResult = await pool.query(countQuery, [searchPattern]);
    
    return apiResponse.success(res, {
      movies: result.rows,
      pagination: {
        total: parseInt(countResult.rows[0].total),
        page: parseInt(page),
        limit: parseInt(limit),
        pages: Math.ceil(countResult.rows[0].total / limit)
      },
      query
    });
    
  } catch (error) {
    console.error('搜索影片失败:', error);
    return apiResponse.error(res, '搜索失败');
  }
};
