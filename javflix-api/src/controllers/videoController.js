const { Pool } = require('pg');
const apiResponse = require('../utils/apiResponse');
const fs = require('fs');
const path = require('path');

// 创建数据库连接池
const pool = new Pool({
  host: process.env.PGHOST || 'localhost',
  port: parseInt(process.env.PGPORT || '5432'),
  database: process.env.PGDATABASE || 'javflix',
  user: process.env.PGUSER || 'longgedemacminim4',
  password: process.env.PGPASSWORD || '',
});

// 获取所有视频
exports.getAllVideos = async (req, res) => {
  try {
    // 从查询参数获取分页、排序信息
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const offset = (page - 1) * limit;
    const sort = req.query.sort || 'id';
    const order = req.query.order || 'desc';
    
    // 根据排序参数构建不同的排序语句
    let orderClause = 'm.id DESC';
    
    switch(sort) {
      case 'releaseDate':
        orderClause = `m.release_date ${order.toUpperCase()}`;
        break;
      case 'popularity':
        // 按照磁力链接数量排序
        orderClause = `COUNT(DISTINCT mag.id) ${order.toUpperCase()}`;
        break;
      case 'random':
        orderClause = 'RANDOM()';
        break;
      case 'quality':
        // 按照高清资源数量排序
        orderClause = `COUNT(DISTINCT CASE WHEN mag.is_hd THEN mag.id END) ${order.toUpperCase()}`;
        break;
      default:
        orderClause = `m.id ${order.toUpperCase()}`;
    }
    
    // 从数据库获取视频数据
    const client = await pool.connect();
    const result = await client.query(`
      SELECT 
        m.id, 
        m.movie_id AS code, 
        m.title, 
        m.image_url AS "imageUrl", 
        m.cover_image AS "coverImage", 
        m.release_date AS "releaseDate", 
        m.duration,
        m.description,
        ARRAY_AGG(DISTINCT g.name) AS genres,
        jsonb_agg(DISTINCT jsonb_build_object(
          'id', s.id,
          'star_id', s.star_id,
          'name', s.name,
          'image_url', s.image_url
        )) FILTER (WHERE s.id IS NOT NULL) AS stars,
        COUNT(DISTINCT mag.id) AS "magnetCount",
        COUNT(DISTINCT CASE WHEN mag.is_hd THEN mag.id END) AS "hdCount"
      FROM 
        movies m
        LEFT JOIN movie_genres mg ON m.id = mg.movie_id
        LEFT JOIN genres g ON mg.genre_id = g.id
        LEFT JOIN movie_stars ms ON m.id = ms.movie_id
        LEFT JOIN stars s ON ms.star_id = s.id
        LEFT JOIN magnets mag ON m.id = mag.movie_id
      GROUP BY 
        m.id
      ORDER BY 
        ${orderClause}
      LIMIT $1 OFFSET $2
    `, [limit, offset]);
    
    // 获取总数量
    const totalResult = await client.query('SELECT COUNT(*) FROM movies');
    client.release();
    
    const total = parseInt(totalResult.rows[0].count);
    const totalPages = Math.ceil(total / limit);
    
    // 处理返回的数据，包括磁力链接信息
    const videos = await Promise.all(result.rows.map(async (video) => {
      // 获取影片的磁力链接
      const magnetsClient = await pool.connect();
      const magnetsResult = await magnetsClient.query(`
        SELECT 
          magnet_id,
          link,
          title,
          size,
          is_hd AS "isHD",
          has_subtitle AS "hasSubtitle",
          share_date AS "shareDate"
        FROM 
          magnets
        WHERE 
          movie_id = $1
      `, [video.id]);
      magnetsClient.release();
      
      // 获取影片的样品图片
      const samplesClient = await pool.connect();
      const samplesResult = await samplesClient.query(`
        SELECT 
          sample_id AS id,
          alt
        FROM 
          samples
        WHERE 
          movie_id = $1
      `, [video.id]);
      samplesClient.release();
      
      return {
        ...video,
        magnets: magnetsResult.rows || [],
        samples: samplesResult.rows || []
      };
    }));
    
    return apiResponse.success(res, {
      data: videos,
      page,
      limit,
      total,
      totalPages
    });
  } catch (error) {
    console.error('获取视频列表失败:', error);
    return apiResponse.error(res, '获取视频列表失败');
  }
};

// 通过ID获取视频
exports.getVideoById = async (req, res) => {
  try {
    const { id } = req.params;
    
    // 首先尝试直接通过ID查询
    const client = await pool.connect();
    let result;
    
    // 检查id是否是数字（数据库ID）或者是字符串（电影编号）
    if (!isNaN(id)) {
      result = await client.query(`
        SELECT 
          m.id, 
          m.movie_id AS code, 
          m.title, 
          m.image_url AS "imageUrl", 
          m.cover_image AS "coverImage", 
          m.release_date AS "releaseDate", 
          m.duration,
          m.description,
          ARRAY_AGG(DISTINCT g.name) AS genres,
          jsonb_agg(DISTINCT jsonb_build_object(
            'id', s.id,
            'star_id', s.star_id,
            'name', s.name,
            'image_url', s.image_url
          )) FILTER (WHERE s.id IS NOT NULL) AS stars,
          COUNT(DISTINCT mag.id) AS "magnetCount"
        FROM 
          movies m
          LEFT JOIN movie_genres mg ON m.id = mg.movie_id
          LEFT JOIN genres g ON mg.genre_id = g.id
          LEFT JOIN movie_stars ms ON m.id = ms.movie_id
          LEFT JOIN stars s ON ms.star_id = s.id
          LEFT JOIN magnets mag ON m.id = mag.movie_id
        WHERE 
          m.id = $1
        GROUP BY 
          m.id
      `, [id]);
    } else {
      // 通过电影编号查询
      result = await client.query(`
        SELECT 
          m.id, 
          m.movie_id AS code, 
          m.title, 
          m.image_url AS "imageUrl", 
          m.cover_image AS "coverImage", 
          m.release_date AS "releaseDate", 
          m.duration,
          m.description,
          ARRAY_AGG(DISTINCT g.name) AS genres,
          jsonb_agg(DISTINCT jsonb_build_object(
            'id', s.id,
            'star_id', s.star_id,
            'name', s.name,
            'image_url', s.image_url
          )) FILTER (WHERE s.id IS NOT NULL) AS stars,
          COUNT(DISTINCT mag.id) AS "magnetCount"
        FROM 
          movies m
          LEFT JOIN movie_genres mg ON m.id = mg.movie_id
          LEFT JOIN genres g ON mg.genre_id = g.id
          LEFT JOIN movie_stars ms ON m.id = ms.movie_id
          LEFT JOIN stars s ON ms.star_id = s.id
          LEFT JOIN magnets mag ON m.id = mag.movie_id
        WHERE 
          m.movie_id = $1
        GROUP BY 
          m.id
      `, [id]);
    }
    
    client.release();
    
    if (result.rows.length === 0) {
      return apiResponse.notFound(res, '未找到视频');
    }
    
    const video = result.rows[0];
    
    // 获取影片的磁力链接
    const magnetsClient = await pool.connect();
    const magnetsResult = await magnetsClient.query(`
      SELECT 
        magnet_id,
        link,
        title,
        size,
        is_hd AS "isHD",
        has_subtitle AS "hasSubtitle",
        share_date AS "shareDate"
      FROM 
        magnets
      WHERE 
        movie_id = $1
    `, [video.id]);
    magnetsClient.release();
    
    // 获取影片的样品图片
    const samplesClient = await pool.connect();
    const samplesResult = await samplesClient.query(`
      SELECT 
        sample_id AS id,
        alt
      FROM 
        samples
      WHERE 
        movie_id = $1
    `, [video.id]);
    samplesClient.release();
    
    // 获取相似影片
    const similarClient = await pool.connect();
    const similarResult = await similarClient.query(`
      SELECT 
        sm.similar_movie_id AS code,
        sm.similar_title AS title,
        sm.similar_image AS "imageUrl"
      FROM 
        similar_movies sm
      WHERE 
        sm.movie_id = $1
    `, [video.id]);
    similarClient.release();
    
    // 整合所有数据
    const videoData = {
      ...video,
      magnets: magnetsResult.rows || [],
      samples: samplesResult.rows || [],
      similarMovies: similarResult.rows || []
    };
    
    return apiResponse.success(res, { data: videoData });
  } catch (error) {
    console.error('获取视频详情失败:', error);
    return apiResponse.error(res, '获取视频详情失败');
  }
};

// 通过ID获取演员详情
exports.getStarById = async (req, res) => {
  try {
    const { id } = req.params;
    
    // 获取演员信息
    const client = await pool.connect();
    let result;
    
    // 检查id是否数字（数据库ID）或字符串（演员ID）
    if (!isNaN(id)) {
      result = await client.query('SELECT * FROM stars WHERE id = $1', [id]);
    } else {
      result = await client.query('SELECT * FROM stars WHERE star_id = $1', [id]);
    }
    
    if (result.rows.length === 0) {
      client.release();
      return apiResponse.notFound(res, '未找到演员');
    }
    
    const star = result.rows[0];
    
    // 获取该演员出演的影片
    const moviesResult = await client.query(`
      SELECT 
        m.id, 
        m.movie_id AS code, 
        m.title, 
        m.image_url AS "imageUrl", 
        m.release_date AS "releaseDate"
      FROM 
        movies m
        JOIN movie_stars ms ON m.id = ms.movie_id
        JOIN stars s ON ms.star_id = s.id
      WHERE 
        s.id = $1
      ORDER BY 
        m.release_date DESC
    `, [star.id]);
    
    client.release();
    
    // 构建返回数据
    const starData = {
      id: star.id,
      starId: star.star_id,
      name: star.name,
      imageUrl: star.image_url,
      movies: moviesResult.rows || []
    };
    
    return apiResponse.success(res, { data: starData });
  } catch (error) {
    console.error('获取演员详情失败:', error);
    return apiResponse.error(res, '获取演员详情失败');
  }
};

// 搜索视频
exports.searchVideos = async (req, res) => {
  try {
    const { q } = req.query;
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const offset = (page - 1) * limit;
    
    console.log('搜索参数:', { q, page, limit, offset });
    
    if (!q) {
      return apiResponse.error(res, '请输入搜索关键词');
    }
    
    // 搜索视频
    const client = await pool.connect();
    console.log('执行搜索SQL:', `SELECT FROM movies WHERE title ILIKE '%${q}%' OR movie_id ILIKE '%${q}%'`);
    
    const result = await client.query(`
      SELECT 
        m.id, 
        m.movie_id AS code, 
        m.title, 
        m.image_url AS "imageUrl", 
        m.cover_image AS "coverImage", 
        m.release_date AS "releaseDate", 
        m.duration
      FROM 
        movies m
      WHERE 
        m.title ILIKE $1
        OR m.movie_id ILIKE $1
      ORDER BY 
        m.id DESC
      LIMIT $2 OFFSET $3
    `, [`%${q}%`, limit, offset]);
    
    console.log('搜索结果数量:', result.rows.length);
    
    // 获取搜索结果总数
    const totalResult = await client.query(`
      SELECT COUNT(*) 
      FROM movies 
      WHERE 
        title ILIKE $1
        OR movie_id ILIKE $1
    `, [`%${q}%`]);
    
    client.release();
    
    const total = parseInt(totalResult.rows[0].count);
    const totalPages = Math.ceil(total / limit);
    
    console.log('搜索总数:', total);
    
    if (total === 0) {
      return apiResponse.notFound(res, '未找到视频');
    }
    
    return apiResponse.success(res, {
      data: result.rows,
      page,
      limit,
      total,
      totalPages,
      query: q
    });
  } catch (error) {
    console.error('搜索视频失败:', error);
    return apiResponse.error(res, '搜索视频失败');
  }
};

// 添加管理功能: 添加新视频
exports.addVideo = async (req, res) => {
  try {
    const videoData = req.body;
    
    if (!videoData || !videoData.title || !videoData.code) {
      return apiResponse.error(res, '请提供必要的视频信息（标题和影片编号）');
    }
    
    const client = await pool.connect();
    
    try {
      // 开始事务
      await client.query('BEGIN');
      
      // 1. 插入电影基本信息
      const movieResult = await client.query(`
        INSERT INTO movies (
          movie_id, title, image_url, cover_image, release_date, 
          duration, description, director, producer, publisher, series
        ) VALUES (
          $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11
        ) RETURNING id
      `, [
        videoData.code,
        videoData.title,
        videoData.imageUrl || '',
        videoData.coverImage || '',
        videoData.releaseDate || new Date(),
        videoData.duration || '',
        videoData.description || '',
        videoData.director || null,
        videoData.producer || null,
        videoData.publisher || null,
        videoData.series || null
      ]);
      
      const movieId = movieResult.rows[0].id;
      
      // 2. 处理类型标签
      if (videoData.genres && Array.isArray(videoData.genres)) {
        for (const genreName of videoData.genres) {
          // 检查该类型是否已存在
          let genreResult = await client.query(
            'SELECT id FROM genres WHERE name = $1', 
            [genreName]
          );
          
          let genreId;
          
          if (genreResult.rows.length === 0) {
            // 如果不存在，创建新类型
            const newGenreResult = await client.query(
              'INSERT INTO genres (name) VALUES ($1) RETURNING id', 
              [genreName]
            );
            genreId = newGenreResult.rows[0].id;
          } else {
            genreId = genreResult.rows[0].id;
          }
          
          // 关联电影和类型
          await client.query(
            'INSERT INTO movie_genres (movie_id, genre_id) VALUES ($1, $2) ON CONFLICT DO NOTHING', 
            [movieId, genreId]
          );
        }
      }
      
      // 3. 处理演员
      if (videoData.stars && Array.isArray(videoData.stars)) {
        for (const star of videoData.stars) {
          if (!star.name) continue;
          
          // 检查演员是否已存在
          let starResult = await client.query(
            'SELECT id FROM stars WHERE name = $1', 
            [star.name]
          );
          
          let starId;
          
          if (starResult.rows.length === 0) {
            // 如果不存在，创建新演员
            const newStarResult = await client.query(
              'INSERT INTO stars (star_id, name, image_url) VALUES ($1, $2, $3) RETURNING id', 
              [star.id || `star-${Date.now()}`, star.name, star.imageUrl || '']
            );
            starId = newStarResult.rows[0].id;
          } else {
            starId = starResult.rows[0].id;
          }
          
          // 关联电影和演员
          await client.query(
            'INSERT INTO movie_stars (movie_id, star_id) VALUES ($1, $2) ON CONFLICT DO NOTHING', 
            [movieId, starId]
          );
        }
      }
      
      // 4. 处理磁力链接
      if (videoData.magnets && Array.isArray(videoData.magnets)) {
        for (const magnet of videoData.magnets) {
          if (!magnet.link) continue;
          
          await client.query(`
            INSERT INTO magnets (
              movie_id, magnet_id, link, title, size, is_hd, has_subtitle, share_date
            ) VALUES (
              $1, $2, $3, $4, $5, $6, $7, $8
            ) ON CONFLICT DO NOTHING
          `, [
            movieId,
            magnet.id || magnet.link.substring(20, 60), // 从链接提取ID
            magnet.link,
            magnet.title || videoData.title,
            magnet.size || '未知',
            magnet.isHD || false,
            magnet.hasSubtitle || false,
            magnet.shareDate || new Date()
          ]);
        }
      }
      
      // 不再处理样品图片 - 用户要求不采集samples
      
      // 提交事务
      await client.query('COMMIT');
      
      return apiResponse.success(res, { 
        message: '视频添加成功', 
        data: { id: movieId, code: videoData.code } 
      });
    } catch (err) {
      // 发生错误时回滚事务
      await client.query('ROLLBACK');
      throw err;
    } finally {
      client.release();
    }
  } catch (error) {
    console.error('添加视频失败:', error);
    return apiResponse.error(res, `添加视频失败: ${error.message}`);
  }
};

// 更新视频信息
exports.updateVideo = async (req, res) => {
  try {
    const { id } = req.params;
    const updateData = req.body;
    
    if (!updateData) {
      return apiResponse.error(res, '请提供要更新的数据');
    }
    
    const client = await pool.connect();
    
    try {
      // 开始事务
      await client.query('BEGIN');
      
      // 检查视频是否存在
      const checkResult = await client.query(
        'SELECT id FROM movies WHERE id = $1 OR movie_id = $1', 
        [id]
      );
      
      if (checkResult.rows.length === 0) {
        await client.query('ROLLBACK');
        return apiResponse.notFound(res, '未找到视频');
      }
      
      const movieId = checkResult.rows[0].id;
      
      // 1. 更新电影基本信息
      if (updateData.title || updateData.imageUrl || updateData.releaseDate || updateData.description) {
        let updateQuery = 'UPDATE movies SET ';
        let updateValues = [];
        let valueIndex = 1;
        
        const updateFields = [];
        
        if (updateData.title) {
          updateFields.push(`title = $${valueIndex++}`);
          updateValues.push(updateData.title);
        }
        
        if (updateData.code) {
          updateFields.push(`movie_id = $${valueIndex++}`);
          updateValues.push(updateData.code);
        }
        
        if (updateData.imageUrl) {
          updateFields.push(`image_url = $${valueIndex++}`);
          updateValues.push(updateData.imageUrl);
        }
        
        if (updateData.coverImage) {
          updateFields.push(`cover_image = $${valueIndex++}`);
          updateValues.push(updateData.coverImage);
        }
        
        if (updateData.releaseDate) {
          updateFields.push(`release_date = $${valueIndex++}`);
          updateValues.push(updateData.releaseDate);
        }
        
        if (updateData.duration) {
          updateFields.push(`duration = $${valueIndex++}`);
          updateValues.push(updateData.duration);
        }
        
        if (updateData.description) {
          updateFields.push(`description = $${valueIndex++}`);
          updateValues.push(updateData.description);
        }
        
        if (updateFields.length > 0) {
          updateQuery += updateFields.join(', ') + ` WHERE id = $${valueIndex}`;
          updateValues.push(movieId);
          
          await client.query(updateQuery, updateValues);
        }
      }
      
      // 2. 更新类型标签
      if (updateData.genres && Array.isArray(updateData.genres)) {
        // 清除旧的关联
        await client.query('DELETE FROM movie_genres WHERE movie_id = $1', [movieId]);
        
        // 添加新的关联
        for (const genreName of updateData.genres) {
          // 检查该类型是否已存在
          let genreResult = await client.query(
            'SELECT id FROM genres WHERE name = $1', 
            [genreName]
          );
          
          let genreId;
          
          if (genreResult.rows.length === 0) {
            // 如果不存在，创建新类型
            const newGenreResult = await client.query(
              'INSERT INTO genres (name) VALUES ($1) RETURNING id', 
              [genreName]
            );
            genreId = newGenreResult.rows[0].id;
          } else {
            genreId = genreResult.rows[0].id;
          }
          
          // 关联电影和类型
          await client.query(
            'INSERT INTO movie_genres (movie_id, genre_id) VALUES ($1, $2) ON CONFLICT DO NOTHING', 
            [movieId, genreId]
          );
        }
      }
      
      // 3. 更新演员
      if (updateData.stars && Array.isArray(updateData.stars)) {
        // 清除旧的关联
        await client.query('DELETE FROM movie_stars WHERE movie_id = $1', [movieId]);
        
        // 添加新的关联
        for (const star of updateData.stars) {
          if (!star.name) continue;
          
          // 检查演员是否已存在
          let starResult = await client.query(
            'SELECT id FROM stars WHERE name = $1', 
            [star.name]
          );
          
          let starId;
          
          if (starResult.rows.length === 0) {
            // 如果不存在，创建新演员
            const newStarResult = await client.query(
              'INSERT INTO stars (star_id, name, image_url) VALUES ($1, $2, $3) RETURNING id', 
              [star.id || `star-${Date.now()}`, star.name, star.imageUrl || '']
            );
            starId = newStarResult.rows[0].id;
          } else {
            starId = starResult.rows[0].id;
          }
          
          // 关联电影和演员
          await client.query(
            'INSERT INTO movie_stars (movie_id, star_id) VALUES ($1, $2) ON CONFLICT DO NOTHING', 
            [movieId, starId]
          );
        }
      }
      
      // 4. 更新磁力链接
      if (updateData.magnets && Array.isArray(updateData.magnets)) {
        // 清除旧的磁力链接
        await client.query('DELETE FROM magnets WHERE movie_id = $1', [movieId]);
        
        // 添加新的磁力链接
        for (const magnet of updateData.magnets) {
          if (!magnet.link) continue;
          
          await client.query(`
            INSERT INTO magnets (
              movie_id, magnet_id, link, title, size, is_hd, has_subtitle, share_date
            ) VALUES (
              $1, $2, $3, $4, $5, $6, $7, $8
            ) ON CONFLICT DO NOTHING
          `, [
            movieId,
            magnet.id || magnet.link.substring(20, 60), // 从链接提取ID
            magnet.link,
            magnet.title || updateData.title,
            magnet.size || '未知',
            magnet.isHD || false,
            magnet.hasSubtitle || false,
            magnet.shareDate || new Date()
          ]);
        }
      }
      
      // 提交事务
      await client.query('COMMIT');
      
      return apiResponse.success(res, { 
        message: '视频更新成功', 
        data: { id: movieId } 
      });
    } catch (err) {
      // 发生错误时回滚事务
      await client.query('ROLLBACK');
      throw err;
    } finally {
      client.release();
    }
  } catch (error) {
    console.error('更新视频失败:', error);
    return apiResponse.error(res, `更新视频失败: ${error.message}`);
  }
};

// 删除视频
exports.deleteVideo = async (req, res) => {
  try {
    const { id } = req.params;
    
    const client = await pool.connect();
    
    try {
      // 开始事务
      await client.query('BEGIN');
      
      // 检查视频是否存在
      const checkResult = await client.query(
        'SELECT id FROM movies WHERE id = $1 OR movie_id = $1', 
        [id]
      );
      
      if (checkResult.rows.length === 0) {
        await client.query('ROLLBACK');
        return apiResponse.notFound(res, '未找到视频');
      }
      
      const movieId = checkResult.rows[0].id;
      
      // 删除关联的数据
      await client.query('DELETE FROM movie_genres WHERE movie_id = $1', [movieId]);
      await client.query('DELETE FROM movie_stars WHERE movie_id = $1', [movieId]);
      await client.query('DELETE FROM magnets WHERE movie_id = $1', [movieId]);
      await client.query('DELETE FROM samples WHERE movie_id = $1', [movieId]);
      await client.query('DELETE FROM similar_movies WHERE movie_id = $1', [movieId]);
      
      // 删除电影本身
      await client.query('DELETE FROM movies WHERE id = $1', [movieId]);
      
      // 提交事务
      await client.query('COMMIT');
      
      return apiResponse.success(res, { 
        message: '视频删除成功'
      });
    } catch (err) {
      // 发生错误时回滚事务
      await client.query('ROLLBACK');
      throw err;
    } finally {
      client.release();
    }
  } catch (error) {
    console.error('删除视频失败:', error);
    return apiResponse.error(res, `删除视频失败: ${error.message}`);
  }
};

// 清空JavaBus数据
exports.cleanJavbusData = async (req, res) => {
  try {
    const client = await pool.connect();
    
    try {
      // 开始事务
      await client.query('BEGIN');
      
      // 删除JavaBus相关表中的所有数据
      await client.query('DELETE FROM movie_genres');
      await client.query('DELETE FROM movie_stars');
      await client.query('DELETE FROM magnets');
      await client.query('DELETE FROM samples');
      await client.query('DELETE FROM similar_movies');
      await client.query('DELETE FROM movies');
      // 可以保留genres和stars表，因为这些可能会被重用
      
      // 提交事务
      await client.query('COMMIT');
      
      return apiResponse.success(res, { 
        message: '成功清空JavaBus数据'
      });
    } catch (err) {
      // 发生错误时回滚事务
      await client.query('ROLLBACK');
      throw err;
    } finally {
      client.release();
    }
  } catch (error) {
    console.error('清空JavaBus数据失败:', error);
    return apiResponse.error(res, `清空JavaBus数据失败: ${error.message}`);
  }
};
