const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const { validationResult } = require('express-validator');

// 登录
exports.login = async (req, res) => {
  try {
    // 验证请求
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        errors: errors.array(),
        message: '输入数据无效',
        success: false
      });
    }

    const { username, password } = req.body;
    const pool = req.app.get('pool');

    // 查找用户
    const userResult = await pool.query(
      'SELECT id, username, email, password, is_admin, created_at FROM users WHERE username = $1',
      [username]
    );

    if (userResult.rows.length === 0) {
      return res.status(401).json({
        message: '用户名或密码错误',
        success: false
      });
    }

    const user = userResult.rows[0];

    // 验证密码
    const isMatch = await bcrypt.compare(password, user.password);
    if (!isMatch) {
      return res.status(401).json({
        message: '用户名或密码错误',
        success: false
      });
    }

    // 更新登录时间
    await pool.query(
      'UPDATE users SET "lastLoginAt" = NOW() WHERE id = $1',
      [user.id]
    );

    // 生成JWT
    const token = jwt.sign(
      { id: user.id },
      process.env.JWT_SECRET || 'javflix_secret_key',
      { expiresIn: '24h' }
    );

    // 返回用户信息和token
    res.json({
      token,
      user: {
        id: user.id,
        username: user.username,
        email: user.email,
        isAdmin: user.is_admin,
        avatar: user.avatar,
        createdAt: user.created_at
      },
      success: true,
      message: '登录成功'
    });
  } catch (error) {
    console.error('登录错误详情:', error);
    res.status(500).json({
      message: '服务器错误',
      error: error.message,
      success: false
    });
  }
};

// 登出
exports.logout = (req, res) => {
  // JWT是无状态的，客户端处理token的移除
  res.json({
    message: '登出成功',
    success: true
  });
};

// 获取用户信息
exports.getUserInfo = async (req, res) => {
  try {
    const pool = req.app.get('pool');
    
    const userResult = await pool.query(
      'SELECT id, username, email, is_admin, avatar, created_at FROM users WHERE id = $1',
      [req.user.id]
    );
    
    if (userResult.rows.length === 0) {
      return res.status(404).json({
        message: '用户不存在',
        success: false
      });
    }

    const user = userResult.rows[0];

    res.json({
      user: {
        id: user.id,
        username: user.username,
        email: user.email,
        isAdmin: user.is_admin,
        avatar: user.avatar,
        createdAt: user.created_at
      },
      success: true,
      message: '获取成功'
    });
  } catch (error) {
    console.error('获取用户信息错误:', error);
    res.status(500).json({
      message: '服务器错误',
      success: false
    });
  }
}; 