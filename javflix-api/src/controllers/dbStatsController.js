const { PrismaClient } = require('../generated/prisma');
const prisma = new PrismaClient();
const { handleApiError } = require('../utils/errorHandler');

// 获取影片统计信息
exports.getMovieStats = async (req, res) => {
  try {
    const totalMovies = await prisma.movies.count();
    
    // 获取最新影片
    const latestMovies = await prisma.movies.findMany({
      take: 5,
      orderBy: {
        release_date: 'desc',
      },
      select: {
        id: true,
        movie_id: true,
        title: true,
        image_url: true,
        release_date: true,
      },
    });
    
    // 获取磁力链接最多的影片
    const popularMovies = await prisma.movies.findMany({
      take: 5,
      include: {
        _count: {
          select: {
            magnets: true,
          },
        },
      },
      orderBy: {
        magnets: {
          _count: 'desc',
        },
      },
    });

    const formattedPopularMovies = popularMovies.map(movie => ({
      id: movie.id,
      movie_id: movie.movie_id,
      title: movie.title,
      image_url: movie.image_url,
      magnet_count: movie._count.magnets,
    }));
    
    // 按年份统计
    const moviesByYearRaw = await prisma.$queryRaw`
      SELECT EXTRACT(YEAR FROM release_date::date) as year, COUNT(*) as count
      FROM "movies"
      WHERE release_date IS NOT NULL
      GROUP BY year
      ORDER BY year DESC
      LIMIT 10
    `;

    // 转换BigInt为普通数字
    const moviesByYear = moviesByYearRaw.map(item => ({
      year: Number(item.year),
      count: Number(item.count)
    }));

    return res.status(200).json({
      success: true,
      data: {
        totalMovies,
        latestMovies,
        popularMovies: formattedPopularMovies,
        moviesByYear,
      },
      message: '获取影片统计信息成功',
      code: 200,
    });
  } catch (error) {
    return handleApiError(res, error);
  }
};

// 获取演员统计信息
exports.getStarStats = async (req, res) => {
  try {
    const totalStars = await prisma.stars.count();
    
    // 出演作品最多的演员
    const topStars = await prisma.stars.findMany({
      take: 10,
      include: {
        _count: {
          select: {
            movie_stars: true,
          },
        },
      },
      orderBy: {
        movie_stars: {
          _count: 'desc',
        },
      },
    });
    
    const formattedTopStars = topStars.map(star => ({
      id: star.id,
      star_id: star.star_id,
      name: star.name,
      image_url: star.image_url,
      movie_count: star._count.movie_stars,
    }));

    return res.status(200).json({
      success: true,
      data: {
        totalStars,
        topStars: formattedTopStars,
      },
      message: '获取演员统计信息成功',
      code: 200,
    });
  } catch (error) {
    return handleApiError(res, error);
  }
};

// 获取标签统计信息
exports.getTagStats = async (req, res) => {
  try {
    const totalGenres = await prisma.genres.count();
    
    // 使用最多的标签
    const popularGenres = await prisma.genres.findMany({
      take: 20,
      include: {
        _count: {
          select: {
            movie_genres: true,
          },
        },
      },
      orderBy: {
        movie_genres: {
          _count: 'desc',
        },
      },
    });

    const formattedGenres = popularGenres.map(genre => ({
      id: genre.id,
      name: genre.name,
      movie_count: genre._count.movie_genres,
    }));

    return res.status(200).json({
      success: true,
      data: {
        totalGenres,
        popularGenres: formattedGenres,
      },
      message: '获取标签统计信息成功',
      code: 200,
    });
  } catch (error) {
    return handleApiError(res, error);
  }
};

// 获取磁力链接统计信息
exports.getMagnetStats = async (req, res) => {
  try {
    const totalMagnets = await prisma.magnets.count();
    
    // 按高清与字幕统计
    const magnetsByQuality = await prisma.magnets.groupBy({
      by: ['is_hd'],
      _count: {
        id: true,
      },
    });
    
    const magnetsBySubtitle = await prisma.magnets.groupBy({
      by: ['has_subtitle'],
      _count: {
        id: true,
      },
    });
    
    // 最大的磁力文件
    const largestMagnets = await prisma.magnets.findMany({
      take: 5,
      orderBy: {
        size: 'desc',
      },
      include: {
        movies: {
          select: {
            movie_id: true,
            title: true,
          },
        },
      },
    });

    const formattedLargestMagnets = largestMagnets.map(magnet => ({
      id: magnet.id,
      title: magnet.title,
      size: magnet.size,
      is_hd: magnet.is_hd,
      has_subtitle: magnet.has_subtitle,
      movie: magnet.movies ? {
        movie_id: magnet.movies.movie_id,
        title: magnet.movies.title,
      } : null,
    }));

    return res.status(200).json({
      success: true,
      data: {
        totalMagnets,
        magnetsByQuality,
        magnetsBySubtitle,
        largestMagnets: formattedLargestMagnets,
      },
      message: '获取磁力链接统计信息成功',
      code: 200,
    });
  } catch (error) {
    return handleApiError(res, error);
  }
}; 