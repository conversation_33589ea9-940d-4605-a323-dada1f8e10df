const { videoStatsService } = require('../../dist/services/VideoStatsService');
const { getSocketService } = require('../../dist/services/SocketService');
const { dataValidationService } = require('../../dist/services/DataValidationService');

/**
 * 通过movie_id获取数字ID
 */
const getVideoIdByMovieId = async (movieId, pool) => {
  try {
    // 如果已经是数字，直接返回
    if (!isNaN(Number(movieId))) {
      return Number(movieId);
    }
    
    // 查询数据库获取数字ID
    const result = await pool.query('SELECT id FROM movies WHERE movie_id = $1', [movieId]);
    
    if (result.rows.length === 0) {
      return null;
    }
    
    return result.rows[0].id;
  } catch (error) {
    console.error('查询视频ID失败:', error);
    return null;
  }
};

/**
 * 增加视频统计
 */
const incrementVideoStats = async (req, res) => {
  try {
    const { videoId: inputVideoId } = req.params;
    const { statType, delta = 1 } = req.body;
    const userId = req.user?.id;
    const pool = req.app.get('pool');

    // 验证输入
    if (!inputVideoId || !statType) {
      return res.status(400).json({
        success: false,
        message: '缺少必需参数'
      });
    }

    if (!['views', 'likes', 'favorites'].includes(statType)) {
      return res.status(400).json({
        success: false,
        message: '无效的统计类型'
      });
    }

    // 获取数字ID
    const videoId = await getVideoIdByMovieId(inputVideoId, pool);
    if (!videoId) {
      return res.status(404).json({
        success: false,
        message: '视频不存在'
      });
    }

    // 调用服务增加统计
    const newValue = await videoStatsService.incrementStats(
      videoId.toString(),
      statType,
      delta,
      userId
    );

    // 通过Socket.IO发送实时更新
    const socketService = getSocketService();
    if (socketService) {
      socketService.emitStatsUpdate(videoId.toString(), statType, newValue, delta);
    }

    return res.status(200).json({
      success: true,
      data: {
        videoId: inputVideoId, // 返回原始输入ID
        numericVideoId: videoId, // 返回数字ID
        statType,
        newValue,
        delta
      },
      message: '统计更新成功'
    });

  } catch (error) {
    console.error('增加视频统计失败:', error);
    return res.status(500).json({
      success: false,
      message: '服务器内部错误',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * 获取视频统计信息
 */
const getVideoStats = async (req, res) => {
  try {
    const { videoId: inputVideoId } = req.params;
    const pool = req.app.get('pool');

    if (!inputVideoId) {
      return res.status(400).json({
        success: false,
        message: '缺少视频ID'
      });
    }

    // 获取数字ID
    const videoId = await getVideoIdByMovieId(inputVideoId, pool);
    if (!videoId) {
      return res.status(404).json({
        success: false,
        message: '视频不存在'
      });
    }

    const stats = await videoStatsService.getVideoStats(videoId.toString());

    if (!stats) {
      return res.status(200).json({
        success: true,
        data: {
          videoId: inputVideoId, // 返回原始输入ID
          numericVideoId: videoId, // 返回数字ID
          views: 0,
          likes: 0,
          favorites: 0,
          updatedAt: Date.now()
        },
        message: '获取视频统计成功'
      });
    }

    return res.status(200).json({
      success: true,
      data: {
        ...stats,
        videoId: inputVideoId, // 返回原始输入ID
        numericVideoId: videoId // 返回数字ID
      },
      message: '获取视频统计成功'
    });

  } catch (error) {
    console.error('获取视频统计失败:', error);
    return res.status(500).json({
      success: false,
      message: '服务器内部错误',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * 批量获取多个视频统计信息
 */
const batchGetVideoStats = async (req, res) => {
  try {
    const { videoIds } = req.body;

    if (!Array.isArray(videoIds) || videoIds.length === 0) {
      return res.status(400).json({
        success: false,
        message: '无效的视频ID列表'
      });
    }

    // 验证所有videoId都是数字
    const invalidIds = videoIds.filter(id => isNaN(Number(id)));
    if (invalidIds.length > 0) {
      return res.status(400).json({
        success: false,
        message: `无效的视频ID: ${invalidIds.join(', ')}`
      });
    }

    const statsArray = await videoStatsService.batchGetStats(videoIds);

    // 转换为对象格式，以videoId为key
    const statsObject = {};
    statsArray.forEach(stats => {
      statsObject[stats.videoId] = {
        views: stats.views,
        likes: stats.likes,
        favorites: stats.favorites,
        updatedAt: stats.updatedAt
      };
    });

    return res.status(200).json({
      success: true,
      data: statsObject,
      message: '批量获取视频统计成功'
    });

  } catch (error) {
    console.error('批量获取视频统计失败:', error);
    return res.status(500).json({
      success: false,
      message: '服务器内部错误',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * 获取融合统计数据（DB累计 + Redis增量）
 * 这是核心的"读时融合"实现
 */
const getFusedVideoStats = async (req, res) => {
  try {
    const { videoId: inputVideoId } = req.params;
    const pool = req.app.get('pool');

    if (!inputVideoId) {
      return res.status(400).json({
        success: false,
        message: '缺少视频ID'
      });
    }

    // 获取数字ID
    const videoId = await getVideoIdByMovieId(inputVideoId, pool);
    if (!videoId) {
      return res.status(404).json({
        success: false,
        message: '视频不存在'
      });
    }

    // 1. 读取数据库累计数据
    // 先获取movie_id
    const movieIdQuery = await pool.query('SELECT movie_id FROM movies WHERE id = $1', [videoId]);
    const movieId = movieIdQuery.rows[0]?.movie_id;
    
    const dbQuery = `
      SELECT 
        m.view_count,
        m.updated_at,
        m.movie_id,
        COALESCE(likes.count, 0) as likes_count,
        COALESCE(favorites.count, 0) as favorites_count
      FROM movies m
      LEFT JOIN (
        SELECT video_id, COUNT(*) as count 
        FROM user_likes 
        WHERE video_id = $1 
        GROUP BY video_id
      ) likes ON likes.video_id = m.id
      LEFT JOIN (
        SELECT video_id, COUNT(*) as count 
        FROM user_favorites 
        WHERE video_id = $2
        GROUP BY video_id
      ) favorites ON favorites.video_id = m.id::text
      WHERE m.id = $1
    `;
    
    const dbResult = await pool.query(dbQuery, [videoId, videoId.toString()]);
    
    const dbStats = dbResult.rows[0] || {
      view_count: 0,
      likes_count: 0,
      favorites_count: 0,
      updated_at: new Date()
    };
    


    // 2. 读取Redis增量数据
    const redisStats = await videoStatsService.getVideoStats(videoId.toString());

    // 3. 融合数据（核心逻辑）
    const dbLikes = Number(dbStats.likes_count) || 0;
    const dbFavorites = Number(dbStats.favorites_count) || 0;
    
    const fusedStats = {
      videoId: inputVideoId,
      numericVideoId: videoId,
      views: (dbStats.view_count || 0) + (redisStats?.views || 0),
      likes: dbLikes + (redisStats?.likes || 0),
      favorites: dbFavorites + (redisStats?.favorites || 0),
      dbBase: {
        views: dbStats.view_count || 0,
        likes: dbLikes,
        favorites: dbFavorites
      },
      redisIncrement: {
        views: redisStats?.views || 0,
        likes: redisStats?.likes || 0,
        favorites: redisStats?.favorites || 0
      },
      lastUpdated: Math.max(
        new Date(dbStats.updated_at).getTime(),
        redisStats?.updatedAt || 0
      ),
      fusionTimestamp: Date.now()
    };

    return res.status(200).json({
      success: true,
      data: fusedStats,
      message: '获取融合统计数据成功'
    });

  } catch (error) {
    console.error('获取融合统计数据失败:', error);
    return res.status(500).json({
      success: false,
      message: '服务器内部错误',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * 批量获取融合统计数据
 */
const batchGetFusedStats = async (req, res) => {
  try {
    const { videoIds } = req.body;
    const pool = req.app.get('pool');

    if (!Array.isArray(videoIds) || videoIds.length === 0) {
      return res.status(400).json({
        success: false,
        message: '无效的视频ID列表'
      });
    }

    // 转换为数字ID列表
    const numericIds = [];
    const idMapping = {};
    
    for (const inputId of videoIds) {
      const numericId = await getVideoIdByMovieId(inputId, pool);
      if (numericId) {
        numericIds.push(numericId);
        idMapping[numericId] = inputId;
      }
    }

    if (numericIds.length === 0) {
      return res.status(200).json({
        success: true,
        data: {},
        message: '没有找到有效的视频'
      });
    }

    // 1. 批量读取数据库数据
    const placeholders = numericIds.map((_, index) => `$${index + 1}`).join(',');
    
    // 分别查询基本信息、likes和favorites
    const baseQuery = `
      SELECT id, view_count, updated_at, movie_id 
      FROM movies 
      WHERE id IN (${placeholders})
    `;
    
    const likesQuery = `
      SELECT video_id, COUNT(*) as count 
      FROM user_likes 
      WHERE video_id IN (${placeholders})
      GROUP BY video_id
    `;
    
    const favoritesQuery = `
      SELECT m.id as movie_num_id, COUNT(uf.*) as count
      FROM movies m
      LEFT JOIN user_favorites uf ON uf.video_id = m.id::text
      WHERE m.id IN (${placeholders})
      GROUP BY m.id
    `;
    
    const [baseResult, likesResult, favoritesResult] = await Promise.all([
      pool.query(baseQuery, numericIds),
      pool.query(likesQuery, numericIds),
      pool.query(favoritesQuery, numericIds)
    ]);
    
    // 构建映射
    const likesMap = {};
    likesResult.rows.forEach(row => {
      likesMap[row.video_id] = row.count;
    });
    
    const favoritesMap = {};
    favoritesResult.rows.forEach(row => {
      favoritesMap[row.movie_num_id] = row.count;
    });
    
    // 构建DB数据映射
    const dbStatsMap = {};
    baseResult.rows.forEach(row => {
      dbStatsMap[row.id] = {
        views: row.view_count || 0,
        likes: parseInt(likesMap[row.id] || 0),
        favorites: parseInt(favoritesMap[row.id] || 0),
        updatedAt: new Date(row.updated_at).getTime()
      };
    });

    // 2. 批量读取Redis数据
    const redisStatsArray = await videoStatsService.batchGetStats(
      numericIds.map(id => id.toString())
    );
    
    // 构建Redis数据映射
    const redisStatsMap = {};
    redisStatsArray.forEach(stats => {
      redisStatsMap[stats.videoId] = {
        views: stats.views || 0,
        likes: stats.likes || 0,
        favorites: stats.favorites || 0,
        updatedAt: stats.updatedAt || 0
      };
    });

    // 3. 融合所有数据
    const fusedStatsObject = {};
    numericIds.forEach(numericId => {
      const inputId = idMapping[numericId];
      const dbStats = dbStatsMap[numericId] || { views: 0, likes: 0, favorites: 0, updatedAt: 0 };
      const redisStats = redisStatsMap[numericId.toString()] || { views: 0, likes: 0, favorites: 0, updatedAt: 0 };

      fusedStatsObject[inputId] = {
        videoId: inputId,
        numericVideoId: numericId,
        views: dbStats.views + redisStats.views,
        likes: dbStats.likes + redisStats.likes,
        favorites: dbStats.favorites + redisStats.favorites,
        dbBase: {
          views: dbStats.views,
          likes: dbStats.likes,
          favorites: dbStats.favorites
        },
        redisIncrement: {
          views: redisStats.views,
          likes: redisStats.likes,
          favorites: redisStats.favorites
        },
        lastUpdated: Math.max(dbStats.updatedAt, redisStats.updatedAt),
        fusionTimestamp: Date.now()
      };
    });

    return res.status(200).json({
      success: true,
      data: fusedStatsObject,
      message: '批量获取融合统计数据成功'
    });

  } catch (error) {
    console.error('批量获取融合统计数据失败:', error);
    return res.status(500).json({
      success: false,
      message: '服务器内部错误',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * 获取需要同步的视频列表（管理员接口）
 */
const getModifiedVideos = async (req, res) => {
  try {
    // 检查管理员权限
    if (!req.user?.isAdmin) {
      return res.status(403).json({
        success: false,
        message: '需要管理员权限'
      });
    }

    const limit = parseInt(req.query.limit) || 100;
    const modifiedVideos = await videoStatsService.getModifiedVideos(limit);

    return res.status(200).json({
      success: true,
      data: modifiedVideos,
      message: '获取修改视频列表成功'
    });

  } catch (error) {
    console.error('获取修改视频列表失败:', error);
    return res.status(500).json({
      success: false,
      message: '服务器内部错误',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * 清理已同步的视频记录（管理员接口）
 */
const clearSyncedVideos = async (req, res) => {
  try {
    // 检查管理员权限
    if (!req.user?.isAdmin) {
      return res.status(403).json({
        success: false,
        message: '需要管理员权限'
      });
    }

    const { videoIds } = req.body;

    if (!Array.isArray(videoIds) || videoIds.length === 0) {
      return res.status(400).json({
        success: false,
        message: '无效的视频ID列表'
      });
    }

    await videoStatsService.clearSyncedVideos(videoIds);

    return res.status(200).json({
      success: true,
      message: '清理同步记录成功'
    });

  } catch (error) {
    console.error('清理同步记录失败:', error);
    return res.status(500).json({
      success: false,
      message: '服务器内部错误',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * 统计系统健康检查
 */
const healthCheck = async (req, res) => {
  try {
    const videoStatsHealth = await videoStatsService.healthCheck();
    const socketService = getSocketService();
    const socketHealth = socketService ? socketService.healthCheck() : {
      status: 'unhealthy',
      details: { message: 'Socket service not available' }
    };

    const overallStatus = videoStatsHealth.status === 'healthy' && socketHealth.status === 'healthy' 
      ? 'healthy' 
      : 'unhealthy';

    return res.status(200).json({
      success: true,
      data: {
        status: overallStatus,
        services: {
          videoStats: videoStatsHealth,
          socket: socketHealth
        },
        timestamp: Date.now()
      },
      message: '健康检查完成'
    });

  } catch (error) {
    console.error('健康检查失败:', error);
    return res.status(500).json({
      success: false,
      message: '健康检查失败',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * 执行数据校验
 */
const performDataValidation = async (req, res) => {
  try {
    const report = await dataValidationService.performFullValidation();
    
    return res.status(200).json({
      success: true,
      data: report,
      message: '数据校验完成'
    });

  } catch (error) {
    console.error('执行数据校验失败:', error);
    return res.status(500).json({
      success: false,
      message: error.message || '数据校验失败',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * 获取最新校验报告
 */
const getValidationReport = async (req, res) => {
  try {
    const report = await dataValidationService.getLastValidationReport();
    
    if (!report) {
      return res.status(200).json({
        success: true,
        data: null,
        message: '暂无校验报告'
      });
    }

    return res.status(200).json({
      success: true,
      data: report,
      message: '获取校验报告成功'
    });

  } catch (error) {
    console.error('获取校验报告失败:', error);
    return res.status(500).json({
      success: false,
      message: '获取校验报告失败',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * 获取补偿日志
 */
const getCompensationLogs = async (req, res) => {
  try {
    const limit = parseInt(req.query.limit) || 50;
    const logs = await dataValidationService.getCompensationLogs(limit);
    
    return res.status(200).json({
      success: true,
      data: logs,
      message: '获取补偿日志成功'
    });

  } catch (error) {
    console.error('获取补偿日志失败:', error);
    return res.status(500).json({
      success: false,
      message: '获取补偿日志失败',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * 数据校验健康检查
 */
const dataValidationHealthCheck = async (req, res) => {
  try {
    const healthStatus = await dataValidationService.healthCheck();
    
    return res.status(200).json({
      success: true,
      data: healthStatus,
      message: '校验系统健康检查完成'
    });

  } catch (error) {
    console.error('校验系统健康检查失败:', error);
    return res.status(500).json({
      success: false,
      message: '校验系统健康检查失败',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

module.exports = {
  incrementVideoStats,
  getVideoStats,
  batchGetVideoStats,
  getFusedVideoStats,
  batchGetFusedStats,
  getModifiedVideos,
  clearSyncedVideos,
  healthCheck,
  performDataValidation,
  getValidationReport,
  getCompensationLogs,
  dataValidationHealthCheck
}; 