const { PrismaClient } = require('../generated/prisma');
const prisma = new PrismaClient();
const { handleApiError } = require('../utils/errorHandler');

// 获取电影列表
exports.getMovies = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const skip = (page - 1) * limit;
    const sort = req.query.sort || 'release_date';
    const order = req.query.order?.toLowerCase() === 'asc' ? 'asc' : 'desc';

    const movies = await prisma.movies.findMany({
      skip,
      take: limit,
      orderBy: {
        [sort]: order,
      },
      include: {
        movie_stars: {
          include: {
            stars: true
          }
        },
        movie_genres: {
          include: {
            genres: true
          }
        },
        directors: true,
        producers: true,
        publishers: true,
        magnets: true,
      },
    });

    // 转换关联数据格式
    const formattedMovies = movies.map(movie => ({
      id: movie.id,
      movie_id: movie.movie_id,
      title: movie.title,
      image_url: movie.image_url,
      release_date: movie.release_date,
      duration: movie.duration,
      description: movie.description,
      view_count: movie.view_count || 0,
      director: movie.directors,
      producer: movie.producers,
      publisher: movie.publishers,
      stars: movie.movie_stars.map(ms => ms.stars),
      genres: movie.movie_genres.map(mg => mg.genres),
      magnets: movie.magnets,
    }));

    const totalItems = await prisma.movies.count();
    const totalPages = Math.ceil(totalItems / limit);

    return res.status(200).json({
      success: true,
      data: {
        items: formattedMovies,
        pagination: {
          currentPage: page,
          totalPages,
          totalItems,
          limit,
        },
      },
      message: '获取影片列表成功',
      code: 200,
    });
  } catch (error) {
    return handleApiError(res, error);
  }
};

// 搜索电影
exports.searchMovies = async (req, res) => {
  try {
    const { keyword } = req.query;
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const skip = (page - 1) * limit;

    if (!keyword) {
      return res.status(400).json({
        success: false,
        message: '搜索关键词不能为空',
        code: 400,
      });
    }

    const movies = await prisma.movies.findMany({
      where: {
        OR: [
          { title: { contains: keyword, mode: 'insensitive' } },
          { movie_id: { contains: keyword, mode: 'insensitive' } },
          { description: { contains: keyword, mode: 'insensitive' } },
        ],
      },
      skip,
      take: limit,
      include: {
        movie_stars: {
          include: {
            stars: true
          }
        },
        movie_genres: {
          include: {
            genres: true
          }
        },
        directors: true,
        producers: true,
        publishers: true,
      },
    });

    // 转换关联数据格式
    const formattedMovies = movies.map(movie => ({
      id: movie.id,
      movie_id: movie.movie_id,
      title: movie.title,
      image_url: movie.image_url,
      release_date: movie.release_date,
      duration: movie.duration,
      description: movie.description,
      view_count: movie.view_count || 0,
      director: movie.directors,
      producer: movie.producers,
      publisher: movie.publishers,
      stars: movie.movie_stars.map(ms => ms.stars),
      genres: movie.movie_genres.map(mg => mg.genres),
    }));

    const totalItems = await prisma.movies.count({
      where: {
        OR: [
          { title: { contains: keyword, mode: 'insensitive' } },
          { movie_id: { contains: keyword, mode: 'insensitive' } },
          { description: { contains: keyword, mode: 'insensitive' } },
        ],
      },
    });
    
    const totalPages = Math.ceil(totalItems / limit);

    return res.status(200).json({
      success: true,
      data: {
        items: formattedMovies,
        pagination: {
          currentPage: page,
          totalPages,
          totalItems,
          limit,
        },
      },
      message: '搜索影片成功',
      code: 200,
    });
  } catch (error) {
    return handleApiError(res, error);
  }
};

// 获取电影详情
exports.getMovieById = async (req, res) => {
  try {
    const { id } = req.params;

    const movie = await prisma.movies.findUnique({
      where: { id: parseInt(id) },
      include: {
        movie_stars: {
          include: {
            stars: true
          }
        },
        movie_genres: {
          include: {
            genres: true
          }
        },
        directors: true,
        producers: true,
        publishers: true,
        magnets: true,
      },
    });

    if (!movie) {
      return res.status(404).json({
        success: false,
        message: '影片不存在',
        code: 404,
      });
    }

    // 转换关联数据格式
    const formattedMovie = {
      id: movie.id,
      movie_id: movie.movie_id,
      title: movie.title,
      image_url: movie.image_url,
      release_date: movie.release_date,
      duration: movie.duration,
      description: movie.description,
      view_count: movie.view_count || 0,
      director: movie.directors,
      producer: movie.producers,
      publisher: movie.publishers,
      stars: movie.movie_stars.map(ms => ms.stars),
      genres: movie.movie_genres.map(mg => mg.genres),
      magnets: movie.magnets,
    };

    return res.status(200).json({
      success: true,
      data: formattedMovie,
      message: '获取影片详情成功',
      code: 200,
    });
  } catch (error) {
    return handleApiError(res, error);
  }
};

// 获取热门电影
exports.getPopularMovies = async (req, res) => {
  try {
    const limit = parseInt(req.query.limit) || 10;

    // 假设我们根据磁力链接数量来确定热门程度
    const movies = await prisma.movies.findMany({
      take: limit,
      include: {
        movie_stars: {
          include: {
            stars: true
          }
        },
        movie_genres: {
          include: {
            genres: true
          }
        },
        magnets: true,
      },
      orderBy: {
        magnets: {
          _count: 'desc',
        },
      },
    });

    // 转换关联数据格式
    const formattedMovies = movies.map(movie => ({
      id: movie.id,
      movie_id: movie.movie_id,
      title: movie.title,
      image_url: movie.image_url,
      release_date: movie.release_date,
      duration: movie.duration,
      view_count: movie.view_count || 0,
      stars: movie.movie_stars.map(ms => ms.stars),
      genres: movie.movie_genres.map(mg => mg.genres),
      magnet_count: movie.magnets.length,
    }));

    return res.status(200).json({
      success: true,
      data: {
        items: formattedMovies,
      },
      message: '获取热门影片成功',
      code: 200,
    });
  } catch (error) {
    return handleApiError(res, error);
  }
};

// 获取最新电影
exports.getRecentMovies = async (req, res) => {
  try {
    const limit = parseInt(req.query.limit) || 10;

    const movies = await prisma.movies.findMany({
      take: limit,
      orderBy: {
        release_date: 'desc',
      },
      include: {
        movie_stars: {
          include: {
            stars: true
          }
        },
        movie_genres: {
          include: {
            genres: true
          }
        },
      },
    });

    // 转换关联数据格式
    const formattedMovies = movies.map(movie => ({
      id: movie.id,
      movie_id: movie.movie_id,
      title: movie.title,
      image_url: movie.image_url,
      release_date: movie.release_date,
      duration: movie.duration,
      view_count: movie.view_count || 0,
      stars: movie.movie_stars.map(ms => ms.stars),
      genres: movie.movie_genres.map(mg => mg.genres),
    }));

    return res.status(200).json({
      success: true,
      data: {
        items: formattedMovies,
      },
      message: '获取最新影片成功',
      code: 200,
    });
  } catch (error) {
    return handleApiError(res, error);
  }
};

// 获取指定类型的电影
exports.getMoviesByGenre = async (req, res) => {
  try {
    const { genreId } = req.params;
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const skip = (page - 1) * limit;

    const movies = await prisma.movies.findMany({
      where: {
        movie_genres: {
          some: {
            genre_id: parseInt(genreId),
          },
        },
      },
      skip,
      take: limit,
      include: {
        movie_stars: {
          include: {
            stars: true
          }
        },
        movie_genres: {
          include: {
            genres: true
          }
        },
      },
    });

    // 转换关联数据格式
    const formattedMovies = movies.map(movie => ({
      id: movie.id,
      movie_id: movie.movie_id,
      title: movie.title,
      image_url: movie.image_url,
      release_date: movie.release_date,
      duration: movie.duration,
      view_count: movie.view_count || 0,
      stars: movie.movie_stars.map(ms => ms.stars),
      genres: movie.movie_genres.map(mg => mg.genres),
    }));

    const totalItems = await prisma.movies.count({
      where: {
        movie_genres: {
          some: {
            genre_id: parseInt(genreId),
          },
        },
      },
    });

    const totalPages = Math.ceil(totalItems / limit);

    return res.status(200).json({
      success: true,
      data: {
        items: formattedMovies,
        pagination: {
          currentPage: page,
          totalPages,
          totalItems,
          limit,
        },
      },
      message: '获取类型影片成功',
      code: 200,
    });
  } catch (error) {
    return handleApiError(res, error);
  }
};

// 获取指定演员的电影
exports.getMoviesByStar = async (req, res) => {
  try {
    const { starId } = req.params;
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const skip = (page - 1) * limit;

    const movies = await prisma.movies.findMany({
      where: {
        movie_stars: {
          some: {
            star_id: parseInt(starId),
          },
        },
      },
      skip,
      take: limit,
      include: {
        movie_stars: {
          include: {
            stars: true
          }
        },
        movie_genres: {
          include: {
            genres: true
          }
        },
      },
    });

    // 转换关联数据格式
    const formattedMovies = movies.map(movie => ({
      id: movie.id,
      movie_id: movie.movie_id,
      title: movie.title,
      image_url: movie.image_url,
      release_date: movie.release_date,
      duration: movie.duration,
      view_count: movie.view_count || 0,
      stars: movie.movie_stars.map(ms => ms.stars),
      genres: movie.movie_genres.map(mg => mg.genres),
    }));

    const totalItems = await prisma.movies.count({
      where: {
        movie_stars: {
          some: {
            star_id: parseInt(starId),
          },
        },
      },
    });

    const totalPages = Math.ceil(totalItems / limit);

    return res.status(200).json({
      success: true,
      data: {
        items: formattedMovies,
        pagination: {
          currentPage: page,
          totalPages,
          totalItems,
          limit,
        },
      },
      message: '获取演员影片成功',
      code: 200,
    });
  } catch (error) {
    return handleApiError(res, error);
  }
};

// 获取电影磁力链接
exports.getMovieMagnets = async (req, res) => {
  try {
    const { movieId } = req.params;
    
    console.log(`开始获取磁力链接，movieId: ${movieId}, 类型: ${typeof movieId}`);
    
    // 健壮性处理：确保有效的movieId
    if (!movieId) {
      return res.status(400).json({
        success: false,
        message: '请提供有效的影片ID',
        code: 400,
      });
    }
    
    // 注意：影片ID有两种形式
    // 1. 数字ID - 数据库中的 id 字段，如 1, 2, 3...
    // 2. 字符串ID - 数据库中的 movie_id 字段，如 CJOD-466, DASS-630...
    
    let movie = null;
    let magnets = [];
    
    try {
      // 首先，通过影片代码(movie_id)查找电影信息
      movie = await prisma.movies.findFirst({
        where: {
          movie_id: {
            equals: movieId.toString(),
            mode: 'insensitive', // 不区分大小写
          }
        },
        select: {
          id: true,
          movie_id: true,
          title: true,
        },
      });
      
      console.log('查询结果:', movie);
      
      if (movie) {
        console.log(`找到影片: ${movie.title}, ID: ${movie.id}`);
        
        // 然后通过找到的数字ID查询磁力链接
        magnets = await prisma.magnets.findMany({
          where: {
            movie_id: movie.id,
          },
          orderBy: {
            size: 'desc',
          },
        });
        console.log(`查询到${magnets.length}个磁力链接`);
      } 
      // 如果找不到影片，尝试将movieId作为数字ID直接查询
      else if (!isNaN(movieId)) {
        const numericId = parseInt(movieId);
        console.log(`尝试直接使用数字ID: ${numericId}`);
        
        // 先检查电影是否存在
        movie = await prisma.movies.findUnique({
          where: {
            id: numericId,
          },
          select: {
            id: true,
            movie_id: true,
            title: true,
          },
        });
        
        if (movie) {
          console.log(`找到电影: ${movie.title}`);
          
          // 再查询磁力链接
          magnets = await prisma.magnets.findMany({
            where: {
              movie_id: numericId,
            },
            orderBy: {
              size: 'desc',
            },
          });
          console.log(`查询到${magnets.length}个磁力链接`);
        } else {
          console.log(`未找到ID为${numericId}的电影`);
        }
      } else {
        console.log(`未找到影片: ${movieId}`);
      }
    } catch (queryError) {
      console.error('查询出错:', queryError);
    }

    // 如果最终没有找到磁力链接，返回404
    if (!magnets || magnets.length === 0) {
      return res.status(404).json({
        success: false,
        message: `未找到影片 "${movieId}" 的磁力链接`,
        code: 404,
      });
    }

    return res.status(200).json({
      success: true,
      data: {
        items: magnets,
        movie: movie ? {
          id: movie.id,
          movie_id: movie.movie_id,
          title: movie.title,
        } : null,
      },
      message: '获取影片磁力链接成功',
      code: 200,
    });
  } catch (error) {
    console.error(`获取磁力链接时发生未预期错误:`, error);
    return handleApiError(res, error);
  }
};

// 增加电影观看次数
exports.incrementMovieViewCount = async (req, res) => {
  try {
    const { id } = req.params;
    const movieId = parseInt(id);

    if (isNaN(movieId)) {
      return res.status(400).json({
        success: false,
        message: '无效的影片ID',
        code: 400,
      });
    }

    // 检查影片是否存在
    const movie = await prisma.movies.findUnique({
      where: { id: movieId },
      select: { id: true, view_count: true }
    });

    if (!movie) {
      return res.status(404).json({
        success: false,
        message: '影片不存在',
        code: 404,
      });
    }

    // 增加观看次数
    const updatedMovie = await prisma.movies.update({
      where: { id: movieId },
      data: {
        view_count: {
          increment: 1
        }
      },
      select: {
        id: true,
        view_count: true
      }
    });

    return res.status(200).json({
      success: true,
      data: {
        id: updatedMovie.id,
        view_count: updatedMovie.view_count
      },
      message: '观看次数更新成功',
      code: 200,
    });
  } catch (error) {
    return handleApiError(res, error);
  }
};

// 减少电影观看次数
exports.decrementMovieViewCount = async (req, res) => {
  try {
    const { id } = req.params;
    const movieId = parseInt(id);

    if (isNaN(movieId)) {
      return res.status(400).json({
        success: false,
        message: '无效的影片ID',
        code: 400,
      });
    }

    // 检查影片是否存在
    const movie = await prisma.movies.findUnique({
      where: { id: movieId },
      select: { id: true, view_count: true }
    });

    if (!movie) {
      return res.status(404).json({
        success: false,
        message: '影片不存在',
        code: 404,
      });
    }

    // 确保观看次数不会小于0
    const newViewCount = Math.max(0, (movie.view_count || 0) - 1);

    // 减少观看次数
    const updatedMovie = await prisma.movies.update({
      where: { id: movieId },
      data: {
        view_count: newViewCount
      },
      select: {
        id: true,
        view_count: true
      }
    });

    return res.status(200).json({
      success: true,
      data: {
        id: updatedMovie.id,
        view_count: updatedMovie.view_count
      },
      message: '观看次数减少成功',
      code: 200,
    });
  } catch (error) {
    return handleApiError(res, error);
  }
};

// 批量获取电影观看次数
exports.batchGetMovieViewCounts = async (req, res) => {
  try {
    const { codes } = req.body;

    if (!codes || !Array.isArray(codes) || codes.length === 0) {
      return res.status(400).json({
        success: false,
        message: '请提供有效的影片代码数组',
        code: 400,
      });
    }

    // 批量查询影片观看次数
    const movies = await prisma.movies.findMany({
      where: {
        movie_id: {
          in: codes
        }
      },
      select: {
        id: true,
        movie_id: true,
        view_count: true
      }
    });

    return res.status(200).json({
      success: true,
      data: movies,
      message: '批量获取观看次数成功',
      code: 200,
    });
  } catch (error) {
    return handleApiError(res, error);
  }
};
