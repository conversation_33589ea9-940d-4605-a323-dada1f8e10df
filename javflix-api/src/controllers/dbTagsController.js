const { PrismaClient } = require('../generated/prisma');
const prisma = new PrismaClient();
const { handleApiError } = require('../utils/errorHandler');

// 获取标签列表
exports.getTags = async (req, res) => {
  try {
    const sort = req.query.sort || 'name';
    const limit = parseInt(req.query.limit) || 100;
    const order = req.query.order?.toLowerCase() === 'asc' ? 'asc' : 'desc';

    const genres = await prisma.genres.findMany({
      take: limit,
      orderBy: {
        [sort]: order,
      },
      include: {
        _count: {
          select: {
            movie_genres: true,
          },
        },
      },
    });

    // 格式化数据
    const formattedGenres = genres.map(genre => ({
      id: genre.id,
      name: genre.name,
      movie_count: genre._count.movie_genres,
    }));

    return res.status(200).json({
      success: true,
      data: {
        items: formattedGenres,
      },
      message: '获取标签列表成功',
      code: 200,
    });
  } catch (error) {
    return handleApiError(res, error);
  }
};

// 获取标签详情
exports.getTagById = async (req, res) => {
  try {
    const { id } = req.params;
    let genre = null;

    // 首先尝试将参数作为数字ID查找
    const numericId = parseInt(id);
    if (!isNaN(numericId)) {
      genre = await prisma.genres.findUnique({
        where: { id: numericId },
        include: {
          _count: {
            select: {
              movie_genres: true,
            },
          },
          movie_genres: {
            take: 10,
            include: {
              movies: {
                include: {
                  movie_stars: {
                    include: {
                      stars: true
                    }
                  }
                }
              }
            }
          }
        },
      });
    }

    // 如果按ID没找到，尝试按名称查找（先解码URL编码）
    if (!genre) {
      try {
        const decodedName = decodeURIComponent(id);
        genre = await prisma.genres.findFirst({
          where: { name: decodedName },
          include: {
            _count: {
              select: {
                movie_genres: true,
              },
            },
            movie_genres: {
              take: 10,
              include: {
                movies: {
                  include: {
                    movie_stars: {
                      include: {
                        stars: true
                      }
                    }
                  }
                }
              }
            }
          },
        });
      } catch (decodeError) {
        console.error('Failed to decode URL parameter:', decodeError);
        // 如果解码失败，尝试直接按原字符串查找
        genre = await prisma.genres.findFirst({
          where: { name: id },
          include: {
            _count: {
              select: {
                movie_genres: true,
              },
            },
            movie_genres: {
              take: 10,
              include: {
                movies: {
                  include: {
                    movie_stars: {
                      include: {
                        stars: true
                      }
                    }
                  }
                }
              }
            }
          },
        });
      }
    }

    if (!genre) {
      return res.status(404).json({
        success: false,
        message: `标签不存在: ${id}`,
        code: 404,
      });
    }

    // 格式化数据
    const formattedGenre = {
      id: genre.id,
      name: genre.name,
      movie_count: genre._count.movie_genres,
      sample_movies: genre.movie_genres.map(mg => ({
        id: mg.movies.id,
        movie_id: mg.movies.movie_id,
        title: mg.movies.title,
        image_url: mg.movies.image_url,
        release_date: mg.movies.release_date,
        stars: mg.movies.movie_stars.map(ms => ms.stars),
      })),
    };

    return res.status(200).json({
      success: true,
      data: formattedGenre,
      message: '获取标签详情成功',
      code: 200,
    });
  } catch (error) {
    return handleApiError(res, error);
  }
};

// 获取热门标签
exports.getPopularTags = async (req, res) => {
  try {
    const limit = parseInt(req.query.limit) || 20;

    // 根据关联电影数量排序
    const genres = await prisma.genres.findMany({
      take: limit,
      include: {
        _count: {
          select: {
            movie_genres: true,
          },
        },
      },
      orderBy: {
        movie_genres: {
          _count: 'desc',
        },
      },
    });

    // 格式化数据
    const formattedGenres = genres.map(genre => ({
      id: genre.id,
      name: genre.name,
      movie_count: genre._count.movie_genres,
    }));

    return res.status(200).json({
      success: true,
      data: {
        items: formattedGenres,
      },
      message: '获取热门标签成功',
      code: 200,
    });
  } catch (error) {
    return handleApiError(res, error);
  }
}; 