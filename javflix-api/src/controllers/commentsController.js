const { pool } = require('../config/db');

// 获取视频评论列表
const getComments = async (req, res) => {
  try {
    const { videoId } = req.params;
    const { page = 1, limit = 20, sort = 'created_at', order = 'DESC' } = req.query;
    const offset = (page - 1) * limit;

    // 获取评论及其点赞状态（如果用户已登录）
    const query = `
      WITH comment_stats AS (
        SELECT 
          c.id,
          c.video_id,
          c.user_id,
          c.content,
          c.likes,
          c.parent_id,
          c.created_at,
          c.updated_at,
          COUNT(cl.id) as actual_likes,
          ${req.user ? `
          CASE WHEN uc.comment_id IS NOT NULL THEN true ELSE false END as user_liked
          ` : 'false as user_liked'}
        FROM comments c
        LEFT JOIN comment_likes cl ON c.id = cl.comment_id
        ${req.user ? `
        LEFT JOIN comment_likes uc ON c.id = uc.comment_id AND uc.user_id = $3
        ` : ''}
        WHERE c.video_id = $1 AND c.is_deleted = false
        GROUP BY c.id${req.user ? ', uc.comment_id' : ''}
      )
      SELECT 
        cs.*,
        -- 获取回复数量
        (SELECT COUNT(*) FROM comments WHERE parent_id = cs.id AND is_deleted = false) as reply_count
      FROM comment_stats cs
      WHERE cs.parent_id IS NULL
      ORDER BY ${sort} ${order}
      LIMIT $2 OFFSET ${offset}
    `;

    const params = req.user ? [videoId, limit, req.user.id] : [videoId, limit];
    const result = await pool.query(query, params);

    // 获取回复评论
    for (let comment of result.rows) {
      if (comment.reply_count > 0) {
        const repliesQuery = `
          SELECT 
            c.id,
            c.video_id,
            c.user_id,
            c.content,
            c.likes,
            c.parent_id,
            c.created_at,
            c.updated_at,
            COUNT(cl.id) as actual_likes,
            ${req.user ? `
            CASE WHEN uc.comment_id IS NOT NULL THEN true ELSE false END as user_liked
            ` : 'false as user_liked'}
          FROM comments c
          LEFT JOIN comment_likes cl ON c.id = cl.comment_id
          ${req.user ? `
          LEFT JOIN comment_likes uc ON c.id = uc.comment_id AND uc.user_id = $2
          ` : ''}
          WHERE c.parent_id = $1 AND c.is_deleted = false
          GROUP BY c.id${req.user ? ', uc.comment_id' : ''}
          ORDER BY c.created_at ASC
        `;

        const repliesParams = req.user ? [comment.id, req.user.id] : [comment.id];
        const repliesResult = await pool.query(repliesQuery, repliesParams);
        comment.replies = repliesResult.rows;
      } else {
        comment.replies = [];
      }
    }

    // 获取总数
    const countQuery = `
      SELECT COUNT(*) as total 
      FROM comments 
      WHERE video_id = $1 AND parent_id IS NULL AND is_deleted = false
    `;
    const countResult = await pool.query(countQuery, [videoId]);
    const total = parseInt(countResult.rows[0].total);

    res.json({
      success: true,
      data: {
        comments: result.rows,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          totalPages: Math.ceil(total / limit)
        }
      }
    });

  } catch (error) {
    console.error('Error fetching comments:', error);
    res.status(500).json({
      success: false,
      message: '获取评论失败',
      error: error.message
    });
  }
};

// 添加评论
const addComment = async (req, res) => {
  try {
    const { video_id, content } = req.body;
    // 使用认证用户ID，如果没有则使用默认测试用户ID
    const user_id = req.user?.id || 'anonymous_user';
    // 额外保存用户名（如果可用）
    const username = req.user?.username || null;

    if (!video_id || !content) {
      return res.status(400).json({
        success: false,
        message: '视频ID和评论内容不能为空'
      });
    }

    if (content.trim().length < 1) {
      return res.status(400).json({
        success: false,
        message: '评论内容不能为空'
      });
    }

    if (content.length > 1000) {
      return res.status(400).json({
        success: false,
        message: '评论内容不能超过1000字符'
      });
    }

    const query = `
      INSERT INTO comments (video_id, user_id, content, username, created_at, updated_at)
      VALUES ($1, $2, $3, $4, NOW(), NOW())
      RETURNING id, video_id, user_id, content, username, likes, parent_id, created_at, updated_at
    `;

    const result = await pool.query(query, [video_id, user_id, content.trim(), username]);
    const comment = result.rows[0];
    
    // 添加统计信息
    comment.actual_likes = 0;
    comment.user_liked = false;
    comment.reply_count = 0;
    comment.replies = [];

    res.status(201).json({
      success: true,
      message: '评论添加成功',
      data: comment
    });

  } catch (error) {
    console.error('Error adding comment:', error);
    res.status(500).json({
      success: false,
      message: '添加评论失败',
      error: error.message
    });
  }
};

// 回复评论
const replyComment = async (req, res) => {
  try {
    const { commentId } = req.params;
    const { content } = req.body;
    const user_id = req.user?.id || 'anonymous_user';
    const username = req.user?.username || null;

    if (!content) {
      return res.status(400).json({
        success: false,
        message: '回复内容不能为空'
      });
    }

    if (content.trim().length < 1) {
      return res.status(400).json({
        success: false,
        message: '回复内容不能为空'
      });
    }

    if (content.length > 1000) {
      return res.status(400).json({
        success: false,
        message: '回复内容不能超过1000字符'
      });
    }

    // 检查父评论是否存在
    const parentCheck = await pool.query(
      'SELECT id, video_id FROM comments WHERE id = $1 AND is_deleted = false',
      [commentId]
    );

    if (parentCheck.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: '父评论不存在'
      });
    }

    const parentComment = parentCheck.rows[0];

    const query = `
      INSERT INTO comments (video_id, user_id, content, username, parent_id, created_at, updated_at)
      VALUES ($1, $2, $3, $4, $5, NOW(), NOW())
      RETURNING id, video_id, user_id, content, username, likes, parent_id, created_at, updated_at
    `;

    const result = await pool.query(query, [
      parentComment.video_id,
      user_id,
      content.trim(),
      username,
      commentId
    ]);
    
    const reply = result.rows[0];
    
    // 添加统计信息
    reply.actual_likes = 0;
    reply.user_liked = false;

    res.status(201).json({
      success: true,
      message: '回复添加成功',
      data: reply
    });

  } catch (error) {
    console.error('Error adding reply:', error);
    res.status(500).json({
      success: false,
      message: '添加回复失败',
      error: error.message
    });
  }
};

// 点赞/取消点赞评论
const toggleLike = async (req, res) => {
  try {
    const { commentId } = req.params;
    const user_id = req.user.id;

    // 检查评论是否存在
    const commentCheck = await pool.query(
      'SELECT id FROM comments WHERE id = $1 AND is_deleted = false',
      [commentId]
    );

    if (commentCheck.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: '评论不存在'
      });
    }

    // 检查是否已经点赞
    const likeCheck = await pool.query(
      'SELECT id FROM comment_likes WHERE comment_id = $1 AND user_id = $2',
      [commentId, user_id]
    );

    let isLiked = false;
    let likesCount = 0;

    if (likeCheck.rows.length > 0) {
      // 已点赞，取消点赞
      await pool.query(
        'DELETE FROM comment_likes WHERE comment_id = $1 AND user_id = $2',
        [commentId, user_id]
      );
      isLiked = false;
    } else {
      // 未点赞，添加点赞
      await pool.query(
        'INSERT INTO comment_likes (comment_id, user_id, created_at) VALUES ($1, $2, NOW())',
        [commentId, user_id]
      );
      isLiked = true;
    }

    // 获取最新点赞数
    const countResult = await pool.query(
      'SELECT COUNT(*) as count FROM comment_likes WHERE comment_id = $1',
      [commentId]
    );
    likesCount = parseInt(countResult.rows[0].count);

    res.json({
      success: true,
      message: isLiked ? '点赞成功' : '取消点赞成功',
      data: {
        isLiked,
        likesCount
      }
    });

  } catch (error) {
    console.error('Error toggling like:', error);
    res.status(500).json({
      success: false,
      message: '操作失败',
      error: error.message
    });
  }
};

// 删除评论
const deleteComment = async (req, res) => {
  try {
    const { commentId } = req.params;
    const user_id = req.user.id;

    // 检查评论是否存在且属于当前用户
    const commentCheck = await pool.query(
      'SELECT id, user_id FROM comments WHERE id = $1 AND is_deleted = false',
      [commentId]
    );

    if (commentCheck.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: '评论不存在'
      });
    }

    const comment = commentCheck.rows[0];
    if (comment.user_id !== user_id) {
      return res.status(403).json({
        success: false,
        message: '无权删除此评论'
      });
    }

    // 软删除评论（保留记录但标记为已删除）
    await pool.query(
      'UPDATE comments SET is_deleted = true, updated_at = NOW() WHERE id = $1',
      [commentId]
    );

    res.json({
      success: true,
      message: '评论删除成功'
    });

  } catch (error) {
    console.error('Error deleting comment:', error);
    res.status(500).json({
      success: false,
      message: '删除评论失败',
      error: error.message
    });
  }
};

// 举报评论
const reportComment = async (req, res) => {
  try {
    const { commentId } = req.params;
    const { reason } = req.body;
    const user_id = req.user.id;

    if (!reason) {
      return res.status(400).json({
        success: false,
        message: '举报原因不能为空'
      });
    }

    // 检查评论是否存在
    const commentCheck = await pool.query(
      'SELECT id FROM comments WHERE id = $1 AND is_deleted = false',
      [commentId]
    );

    if (commentCheck.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: '评论不存在'
      });
    }

    // 这里可以实现举报逻辑，比如保存到举报表
    // 暂时返回成功响应
    res.json({
      success: true,
      message: '举报提交成功，我们会尽快处理'
    });

  } catch (error) {
    console.error('Error reporting comment:', error);
    res.status(500).json({
      success: false,
      message: '举报失败',
      error: error.message
    });
  }
};

// 获取评论统计信息
const getCommentsStats = async (req, res) => {
  try {
    const { videoId } = req.params;

    const query = `
      SELECT 
        COUNT(*) as total_comments,
        COUNT(CASE WHEN parent_id IS NULL THEN 1 END) as root_comments,
        COUNT(CASE WHEN parent_id IS NOT NULL THEN 1 END) as reply_comments
      FROM comments 
      WHERE video_id = $1 AND is_deleted = false
    `;

    const result = await pool.query(query, [videoId]);
    const stats = result.rows[0];

    res.json({
      success: true,
      data: {
        totalComments: parseInt(stats.total_comments),
        rootComments: parseInt(stats.root_comments),
        replyComments: parseInt(stats.reply_comments)
      }
    });

  } catch (error) {
    console.error('Error fetching comments stats:', error);
    res.status(500).json({
      success: false,
      message: '获取评论统计失败',
      error: error.message
    });
  }
};

module.exports = {
  getComments,
  addComment,
  replyComment,
  toggleLike,
  deleteComment,
  reportComment,
  getCommentsStats
}; 