const pool = require('../config/database');
const apiResponse = require('../utils/apiResponse');
const MovieProcessingService = require('../services/MovieProcessingService');

/**
 * 获取待处理影片列表
 */
exports.getPendingMovies = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const offset = (page - 1) * limit;
    
    const { rows } = await pool.query(
      `SELECT 
        m.id, m.movie_id, m.title, m.image_url, m.cached_image_url,
        m.release_date, m.duration, m.status, m.processing_priority,
        m.created_at, m.updated_at,
        COUNT(mag.id) as magnet_count
      FROM movies m 
      LEFT JOIN magnets mag ON m.id = mag.movie_id
      WHERE m.status = 'draft'
      GROUP BY m.id
      ORDER BY m.processing_priority DESC, m.created_at ASC
      LIMIT $1 OFFSET $2`,
      [limit, offset]
    );
    
    // 获取总数
    const countResult = await pool.query(
      'SELECT COUNT(*) FROM movies WHERE status = $1',
      ['draft']
    );
    const total = parseInt(countResult.rows[0].count);
    const totalPages = Math.ceil(total / limit);
    
    return apiResponse.success(res, {
      movies: rows,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    }, '待处理影片获取成功');
  } catch (error) {
    console.error('获取待处理影片失败:', error);
    return apiResponse.error(res, '获取待处理影片失败', 500, error);
  }
};

/**
 * 获取处理中影片列表
 */
exports.getProcessingMovies = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const offset = (page - 1) * limit;
    
    const { rows } = await pool.query(
      `SELECT 
        m.id, m.movie_id, m.title, m.image_url, m.cached_image_url,
        m.release_date, m.duration, m.status, m.processing_priority,
        m.processing_started_at, m.created_at,
        vpt.task_uuid, vpt.progress, vpt.status as task_status,
        vpt.started_at as task_started_at
      FROM movies m 
      LEFT JOIN video_processing_tasks vpt ON m.id = vpt.movie_id
      WHERE m.status = 'processing'
      ORDER BY m.processing_priority DESC, m.processing_started_at ASC
      LIMIT $1 OFFSET $2`,
      [limit, offset]
    );
    
    // 获取总数
    const countResult = await pool.query(
      'SELECT COUNT(*) FROM movies WHERE status = $1',
      ['processing']
    );
    const total = parseInt(countResult.rows[0].count);
    const totalPages = Math.ceil(total / limit);
    
    return apiResponse.success(res, {
      movies: rows,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    }, '处理中影片获取成功');
  } catch (error) {
    console.error('获取处理中影片失败:', error);
    return apiResponse.error(res, '获取处理中影片失败', 500, error);
  }
};

/**
 * 获取已处理影片列表
 */
exports.getProcessedMovies = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const offset = (page - 1) * limit;
    
    const { rows } = await pool.query(
      `SELECT 
        m.id, m.movie_id, m.title, m.image_url, m.cached_image_url,
        m.release_date, m.duration, m.status, m.processing_priority,
        m.processing_completed_at, m.video_urls,
        CASE 
          WHEN m.video_urls IS NOT NULL AND jsonb_array_length(COALESCE(m.video_urls->'qualities', '[]'::jsonb)) > 0 
          THEN true 
          ELSE false 
        END as has_video_urls
      FROM movies m 
      WHERE m.status = 'processed'
      ORDER BY m.processing_completed_at DESC
      LIMIT $1 OFFSET $2`,
      [limit, offset]
    );
    
    // 获取总数
    const countResult = await pool.query(
      'SELECT COUNT(*) FROM movies WHERE status = $1',
      ['processed']
    );
    const total = parseInt(countResult.rows[0].count);
    const totalPages = Math.ceil(total / limit);
    
    return apiResponse.success(res, {
      movies: rows,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    }, '已处理影片获取成功');
  } catch (error) {
    console.error('获取已处理影片失败:', error);
    return apiResponse.error(res, '获取已处理影片失败', 500, error);
  }
};

/**
 * 获取已发布影片列表（管理员视图）
 */
exports.getPublishedMovies = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const offset = (page - 1) * limit;
    
    const { rows } = await pool.query(
      `SELECT 
        m.id, m.movie_id, m.title, m.image_url, m.cached_image_url,
        m.release_date, m.duration, m.status, m.view_count,
        m.published_at, m.video_urls,
        CASE 
          WHEN m.video_urls IS NOT NULL AND jsonb_array_length(COALESCE(m.video_urls->'qualities', '[]'::jsonb)) > 0 
          THEN true 
          ELSE false 
        END as has_video_urls
      FROM movies m 
      WHERE m.status = 'published'
      ORDER BY m.published_at DESC
      LIMIT $1 OFFSET $2`,
      [limit, offset]
    );
    
    // 获取总数
    const countResult = await pool.query(
      'SELECT COUNT(*) FROM movies WHERE status = $1',
      ['published']
    );
    const total = parseInt(countResult.rows[0].count);
    const totalPages = Math.ceil(total / limit);
    
    return apiResponse.success(res, {
      movies: rows,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    }, '已发布影片获取成功');
  } catch (error) {
    console.error('获取已发布影片失败:', error);
    return apiResponse.error(res, '获取已发布影片失败', 500, error);
  }
};

/**
 * 获取影片处理统计信息
 */
exports.getProcessingStats = async (req, res) => {
  try {
    const statsResult = await pool.query(`
      SELECT 
        status,
        COUNT(*) as count
      FROM movies 
      GROUP BY status
    `);
    
    const stats = {
      draft: 0,
      processing: 0,
      processed: 0,
      published: 0,
      failed: 0,
      total: 0
    };
    
    statsResult.rows.forEach(row => {
      stats[row.status] = parseInt(row.count);
      stats.total += parseInt(row.count);
    });
    
    // 获取今日新增统计
    const todayResult = await pool.query(`
      SELECT 
        status,
        COUNT(*) as count
      FROM movies 
      WHERE DATE(created_at) = CURRENT_DATE
      GROUP BY status
    `);
    
    const todayStats = {
      draft: 0,
      processing: 0,
      processed: 0,
      published: 0,
      failed: 0,
      total: 0
    };
    
    todayResult.rows.forEach(row => {
      todayStats[row.status] = parseInt(row.count);
      todayStats.total += parseInt(row.count);
    });
    
    // 获取处理任务统计
    const taskStatsResult = await pool.query(`
      SELECT 
        status,
        COUNT(*) as count
      FROM video_processing_tasks 
      GROUP BY status
    `);
    
    const taskStats = {
      pending: 0,
      running: 0,
      completed: 0,
      failed: 0,
      cancelled: 0
    };
    
    taskStatsResult.rows.forEach(row => {
      taskStats[row.status] = parseInt(row.count);
    });
    
    return apiResponse.success(res, {
      movieStats: stats,
      todayStats: todayStats,
      taskStats: taskStats
    }, '统计信息获取成功');
  } catch (error) {
    console.error('获取统计信息失败:', error);
    return apiResponse.error(res, '获取统计信息失败', 500, error);
  }
};

/**
 * 开始处理影片
 */
exports.startProcessing = async (req, res) => {
  try {
    const { movieIds, config } = req.body;
    
    if (!movieIds || !Array.isArray(movieIds) || movieIds.length === 0) {
      return apiResponse.badRequest(res, '请提供要处理的影片ID列表');
    }
    
    const movieProcessingService = new MovieProcessingService();
    const results = await movieProcessingService.startProcessing(movieIds, config);
    
    return apiResponse.success(res, { results }, '影片处理任务已创建');
  } catch (error) {
    console.error('开始处理影片失败:', error);
    return apiResponse.error(res, '开始处理影片失败', 500, error);
  }
};

/**
 * 发布影片
 */
exports.publishMovies = async (req, res) => {
  try {
    const { movieIds } = req.body;
    
    if (!movieIds || !Array.isArray(movieIds) || movieIds.length === 0) {
      return apiResponse.badRequest(res, '请提供要发布的影片ID列表');
    }
    
    const movieProcessingService = new MovieProcessingService();
    const results = await movieProcessingService.publishMovies(movieIds);
    
    return apiResponse.success(res, { results }, '影片发布成功');
  } catch (error) {
    console.error('发布影片失败:', error);
    return apiResponse.error(res, '发布影片失败', 500, error);
  }
};

/**
 * 处理视频处理完成回调
 */
exports.handleProcessingCallback = async (req, res) => {
  try {
    const { taskUuid, result } = req.body;
    
    if (!taskUuid) {
      return apiResponse.badRequest(res, '缺少任务UUID');
    }
    
    const movieProcessingService = new MovieProcessingService();
    await movieProcessingService.handleProcessingResult(taskUuid, result);
    
    return apiResponse.success(res, null, '回调处理成功');
  } catch (error) {
    console.error('处理回调失败:', error);
    return apiResponse.error(res, '处理回调失败', 500, error);
  }
};

/**
 * 更新影片处理优先级
 */
exports.updatePriority = async (req, res) => {
  try {
    const { movieId, priority } = req.body;
    
    if (!movieId || priority === undefined) {
      return apiResponse.badRequest(res, '请提供影片ID和优先级');
    }
    
    if (priority < 1 || priority > 10) {
      return apiResponse.badRequest(res, '优先级必须在1-10之间');
    }
    
    await pool.query(
      'UPDATE movies SET processing_priority = $1 WHERE id = $2',
      [priority, movieId]
    );
    
    return apiResponse.success(res, null, '优先级更新成功');
  } catch (error) {
    console.error('更新优先级失败:', error);
    return apiResponse.error(res, '更新优先级失败', 500, error);
  }
};

/**
 * 取消处理任务
 */
exports.cancelTask = async (req, res) => {
  try {
    const { taskId } = req.params;
    
    // 更新任务状态为取消
    const result = await pool.query(
      'UPDATE video_processing_tasks SET status = $1 WHERE task_uuid = $2 RETURNING movie_id',
      ['cancelled', taskId]
    );
    
    if (result.rows.length === 0) {
      return apiResponse.notFound(res, '任务不存在');
    }
    
    // 将影片状态重置为draft
    await pool.query(
      'UPDATE movies SET status = $1 WHERE id = $2',
      ['draft', result.rows[0].movie_id]
    );
    
    return apiResponse.success(res, null, '任务已取消');
  } catch (error) {
    console.error('取消任务失败:', error);
    return apiResponse.error(res, '取消任务失败', 500, error);
  }
};

/**
 * 获取处理任务列表
 */
exports.getProcessingTasks = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const offset = (page - 1) * limit;
    const status = req.query.status;
    
    let whereClause = '';
    let params = [limit, offset];
    
    if (status) {
      whereClause = 'WHERE vpt.status = $3';
      params.push(status);
    }
    
    const { rows } = await pool.query(
      `SELECT 
        vpt.*, 
        m.movie_id, m.title, m.image_url, m.cached_image_url
      FROM video_processing_tasks vpt
      JOIN movies m ON vpt.movie_id = m.id
      ${whereClause}
      ORDER BY vpt.created_at DESC
      LIMIT $1 OFFSET $2`,
      params
    );
    
    // 获取总数
    const countParams = status ? [status] : [];
    const countResult = await pool.query(
      `SELECT COUNT(*) FROM video_processing_tasks vpt ${status ? 'WHERE vpt.status = $1' : ''}`,
      countParams
    );
    const total = parseInt(countResult.rows[0].count);
    const totalPages = Math.ceil(total / limit);
    
    return apiResponse.success(res, {
      tasks: rows,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    }, '处理任务获取成功');
  } catch (error) {
    console.error('获取处理任务失败:', error);
    return apiResponse.error(res, '获取处理任务失败', 500, error);
  }
};

/**
 * 获取单个任务详情
 */
exports.getTaskDetails = async (req, res) => {
  try {
    const { taskId } = req.params;
    
    const { rows } = await pool.query(
      `SELECT 
        vpt.*, 
        m.movie_id, m.title, m.image_url, m.cached_image_url, m.status as movie_status
      FROM video_processing_tasks vpt
      JOIN movies m ON vpt.movie_id = m.id
      WHERE vpt.task_uuid = $1`,
      [taskId]
    );
    
    if (rows.length === 0) {
      return apiResponse.notFound(res, '任务不存在');
    }
    
    return apiResponse.success(res, { task: rows[0] }, '任务详情获取成功');
  } catch (error) {
    console.error('获取任务详情失败:', error);
    return apiResponse.error(res, '获取任务详情失败', 500, error);
  }
};
