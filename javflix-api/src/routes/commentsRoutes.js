const express = require('express');
const commentsController = require('../controllers/commentsController');
const { auth } = require('../middleware/auth');

const router = express.Router();

// 获取视频评论列表
// GET /api/comments/:videoId
router.get('/:videoId', commentsController.getComments);

// 添加评论
// POST /api/comments
router.post('/', auth, commentsController.addComment);

// 回复评论
// POST /api/comments/:commentId/reply
router.post('/:commentId/reply', auth, commentsController.replyComment);

// 点赞/取消点赞评论
// POST /api/comments/:commentId/like
router.post('/:commentId/like', auth, commentsController.toggleLike);

// 删除评论（只能删除自己的评论）
// DELETE /api/comments/:commentId
router.delete('/:commentId', auth, commentsController.deleteComment);

// 举报评论
// POST /api/comments/:commentId/report
router.post('/:commentId/report', auth, commentsController.reportComment);

// 获取评论统计信息
// GET /api/comments/:videoId/stats
router.get('/:videoId/stats', commentsController.getCommentsStats);

module.exports = router; 