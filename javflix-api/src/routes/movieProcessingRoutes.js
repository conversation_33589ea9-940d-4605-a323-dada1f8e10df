// 影片处理相关路由
const express = require('express');
const router = express.Router();
const MovieProcessingService = require('../services/MovieProcessingService');
const { checkAdmin } = require('../middleware/auth');
const apiResponse = require('../utils/apiResponse');

const movieProcessingService = new MovieProcessingService();

/**
 * @route   GET /api/movie-processing/pending
 * @desc    获取待处理影片列表
 * @access  管理员
 */
router.get('/pending', checkAdmin, async (req, res) => {
  try {
    const { page = 1, limit = 20, status } = req.query;
    
    const result = await movieProcessingService.getPendingMovies(
      parseInt(page), 
      parseInt(limit), 
      status
    );
    
    return apiResponse.success(res, result);
  } catch (error) {
    console.error('获取待处理影片列表失败:', error);
    return apiResponse.error(res, '获取待处理影片列表失败');
  }
});

/**
 * @route   POST /api/movie-processing/start
 * @desc    开始处理影片
 * @access  管理员
 */
router.post('/start', checkAdmin, async (req, res) => {
  try {
    const { movieIds, config = {} } = req.body;
    
    if (!movieIds || !Array.isArray(movieIds) || movieIds.length === 0) {
      return apiResponse.error(res, '请选择要处理的影片');
    }
    
    const results = await movieProcessingService.startProcessing(movieIds, config);
    
    const successCount = results.filter(r => r.success).length;
    const failCount = results.length - successCount;
    
    return apiResponse.success(res, {
      results,
      summary: {
        total: results.length,
        success: successCount,
        failed: failCount
      }
    }, `成功启动${successCount}个处理任务，失败${failCount}个`);
    
  } catch (error) {
    console.error('启动影片处理失败:', error);
    return apiResponse.error(res, '启动影片处理失败');
  }
});

/**
 * @route   POST /api/movie-processing/publish
 * @desc    发布影片
 * @access  管理员
 */
router.post('/publish', checkAdmin, async (req, res) => {
  try {
    const { movieIds } = req.body;
    
    if (!movieIds || !Array.isArray(movieIds) || movieIds.length === 0) {
      return apiResponse.error(res, '请选择要发布的影片');
    }
    
    const results = await movieProcessingService.publishMovies(movieIds);
    
    const successCount = results.filter(r => r.success).length;
    const failCount = results.length - successCount;
    
    return apiResponse.success(res, {
      results,
      summary: {
        total: results.length,
        success: successCount,
        failed: failCount
      }
    }, `成功发布${successCount}个影片，失败${failCount}个`);
    
  } catch (error) {
    console.error('发布影片失败:', error);
    return apiResponse.error(res, '发布影片失败');
  }
});

/**
 * @route   GET /api/movie-processing/stats
 * @desc    获取处理统计
 * @access  管理员
 */
router.get('/stats', checkAdmin, async (req, res) => {
  try {
    const stats = await movieProcessingService.getProcessingStats();
    return apiResponse.success(res, stats);
  } catch (error) {
    console.error('获取处理统计失败:', error);
    return apiResponse.error(res, '获取处理统计失败');
  }
});

/**
 * @route   POST /api/movie-processing/callback
 * @desc    处理完成回调 (Go服务器调用)
 * @access  内部API
 */
router.post('/callback', async (req, res) => {
  try {
    const { taskUuid, success, videoUrls, error } = req.body;
    
    if (!taskUuid) {
      return apiResponse.error(res, '缺少taskUuid参数');
    }
    
    const result = {
      success,
      videoUrls: videoUrls || {},
      error: error || null
    };
    
    await movieProcessingService.onProcessingComplete(taskUuid, result);
    
    return apiResponse.success(res, { message: '回调处理成功' });
    
  } catch (error) {
    console.error('处理回调失败:', error);
    return apiResponse.error(res, '处理回调失败');
  }
});

/**
 * @route   PUT /api/movie-processing/priority
 * @desc    更新影片处理优先级
 * @access  管理员
 */
router.put('/priority', checkAdmin, async (req, res) => {
  try {
    const { movieId, priority } = req.body;
    
    if (!movieId || priority === undefined) {
      return apiResponse.error(res, '缺少必要参数');
    }
    
    if (priority < 1 || priority > 10) {
      return apiResponse.error(res, '优先级必须在1-10之间');
    }
    
    const { pool } = require('../config/db');
    await pool.query(
      'UPDATE movies SET processing_priority = $1 WHERE id = $2',
      [priority, movieId]
    );
    
    return apiResponse.success(res, { message: '优先级更新成功' });
    
  } catch (error) {
    console.error('更新优先级失败:', error);
    return apiResponse.error(res, '更新优先级失败');
  }
});

/**
 * @route   DELETE /api/movie-processing/cancel/:movieId
 * @desc    取消影片处理
 * @access  管理员
 */
router.delete('/cancel/:movieId', checkAdmin, async (req, res) => {
  try {
    const { movieId } = req.params;
    
    const { pool } = require('../config/db');
    
    // 检查影片状态
    const movieResult = await pool.query(
      'SELECT status FROM movies WHERE id = $1',
      [movieId]
    );
    
    if (movieResult.rows.length === 0) {
      return apiResponse.notFound(res, '影片不存在');
    }
    
    const movie = movieResult.rows[0];
    
    if (movie.status !== 'processing') {
      return apiResponse.error(res, '只能取消处理中的影片');
    }
    
    // 更新状态为draft
    await pool.query(
      'UPDATE movies SET status = $1 WHERE id = $2',
      ['draft', movieId]
    );
    
    // TODO: 通知Go服务器取消任务
    
    return apiResponse.success(res, { message: '影片处理已取消' });
    
  } catch (error) {
    console.error('取消影片处理失败:', error);
    return apiResponse.error(res, '取消影片处理失败');
  }
});

/**
 * @route   GET /api/movie-processing/queue
 * @desc    获取处理队列状态
 * @access  管理员
 */
router.get('/queue', checkAdmin, async (req, res) => {
  try {
    const { pool } = require('../config/db');
    
    const query = `
      SELECT 
        m.id,
        m.movie_id,
        m.title,
        m.status,
        m.processing_priority,
        vpt.progress,
        vpt.status as task_status,
        vpt.created_at as task_created_at
      FROM movies m
      LEFT JOIN video_processing_tasks vpt ON m.id = vpt.movie_id
      WHERE m.status = 'processing'
      ORDER BY m.processing_priority DESC, vpt.created_at ASC
    `;
    
    const result = await pool.query(query);
    
    return apiResponse.success(res, {
      queue: result.rows,
      count: result.rows.length
    });
    
  } catch (error) {
    console.error('获取处理队列失败:', error);
    return apiResponse.error(res, '获取处理队列失败');
  }
});

/**
 * @route   POST /api/movie-processing/start-real
 * @desc    开始真实的视频处理（发送到Go服务器）
 * @access  管理员
 */
router.post('/start-real', checkAdmin, async (req, res) => {
  try {
    const { movieIds, config = {} } = req.body;

    if (!movieIds || !Array.isArray(movieIds) || movieIds.length === 0) {
      return apiResponse.error(res, '请选择要处理的影片');
    }

    const results = await movieProcessingService.startRealProcessing(movieIds, config);

    const successCount = results.filter(r => r.success).length;
    const failCount = results.length - successCount;

    return apiResponse.success(res, {
      results,
      summary: {
        total: results.length,
        success: successCount,
        failed: failCount
      }
    }, `成功启动${successCount}个真实处理任务，失败${failCount}个`);

  } catch (error) {
    console.error('启动真实影片处理失败:', error);
    return apiResponse.error(res, '启动真实影片处理失败');
  }
});

/**
 * @route   POST /api/movie-processing/progress
 * @desc    接收Go服务器的进度更新
 * @access  内部API
 */
router.post('/progress', async (req, res) => {
  try {
    const { taskUuid, progress, status, message, completed, total } = req.body;

    if (!taskUuid) {
      return apiResponse.error(res, '缺少taskUuid参数');
    }

    // 传递所有字段给服务层，包括completed和total
    await movieProcessingService.updateTaskProgress(taskUuid, progress, status, message, completed, total);

    return apiResponse.success(res, { message: '进度更新成功' });

  } catch (error) {
    console.error('进度更新失败:', error);
    return apiResponse.error(res, '进度更新失败');
  }
});

module.exports = router;
