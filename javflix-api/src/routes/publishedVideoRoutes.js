// 已发布影片路由 - 用户前端专用
const express = require('express');
const router = express.Router();
const publishedVideoController = require('../controllers/publishedVideoController');
const { rateLimiter } = require('../middleware/rateLimiter');

/**
 * @route   GET /api/published-videos
 * @desc    获取已发布影片列表
 * @access  公开
 */
router.get('/', 
  rateLimiter({ maxRequests: 100, windowMs: 60000 }), // 每分钟100次请求
  publishedVideoController.getPublishedVideos
);

/**
 * @route   GET /api/published-videos/popular
 * @desc    获取热门已发布影片
 * @access  公开
 */
router.get('/popular', 
  rateLimiter({ maxRequests: 50, windowMs: 60000 }),
  publishedVideoController.getPopularPublishedVideos
);

/**
 * @route   GET /api/published-videos/recent
 * @desc    获取最新已发布影片
 * @access  公开
 */
router.get('/recent', 
  rateLimiter({ maxRequests: 50, windowMs: 60000 }),
  publishedVideoController.getRecentPublishedVideos
);

/**
 * @route   GET /api/published-videos/search
 * @desc    搜索已发布影片
 * @access  公开
 */
router.get('/search', 
  rateLimiter({ maxRequests: 30, windowMs: 60000 }),
  publishedVideoController.searchPublishedVideos
);

/**
 * @route   GET /api/published-videos/:id
 * @desc    获取单个已发布影片详情
 * @access  公开
 */
router.get('/:id', 
  rateLimiter({ maxRequests: 200, windowMs: 60000 }),
  publishedVideoController.getPublishedVideoById
);

module.exports = router;
