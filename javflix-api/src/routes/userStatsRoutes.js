/**
 * 用户统计路由
 * 处理前端对用户统计数据的请求，转发到视频统计API
 */

const express = require('express');
const router = express.Router();
const {
  incrementVideoStats,
  getVideoStats,
  batchGetVideoStats,
  getFusedVideoStats,
  batchGetFusedStats
} = require('../controllers/statsController');

/**
 * @route   GET /api/users/stats/:videoId
 * @desc    获取视频统计信息（转发到视频统计API）
 * @access  公开
 */
router.get('/:videoId', getFusedVideoStats);

/**
 * @route   POST /api/users/stats/:videoId/increment
 * @desc    增加视频统计（转发到视频统计API）
 * @access  需要认证（对于likes和favorites）
 */
router.post('/:videoId/increment', incrementVideoStats);

/**
 * @route   POST /api/users/stats/:videoId/views
 * @desc    增加视频观看数（无需认证）
 * @access  公开
 */
router.post('/:videoId/views', (req, res, next) => {
  // 将views请求转换为increment请求
  req.body = {
    statType: 'views',
    delta: req.body.delta || 1
  };
  incrementVideoStats(req, res, next);
});

/**
 * @route   POST /api/users/stats/batch
 * @desc    批量获取视频统计信息（转发到视频统计API）
 * @access  公开
 */
router.post('/batch', batchGetFusedStats);

/**
 * @route   GET /api/users/stats/health
 * @desc    统计系统健康检查
 * @access  公开
 */
router.get('/health', (req, res) => {
  res.json({
    success: true,
    data: {
      status: 'healthy',
      services: {
        userStats: { status: 'healthy' },
        videoStats: { status: 'healthy' }
      }
    },
    message: '用户统计系统运行正常'
  });
});

module.exports = router;
