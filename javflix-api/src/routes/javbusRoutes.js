const express = require('express');
const router = express.Router();
const javbusController = require('../controllers/javbusController');

// 获取影片列表
router.get('/movies', javbusController.getMovies);

// 搜索影片
router.get('/search', javbusController.searchMovies);

// 获取影片详情
router.get('/movies/:movieId', javbusController.getMovieDetail);

// 获取影片磁力链接
router.get('/magnets/:movieId', javbusController.getMovieMagnets);

// 获取演员详情
router.get('/stars/:starId', javbusController.getStarDetail);

module.exports = router; 