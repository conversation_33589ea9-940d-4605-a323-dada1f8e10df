const express = require('express');
const router = express.Router();
const {
  incrementVideoStats,
  getVideoStats,
  batchGetVideoStats,
  getFusedVideoStats,
  batchGetFusedStats,
  getModifiedVideos,
  clearSyncedVideos,
  healthCheck,
  performDataValidation,
  getValidationReport,
  getCompensationLogs,
  dataValidationHealthCheck
} = require('../controllers/statsController');

// 中间件：简单的认证检查
const authenticate = (req, res, next) => {
  // 这里可以添加认证逻辑
  // 暂时跳过认证检查，实际应用中需要验证token
  next();
};

// 管理员认证中间件
const requireAdmin = (req, res, next) => {
  // 检查用户是否为管理员
  if (!req.user?.isAdmin) {
    return res.status(403).json({
      success: false,
      message: '需要管理员权限'
    });
  }
  next();
};

// === 核心融合API路由 ===
/**
 * @route   GET /api/video-stats/fused/:videoId
 * @desc    获取融合统计数据（DB累计 + Redis增量）
 * @access  公开
 */
router.get('/fused/:videoId', getFusedVideoStats);

/**
 * @route   POST /api/video-stats/fused/batch
 * @desc    批量获取融合统计数据
 * @access  公开
 */
router.post('/fused/batch', batchGetFusedStats);

// === 系统健康检查路由（必须在动态路由之前） ===
/**
 * @route   GET /api/video-stats/health
 * @desc    统计系统健康检查
 * @access  公开
 */
router.get('/health', healthCheck);

// === 传统Redis统计API路由 ===
/**
 * @route   POST /api/video-stats/batch
 * @desc    批量获取多个视频统计信息（仅Redis数据）
 * @access  公开
 */
router.post('/batch', batchGetVideoStats);

/**
 * @route   POST /api/video-stats/:videoId/increment
 * @desc    增加视频统计
 * @access  需要认证（对于likes和favorites）
 */
router.post('/:videoId/increment', incrementVideoStats);

/**
 * @route   GET /api/video-stats/:videoId
 * @desc    获取视频统计信息（仅Redis数据）
 * @access  公开
 */
router.get('/:videoId', getVideoStats);

// === 数据校验API路由 ===
/**
 * @route   POST /api/video-stats/validation/run
 * @desc    执行完整数据校验
 * @access  管理员
 */
router.post('/validation/run', authenticate, requireAdmin, performDataValidation);

/**
 * @route   GET /api/video-stats/validation/report
 * @desc    获取最新校验报告
 * @access  管理员
 */
router.get('/validation/report', authenticate, requireAdmin, getValidationReport);

/**
 * @route   GET /api/video-stats/validation/logs
 * @desc    获取补偿日志
 * @access  管理员
 */
router.get('/validation/logs', authenticate, requireAdmin, getCompensationLogs);

/**
 * @route   GET /api/video-stats/validation/health
 * @desc    数据校验系统健康检查
 * @access  公开
 */
router.get('/validation/health', dataValidationHealthCheck);

// === 管理员API路由 ===
/**
 * @route   GET /api/video-stats/admin/modified
 * @desc    获取需要同步的视频列表
 * @access  管理员
 */
router.get('/admin/modified', authenticate, requireAdmin, getModifiedVideos);

/**
 * @route   POST /api/video-stats/admin/clear-synced
 * @desc    清理已同步的视频记录
 * @access  管理员
 */
router.post('/admin/clear-synced', authenticate, requireAdmin, clearSyncedVideos);

module.exports = router; 