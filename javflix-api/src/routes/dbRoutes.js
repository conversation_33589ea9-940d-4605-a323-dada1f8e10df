const express = require('express');
const router = express.Router();

// 导入控制器
const dbMoviesController = require('../controllers/dbMoviesController');
const dbStarsController = require('../controllers/dbStarsController');
const dbTagsController = require('../controllers/dbTagsController');
const dbStatsController = require('../controllers/dbStatsController');

// 影片路由
router.get('/movies', dbMoviesController.getMovies);
router.get('/movies/search', dbMoviesController.searchMovies);
router.get('/movies/popular', dbMoviesController.getPopularMovies);
router.get('/movies/recent', dbMoviesController.getRecentMovies);
router.get('/movies/:id', dbMoviesController.getMovieById);
router.get('/movies/genre/:genreId', dbMoviesController.getMoviesByGenre);
router.get('/movies/star/:starId', dbMoviesController.getMoviesByStar);
router.get('/movies/:movieId/magnets', dbMoviesController.getMovieMagnets);
router.post('/movies/:id/view', dbMoviesController.incrementMovieViewCount);
router.delete('/movies/:id/view', dbMoviesController.decrementMovieViewCount);
router.post('/movies/batch-views', dbMoviesController.batchGetMovieViewCounts);

// 演员路由
router.get('/stars', dbStarsController.getStars);
router.get('/stars/search', dbStarsController.searchStars);
router.get('/stars/popular', dbStarsController.getPopularStars);
router.get('/stars/:id', dbStarsController.getStarById);

// 标签路由
router.get('/genres', dbTagsController.getTags);
router.get('/genres/popular', dbTagsController.getPopularTags);
router.get('/genres/:id', dbTagsController.getTagById);

// 统计路由
router.get('/stats/movies', dbStatsController.getMovieStats);
router.get('/stats/stars', dbStatsController.getStarStats);
router.get('/stats/genres', dbStatsController.getTagStats);
router.get('/stats/magnets', dbStatsController.getMagnetStats);

module.exports = router; 