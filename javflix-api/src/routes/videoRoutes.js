const express = require('express');
const router = express.Router();
const apiResponse = require('../utils/apiResponse');
const { Pool } = require('pg');
const videoController = require('../controllers/videoController');
const auth = require('../middleware/auth');

// 创建连接池
const pool = new Pool({
  host: process.env.PGHOST || 'localhost',
  port: parseInt(process.env.PGPORT || '5432'),
  database: process.env.PGDATABASE || 'javflix',
  user: process.env.PGUSER || 'longgedemacminim4',
  password: process.env.PGPASSWORD || '',
});

/**
 * @route   GET /api/videos
 * @desc    获取所有视频，支持分页
 * @access  公开
 */
router.get('/', videoController.getAllVideos);

/**
 * @route   GET /api/videos/popular
 * @desc    获取热门视频
 * @access  公开
 */
router.get('/popular', async (req, res) => {
  try {
    const limit = parseInt(req.query.limit) || 10;
    
    const { rows } = await pool.query(
      'SELECT * FROM videos ORDER BY views DESC LIMIT $1',
      [limit]
    );
    
    return apiResponse.success(res, { videos: rows }, '热门视频获取成功');
  } catch (error) {
    console.error('获取热门视频失败:', error);
    return apiResponse.error(res, '获取热门视频失败', 500, error);
  }
});

/**
 * @route   GET /api/videos/recent
 * @desc    获取最新视频
 * @access  公开
 */
router.get('/recent', async (req, res) => {
  try {
    const limit = parseInt(req.query.limit) || 10;
    
    const { rows } = await pool.query(
      'SELECT * FROM videos ORDER BY "releaseDate" DESC LIMIT $1',
      [limit]
    );
    
    return apiResponse.success(res, { videos: rows }, '最新视频获取成功');
  } catch (error) {
    console.error('获取最新视频失败:', error);
    return apiResponse.error(res, '获取最新视频失败', 500, error);
  }
});

/**
 * @route   GET /api/videos/search
 * @desc    搜索视频
 * @access  公开
 */
router.get('/search', videoController.searchVideos);
    
/**
 * @route   GET /api/videos/stars/:id
 * @desc    获取演员详情
 * @access  公开
 */
router.get('/stars/:id', videoController.getStarById);

/**
 * @route   GET /api/videos/slug/:slug
 * @desc    通过Slug获取视频详情
 * @access  公开
 */
router.get('/slug/:slug', async (req, res) => {
  try {
    const { slug } = req.params;
    
    if (!slug) {
      return apiResponse.validationError(res, '无效的视频Slug');
    }
    
    // 获取视频信息 - 使用code字段代替slug
    const videoResult = await pool.query('SELECT * FROM videos WHERE code = $1', [slug]);
    
    if (videoResult.rows.length === 0) {
      return apiResponse.notFound(res, '视频不存在');
    }
    
    const video = videoResult.rows[0];
    
    // 获取关联女优信息
    const actressesResult = await pool.query(
      `SELECT a.* FROM actresses a 
       JOIN video_actresses va ON a.id = va.actress_id 
       WHERE va.video_id = $1`,
      [video.id]
    );
    
    // 更新观看次数
    await pool.query(
      'UPDATE videos SET views = views + 1 WHERE id = $1',
      [video.id]
    );
    
    // 组合数据返回
    video.actresses = actressesResult.rows;
    
    return apiResponse.success(res, { video }, '视频详情获取成功');
  } catch (error) {
    console.error('获取视频详情失败:', error);
    return apiResponse.error(res, '获取视频详情失败', 500, error);
  }
});

/**
 * @route   GET /api/videos/:id
 * @desc    通过ID获取视频详情
 * @access  公开
 */
router.get('/:id', videoController.getVideoById);

// ===== 管理API路由 =====

/**
 * @route   POST /api/videos
 * @desc    添加新视频
 * @access  私有(仅管理员)
 */
router.post('/', auth.checkAdmin, videoController.addVideo);

/**
 * @route   PUT /api/videos/:id
 * @desc    更新视频信息
 * @access  私有(仅管理员)
 */
router.put('/:id', auth.checkAdmin, videoController.updateVideo);

/**
 * @route   DELETE /api/videos/:id
 * @desc    删除视频
 * @access  私有(仅管理员)
 */
router.delete('/:id', auth.checkAdmin, videoController.deleteVideo);

/**
 * @route   POST /api/videos/clean-javbus
 * @desc    清空所有JavBus数据
 * @access  私有(仅管理员)
 */
router.post('/clean-javbus', auth.checkAdmin, videoController.cleanJavbusData);

module.exports = router; 