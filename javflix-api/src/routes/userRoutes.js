const express = require('express');
const router = express.Router();
const bcrypt = require('bcrypt');
const jwt = require('jsonwebtoken');
const { Pool } = require('pg');

// 创建数据库连接池
const pool = new Pool({
  host: process.env.PGHOST || 'localhost',
  port: parseInt(process.env.PGPORT || '5432'),
  database: process.env.PGDATABASE || 'javflix',
  user: process.env.PGUSER || 'longgedemacminim4',
  password: process.env.PGPASSWORD || '',
});

// 通用响应格式
const sendResponse = (res, success, data = null, message = '', code = 200) => {
  res.status(code).json({
    success,
    code,
    message,
    data,
    timestamp: new Date().toISOString()
  });
};

// 认证中间件
const authenticate = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      console.log('认证失败: 缺少或无效的Authorization头', {
        authHeader,
        userAgent: req.headers['user-agent'],
        ip: req.ip || req.connection.remoteAddress
      });
      return sendResponse(res, false, null, '请提供有效的认证令牌', 401);
    }
    
    const token = authHeader.split(' ')[1];
    
    // 验证token格式
    if (!token || token.trim() === '') {
      console.log('认证失败: Token为空');
      return sendResponse(res, false, null, '认证令牌不能为空', 401);
    }
    
    // 验证JWT格式 (应该有3个部分，用点分隔)
    const tokenParts = token.split('.');
    if (tokenParts.length !== 3) {
      console.log('认证失败: Token格式不正确', {
        token: token.substring(0, 20) + '...',
        parts: tokenParts.length,
        tokenLength: token.length
      });
      return sendResponse(res, false, null, '认证令牌格式不正确', 401);
    }
    
    const secret = process.env.JWT_SECRET || 'javflix_secret_key';
    
    let decoded;
    try {
      decoded = jwt.verify(token, secret);
    } catch (jwtError) {
      console.log('认证失败: JWT验证错误', {
        error: jwtError.message,
        name: jwtError.name,
        token: token.substring(0, 20) + '...',
        secret: secret.substring(0, 10) + '...'
      });
      
      if (jwtError.name === 'TokenExpiredError') {
        return sendResponse(res, false, null, '认证令牌已过期，请重新登录', 401);
      } else if (jwtError.name === 'JsonWebTokenError') {
        return sendResponse(res, false, null, '认证令牌无效，请重新登录', 401);
      } else {
        return sendResponse(res, false, null, '认证令牌验证失败', 401);
      }
    }
    
    if (!decoded || !decoded.id) {
      console.log('认证失败: Token载荷无效', { decoded });
      return sendResponse(res, false, null, '认证令牌载荷无效', 401);
    }
    
    // 检查用户是否存在
    const userResult = await pool.query('SELECT * FROM users WHERE id = $1', [decoded.id]);
    
    if (userResult.rows.length === 0) {
      console.log('认证失败: 用户不存在', { userId: decoded.id });
      return sendResponse(res, false, null, '无效的用户令牌', 401);
    }
    
    req.user = userResult.rows[0];
    delete req.user.password; // 移除密码
    
    next();
  } catch (error) {
    console.error('认证失败: 未预期的错误', {
      error: error.message,
      stack: error.stack,
      authHeader: req.headers.authorization ? req.headers.authorization.substring(0, 30) + '...' : 'undefined'
    });
    return sendResponse(res, false, null, '认证失败，服务器内部错误', 500);
  }
};

// 生成JWT令牌
const generateToken = (userId) => {
  const secret = process.env.JWT_SECRET || 'javflix_secret_key';
  return jwt.sign({ id: userId }, secret, { expiresIn: '7d' });
};

/**
 * @route   POST /api/users/register
 * @desc    用户注册
 * @access  公开
 */
router.post('/register', async (req, res) => {
  try {
    const { username, email, password } = req.body;
    
    if (!username || !email || !password) {
      return sendResponse(res, false, null, '用户名、邮箱和密码都是必填项', 400);
    }
    
    // 检查邮箱是否已存在
    const emailCheck = await pool.query('SELECT * FROM users WHERE email = $1', [email]);
    if (emailCheck.rows.length > 0) {
      return sendResponse(res, false, null, '该邮箱已被注册', 400);
    }
    
    // 检查用户名是否已存在
    const usernameCheck = await pool.query('SELECT * FROM users WHERE username = $1', [username]);
    if (usernameCheck.rows.length > 0) {
      return sendResponse(res, false, null, '该用户名已被使用', 400);
    }
    
    // 加密密码
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(password, salt);
    
    // 创建用户
    const now = new Date();
    const userResult = await pool.query(
      `INSERT INTO users (username, email, password, is_admin, is_active, created_at, updated_at) 
       VALUES ($1, $2, $3, $4, $5, $6, $7) RETURNING *`,
      [username, email, hashedPassword, false, true, now, now]
    );
    
    const newUser = userResult.rows[0];
    delete newUser.password;
    
    const token = generateToken(newUser.id);
    
    return sendResponse(res, true, { user: newUser, token }, '注册成功', 201);
  } catch (error) {
    console.error('注册失败:', error);
    return sendResponse(res, false, null, '注册失败', 500);
  }
});

/**
 * @route   POST /api/users/login
 * @desc    用户登录
 * @access  公开
 */
router.post('/login', async (req, res) => {
  try {
    const { email, password } = req.body;
    
    if (!email || !password) {
      return sendResponse(res, false, null, '邮箱和密码都是必填项', 400);
    }
    
    // 查找用户
    const userResult = await pool.query('SELECT * FROM users WHERE email = $1', [email]);
    if (userResult.rows.length === 0) {
      return sendResponse(res, false, null, '邮箱或密码错误', 401);
    }
    
    const user = userResult.rows[0];
    
    // 检查密码
    const isPasswordValid = await bcrypt.compare(password, user.password);
    if (!isPasswordValid) {
      return sendResponse(res, false, null, '邮箱或密码错误', 401);
    }
    
    // 更新最后登录时间
    await pool.query('UPDATE users SET "lastLoginAt" = $1 WHERE id = $2', [new Date(), user.id]);
    
    delete user.password;
    const token = generateToken(user.id);
    
    return sendResponse(res, true, { user, token }, '登录成功');
  } catch (error) {
    console.error('登录失败:', error);
    return sendResponse(res, false, null, '登录失败', 500);
  }
});

/**
 * @route   GET /api/users/verify-token
 * @desc    验证token有效性（调试用）
 * @access  公开
 */
router.get('/verify-token', async (req, res) => {
  try {
    const authHeader = req.headers.authorization;
    
    console.log('Token验证请求:', {
      authHeader: authHeader ? authHeader.substring(0, 30) + '...' : 'undefined',
      userAgent: req.headers['user-agent'],
      ip: req.ip || req.connection.remoteAddress
    });
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return sendResponse(res, false, null, '缺少认证头', 400);
    }
    
    const token = authHeader.split(' ')[1];
    
    const response = {
      hasToken: !!token,
      tokenLength: token ? token.length : 0,
      tokenParts: token ? token.split('.').length : 0,
      tokenPrefix: token ? token.substring(0, 20) + '...' : 'N/A'
    };
    
    if (!token || token.trim() === '') {
      return sendResponse(res, false, response, 'Token为空', 400);
    }
    
    if (token.split('.').length !== 3) {
      return sendResponse(res, false, response, 'Token格式不正确，应包含3个部分', 400);
    }
    
    const secret = process.env.JWT_SECRET || 'javflix_secret_key';
    
    try {
      const decoded = jwt.verify(token, secret);
      response.valid = true;
      response.payload = { id: decoded.id };
      
      // 检查用户是否存在
      const userResult = await pool.query('SELECT id, username, email FROM users WHERE id = $1', [decoded.id]);
      
      if (userResult.rows.length === 0) {
        return sendResponse(res, false, response, '用户不存在', 404);
      }
      
      response.user = userResult.rows[0];
      return sendResponse(res, true, response, 'Token有效');
      
    } catch (jwtError) {
      response.valid = false;
      response.error = jwtError.message;
      response.errorType = jwtError.name;
      
      return sendResponse(res, false, response, `Token验证失败: ${jwtError.message}`, 401);
    }
    
  } catch (error) {
    console.error('Token验证错误:', error);
    return sendResponse(res, false, null, '验证过程出错', 500);
  }
});

/**
 * @route   GET /api/users/profile
 * @desc    获取用户信息
 * @access  私有
 */
router.get('/profile', authenticate, async (req, res) => {
  try {
    return sendResponse(res, true, { user: req.user }, '获取用户信息成功');
  } catch (error) {
    console.error('获取用户信息失败:', error);
    return sendResponse(res, false, null, '获取用户信息失败', 500);
  }
});

/**
 * @route   GET /api/users/profile/stats
 * @desc    获取用户个人统计信息
 * @access  私有
 */
router.get('/profile/stats', authenticate, async (req, res) => {
  try {
    const userId = req.user.id;

    // 获取基础统计数据
    const statsPromises = [
      // 点赞总数
      pool.query('SELECT COUNT(*) as count FROM user_likes WHERE user_id = $1', [userId]),
      // 收藏总数  
      pool.query('SELECT COUNT(*) as count FROM user_favorites WHERE user_id = $1', [userId]),
      // 观看历史总数
      pool.query('SELECT COUNT(*) as count FROM watch_history WHERE user_id = $1', [userId]),
      // 关注演员总数
      pool.query('SELECT COUNT(*) as count FROM user_following WHERE user_id = $1', [userId]),
      // 用户创建时间
      pool.query('SELECT created_at FROM users WHERE id = $1', [userId])
    ];

    const [
      likesResult,
      favoritesResult,
      historyResult,
      followingResult,
      userResult
    ] = await Promise.all(statsPromises);

    // 计算数据
    const totalLikes = parseInt(likesResult.rows[0]?.count || 0);
    const totalFavorites = parseInt(favoritesResult.rows[0]?.count || 0);
    const totalWatched = parseInt(historyResult.rows[0]?.count || 0);
    const totalFollowing = parseInt(followingResult.rows[0]?.count || 0);

    // 计算加入天数
    const userCreatedAt = userResult.rows[0]?.created_at;
    const joinDate = userCreatedAt ? new Date(userCreatedAt) : new Date();
    const now = new Date();
    const joinDays = Math.floor((now.getTime() - joinDate.getTime()) / (1000 * 60 * 60 * 24));

    // 估算观看时长（假设平均90分钟/部）
    const estimatedWatchMinutes = totalWatched * 90;
    const totalWatchHours = Math.floor(estimatedWatchMinutes / 60);

    // 计算用户等级（基于活跃度）
    const activityScore = totalLikes + totalFavorites * 2 + totalWatched * 3 + totalFollowing;
    let userLevel = 1;
    if (activityScore >= 500) userLevel = 10;
    else if (activityScore >= 300) userLevel = 9;
    else if (activityScore >= 200) userLevel = 8;
    else if (activityScore >= 150) userLevel = 7;
    else if (activityScore >= 100) userLevel = 6;
    else if (activityScore >= 70) userLevel = 5;
    else if (activityScore >= 50) userLevel = 4;
    else if (activityScore >= 30) userLevel = 3;
    else if (activityScore >= 15) userLevel = 2;

    const stats = {
      totalLikes,
      totalFavorites,
      totalWatched,
      totalFollowing,
      totalWatchTime: `${totalWatchHours}小时`,
      totalWatchMinutes: estimatedWatchMinutes,
      completionRate: totalWatched > 0 ? '85%' : '0%', // 简化估算
      joinDays,
      userLevel,
      activityScore
    };

    return sendResponse(res, true, stats, '获取用户统计信息成功');
  } catch (error) {
    console.error('获取用户统计信息失败:', error);
    return sendResponse(res, false, null, '获取用户统计信息失败', 500);
  }
});

/**
 * @route   GET /api/users/likes
 * @desc    获取用户点赞的视频列表
 * @access  私有
 */
router.get('/likes', authenticate, async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const offset = (page - 1) * limit;
    
    const likesResult = await pool.query(
      `SELECT v.*, ul.created_at as liked_at
       FROM movies v
       JOIN user_likes ul ON v.id = ul.video_id
       WHERE ul.user_id = $1
       ORDER BY ul.created_at DESC
       LIMIT $2 OFFSET $3`,
      [req.user.id, limit, offset]
    );
    
    const countResult = await pool.query(
      'SELECT COUNT(*) FROM user_likes WHERE user_id = $1',
      [req.user.id]
    );
    const total = parseInt(countResult.rows[0].count);
    
    return sendResponse(res, true, {
      data: likesResult.rows,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    }, '获取点赞视频列表成功');
  } catch (error) {
    console.error('获取点赞视频列表失败:', error);
    return sendResponse(res, false, null, '获取点赞视频列表失败', 500);
  }
});

/**
 * @route   GET /api/users/favorites
 * @desc    获取用户收藏视频
 * @access  私有
 */
router.get('/favorites', authenticate, async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const offset = (page - 1) * limit;
    
    const favoritesResult = await pool.query(
      `SELECT v.*, uf.created_at as favorited_at
       FROM movies v
       JOIN user_favorites uf ON v.id::varchar = uf.video_id
       WHERE uf.user_id = $1
       ORDER BY uf.created_at DESC
       LIMIT $2 OFFSET $3`,
      [req.user.id, limit, offset]
    );
    
    const countResult = await pool.query(
      'SELECT COUNT(*) FROM user_favorites WHERE user_id = $1',
      [req.user.id]
    );
    const total = parseInt(countResult.rows[0].count);
    
    return sendResponse(res, true, {
      videos: favoritesResult.rows,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    }, '获取收藏视频成功');
  } catch (error) {
    console.error('获取收藏视频失败:', error);
    return sendResponse(res, false, null, '获取收藏视频失败', 500);
  }
});

/**
 * @route   GET /api/users/watch-history
 * @desc    获取用户观看历史
 * @access  私有
 */
router.get('/watch-history', authenticate, async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const offset = (page - 1) * limit;
    
    const historyResult = await pool.query(
      `SELECT v.*, wh.progress, wh.completed, wh.watched_at
       FROM watch_history wh
       JOIN movies v ON wh.video_id = v.id
       WHERE wh.user_id = $1
       ORDER BY wh.watched_at DESC
       LIMIT $2 OFFSET $3`,
      [req.user.id, limit, offset]
    );
    
    const countResult = await pool.query(
      'SELECT COUNT(*) FROM watch_history WHERE user_id = $1',
      [req.user.id]
    );
    const total = parseInt(countResult.rows[0].count);
    
    return sendResponse(res, true, {
      history: historyResult.rows,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    }, '获取观看历史成功');
  } catch (error) {
    console.error('获取观看历史失败:', error);
    return sendResponse(res, false, null, '获取观看历史失败', 500);
  }
});

/**
 * @route   GET /api/users/following
 * @desc    获取用户关注的演员列表
 * @access  私有
 */
router.get('/following', authenticate, async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const offset = (page - 1) * limit;
    
    const followingResult = await pool.query(
      `SELECT s.*, uf.created_at as followed_at
       FROM stars s
       JOIN user_following uf ON s.id = uf.star_id
       WHERE uf.user_id = $1
       ORDER BY uf.created_at DESC
       LIMIT $2 OFFSET $3`,
      [req.user.id, limit, offset]
    );
    
    const countResult = await pool.query(
      'SELECT COUNT(*) FROM user_following WHERE user_id = $1',
      [req.user.id]
    );
    const total = parseInt(countResult.rows[0].count);
    
    return sendResponse(res, true, {
      stars: followingResult.rows,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    }, '获取关注演员列表成功');
  } catch (error) {
    console.error('获取关注演员列表失败:', error);
    return sendResponse(res, false, null, '获取关注演员列表失败', 500);
  }
});

/**
 * @route   POST /api/users/likes/:videoId
 * @desc    切换点赞状态（点赞/取消点赞）
 * @access  私有
 */
router.post('/likes/:videoId', authenticate, async (req, res) => {
  try {
    const videoId = parseInt(req.params.videoId);
    
    if (isNaN(videoId)) {
      return sendResponse(res, false, null, '无效的视频ID', 400);
    }
    
    // 检查视频是否存在
    const videoCheck = await pool.query('SELECT * FROM movies WHERE id = $1', [videoId]);
    if (videoCheck.rows.length === 0) {
      return sendResponse(res, false, null, '视频不存在', 404);
    }
    
    // 检查是否已点赞
    const likeCheck = await pool.query(
      'SELECT * FROM user_likes WHERE user_id = $1 AND video_id = $2',
      [req.user.id, videoId]
    );
    
    if (likeCheck.rows.length > 0) {
      // 已经点赞，执行取消点赞
      await pool.query(
        'DELETE FROM user_likes WHERE user_id = $1 AND video_id = $2',
        [req.user.id, videoId]
      );
      return sendResponse(res, true, { isLiked: false }, '取消点赞成功');
    } else {
      // 未点赞，执行点赞
      await pool.query(
        'INSERT INTO user_likes (user_id, video_id, created_at) VALUES ($1, $2, $3)',
        [req.user.id, videoId, new Date()]
      );
      return sendResponse(res, true, { isLiked: true }, '视频点赞成功', 201);
    }
  } catch (error) {
    console.error('点赞操作失败:', error);
    return sendResponse(res, false, null, '点赞操作失败', 500);
  }
});

/**
 * @route   GET /api/users/favorites/:videoId
 * @desc    检查视频收藏状态
 * @access  私有
 */
router.get('/favorites/:videoId', authenticate, async (req, res) => {
  try {
    const videoId = parseInt(req.params.videoId);
    
    if (isNaN(videoId)) {
      return sendResponse(res, false, null, '无效的视频ID', 400);
    }
    
    // 检查是否已收藏
    const favoriteCheck = await pool.query(
      'SELECT * FROM user_favorites WHERE user_id = $1 AND video_id = $2',
      [req.user.id, videoId.toString()]
    );
    
    const isFavorited = favoriteCheck.rows.length > 0;
    
    return sendResponse(res, true, { isFavorited }, '获取收藏状态成功');
  } catch (error) {
    console.error('获取收藏状态失败:', error);
    return sendResponse(res, false, null, '获取收藏状态失败', 500);
  }
});

/**
 * @route   GET /api/users/likes/:videoId
 * @desc    检查视频点赞状态
 * @access  私有
 */
router.get('/likes/:videoId', authenticate, async (req, res) => {
  try {
    const videoId = parseInt(req.params.videoId);
    
    if (isNaN(videoId)) {
      return sendResponse(res, false, null, '无效的视频ID', 400);
    }
    
    // 检查是否已点赞
    const likeCheck = await pool.query(
      'SELECT * FROM user_likes WHERE user_id = $1 AND video_id = $2',
      [req.user.id, videoId]
    );
    
    const isLiked = likeCheck.rows.length > 0;
    
    return sendResponse(res, true, { isLiked }, '获取点赞状态成功');
  } catch (error) {
    console.error('获取点赞状态失败:', error);
    return sendResponse(res, false, null, '获取点赞状态失败', 500);
  }
});

/**
 * @route   POST /api/users/favorites/:videoId
 * @desc    切换收藏状态（收藏/取消收藏）
 * @access  私有
 */
router.post('/favorites/:videoId', authenticate, async (req, res) => {
  try {
    const videoId = parseInt(req.params.videoId);
    
    if (isNaN(videoId)) {
      return sendResponse(res, false, null, '无效的视频ID', 400);
    }
    
    // 检查视频是否存在
    const videoCheck = await pool.query('SELECT * FROM movies WHERE id = $1', [videoId]);
    if (videoCheck.rows.length === 0) {
      return sendResponse(res, false, null, '视频不存在', 404);
    }
    
    // 检查是否已收藏
    const favoriteCheck = await pool.query(
      'SELECT * FROM user_favorites WHERE user_id = $1 AND video_id = $2',
      [req.user.id, videoId.toString()]
    );
    
    if (favoriteCheck.rows.length > 0) {
      // 已经收藏，执行取消收藏
      await pool.query(
        'DELETE FROM user_favorites WHERE user_id = $1 AND video_id = $2',
        [req.user.id, videoId.toString()]
      );
      return sendResponse(res, true, { isFavorited: false }, '取消收藏成功');
    } else {
      // 未收藏，执行收藏
      await pool.query(
        'INSERT INTO user_favorites (user_id, video_id, created_at) VALUES ($1, $2, $3)',
        [req.user.id, videoId.toString(), new Date()]
      );
      return sendResponse(res, true, { isFavorited: true }, '视频收藏成功', 201);
    }
  } catch (error) {
    console.error('收藏操作失败:', error);
    return sendResponse(res, false, null, '收藏操作失败', 500);
  }
});

/**
 * @route   POST /api/users/watch-history
 * @desc    添加观看历史记录
 * @access  私有
 */
router.post('/watch-history', authenticate, async (req, res) => {
  try {
    const { videoId, progress, completed } = req.body;
    const videoIdInt = parseInt(videoId);
    const progressInt = parseInt(progress || 0);
    const completedBool = completed === true;
    
    if (isNaN(videoIdInt)) {
      return sendResponse(res, false, null, '无效的视频ID', 400);
    }
    
    // 检查视频是否存在
    const videoCheck = await pool.query('SELECT * FROM movies WHERE id = $1', [videoIdInt]);
    if (videoCheck.rows.length === 0) {
      return sendResponse(res, false, null, '视频不存在', 404);
    }
    
    // 检查是否已有观看记录
    const historyCheck = await pool.query(
      'SELECT * FROM watch_history WHERE user_id = $1 AND video_id = $2',
      [req.user.id, videoIdInt]
    );
    
    const now = new Date();
    
    if (historyCheck.rows.length > 0) {
      // 更新现有记录
      await pool.query(
        `UPDATE watch_history 
         SET progress = $1, completed = $2, watched_at = $3
         WHERE user_id = $4 AND video_id = $5`,
        [progressInt, completedBool, now, req.user.id, videoIdInt]
      );
    } else {
      // 添加新记录
      await pool.query(
        `INSERT INTO watch_history (user_id, video_id, progress, completed, watched_at)
         VALUES ($1, $2, $3, $4, $5)`,
        [req.user.id, videoIdInt, progressInt, completedBool, now]
      );
    }
    
    return sendResponse(res, true, null, '添加观看历史成功', 201);
  } catch (error) {
    console.error('添加观看历史失败:', error);
    return sendResponse(res, false, null, '添加观看历史失败', 500);
  }
});

/**
 * @route   DELETE /api/users/likes/:videoId
 * @desc    取消点赞视频
 * @access  私有
 */
router.delete('/likes/:videoId', authenticate, async (req, res) => {
  try {
    const videoId = req.params.videoId;
    
    if (!videoId) {
      return sendResponse(res, false, null, '视频ID不能为空', 400);
    }
    
    // 检查视频是否存在
    const videoCheck = await pool.query(
      'SELECT id FROM movies WHERE movie_id = $1',
      [videoId]
    );
    
    if (videoCheck.rows.length === 0) {
      return sendResponse(res, false, null, '视频不存在', 404);
    }
    
    const videoDbId = videoCheck.rows[0].id;
    
    // 删除点赞记录
    const deleteResult = await pool.query(
      'DELETE FROM user_likes WHERE user_id = $1 AND video_id = $2',
      [req.user.id, videoDbId]
    );
    
    if (deleteResult.rowCount === 0) {
      return sendResponse(res, false, null, '未找到点赞记录', 404);
    }
    
    return sendResponse(res, true, null, '取消点赞成功');
  } catch (error) {
    console.error('取消点赞失败:', error);
    return sendResponse(res, false, null, '取消点赞失败', 500);
  }
});

/**
 * @route   DELETE /api/users/favorites/:videoId
 * @desc    取消收藏视频
 * @access  私有
 */
router.delete('/favorites/:videoId', authenticate, async (req, res) => {
  try {
    const videoId = req.params.videoId;
    
    if (!videoId) {
      return sendResponse(res, false, null, '视频ID不能为空', 400);
    }
    
    // 删除收藏记录
    const deleteResult = await pool.query(
      'DELETE FROM user_favorites WHERE user_id = $1 AND video_id = $2',
      [req.user.id, videoId]
    );
    
    if (deleteResult.rowCount === 0) {
      return sendResponse(res, false, null, '未找到收藏记录', 404);
    }
    
    return sendResponse(res, true, null, '取消收藏成功');
  } catch (error) {
    console.error('取消收藏失败:', error);
    return sendResponse(res, false, null, '取消收藏失败', 500);
  }
});

/**
 * @route   POST /api/users/following/:starId
 * @desc    关注演员
 * @access  私有
 */
router.post('/following/:starId', authenticate, async (req, res) => {
  try {
    const starId = parseInt(req.params.starId);
    
    if (!starId || isNaN(starId)) {
      return sendResponse(res, false, null, '无效的演员ID', 400);
    }
    
    // 检查是否已经关注
    const existingFollow = await pool.query(
      'SELECT id FROM user_following WHERE user_id = $1 AND star_id = $2',
      [req.user.id, starId]
    );
    
    if (existingFollow.rows.length > 0) {
      return sendResponse(res, false, null, '已经关注过该演员', 400);
    }
    
    await pool.query(
      'INSERT INTO user_following (user_id, star_id, created_at) VALUES ($1, $2, $3)',
      [req.user.id, starId, new Date()]
    );
    
    return sendResponse(res, true, null, '关注演员成功', 201);
  } catch (error) {
    console.error('关注演员失败:', error);
    return sendResponse(res, false, null, '关注演员失败', 500);
  }
});

/**
 * @route   GET /api/users/test-connection
 * @desc    测试后端连接
 * @access  公开
 */
router.get('/test-connection', (req, res) => {
  console.log('收到测试连接请求');
  return sendResponse(res, true, { message: '连接成功' }, '测试连接成功');
});

// 获取用户总数统计 - 用于管理面板
router.get('/stats', async (req, res) => {
  try {
    const pool = req.app.get('pool');

    // 获取用户总数
    const userCountResult = await pool.query('SELECT COUNT(*) as count FROM users');
    const totalUsers = parseInt(userCountResult.rows[0].count) || 0;

    // 获取活跃用户数（最近30天登录）
    const activeUsersResult = await pool.query(`
      SELECT COUNT(*) as count
      FROM users
      WHERE "lastLoginAt" > NOW() - INTERVAL '30 days'
    `);
    const activeUsers = parseInt(activeUsersResult.rows[0].count) || 0;

    // 获取新用户数（最近7天注册）
    const newUsersResult = await pool.query(`
      SELECT COUNT(*) as count
      FROM users
      WHERE created_at > NOW() - INTERVAL '7 days'
    `);
    const newUsers = parseInt(newUsersResult.rows[0].count) || 0;

    return sendResponse(res, true, {
      totalUsers,
      activeUsers,
      newUsers,
      inactiveUsers: totalUsers - activeUsers
    }, '获取用户统计信息成功');
  } catch (error) {
    console.error('获取用户统计信息失败:', error);
    return sendResponse(res, false, null, '获取用户统计信息失败', 500);
  }
});

module.exports = router;