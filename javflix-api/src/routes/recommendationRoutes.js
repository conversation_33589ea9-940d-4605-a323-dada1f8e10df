const express = require('express');
const router = express.Router();
const apiResponse = require('../utils/apiResponse');
const { Pool } = require('pg');

// 创建连接池
const pool = new Pool({
  host: process.env.PGHOST || 'localhost',
  port: parseInt(process.env.PGPORT || '5432'),
  database: process.env.PGDATABASE || 'javflix',
  user: process.env.PGUSER || 'longgedemacminim4',
  password: process.env.PGPASSWORD || '',
});

/**
 * @route   GET /api/recommendations
 * @desc    获取推荐视频
 * @access  公开
 */
router.get('/', async (req, res) => {
  try {
    const userId = req.query.userId || null;
    const limit = parseInt(req.query.limit) || 10;
    const locale = req.query.locale || 'zh-CN';
    
    // 基础推荐 - 如果没有用户ID，返回热门视频
    let query;
    const params = [limit];
    
    if (!userId) {
      // 没有用户ID，返回热门视频
      query = 'SELECT * FROM movies ORDER BY views DESC LIMIT $1';
    } else {
      // 有用户ID，基于用户历史记录推荐
      // 这里使用简单推荐算法，推荐用户没看过的热门视频
      query = `
        SELECT v.* FROM movies v
        WHERE v.id NOT IN (
          SELECT video_id FROM histories WHERE user_id = $2
        )
        ORDER BY v.views DESC
        LIMIT $1
      `;
      params.push(userId);
    }
    
    // 执行查询
    const { rows } = await pool.query(query, params);
    
    return apiResponse.success(res, { videos: rows }, '获取推荐视频成功');
  } catch (error) {
    console.error('获取推荐视频失败:', error);
    return apiResponse.error(res, '获取推荐视频失败', 500, error);
  }
});

/**
 * @route   GET /api/recommendations/featured
 * @desc    获取精选视频
 * @access  公开
 */
router.get('/featured', async (req, res) => {
  try {
    const limit = parseInt(req.query.limit) || 6;
    
    // 获取高质量、高评分的视频作为精选
    const { rows } = await pool.query(
      'SELECT * FROM videos WHERE status = $1 ORDER BY views DESC LIMIT $2',
      ['approved', limit]
    );
    
    return apiResponse.success(res, { videos: rows }, '获取精选视频成功');
  } catch (error) {
    console.error('获取精选视频失败:', error);
    return apiResponse.error(res, '获取精选视频失败', 500, error);
  }
});

module.exports = router; 