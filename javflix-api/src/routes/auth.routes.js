const express = require('express');
const { body } = require('express-validator');
const authController = require('../controllers/auth.controller');
const { auth } = require('../middleware/auth');

const router = express.Router();

// @route   POST /api/auth/login
// @desc    登录并获取token
// @access  Public
router.post(
  '/login',
  [
    body('username').notEmpty().withMessage('用户名不能为空'),
    body('password').notEmpty().withMessage('密码不能为空')
  ],
  authController.login
);

// @route   POST /api/auth/logout
// @desc    登出
// @access  Private
router.post('/logout', auth, authController.logout);

// @route   GET /api/auth/info
// @desc    获取当前登录用户信息
// @access  Private
router.get('/info', auth, authController.getUserInfo);

module.exports = router; 