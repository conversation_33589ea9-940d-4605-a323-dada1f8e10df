const express = require('express');
const router = express.Router();
const apiResponse = require('../utils/apiResponse');
const { Pool } = require('pg');

// 创建连接池
const pool = new Pool({
  host: process.env.PGHOST || 'localhost',
  port: parseInt(process.env.PGPORT || '5432'),
  database: process.env.PGDATABASE || 'javflix',
  user: process.env.PGUSER || 'longgedemacminim4',
  password: process.env.PGPASSWORD || '',
});

/**
 * @route   GET /api/featured-videos
 * @desc    获取精选视频（别名路由，为了兼容性）
 * @access  公开
 */
router.get('/', async (req, res) => {
  try {
    const limit = parseInt(req.query.limit) || 6;
    const locale = req.query.locale || 'zh-CN';
    
    // 查询精选视频
    const { rows } = await pool.query(
      'SELECT * FROM videos WHERE status = $1 ORDER BY views DESC LIMIT $2',
      ['approved', limit]
    );
    
    return apiResponse.success(res, { videos: rows }, '精选视频获取成功');
  } catch (error) {
    console.error('获取精选视频失败:', error);
    return apiResponse.error(res, '获取精选视频失败', 500, error);
  }
});

module.exports = router; 