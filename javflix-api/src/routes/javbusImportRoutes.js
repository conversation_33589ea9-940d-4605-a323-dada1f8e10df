const express = require('express');
const router = express.Router();
const javbusImportController = require('../controllers/javbusImportController');

// 从JavBus导入影片数据
router.get('/import-movies', javbusImportController.importMovies);

// 获取已导入的影片列表
router.get('/imported-movies', javbusImportController.getImportedMovies);

// 保存影片数据到数据库
router.post('/save-movies', javbusImportController.saveMovies);

// 获取导入状态
router.get('/import-status', javbusImportController.getImportStatus);

// 下载影片相关图片到本地
router.post('/download-images', javbusImportController.downloadMovieImages);

// 获取详细的导入统计信息
router.get('/import-stats', javbusImportController.getImportStats);

// 更新发行日期 - 从磁力链接中找到最早的分享日期
router.post('/update-release-date', javbusImportController.updateReleaseDate);

module.exports = router; 