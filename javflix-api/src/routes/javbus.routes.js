const express = require('express');
const javbusController = require('../controllers/javbus.controller');
const { auth } = require('../middleware/auth');

const router = express.Router();

// @route   GET /api/javbus/movies
// @desc    获取影片列表
// @access  Private
router.get('/movies', auth, javbusController.getMovies);

// @route   GET /api/javbus/search
// @desc    搜索影片
// @access  Private
router.get('/search', auth, javbusController.searchMovies);

// @route   GET /api/javbus/movies/:id
// @desc    获取影片详情
// @access  Private
router.get('/movies/:id', auth, javbusController.getMovieDetail);

// @route   GET /api/javbus/magnets/:id
// @desc    获取影片磁力链接
// @access  Private
router.get('/magnets/:id', auth, javbusController.getMovieMagnets);

// @route   GET /api/javbus/stars/:id
// @desc    获取演员详情
// @access  Private
router.get('/stars/:id', auth, javbusController.getStarDetail);

module.exports = router; 