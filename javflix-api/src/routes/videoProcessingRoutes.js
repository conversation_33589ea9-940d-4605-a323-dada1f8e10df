const express = require('express');
const router = express.Router();
const { Pool } = require('pg');

// 数据库连接
const pool = new Pool({
  host: process.env.PGHOST || 'localhost',
  port: process.env.PGPORT || 5432,
  database: process.env.PGDATABASE || 'javflix',
  user: process.env.PGUSER || 'longgedemacminim4',
  password: process.env.PGPASSWORD || '',
});

// 获取视频处理任务列表
router.get('/tasks', async (req, res) => {
  try {
    const { page = 1, limit = 20, status } = req.query;
    const offset = (page - 1) * limit;
    
    let query = `
      SELECT 
        vpt.*,
        m.title as movie_title,
        m.movie_id as movie_code
      FROM video_processing_tasks vpt
      LEFT JOIN movies m ON vpt.movie_id = m.id
    `;
    
    const params = [];
    
    if (status) {
      query += ` WHERE vpt.status = $${params.length + 1}`;
      params.push(status);
    }
    
    query += ` ORDER BY vpt.created_at DESC LIMIT $${params.length + 1} OFFSET $${params.length + 2}`;
    params.push(limit, offset);
    
    const result = await pool.query(query, params);
    
    // 获取总数
    let countQuery = 'SELECT COUNT(*) FROM video_processing_tasks vpt';
    const countParams = [];
    
    if (status) {
      countQuery += ` WHERE vpt.status = $1`;
      countParams.push(status);
    }
    
    const countResult = await pool.query(countQuery, countParams);
    const total = parseInt(countResult.rows[0].count);
    
    res.json({
      success: true,
      data: result.rows,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('获取任务列表失败:', error);
    res.status(500).json({
      success: false,
      message: '获取任务列表失败',
      error: error.message
    });
  }
});

// 获取任务统计信息
router.get('/stats', async (req, res) => {
  try {
    const statsQuery = `
      SELECT 
        status,
        COUNT(*) as count
      FROM video_processing_tasks 
      GROUP BY status
    `;
    
    const result = await pool.query(statsQuery);
    
    const stats = {
      processing: 0,
      completed: 0,
      failed: 0,
      pending: 0,
      total: 0
    };
    
    result.rows.forEach(row => {
      const status = row.status;
      const count = parseInt(row.count);
      
      if (status === 'processing' || status === 'downloading') {
        stats.processing += count;
      } else if (status === 'completed') {
        stats.completed += count;
      } else if (status === 'failed') {
        stats.failed += count;
      } else if (status === 'pending') {
        stats.pending += count;
      }
      
      stats.total += count;
    });
    
    res.json({
      success: true,
      data: stats
    });
  } catch (error) {
    console.error('获取统计信息失败:', error);
    res.status(500).json({
      success: false,
      message: '获取统计信息失败',
      error: error.message
    });
  }
});

// 获取单个任务详情
router.get('/tasks/:taskId', async (req, res) => {
  try {
    const { taskId } = req.params;
    
    const query = `
      SELECT 
        vpt.*,
        m.title as movie_title,
        m.movie_id as movie_code,
        m.image_url as movie_image
      FROM video_processing_tasks vpt
      LEFT JOIN movies m ON vpt.movie_id = m.id
      WHERE vpt.task_uuid = $1
    `;
    
    const result = await pool.query(query, [taskId]);
    
    if (result.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: '任务不存在'
      });
    }
    
    res.json({
      success: true,
      data: result.rows[0]
    });
  } catch (error) {
    console.error('获取任务详情失败:', error);
    res.status(500).json({
      success: false,
      message: '获取任务详情失败',
      error: error.message
    });
  }
});

// 重试失败的任务
router.post('/tasks/:taskId/retry', async (req, res) => {
  try {
    const { taskId } = req.params;
    
    // 检查任务是否存在且状态为失败
    const checkQuery = `
      SELECT * FROM video_processing_tasks 
      WHERE task_uuid = $1 AND status = 'failed'
    `;
    
    const checkResult = await pool.query(checkQuery, [taskId]);
    
    if (checkResult.rows.length === 0) {
      return res.status(400).json({
        success: false,
        message: '任务不存在或状态不允许重试'
      });
    }
    
    // 重置任务状态
    const updateQuery = `
      UPDATE video_processing_tasks 
      SET 
        status = 'pending',
        progress = 0,
        error_message = NULL,
        started_at = NULL,
        completed_at = NULL,
        updated_at = CURRENT_TIMESTAMP
      WHERE task_uuid = $1
    `;
    
    await pool.query(updateQuery, [taskId]);
    
    // 这里可以添加重新加入处理队列的逻辑
    // 例如发送消息到Redis队列或直接调用处理服务
    
    res.json({
      success: true,
      message: '任务重试请求已提交'
    });
  } catch (error) {
    console.error('重试任务失败:', error);
    res.status(500).json({
      success: false,
      message: '重试任务失败',
      error: error.message
    });
  }
});

// 取消正在进行的任务
router.post('/tasks/:taskId/cancel', async (req, res) => {
  const client = await pool.connect();

  try {
    await client.query('BEGIN');

    const { taskId } = req.params;

    // 检查任务是否存在且可以取消
    const checkQuery = `
      SELECT vpt.*, m.movie_id as movie_code
      FROM video_processing_tasks vpt
      LEFT JOIN movies m ON vpt.movie_id = m.id
      WHERE vpt.task_uuid = $1 AND vpt.status IN ('pending', 'processing', 'downloading')
    `;

    const checkResult = await client.query(checkQuery, [taskId]);

    if (checkResult.rows.length === 0) {
      await client.query('ROLLBACK');
      return res.status(400).json({
        success: false,
        message: '任务不存在或状态不允许取消'
      });
    }

    const task = checkResult.rows[0];
    const movieId = task.movie_id;
    const movieCode = task.movie_code;

    // 1. 删除视频处理任务
    const deleteTaskQuery = `DELETE FROM video_processing_tasks WHERE task_uuid = $1`;
    await client.query(deleteTaskQuery, [taskId]);

    // 2. 删除相关的磁力链接记录
    if (movieId) {
      const deleteMagnetsQuery = `DELETE FROM magnets WHERE movie_id = $1`;
      await client.query(deleteMagnetsQuery, [movieId]);

      // 3. 检查是否还有其他任务引用这个电影，如果没有则删除电影记录
      const otherTasksQuery = `
        SELECT COUNT(*) as count
        FROM video_processing_tasks
        WHERE movie_id = $1
      `;
      const otherTasksResult = await client.query(otherTasksQuery, [movieId]);

      if (parseInt(otherTasksResult.rows[0].count) === 0) {
        const deleteMovieQuery = `DELETE FROM movies WHERE id = $1`;
        await client.query(deleteMovieQuery, [movieId]);
      }
    }

    await client.query('COMMIT');

    // 通知处理服务取消任务的逻辑
    console.log(`任务 ${taskId} 已取消并删除，相关电影: ${movieCode || 'N/A'}`);

    res.json({
      success: true,
      message: '任务已取消并删除相关数据'
    });
  } catch (error) {
    await client.query('ROLLBACK');
    console.error('取消任务失败:', error);
    res.status(500).json({
      success: false,
      message: '取消任务失败',
      error: error.message
    });
  } finally {
    client.release();
  }
});

// 删除任务
router.delete('/tasks/:taskId', async (req, res) => {
  try {
    const { taskId } = req.params;
    
    const deleteQuery = `
      DELETE FROM video_processing_tasks 
      WHERE task_uuid = $1
    `;
    
    const result = await pool.query(deleteQuery, [taskId]);
    
    if (result.rowCount === 0) {
      return res.status(404).json({
        success: false,
        message: '任务不存在'
      });
    }
    
    res.json({
      success: true,
      message: '任务删除成功'
    });
  } catch (error) {
    console.error('删除任务失败:', error);
    res.status(500).json({
      success: false,
      message: '删除任务失败',
      error: error.message
    });
  }
});

// 批量操作任务
router.post('/tasks/batch', async (req, res) => {
  const client = await pool.connect();

  try {
    const { action, taskIds } = req.body;

    if (!action || !taskIds || !Array.isArray(taskIds)) {
      return res.status(400).json({
        success: false,
        message: '参数错误'
      });
    }

    await client.query('BEGIN');

    let result;
    let successMessage;

    switch (action) {
      case 'delete':
        // 获取要删除的任务的电影ID
        const getMovieIdsQuery = `
          SELECT DISTINCT movie_id FROM video_processing_tasks
          WHERE task_uuid = ANY($1)
        `;
        const movieIdsResult = await client.query(getMovieIdsQuery, [taskIds]);
        const movieIds = movieIdsResult.rows.map(row => row.movie_id).filter(id => id);

        // 删除任务
        const deleteTasksQuery = `DELETE FROM video_processing_tasks WHERE task_uuid = ANY($1)`;
        result = await client.query(deleteTasksQuery, [taskIds]);

        // 删除相关磁力链接和电影记录
        for (const movieId of movieIds) {
          await client.query(`DELETE FROM magnets WHERE movie_id = $1`, [movieId]);

          // 检查是否还有其他任务引用这个电影
          const otherTasksResult = await client.query(
            `SELECT COUNT(*) as count FROM video_processing_tasks WHERE movie_id = $1`,
            [movieId]
          );

          if (parseInt(otherTasksResult.rows[0].count) === 0) {
            await client.query(`DELETE FROM movies WHERE id = $1`, [movieId]);
          }
        }

        successMessage = '批量删除成功';
        break;

      case 'retry':
        const retryQuery = `
          UPDATE video_processing_tasks
          SET status = 'pending', progress = 0, error_message = NULL,
              started_at = NULL, completed_at = NULL, updated_at = CURRENT_TIMESTAMP
          WHERE task_uuid = ANY($1) AND status = 'failed'
        `;
        result = await client.query(retryQuery, [taskIds]);
        successMessage = '批量重试成功';
        break;

      case 'cancel':
        // 获取要取消的任务的电影ID
        const getCancelMovieIdsQuery = `
          SELECT DISTINCT movie_id FROM video_processing_tasks
          WHERE task_uuid = ANY($1) AND status IN ('pending', 'processing', 'downloading')
        `;
        const cancelMovieIdsResult = await client.query(getCancelMovieIdsQuery, [taskIds]);
        const cancelMovieIds = cancelMovieIdsResult.rows.map(row => row.movie_id).filter(id => id);

        // 删除任务（取消即删除）
        const cancelTasksQuery = `
          DELETE FROM video_processing_tasks
          WHERE task_uuid = ANY($1) AND status IN ('pending', 'processing', 'downloading')
        `;
        result = await client.query(cancelTasksQuery, [taskIds]);

        // 删除相关磁力链接和电影记录
        for (const movieId of cancelMovieIds) {
          await client.query(`DELETE FROM magnets WHERE movie_id = $1`, [movieId]);

          // 检查是否还有其他任务引用这个电影
          const otherTasksResult = await client.query(
            `SELECT COUNT(*) as count FROM video_processing_tasks WHERE movie_id = $1`,
            [movieId]
          );

          if (parseInt(otherTasksResult.rows[0].count) === 0) {
            await client.query(`DELETE FROM movies WHERE id = $1`, [movieId]);
          }
        }

        successMessage = '批量取消并删除成功';
        break;

      default:
        await client.query('ROLLBACK');
        return res.status(400).json({
          success: false,
          message: '不支持的操作'
        });
    }

    await client.query('COMMIT');

    res.json({
      success: true,
      message: successMessage,
      affected: result.rowCount
    });
  } catch (error) {
    await client.query('ROLLBACK');
    console.error('批量操作失败:', error);
    res.status(500).json({
      success: false,
      message: '批量操作失败',
      error: error.message
    });
  } finally {
    client.release();
  }
});

// 获取系统状态
router.get('/system-status', async (req, res) => {
  try {
    // 这里可以添加系统状态检查逻辑
    // 例如检查Go服务器状态、Redis状态等
    
    res.json({
      success: true,
      data: {
        goServer: 'running', // 可以通过HTTP请求检查
        redis: 'running',    // 可以通过Redis ping检查
        database: 'running', // 已经连接成功
        diskSpace: '75%',    // 可以通过系统调用获取
        memory: '60%'        // 可以通过系统调用获取
      }
    });
  } catch (error) {
    console.error('获取系统状态失败:', error);
    res.status(500).json({
      success: false,
      message: '获取系统状态失败',
      error: error.message
    });
  }
});

module.exports = router;
