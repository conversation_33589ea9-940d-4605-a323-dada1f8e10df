const express = require('express');
const router = express.Router();
const apiResponse = require('../utils/apiResponse');

// 获取电影统计信息
router.get('/movies', async (req, res) => {
  const pool = req.app.get('pool'); // 从 app 对象获取 pool
  try {
    const result = await pool.query('SELECT COUNT(*) FROM movies');
    // 根据 README 更新响应格式
    res.json({
      success: true,
      data: { count: parseInt(result.rows[0].count) },
      message: '电影统计信息获取成功',
      code: 200
    });
  } catch (error) {
    console.error('获取电影统计信息失败:', error);
    apiResponse.error(res, '获取电影统计信息失败', 500, error);
  }
});

// 获取演员统计信息
router.get('/stars', async (req, res) => {
  const pool = req.app.get('pool');
  try {
    const result = await pool.query('SELECT COUNT(*) FROM stars');
    res.json({
      success: true,
      data: { count: parseInt(result.rows[0].count) },
      message: '演员统计信息获取成功',
      code: 200
    });
  } catch (error) {
    console.error('获取演员统计信息失败:', error);
    apiResponse.error(res, '获取演员统计信息失败', 500, error);
  }
});

// 获取类型/标签统计信息
router.get('/genres', async (req, res) => {
  const pool = req.app.get('pool');
  try {
    const result = await pool.query('SELECT COUNT(*) FROM genres');
    res.json({
      success: true,
      data: { count: parseInt(result.rows[0].count) },
      message: '类型统计信息获取成功',
      code: 200
    });
  } catch (error) {
    console.error('获取类型统计信息失败:', error);
    apiResponse.error(res, '获取类型统计信息失败', 500, error);
  }
});

// 获取磁力链接统计信息
router.get('/magnets', async (req, res) => {
  const pool = req.app.get('pool');
  try {
    const result = await pool.query('SELECT COUNT(*) FROM magnets');
    res.json({
      success: true,
      data: { count: parseInt(result.rows[0].count) },
      message: '磁力链接统计信息获取成功',
      code: 200
    });
  } catch (error) {
    console.error('获取磁力链接统计信息失败:', error);
    apiResponse.error(res, '获取磁力链接统计信息失败', 500, error);
  }
});

// 获取用户统计信息 (假设用户表名为 users)
router.get('/users', async (req, res) => {
  const pool = req.app.get('pool');
  try {
    const result = await pool.query('SELECT COUNT(*) FROM users'); // 请确保 users 表存在
    res.json({
      success: true,
      data: { count: parseInt(result.rows[0].count) },
      message: '用户统计信息获取成功',
      code: 200
    });
  } catch (error) {
    console.error('获取用户统计信息失败:', error);
    apiResponse.error(res, '获取用户统计信息失败', 500, error);
  }
});

module.exports = router;
