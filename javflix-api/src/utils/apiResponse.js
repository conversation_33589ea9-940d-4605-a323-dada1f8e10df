/**
 * 标准化API响应格式
 * 统一所有API的响应格式，包括：
 * - success: 表示请求是否成功
 * - code: HTTP状态码
 * - message: 响应消息
 * - data: 响应数据
 * - timestamp: 响应时间戳
 * - pagination: 分页信息(如果适用)
 */

/**
 * 成功响应
 * @param {Object} res - Express响应对象
 * @param {any} data - 响应数据
 * @param {string} message - 响应消息
 * @param {number} code - HTTP状态码
 * @param {Object} pagination - 分页信息
 */
exports.success = (res, data = null, message = '操作成功', code = 200, pagination = null) => {
  const response = {
    success: true,
    code,
    message,
    timestamp: new Date().toISOString()
  };

  if (data !== null) {
    response.data = data;
  }

  if (pagination !== null) {
    response.pagination = pagination;
  }

  return res.status(code).json(response);
};

/**
 * 错误响应
 * @param {Object} res - Express响应对象
 * @param {string} message - 错误消息
 * @param {number} code - HTTP状态码
 * @param {any} error - 详细错误信息(仅在开发环境显示)
 */
exports.error = (res, message = '操作失败', code = 500, error = null) => {
  const response = {
    success: false,
    code,
    message,
    timestamp: new Date().toISOString()
  };

  // 在开发环境中添加详细错误信息
  if (process.env.NODE_ENV === 'development' && error) {
    response.error = typeof error === 'object' ? error.message : error;
    response.stack = typeof error === 'object' ? error.stack : null;
  }

  return res.status(code).json(response);
};

/**
 * 未找到响应
 * @param {Object} res - Express响应对象
 * @param {string} message - 错误消息
 */
exports.notFound = (res, message = '资源不存在') => {
  return exports.error(res, message, 404);
};

/**
 * 无权限响应
 * @param {Object} res - Express响应对象
 * @param {string} message - 错误消息
 */
exports.unauthorized = (res, message = '未授权访问') => {
  return exports.error(res, message, 401);
};

/**
 * 参数验证失败响应
 * @param {Object} res - Express响应对象
 * @param {string} message - 错误消息
 * @param {Object} errors - 验证错误详情
 */
exports.validationError = (res, message = '参数验证失败', errors = null) => {
  const response = {
    success: false,
    code: 422,
    message,
    timestamp: new Date().toISOString()
  };

  if (errors) {
    response.errors = errors;
  }

  return res.status(422).json(response);
}; 