/**
 * 处理API错误的通用函数
 * @param {Object} res - Express响应对象
 * @param {Error} error - 错误对象
 * @returns {Object} 错误响应
 */
exports.handleApiError = (res, error) => {
  console.error('API错误:', error);
  
  // 判断错误类型并返回适当的状态码和消息
  if (error.name === 'PrismaClientKnownRequestError') {
    // 处理Prisma特定错误
    if (error.code === 'P2025') {
      // 记录未找到资源错误
      return res.status(404).json({
        success: false,
        message: '请求的资源不存在',
        code: 404,
      });
    } else if (error.code === 'P2002') {
      // 记录唯一性约束错误
      return res.status(409).json({
        success: false,
        message: '资源已存在，违反唯一性约束',
        code: 409,
      });
    }
  } else if (error.name === 'PrismaClientValidationError') {
    // 处理验证错误
    return res.status(400).json({
      success: false,
      message: '请求数据无效',
      error: error.message,
      code: 400,
    });
  }
  
  // 默认错误响应
  return res.status(500).json({
    success: false,
    message: '服务器内部错误',
    error: process.env.NODE_ENV === 'development' ? error.message : undefined,
    code: 500,
  });
}; 