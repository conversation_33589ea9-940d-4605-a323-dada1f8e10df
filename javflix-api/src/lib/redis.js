/**
 * Redis客户端配置 - JavaScript版本
 * 为了兼容 JavaScript 中间件
 */

const Redis = require('ioredis');
require('dotenv').config();

/**
 * Redis连接配置
 */
const redisConfig = {
  host: process.env.REDIS_HOST || 'localhost',
  port: parseInt(process.env.REDIS_PORT || '6379'),
  password: process.env.REDIS_PASSWORD,
  db: parseInt(process.env.REDIS_DB || '0'),
  retryDelayOnFailover: 100,
  maxRetriesPerRequest: 3,
  lazyConnect: true,
  keepAlive: 30000,
  connectTimeout: 10000,
  commandTimeout: 5000,
};

/**
 * 创建Redis客户端实例
 */
const redisClient = new Redis(redisConfig);

// Redis连接事件监听
redisClient.on('connect', () => {
  console.log('✅ Redis连接成功');
});

redisClient.on('ready', () => {
  console.log('🚀 Redis准备就绪');
});

redisClient.on('error', (error) => {
  console.error('❌ Redis连接错误:', error);
});

redisClient.on('close', () => {
  console.log('🔌 Redis连接已关闭');
});

redisClient.on('reconnecting', () => {
  console.log('🔄 Redis重新连接中...');
});

/**
 * 测试Redis连接
 */
async function testRedisConnection() {
  try {
    await redisClient.ping();
    console.log('✅ Redis连接测试成功');
    return true;
  } catch (error) {
    console.error('❌ Redis连接测试失败:', error);
    return false;
  }
}

module.exports = {
  redisClient,
  redis: redisClient, // 为了向后兼容
  testRedisConnection
};
