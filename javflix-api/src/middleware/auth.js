const jwt = require('jsonwebtoken');
const { Pool } = require('pg');

// 创建数据库连接池
const pool = new Pool({
  host: process.env.PGHOST || 'localhost',
  port: parseInt(process.env.PGPORT || '5432'),
  database: process.env.PGDATABASE || 'javflix',
  user: process.env.PGUSER || 'longgedemacminim4',
  password: process.env.PGPASSWORD || '',
});

// JWT认证中间件
const auth = async (req, res, next) => {
  // 获取token
  const authHeader = req.headers.authorization;
  
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return res.status(401).json({
      message: '未提供授权令牌',
      success: false
    });
  }
  
  const token = authHeader.split(' ')[1];
  
  try {
    // 验证token
    const decoded = jwt.verify(token, process.env.JWT_SECRET || 'javflix_secret_key');
    
    // 查询完整的用户信息
    const userResult = await pool.query('SELECT id, username, email, is_admin, created_at FROM users WHERE id = $1', [decoded.id]);
    
    if (userResult.rows.length === 0) {
      return res.status(401).json({
        message: '用户不存在',
        success: false
      });
    }
    
    // 将完整用户信息添加到请求对象
    req.user = userResult.rows[0];
    
    next();
  } catch (error) {
    return res.status(401).json({
      message: '无效的授权令牌',
      success: false
    });
  }
};

// 检查管理员权限中间件
const checkAdmin = async (req, res, next) => {
  try {
    // 先验证用户是否已登录
    const authHeader = req.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({
        message: '未提供授权令牌',
        success: false
      });
    }
    
    const token = authHeader.split(' ')[1];
    
    // 验证token
    const decoded = jwt.verify(token, process.env.JWT_SECRET || 'javflix_secret_key');
    
    // 将用户ID添加到请求对象
    req.user = {
      id: decoded.id
    };
    
    // 查询数据库确认用户是否为管理员
    const userResult = await pool.query('SELECT is_admin FROM users WHERE id = $1', [req.user.id]);
    
    if (userResult.rows.length === 0) {
      return res.status(401).json({
        message: '用户不存在',
        success: false
      });
    }
    
    if (!userResult.rows[0].is_admin) {
      return res.status(403).json({
        message: '需要管理员权限',
        success: false
      });
    }
    
    next();
  } catch (error) {
    console.error('验证管理员权限失败:', error);
    return res.status(401).json({
      message: '验证管理员权限失败',
      success: false
    });
  }
};

module.exports = { auth, checkAdmin }; 