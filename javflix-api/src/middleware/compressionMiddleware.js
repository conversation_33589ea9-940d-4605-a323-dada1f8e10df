/**
 * 载荷压缩中间件
 * 基于 Medium 文章的压缩优化最佳实践
 * https://medium.com/@crok07.benahmed/top-7-ways-to-10x-your-api-performance-caching-connection-pooling-avoiding-n-1-problem-33516b657af2
 */

const compression = require('compression');

/**
 * 智能压缩中间件配置
 */
const compressionMiddleware = compression({
  // 压缩级别 (1-9, 6是默认值，平衡压缩率和速度)
  level: 6,
  
  // 压缩阈值 - 只压缩大于1KB的响应
  threshold: 1024,
  
  // 自定义过滤器 - 决定哪些响应需要压缩
  filter: (req, res) => {
    // 如果客户端明确表示不接受压缩，则跳过
    if (req.headers['x-no-compression']) {
      return false;
    }
    
    // 检查Content-Type
    const contentType = res.getHeader('content-type');
    
    // 压缩文本类型的响应
    if (contentType) {
      const textTypes = [
        'application/json',
        'application/javascript',
        'text/html',
        'text/css',
        'text/plain',
        'text/xml',
        'application/xml'
      ];
      
      return textTypes.some(type => contentType.includes(type));
    }
    
    // 默认使用compression的内置过滤器
    return compression.filter(req, res);
  },
  
  // 压缩窗口大小 (影响压缩率和内存使用)
  windowBits: 15,
  
  // 内存级别 (1-9, 8是默认值)
  memLevel: 8,
  
  // 压缩策略
  strategy: require('zlib').constants.Z_DEFAULT_STRATEGY
});

/**
 * 高性能压缩中间件 - 针对API响应优化
 */
function apiCompressionMiddleware(req, res, next) {
  // 为API响应设置特定的压缩参数
  const apiCompression = compression({
    level: 4, // 较低的压缩级别以提高速度
    threshold: 512, // 更低的阈值，因为API响应通常较小
    filter: (req, res) => {
      // 只压缩JSON响应
      const contentType = res.getHeader('content-type');
      return contentType && contentType.includes('application/json');
    }
  });
  
  return apiCompression(req, res, next);
}

/**
 * 条件压缩中间件 - 根据客户端类型调整压缩策略
 */
function conditionalCompressionMiddleware(req, res, next) {
  const userAgent = req.headers['user-agent'] || '';
  
  let compressionConfig = {
    level: 6,
    threshold: 1024
  };
  
  // 移动设备使用更高的压缩率以节省带宽
  if (/Mobile|Android|iPhone|iPad/.test(userAgent)) {
    compressionConfig.level = 8;
    compressionConfig.threshold = 512;
  }
  
  // 高速网络环境可以使用较低的压缩率以提高速度
  if (req.headers['connection-type'] === 'wifi' || 
      req.headers['downlink'] && parseFloat(req.headers['downlink']) > 10) {
    compressionConfig.level = 4;
  }
  
  const dynamicCompression = compression(compressionConfig);
  return dynamicCompression(req, res, next);
}

/**
 * 压缩统计中间件 - 记录压缩效果
 */
function compressionStatsMiddleware(req, res, next) {
  const startTime = Date.now();
  let originalSize = 0;
  let compressedSize = 0;
  
  // 拦截响应以计算压缩前后的大小
  const originalWrite = res.write;
  const originalEnd = res.end;
  
  res.write = function(chunk, encoding) {
    if (chunk) {
      originalSize += Buffer.isBuffer(chunk) ? chunk.length : Buffer.byteLength(chunk, encoding);
    }
    return originalWrite.call(this, chunk, encoding);
  };
  
  res.end = function(chunk, encoding) {
    if (chunk) {
      originalSize += Buffer.isBuffer(chunk) ? chunk.length : Buffer.byteLength(chunk, encoding);
    }
    
    const endTime = Date.now();
    const processingTime = endTime - startTime;
    
    // 获取实际发送的大小（压缩后）
    compressedSize = parseInt(res.getHeader('content-length')) || originalSize;
    
    // 计算压缩率
    const compressionRatio = originalSize > 0 ? 
      ((originalSize - compressedSize) / originalSize * 100).toFixed(2) : 0;
    
    // 添加压缩统计头
    res.setHeader('X-Original-Size', originalSize);
    res.setHeader('X-Compressed-Size', compressedSize);
    res.setHeader('X-Compression-Ratio', `${compressionRatio}%`);
    res.setHeader('X-Processing-Time', `${processingTime}ms`);
    
    // 记录压缩统计
    if (originalSize > 1024) { // 只记录大于1KB的响应
      console.log(`📦 压缩统计 ${req.path}: ${originalSize}B → ${compressedSize}B (${compressionRatio}% 减少, ${processingTime}ms)`);
    }
    
    return originalEnd.call(this, chunk, encoding);
  };
  
  next();
}

module.exports = {
  compressionMiddleware,
  apiCompressionMiddleware,
  conditionalCompressionMiddleware,
  compressionStatsMiddleware
};