// 全局错误处理中间件
const errorHandler = (err, req, res, next) => {
  console.error('错误:', err.message);
  
  // 检查Mongoose验证错误
  if (err.name === 'ValidationError') {
    return res.status(400).json({
      message: '数据验证错误',
      errors: Object.values(err.errors).map(error => error.message),
      success: false
    });
  }
  
  // JWT认证错误
  if (err.name === 'UnauthorizedError') {
    return res.status(401).json({
      message: '认证失败',
      success: false
    });
  }
  
  // 默认返回500错误
  res.status(err.statusCode || 500).json({
    message: err.message || '服务器错误',
    success: false,
    stack: process.env.NODE_ENV === 'production' ? null : err.stack
  });
};

module.exports = errorHandler; 