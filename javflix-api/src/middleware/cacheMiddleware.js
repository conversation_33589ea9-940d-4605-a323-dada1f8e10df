/**
 * Redis缓存中间件
 * 基于 Medium 文章的缓存优化最佳实践
 * https://medium.com/@crok07.benahmed/top-7-ways-to-10x-your-api-performance-caching-connection-pooling-avoiding-n-1-problem-33516b657af2
 */

const { redis } = require('../lib/redis');

/**
 * 缓存中间件工厂函数
 * @param {number} ttl - 缓存时间（秒）
 * @param {string} keyPrefix - 缓存键前缀
 * @param {function} keyGenerator - 自定义键生成函数
 */
function cacheMiddleware(ttl = 300, keyPrefix = 'api', keyGenerator = null) {
  return async (req, res, next) => {
    try {
      // 生成缓存键
      let cacheKey;
      if (keyGenerator && typeof keyGenerator === 'function') {
        cacheKey = keyGenerator(req);
      } else {
        // 默认键生成策略：包含路径、查询参数和用户ID
        const userId = req.user?.id || 'anonymous';
        const queryString = new URLSearchParams(req.query).toString();
        cacheKey = `${keyPrefix}:${req.path}:${queryString}:${userId}`;
      }

      // 尝试从缓存获取数据
      const cachedData = await redis.get(cacheKey);
      
      if (cachedData) {
        // 缓存命中
        const parsedData = JSON.parse(cachedData);
        
        // 添加缓存头信息
        res.set({
          'X-Cache': 'HIT',
          'X-Cache-Key': cacheKey,
          'X-Cache-TTL': ttl,
          'Cache-Control': `public, max-age=${ttl}`
        });
        
        console.log(`🎯 缓存命中: ${cacheKey}`);
        return res.json(parsedData);
      }

      // 缓存未命中，继续处理请求
      console.log(`💾 缓存未命中: ${cacheKey}`);
      
      // 重写res.json方法以拦截响应
      const originalJson = res.json;
      res.json = function(data) {
        // 只缓存成功的响应
        if (data && (data.success === true || res.statusCode === 200)) {
          // 异步缓存数据，不阻塞响应
          setImmediate(async () => {
            try {
              await redis.setex(cacheKey, ttl, JSON.stringify(data));
              console.log(`💾 数据已缓存: ${cacheKey} (TTL: ${ttl}s)`);
            } catch (cacheError) {
              console.error('缓存写入失败:', cacheError);
            }
          });
        }

        // 添加缓存头信息
        res.set({
          'X-Cache': 'MISS',
          'X-Cache-Key': cacheKey,
          'X-Cache-TTL': ttl,
          'Cache-Control': `public, max-age=${ttl}`
        });

        // 调用原始的json方法
        return originalJson.call(this, data);
      };

      next();
    } catch (error) {
      console.error('缓存中间件错误:', error);
      // 缓存错误不应该影响正常请求
      next();
    }
  };
}

/**
 * 智能缓存中间件 - 根据端点类型自动选择TTL
 */
function smartCache(req, res, next) {
  let ttl = 300; // 默认5分钟
  let keyPrefix = 'api';

  // 根据路径确定缓存策略
  if (req.path.includes('/popular')) {
    ttl = 600; // 热门内容缓存10分钟
    keyPrefix = 'popular';
  } else if (req.path.includes('/search')) {
    ttl = 180; // 搜索结果缓存3分钟
    keyPrefix = 'search';
  } else if (req.path.includes('/videos') && req.method === 'GET') {
    ttl = 300; // 视频列表缓存5分钟
    keyPrefix = 'videos';
  } else if (req.path.includes('/stats')) {
    ttl = 120; // 统计数据缓存2分钟
    keyPrefix = 'stats';
  } else if (req.path.includes('/db/')) {
    ttl = 900; // 数据库查询缓存15分钟
    keyPrefix = 'db';
  }

  return cacheMiddleware(ttl, keyPrefix)(req, res, next);
}

/**
 * 缓存预热函数
 */
async function warmupCache() {
  console.log('🔥 开始缓存预热...');
  
  const warmupUrls = [
    '/api/popular-videos?limit=20',
    '/api/db/movies?limit=20',
    '/api/db/stars?limit=20',
    '/api/videos?limit=20'
  ];

  for (const url of warmupUrls) {
    try {
      // 这里可以调用内部API或直接执行查询
      console.log(`🔥 预热缓存: ${url}`);
      // 实际实现中，您可能需要调用相应的控制器函数
    } catch (error) {
      console.error(`缓存预热失败 ${url}:`, error);
    }
  }
  
  console.log('✅ 缓存预热完成');
}

/**
 * 缓存失效函数
 */
async function invalidateCache(pattern) {
  try {
    const keys = await redis.keys(pattern);
    if (keys.length > 0) {
      await redis.del(...keys);
      console.log(`🗑️ 已清除 ${keys.length} 个缓存键: ${pattern}`);
    }
  } catch (error) {
    console.error('缓存失效失败:', error);
  }
}

/**
 * 缓存统计函数
 */
async function getCacheStats() {
  try {
    const info = await redis.info('memory');
    const keyspace = await redis.info('keyspace');
    
    return {
      memory: info,
      keyspace: keyspace,
      timestamp: new Date().toISOString()
    };
  } catch (error) {
    console.error('获取缓存统计失败:', error);
    return null;
  }
}

module.exports = {
  cacheMiddleware,
  smartCache,
  warmupCache,
  invalidateCache,
  getCacheStats
};