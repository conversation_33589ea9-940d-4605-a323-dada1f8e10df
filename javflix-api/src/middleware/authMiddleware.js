const jwt = require('jsonwebtoken');
const { Pool } = require('pg');
const apiResponse = require('../utils/apiResponse');

// 创建连接池
const pool = new Pool({
  host: process.env.PGHOST || 'localhost',
  port: parseInt(process.env.PGPORT || '5432'),
  database: process.env.PGDATABASE || 'javflix',
  user: process.env.PGUSER || 'longgedemacminim4',
  password: process.env.PGPASSWORD || '',
});

/**
 * 认证中间件
 * 验证用户的JWT令牌，并将用户信息附加到请求对象
 */
exports.authenticate = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return apiResponse.unauthorized(res, '请提供有效的认证令牌');
    }
    
    const token = authHeader.split(' ')[1];
    const secret = process.env.JWT_SECRET || 'your_jwt_secret_key';
    
    const decoded = jwt.verify(token, secret);
    
    // 检查用户是否存在
    const userResult = await pool.query('SELECT * FROM users WHERE id = $1', [decoded.id]);
    
    if (userResult.rows.length === 0) {
      return apiResponse.unauthorized(res, '无效的用户令牌');
    }
    
    // 将用户附加到请求对象
    req.user = userResult.rows[0];
    delete req.user.password; // 移除密码
    
    next();
  } catch (error) {
    return apiResponse.unauthorized(res, '认证失败，令牌无效或已过期');
  }
};

/**
 * 管理员权限中间件
 * 必须在authenticate中间件之后使用
 */
exports.isAdmin = (req, res, next) => {
  if (!req.user) {
    return apiResponse.unauthorized(res, '请先登录');
  }
  
  if (!req.user.is_admin && !req.user.isAdmin) {
    return apiResponse.error(res, '您没有管理员权限', 403);
  }
  
  next();
}; 