"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.rateLimiter = rateLimiter;
exports.userRateLimiter = userRateLimiter;
exports.endpointRateLimiter = endpointRateLimiter;
exports.globalRateLimiter = globalRateLimiter;
exports.adaptiveRateLimiter = adaptiveRateLimiter;
exports.getRateLimitStatus = getRateLimitStatus;
exports.clearUserRateLimit = clearUserRateLimit;
const redis_1 = require("../lib/redis");
/**
 * 高性能限流中间件
 * 基于Redis的滑动窗口算法实现
 */
function rateLimiter(options) {
    const { maxRequests, windowMs, keyGenerator = (req) => `rate_limit:${req.ip}:${req.route?.path || req.path}`, skipSuccessfulRequests = false, skipFailedRequests = false, message = '请求过于频繁，请稍后重试' } = options;
    return async (req, res, next) => {
        try {
            const key = keyGenerator(req);
            const now = Date.now();
            const windowStart = now - windowMs;
            // 使用Redis的Lua脚本确保原子性操作
            const luaScript = `
        local key = KEYS[1]
        local window_start = tonumber(ARGV[1])
        local now = tonumber(ARGV[2])
        local max_requests = tonumber(ARGV[3])
        local window_ms = tonumber(ARGV[4])
        
        -- 清理过期的请求记录
        redis.call('ZREMRANGEBYSCORE', key, '-inf', window_start)
        
        -- 获取当前窗口内的请求数量
        local current_requests = redis.call('ZCARD', key)
        
        -- 检查是否超过限制
        if current_requests < max_requests then
          -- 添加当前请求到有序集合
          redis.call('ZADD', key, now, now)
          -- 设置过期时间
          redis.call('EXPIRE', key, math.ceil(window_ms / 1000))
          return {current_requests + 1, max_requests - current_requests - 1, now + window_ms}
        else
          -- 获取最早请求的时间
          local oldest_request = redis.call('ZRANGE', key, 0, 0, 'WITHSCORES')
          local reset_time = now + window_ms
          if #oldest_request > 0 then
            reset_time = tonumber(oldest_request[2]) + window_ms
          end
          return {current_requests, 0, reset_time}
        end
      `;
            const result = await redis_1.redisClient.eval(luaScript, 1, key, windowStart.toString(), now.toString(), maxRequests.toString(), windowMs.toString());
            const [totalRequests, remainingRequests, resetTime] = result;
            // 设置响应头
            res.set({
                'X-RateLimit-Limit': maxRequests.toString(),
                'X-RateLimit-Remaining': remainingRequests.toString(),
                'X-RateLimit-Reset': Math.ceil(resetTime / 1000).toString(),
                'X-RateLimit-Window': windowMs.toString()
            });
            // 检查是否超过限制
            if (remainingRequests <= 0) {
                const retryAfter = Math.ceil((resetTime - now) / 1000);
                res.set('Retry-After', retryAfter.toString());
                return res.status(429).json({
                    success: false,
                    error: message,
                    rateLimitInfo: {
                        limit: maxRequests,
                        remaining: 0,
                        resetTime: Math.ceil(resetTime / 1000),
                        retryAfter
                    }
                });
            }
            // 将限流信息添加到请求对象中
            req.rateLimit = {
                limit: maxRequests,
                remaining: remainingRequests,
                resetTime: Math.ceil(resetTime / 1000)
            };
            next();
        }
        catch (error) {
            console.error('限流中间件错误:', error);
            // 如果Redis出错，允许请求通过但记录错误
            next();
        }
    };
}
/**
 * 用户特定的限流器
 * 基于用户ID而不是IP地址
 */
function userRateLimiter(options) {
    return rateLimiter({
        ...options,
        keyGenerator: (req) => {
            const userId = req.user?.id;
            const route = req.route?.path || req.path;
            return userId ? `user_rate_limit:${userId}:${route}` : `ip_rate_limit:${req.ip}:${route}`;
        }
    });
}
/**
 * API端点特定的限流器
 */
function endpointRateLimiter(endpoint, options) {
    return rateLimiter({
        ...options,
        keyGenerator: (req) => `endpoint_rate_limit:${endpoint}:${req.ip}`
    });
}
/**
 * 全局限流器
 * 用于整个API的全局限制
 */
function globalRateLimiter(options) {
    return rateLimiter({
        ...options,
        keyGenerator: (req) => `global_rate_limit:${req.ip}`
    });
}
/**
 * 智能限流器
 * 根据请求类型和用户状态动态调整限制
 */
function adaptiveRateLimiter() {
    return async (req, res, next) => {
        const isAuthenticated = !!req.user;
        const method = req.method;
        const path = req.path;
        let options;
        // 根据请求类型动态设置限制
        if (method === 'GET') {
            // 读取操作相对宽松
            options = {
                maxRequests: isAuthenticated ? 100 : 60,
                windowMs: 60000 // 1分钟
            };
        }
        else if (method === 'POST' || method === 'PUT' || method === 'DELETE') {
            // 写入操作更严格
            options = {
                maxRequests: isAuthenticated ? 30 : 10,
                windowMs: 60000
            };
        }
        else {
            // 其他请求类型
            options = {
                maxRequests: 20,
                windowMs: 60000
            };
        }
        // 搜索请求特殊处理
        if (path.includes('/search')) {
            options = {
                maxRequests: isAuthenticated ? 40 : 20,
                windowMs: 60000
            };
        }
        return rateLimiter(options)(req, res, next);
    };
}
/**
 * 获取用户当前限流状态
 */
async function getRateLimitStatus(userId, endpoint) {
    try {
        const key = `user_rate_limit:${userId}:${endpoint}`;
        const now = Date.now();
        const windowMs = 60000; // 假设1分钟窗口
        const windowStart = now - windowMs;
        // 清理过期记录并获取当前请求数
        await redis_1.redisClient.zremrangebyscore(key, '-inf', windowStart);
        const currentRequests = await redis_1.redisClient.zcard(key);
        // 获取最早请求时间
        const oldestRequests = await redis_1.redisClient.zrange(key, 0, 0, 'WITHSCORES');
        let resetTime = now + windowMs;
        if (oldestRequests.length > 0) {
            const oldestTime = parseInt(oldestRequests[1]);
            resetTime = oldestTime + windowMs;
        }
        return {
            totalRequests: currentRequests,
            remainingRequests: Math.max(0, 60 - currentRequests), // 假设默认限制60
            resetTime: Math.ceil(resetTime / 1000)
        };
    }
    catch (error) {
        console.error('获取限流状态失败:', error);
        return null;
    }
}
/**
 * 清除用户的限流记录
 */
async function clearUserRateLimit(userId, endpoint) {
    try {
        if (endpoint) {
            const key = `user_rate_limit:${userId}:${endpoint}`;
            await redis_1.redisClient.del(key);
        }
        else {
            // 清除该用户的所有限流记录
            const keys = await redis_1.redisClient.keys(`user_rate_limit:${userId}:*`);
            if (keys.length > 0) {
                await redis_1.redisClient.del(...keys);
            }
        }
        return true;
    }
    catch (error) {
        console.error('清除限流记录失败:', error);
        return false;
    }
}
