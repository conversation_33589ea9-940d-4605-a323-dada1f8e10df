const { Pool } = require('pg');

// 数据库连接配置
const pool = new Pool({
  host: process.env.PGHOST || 'localhost',
  port: parseInt(process.env.PGPORT || '5432'),
  database: process.env.PGDATABASE || 'javflix',
  user: process.env.PGUSER || 'longgedemacminim4',
  password: process.env.PGPASSWORD || '',
});

async function clearStars() {
  const client = await pool.connect();
  
  try {
    // 开始事务
    await client.query('BEGIN');
    
    console.log('开始清空女优数据...');
    
    // 1. 删除电影-女优关联表数据
    const movieStarsResult = await client.query('DELETE FROM movie_stars');
    console.log(`- 已删除 ${movieStarsResult.rowCount} 条电影-女优关联记录`);
    
    // 2. 删除所有女优数据
    const starsResult = await client.query('DELETE FROM stars');
    console.log(`- 已删除 ${starsResult.rowCount} 位女优记录`);
    
    // 3. 重置自增序列
    await client.query('ALTER SEQUENCE IF EXISTS stars_id_seq RESTART WITH 1');
    console.log('- 已重置女优ID序列');
    
    // 提交事务
    await client.query('COMMIT');
    
    // 验证清空结果
    const verifyStars = await client.query('SELECT COUNT(*) as remaining_stars FROM stars');
    const verifyMovieStars = await client.query('SELECT COUNT(*) as remaining_movie_stars FROM movie_stars');
    
    console.log('\n===== 清空结果 =====');
    console.log(`剩余女优数量: ${verifyStars.rows[0].remaining_stars}`);
    console.log(`剩余电影-女优关联数量: ${verifyMovieStars.rows[0].remaining_movie_stars}`);
    
    if (verifyStars.rows[0].remaining_stars === '0' && verifyMovieStars.rows[0].remaining_movie_stars === '0') {
      console.log('✅ 女优数据清空成功!');
    } else {
      console.log('❌ 女优数据未完全清空');
    }
    
  } catch (error) {
    // 回滚事务
    await client.query('ROLLBACK');
    console.error('清空女优数据失败:', error);
    throw error;
  } finally {
    client.release();
  }
}

async function main() {
  try {
    await clearStars();
    console.log('\n🎉 女优数据清空完成!');
  } catch (error) {
    console.error('\n💥 清空过程中发生错误:', error);
    process.exit(1);
  } finally {
    await pool.end();
  }
}

// 执行主函数
main(); 