const { Pool } = require('pg');
const fs = require('fs');
const path = require('path');

// 数据库配置
const pool = new Pool({
  user: 'postgres',
  host: 'localhost',
  database: 'javflix',
  password: 'password',
  port: 5432,
});

async function runMigration() {
  const client = await pool.connect();
  
  try {
    console.log('🚀 开始执行数据库迁移...');
    
    // 读取迁移文件
    const migrationPath = path.join(__dirname, 'migrations', 'add_download_progress_fields.sql');
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
    
    // 执行迁移
    await client.query(migrationSQL);
    
    console.log('✅ 数据库迁移执行成功！');
    console.log('📊 已添加字段: completed_bytes, total_bytes');
    
  } catch (error) {
    console.error('❌ 数据库迁移失败:', error);
    process.exit(1);
  } finally {
    client.release();
    await pool.end();
  }
}

// 执行迁移
runMigration();
