const { Pool } = require('pg');
require('dotenv').config();

// 创建数据库连接池
const pool = new Pool({
  host: process.env.PGHOST || 'localhost',
  port: parseInt(process.env.PGPORT || '5432'),
  database: process.env.PGDATABASE || 'javflix',
  user: process.env.PGUSER || 'longgedemacminim4',
  password: process.env.PGPASSWORD || '',
});

async function updateActorImages() {
  const client = await pool.connect();
  
  try {
    console.log('开始更新演员图片URL...');
    
    // 开始事务
    await client.query('BEGIN');
    
    // 查询所有演员
    const starsResult = await client.query(`
      SELECT id, star_id, name, image_url, cached_image_url
      FROM stars
      WHERE image_url LIKE 'https://%'
    `);
    
    console.log(`找到 ${starsResult.rows.length} 位演员需要更新图片URL`);
    
    // 逐个更新演员的图片URL
    for (const star of starsResult.rows) {
      console.log(`更新演员 ${star.name} (ID: ${star.id}) 的图片URL`);
      
      // 如果有本地缓存图片，就用本地的，否则置为null
      const imageUrl = star.cached_image_url || null;
      
      await client.query(`
        UPDATE stars
        SET image_url = $1
        WHERE id = $2
      `, [imageUrl, star.id]);
    }
    
    // 提交事务
    await client.query('COMMIT');
    console.log('成功更新演员图片URL');
    
  } catch (error) {
    await client.query('ROLLBACK');
    console.error('更新演员图片URL失败:', error);
  } finally {
    client.release();
  }
}

// 执行更新
updateActorImages()
  .then(() => {
    console.log('演员图片URL更新完成');
    process.exit(0);
  })
  .catch(error => {
    console.error('发生错误:', error);
    process.exit(1);
  }); 