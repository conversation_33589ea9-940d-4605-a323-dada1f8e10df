{"name": "javflix-api", "version": "1.0.0", "main": "index.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "dev:ts": "npm run build && node dist/server.js", "dev:watch": "nodemon --exec \"npm run build && npm start\" --watch src --ext ts", "build": "tsc", "test": "echo \"Error: no test specified\" && exit 1", "clean-javbus": "node clean-javbus-data.js"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@neondatabase/serverless": "^1.0.0", "@prisma/client": "^6.8.2", "@types/ioredis": "^4.28.10", "@types/socket.io-client": "^1.4.36", "axios": "^1.9.0", "bcrypt": "^6.0.0", "bcryptjs": "^3.0.2", "compression": "^1.8.0", "cors": "^2.8.5", "dotenv": "^16.5.0", "drizzle-orm": "^0.43.1", "express": "^4.21.2", "express-validator": "^7.2.1", "helmet": "^8.1.0", "ioredis": "^5.6.1", "jsonwebtoken": "^9.0.2", "mongoose": "^8.15.0", "node-cron": "^4.0.5", "node-fetch": "^3.3.2", "pg": "^8.16.0", "prisma": "^6.8.2", "redis": "^5.1.0", "slugify": "^1.6.6", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "uuid": "^11.1.0"}, "devDependencies": {"@types/bcrypt": "^5.0.2", "@types/cors": "^2.8.18", "@types/express": "^4.17.22", "@types/jsonwebtoken": "^9.0.9", "@types/node": "^22.15.21", "@types/pg": "^8.15.2", "@types/uuid": "^10.0.0", "nodemon": "^3.1.10", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}