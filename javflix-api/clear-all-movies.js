const { Pool } = require('pg');
require('dotenv').config();

const pool = new Pool({
  host: process.env.PGHOST || 'localhost',
  port: process.env.PGPORT || 5432,
  database: process.env.PGDATABASE || 'javflix',
  user: process.env.PGUSER || 'longgedemacminim4',
  password: process.env.PGPASSWORD || '',
});

async function clearAllMoviesData() {
  const client = await pool.connect();
  
  try {
    console.log('🔄 开始清空所有影片数据...');
    
    // 按照外键依赖关系的顺序删除数据
    const deleteQueries = [
      // 1. 先删除视频处理任务
      'DELETE FROM video_processing_tasks;',
      
      // 2. 删除影片关联表
      'DELETE FROM movie_stars;',
      'DELETE FROM movie_genres;',
      'DELETE FROM movie_directors;',
      'DELETE FROM movie_producers;',
      'DELETE FROM movie_publishers;',
      'DELETE FROM movie_series;',
      'DELETE FROM movie_categories;',
      
      // 3. 删除磁力链接
      'DELETE FROM magnets;',
      
      // 4. 删除影片主表
      'DELETE FROM movies;',
      
      // 5. 删除独立的数据表（没有被其他表引用的）
      'DELETE FROM stars;',
      'DELETE FROM genres;',
      'DELETE FROM directors;',
      'DELETE FROM producers;',
      'DELETE FROM publishers;',
      'DELETE FROM series;',
      'DELETE FROM categories;'
    ];

    let totalDeleted = 0;
    
    for (const query of deleteQueries) {
      try {
        const result = await client.query(query);
        const tableName = query.match(/FROM (\w+)/)[1];
        console.log(`✅ 清空表 ${tableName}: 删除了 ${result.rowCount} 条记录`);
        totalDeleted += result.rowCount;
      } catch (error) {
        const tableName = query.match(/FROM (\w+)/)[1];
        console.error(`❌ 清空表 ${tableName} 失败:`, error.message);
      }
    }

    // 重置自增序列
    console.log('\n🔄 重置自增序列...');
    const resetSequences = [
      'ALTER SEQUENCE movies_id_seq RESTART WITH 1;',
      'ALTER SEQUENCE stars_id_seq RESTART WITH 1;',
      'ALTER SEQUENCE genres_id_seq RESTART WITH 1;',
      'ALTER SEQUENCE directors_id_seq RESTART WITH 1;',
      'ALTER SEQUENCE producers_id_seq RESTART WITH 1;',
      'ALTER SEQUENCE publishers_id_seq RESTART WITH 1;',
      'ALTER SEQUENCE series_id_seq RESTART WITH 1;',
      'ALTER SEQUENCE categories_id_seq RESTART WITH 1;',
      'ALTER SEQUENCE magnets_id_seq RESTART WITH 1;'
    ];

    for (const query of resetSequences) {
      try {
        await client.query(query);
        const seqName = query.match(/(\w+_seq)/)[1];
        console.log(`✅ 重置序列: ${seqName}`);
      } catch (error) {
        const seqName = query.match(/(\w+_seq)/)[1];
        console.log(`⚠️  序列 ${seqName} 重置失败或不存在:`, error.message);
      }
    }

    // 验证清空结果
    console.log('\n📊 验证清空结果:');
    const verifyQueries = [
      'SELECT COUNT(*) as count FROM movies;',
      'SELECT COUNT(*) as count FROM stars;',
      'SELECT COUNT(*) as count FROM magnets;',
      'SELECT COUNT(*) as count FROM video_processing_tasks;'
    ];

    for (const query of verifyQueries) {
      const result = await client.query(query);
      const tableName = query.match(/FROM (\w+)/)[1];
      console.log(`  - ${tableName}: ${result.rows[0].count} 条记录`);
    }

    console.log(`\n🎉 数据清空完成！总共删除了 ${totalDeleted} 条记录`);
    console.log('💡 现在可以重新开始采集影片数据了');
    
  } catch (error) {
    console.error('❌ 清空数据时出错:', error);
    throw error;
  } finally {
    client.release();
    await pool.end();
  }
}

// 运行脚本
clearAllMoviesData()
  .then(() => {
    console.log('✅ 脚本执行完成');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ 脚本执行失败:', error);
    process.exit(1);
  });
