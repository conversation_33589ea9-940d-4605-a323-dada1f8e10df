/**
 * JAVFLIX.TV 最终性能验证测试
 * 基于 Medium 文章的7大API性能优化实践
 */

const http = require('http');

const TEST_CONFIG = {
  baseUrl: 'http://localhost:4000',
  timeout: 10000
};

function makeRequest(url) {
  return new Promise((resolve, reject) => {
    const startTime = Date.now();
    
    const req = http.request(`${TEST_CONFIG.baseUrl}${url}`, {
      method: 'GET',
      timeout: TEST_CONFIG.timeout
    }, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        const endTime = Date.now();
        const responseTime = endTime - startTime;
        
        resolve({
          success: res.statusCode === 200,
          statusCode: res.statusCode,
          responseTime,
          dataSize: data.length,
          cacheStatus: res.headers['x-cache'] || 'Unknown',
          contentEncoding: res.headers['content-encoding'] || 'none'
        });
      });
    });
    
    req.on('error', (error) => {
      reject({
        success: false,
        error: error.message,
        responseTime: Date.now() - startTime
      });
    });
    
    req.on('timeout', () => {
      req.destroy();
      reject({
        success: false,
        error: 'Request timeout'
      });
    });
    
    req.end();
  });
}

async function testCaching() {
  console.log('\\n💾 缓存效果测试');
  console.log('--------------------------------');
  
  const testUrl = '/api/popular-videos?limit=10';
  
  console.log('🔸 第一次请求 (预期缓存未命中)...');
  const first = await makeRequest(testUrl);
  console.log(`   📈 响应时间: ${first.responseTime}ms | 缓存: ${first.cacheStatus}`);
  
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  console.log('🔸 第二次请求 (预期缓存命中)...');
  const second = await makeRequest(testUrl);
  console.log(`   📈 响应时间: ${second.responseTime}ms | 缓存: ${second.cacheStatus}`);
  
  if (second.cacheStatus === 'HIT') {
    const improvement = ((first.responseTime - second.responseTime) / first.responseTime * 100);
    console.log(`   ✅ 缓存生效! 性能提升: ${improvement.toFixed(1)}%`);
    return { working: true, improvement };
  } else {
    console.log('   ⚠️  缓存未按预期工作');
    return { working: false, improvement: 0 };
  }
}

async function testConcurrency(url, count = 10) {
  console.log(`\\n🔥 并发测试: ${count}个并发请求`);
  console.log('--------------------------------');
  
  const startTime = Date.now();
  const promises = [];
  
  for (let i = 0; i < count; i++) {
    promises.push(makeRequest(url));
  }
  
  try {
    const results = await Promise.all(promises);
    const endTime = Date.now();
    const totalTime = endTime - startTime;
    
    const successCount = results.filter(r => r.success).length;
    const avgResponseTime = results.reduce((sum, r) => sum + r.responseTime, 0) / results.length;
    const throughput = Math.round((count / totalTime) * 1000);
    
    const cacheHits = results.filter(r => r.cacheStatus === 'HIT').length;
    const cacheMisses = results.filter(r => r.cacheStatus === 'MISS').length;
    
    console.log('📊 并发测试结果:');
    console.log(`   - 总耗时: ${totalTime}ms`);
    console.log(`   - 平均响应时间: ${Math.round(avgResponseTime)}ms`);
    console.log(`   - 成功率: ${Math.round((successCount/count)*100)}%`);
    console.log(`   - 吞吐量: ${throughput} 请求/秒`);
    console.log(`   - 缓存命中: ${cacheHits} | 缓存未命中: ${cacheMisses}`);
    
    return {
      successRate: (successCount/count)*100,
      avgResponseTime,
      throughput,
      cacheHitRate: cacheHits > 0 ? (cacheHits/(cacheHits+cacheMisses))*100 : 0
    };
  } catch (error) {
    console.log(`❌ 并发测试失败: ${error.message}`);
    return null;
  }
}

async function runFinalTests() {
  console.log('🎬 JAVFLIX.TV 最终性能验证测试');
  console.log('基于7大API性能优化最佳实践');
  console.log('参考: https://medium.com/@crok07.benahmed/top-7-ways-to-10x-your-api-performance');
  console.log('============================================================');
  
  console.log('🔍 检查服务器连通性...');
  try {
    const health = await makeRequest('/');
    if (health.success) {
      console.log('✅ 服务器连接正常');
    } else {
      console.log('❌ 服务器连接失败');
      return;
    }
  } catch (error) {
    console.log('❌ 服务器连接超时');
    return;
  }
  
  console.log('\\n📋 第一阶段: 基础API性能测试');
  console.log('========================================');
  
  const basicTests = [
    { url: '/api/videos?limit=20', name: '视频列表API' },
    { url: '/api/popular-videos?limit=10', name: '热门视频API' },
    { url: '/api/videos/search?q=NACR', name: '视频搜索API' },
    { url: '/api/db/movies?limit=15', name: '数据库电影API' },
    { url: '/api/user-stats?userId=1', name: '用户统计API' }
  ];
  
  const basicResults = [];
  
  for (const test of basicTests) {
    console.log(`\\n🧪 测试: ${test.name}`);
    try {
      const result = await makeRequest(test.url);
      if (result.success) {
        console.log(`   ✅ 成功 | 响应时间: ${result.responseTime}ms | 数据大小: ${result.dataSize}B | 缓存: ${result.cacheStatus}`);
        basicResults.push({ name: test.name, ...result });
      } else {
        console.log(`   ❌ 失败 (${result.statusCode})`);
      }
    } catch (error) {
      console.log(`   ❌ 错误: ${error.error}`);
    }
  }
  
  console.log('\\n📋 第二阶段: 缓存效果验证');
  console.log('========================================');
  
  const cacheResult = await testCaching();
  
  console.log('\\n📋 第三阶段: 并发性能测试');
  console.log('========================================');
  
  const concurrentResults = [];
  
  const mediumConcurrent = await testConcurrency('/api/popular-videos?limit=5', 15);
  if (mediumConcurrent) {
    concurrentResults.push({ type: 'medium', ...mediumConcurrent });
  }
  
  const highConcurrent = await testConcurrency('/api/videos?limit=10', 30);
  if (highConcurrent) {
    concurrentResults.push({ type: 'high', ...highConcurrent });
  }
  
  console.log('\\n============================================================');
  console.log('📊 JAVFLIX.TV 最终性能测试报告');
  console.log('============================================================');
  
  const successfulTests = basicResults.filter(r => r.success);
  const avgResponseTime = successfulTests.reduce((sum, r) => sum + r.responseTime, 0) / successfulTests.length;
  const totalDataSize = successfulTests.reduce((sum, r) => sum + r.dataSize, 0);
  
  console.log('🎯 基础性能统计:');
  console.log(`   - 成功测试数: ${successfulTests.length}/${basicTests.length}`);
  console.log(`   - 平均响应时间: ${Math.round(avgResponseTime)}ms`);
  console.log(`   - 总数据传输: ${Math.round(totalDataSize/1024)}KB`);
  
  console.log('\\n💾 缓存系统评估:');
  if (cacheResult.working) {
    console.log('   ✅ 缓存系统正常工作');
    console.log(`   📈 性能提升: ${cacheResult.improvement.toFixed(1)}%`);
  } else {
    console.log('   ⚠️  缓存系统需要检查');
  }
  
  if (concurrentResults.length > 0) {
    const avgThroughput = concurrentResults.reduce((sum, r) => sum + r.throughput, 0) / concurrentResults.length;
    const avgSuccessRate = concurrentResults.reduce((sum, r) => sum + r.successRate, 0) / concurrentResults.length;
    const avgCacheHitRate = concurrentResults.reduce((sum, r) => sum + r.cacheHitRate, 0) / concurrentResults.length;
    
    console.log('\\n🚀 并发性能评估:');
    console.log(`   - 平均吞吐量: ${Math.round(avgThroughput)} 请求/秒`);
    console.log(`   - 平均成功率: ${avgSuccessRate.toFixed(1)}%`);
    console.log(`   - 平均缓存命中率: ${avgCacheHitRate.toFixed(1)}%`);
  }
  
  let finalScore = 0;
  let grade = 'F';
  
  if (avgResponseTime <= 100) finalScore += 30;
  else if (avgResponseTime <= 200) finalScore += 25;
  else if (avgResponseTime <= 500) finalScore += 20;
  else finalScore += 10;
  
  if (cacheResult.working && cacheResult.improvement > 30) finalScore += 25;
  else if (cacheResult.working) finalScore += 20;
  else finalScore += 5;
  
  if (concurrentResults.length > 0) {
    const avgThroughput = concurrentResults.reduce((sum, r) => sum + r.throughput, 0) / concurrentResults.length;
    if (avgThroughput >= 500) finalScore += 25;
    else if (avgThroughput >= 200) finalScore += 20;
    else if (avgThroughput >= 100) finalScore += 15;
    else finalScore += 10;
  }
  
  const successRate = (successfulTests.length / basicTests.length) * 100;
  if (successRate === 100) finalScore += 20;
  else if (successRate >= 80) finalScore += 15;
  else if (successRate >= 60) finalScore += 10;
  else finalScore += 5;
  
  if (finalScore >= 90) grade = 'A+';
  else if (finalScore >= 85) grade = 'A';
  else if (finalScore >= 80) grade = 'B+';
  else if (finalScore >= 75) grade = 'B';
  else if (finalScore >= 70) grade = 'C+';
  else if (finalScore >= 65) grade = 'C';
  else if (finalScore >= 60) grade = 'D';
  
  console.log(`\\n🏆 最终性能评级: ${finalScore}/100 (${grade}级)`);
  
  if (finalScore >= 85) {
    console.log('\\n🎉 恭喜! JAVFLIX.TV的API性能表现优秀!');
    console.log('✅ 已成功实施多项性能优化策略');
  } else if (finalScore >= 75) {
    console.log('\\n👍 不错! JAVFLIX.TV的API性能表现良好');
    console.log('💡 还有进一步优化的空间');
  } else {
    console.log('\\n💪 JAVFLIX.TV的API性能有改进空间');
    console.log('🔧 建议继续优化以下方面:');
    if (avgResponseTime > 200) console.log('   - 响应时间优化');
    if (!cacheResult.working) console.log('   - 缓存策略优化');
    if (concurrentResults.length > 0) {
      const avgThroughput = concurrentResults.reduce((sum, r) => sum + r.throughput, 0) / concurrentResults.length;
      if (avgThroughput < 200) console.log('   - 并发处理能力优化');
    }
  }
  
  console.log('\\n📖 性能优化参考资料:');
  console.log('🔗 7种10倍提升API性能的方法: https://medium.com/@crok07.benahmed/top-7-ways-to-10x-your-api-performance');
  console.log('\\n🎉 性能测试完成!');
}

runFinalTests().catch(console.error);