const axios = require('axios');
const { Pool } = require('pg');

// 配置数据库连接
const pool = new Pool({
  host: process.env.PGHOST || 'localhost',
  port: parseInt(process.env.PGPORT || '5432'),
  database: process.env.PGDATABASE || 'javflix',
  user: process.env.PGUSER || 'longgedemacminim4',
  password: process.env.PGPASSWORD || '',
});

// JavBus API URL
const JAVBUS_API_URL = 'http://localhost:3000/api';

async function debugImport() {
  try {
    console.log('开始调试导入功能...');
    
    // 测试数据库连接
    console.log('1. 测试数据库连接...');
    const testClient = await pool.connect();
    await testClient.query('SELECT 1');
    testClient.release();
    console.log('✅ 数据库连接成功');
    
    // 测试JavBus API连接
    console.log('2. 测试JavBus API连接...');
    const testResponse = await axios.get(`${JAVBUS_API_URL}/movies?page=1`);
    console.log(`✅ JavBus API响应状态: ${testResponse.status}`);
    console.log(`✅ JavBus API返回影片数量: ${testResponse.data.movies ? testResponse.data.movies.length : 0}`);
    
    // 测试获取影片详情
    console.log('3. 测试获取影片详情...');
    const firstMovie = testResponse.data.movies[0];
    console.log(`测试影片: ${firstMovie.id}`);
    
    const detailResponse = await axios.get(`${JAVBUS_API_URL}/movies/${firstMovie.id}`);
    console.log(`✅ 影片详情获取成功: ${detailResponse.data.title}`);
    
    // 测试获取磁力链接
    console.log('4. 测试获取磁力链接...');
    const magnetsResponse = await axios.get(`${JAVBUS_API_URL}/magnets/${firstMovie.id}`, {
      params: {
        gid: detailResponse.data.gid,
        uc: detailResponse.data.uc
      }
    });
    console.log(`✅ 磁力链接获取成功，数量: ${magnetsResponse.data ? magnetsResponse.data.length : 0}`);
    
    console.log('🎉 所有测试通过！');
    
  } catch (error) {
    console.error('❌ 调试过程中出现错误:', error.message);
    console.error('错误详情:', error);
  } finally {
    await pool.end();
  }
}

debugImport(); 