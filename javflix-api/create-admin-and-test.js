const { Pool } = require('pg');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const axios = require('axios');
require('dotenv').config();

const pool = new Pool({
  host: process.env.PGHOST || 'localhost',
  port: process.env.PGPORT || 5432,
  database: process.env.PGDATABASE || 'javflix',
  user: process.env.PGUSER || 'longgedemacminim4',
  password: process.env.PGPASSWORD || '',
});

async function createAdminAndTest() {
  const client = await pool.connect();
  
  try {
    console.log('🔄 开始创建管理员用户并测试视频处理...');
    
    // 1. 创建管理员用户
    const adminUsername = 'admin';
    const adminPassword = 'admin123';
    const hashedPassword = await bcrypt.hash(adminPassword, 10);
    
    // 检查管理员是否已存在
    const existingAdmin = await client.query('SELECT id FROM users WHERE username = $1', [adminUsername]);
    
    let adminId;
    if (existingAdmin.rows.length > 0) {
      adminId = existingAdmin.rows[0].id;
      console.log('✅ 管理员用户已存在，ID:', adminId);
    } else {
      // 创建管理员用户
      const createUserResult = await client.query(`
        INSERT INTO users (username, email, password, is_admin, created_at)
        VALUES ($1, $2, $3, $4, NOW())
        RETURNING id
      `, [adminUsername, '<EMAIL>', hashedPassword, true]);
      
      adminId = createUserResult.rows[0].id;
      console.log('✅ 管理员用户创建成功，ID:', adminId);
    }
    
    // 2. 生成JWT token
    const jwtSecret = process.env.JWT_SECRET || 'javflix_secret_key';
    const token = jwt.sign({ id: adminId }, jwtSecret, { expiresIn: '24h' });
    
    console.log('✅ JWT Token生成成功');
    console.log('🔑 Token:', token);
    
    // 3. 测试登录API
    try {
      const loginResponse = await axios.post('http://localhost:4000/api/auth/login', {
        username: adminUsername,
        password: adminPassword
      });
      
      console.log('✅ 登录测试成功');
      const apiToken = loginResponse.data.token;
      
      // 4. 检查是否有影片可以处理
      const moviesResult = await client.query('SELECT id, movie_id, title FROM movies ORDER BY id LIMIT 5');
      
      if (moviesResult.rows.length === 0) {
        console.log('⚠️  没有找到影片，请先运行影片采集');
        return;
      }
      
      console.log(`📋 找到 ${moviesResult.rows.length} 部影片可以处理:`);
      moviesResult.rows.forEach(movie => {
        console.log(`  - ID: ${movie.id}, 影片号: ${movie.movie_id}, 标题: ${movie.title}`);
      });
      
      // 5. 测试视频处理API
      const firstMovieId = moviesResult.rows[0].id;
      console.log(`🎬 开始测试处理影片 ID: ${firstMovieId}`);
      
      const processResponse = await axios.post('http://localhost:4000/api/movie-processing/start-real', {
        movieIds: [firstMovieId],
        config: {
          priority: 5,
          watermark: {
            enabled: true,
            text: "JAVFLIX.TV",
            position: "bottom-right",
            opacity: 0.8
          },
          slice: {
            qualities: [
              {"resolution": "720p", "bitrate": "3000k"}
            ],
            segmentDuration: 10,
            generateThumbnails: true
          }
        }
      }, {
        headers: {
          'Authorization': `Bearer ${apiToken}`,
          'Content-Type': 'application/json'
        }
      });
      
      console.log('✅ 视频处理请求发送成功!');
      console.log('📤 响应:', processResponse.data);
      
      // 6. 等待几秒钟检查处理状态
      console.log('⏳ 等待5秒钟检查处理状态...');
      await new Promise(resolve => setTimeout(resolve, 5000));
      
      // 检查任务状态
      const tasksResult = await client.query(`
        SELECT task_uuid, movie_id, status, progress, current_step, created_at 
        FROM video_processing_tasks 
        WHERE movie_id = $1 
        ORDER BY created_at DESC 
        LIMIT 1
      `, [firstMovieId]);
      
      if (tasksResult.rows.length > 0) {
        const task = tasksResult.rows[0];
        console.log('📊 任务状态:');
        console.log(`  - UUID: ${task.task_uuid}`);
        console.log(`  - 状态: ${task.status}`);
        console.log(`  - 进度: ${task.progress}%`);
        console.log(`  - 当前步骤: ${task.current_step}`);
        console.log(`  - 创建时间: ${task.created_at}`);
      } else {
        console.log('⚠️  没有找到任务记录');
      }
      
    } catch (apiError) {
      console.error('❌ API测试失败:', apiError.response?.data || apiError.message);
    }
    
  } catch (error) {
    console.error('❌ 创建管理员或测试失败:', error);
    throw error;
  } finally {
    client.release();
    await pool.end();
  }
}

// 运行脚本
createAdminAndTest()
  .then(() => {
    console.log('✅ 脚本执行完成');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ 脚本执行失败:', error);
    process.exit(1);
  });
