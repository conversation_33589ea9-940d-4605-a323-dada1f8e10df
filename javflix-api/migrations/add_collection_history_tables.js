const { Pool } = require('pg');

async function createTables() {
  const pool = new Pool({
    host: process.env.PGHOST || 'localhost',
    port: parseInt(process.env.PGPORT || '5432'),
    database: process.env.PGDATABASE || 'javflix',
    user: process.env.PGUSER || 'postgres',
    password: process.env.PGPASSWORD || '',
  });

  try {
    console.log('开始创建表...');

    // 创建收藏分类表
    await pool.query(`
      CREATE TABLE IF NOT EXISTS favorite_categories (
        id SERIAL PRIMARY KEY,
        user_id INTEGER NOT NULL,
        name VARCHAR(100) NOT NULL,
        description TEXT,
        created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
      );
    `);
    console.log('收藏分类表已创建');

    // 创建收藏分类关联表
    await pool.query(`
      CREATE TABLE IF NOT EXISTS favorite_category_items (
        id SERIAL PRIMARY KEY,
        category_id INTEGER NOT NULL,
        video_id INTEGER NOT NULL,
        created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (category_id) REFERENCES favorite_categories(id) ON DELETE CASCADE,
        FOREIGN KEY (video_id) REFERENCES movies(id) ON DELETE CASCADE,
        UNIQUE(category_id, video_id)
      );
    `);
    console.log('收藏分类关联表已创建');

    // 修改user_favorites表，添加note字段
    await pool.query(`
      ALTER TABLE user_favorites 
      ADD COLUMN IF NOT EXISTS note TEXT,
      ADD COLUMN IF NOT EXISTS sort_order INTEGER DEFAULT 0;
    `);
    console.log('已更新user_favorites表');

    // 创建观看历史表
    await pool.query(`
      CREATE TABLE IF NOT EXISTS watch_history (
        id SERIAL PRIMARY KEY,
        user_id INTEGER NOT NULL,
        video_id INTEGER NOT NULL,
        progress INTEGER DEFAULT 0,
        completed BOOLEAN DEFAULT FALSE,
        watched_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (video_id) REFERENCES movies(id) ON DELETE CASCADE,
        UNIQUE(user_id, video_id)
      );
    `);
    console.log('观看历史表已创建');

    // 创建稍后观看表
    await pool.query(`
      CREATE TABLE IF NOT EXISTS watch_later (
        id SERIAL PRIMARY KEY,
        user_id INTEGER NOT NULL,
        video_id INTEGER NOT NULL,
        created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (video_id) REFERENCES movies(id) ON DELETE CASCADE,
        UNIQUE(user_id, video_id)
      );
    `);
    console.log('稍后观看表已创建');

    console.log('表创建完成');
  } catch (error) {
    console.error('创建表时出错:', error);
  } finally {
    await pool.end();
  }
}

createTables(); 