-- 添加下载进度跟踪字段到video_processing_tasks表
-- 执行时间: 2025-01-04

-- 添加completed_bytes和total_bytes字段
ALTER TABLE video_processing_tasks 
ADD COLUMN IF NOT EXISTS completed_bytes BIGINT DEFAULT 0,
ADD COLUMN IF NOT EXISTS total_bytes BIGINT DEFAULT 0;

-- 添加索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_video_processing_tasks_progress 
ON video_processing_tasks(task_uuid, status, progress);

-- 添加注释
COMMENT ON COLUMN video_processing_tasks.completed_bytes IS '已下载字节数';
COMMENT ON COLUMN video_processing_tasks.total_bytes IS '总文件大小字节数';

-- 验证字段是否添加成功
SELECT column_name, data_type, is_nullable, column_default 
FROM information_schema.columns 
WHERE table_name = 'video_processing_tasks' 
AND column_name IN ('completed_bytes', 'total_bytes')
ORDER BY column_name;
