const { Pool } = require('pg');

async function createUserLikesTable() {
  const pool = new Pool({
    host: process.env.PGHOST || 'localhost',
    port: parseInt(process.env.PGPORT || '5432'),
    database: process.env.PGDATABASE || 'javflix',
    user: process.env.PGUSER || 'postgres',
    password: process.env.PGPASSWORD || '',
  });

  try {
    console.log('开始创建用户点赞表...');

    // 创建用户点赞表
    await pool.query(`
      CREATE TABLE IF NOT EXISTS user_likes (
        id SERIAL PRIMARY KEY,
        user_id INTEGER NOT NULL,
        video_id INTEGER NOT NULL,
        created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (video_id) REFERENCES movies(id) ON DELETE CASCADE,
        UNIQUE(user_id, video_id)
      );
    `);
    console.log('用户点赞表已创建');

    // 创建索引以提高查询性能
    await pool.query(`
      CREATE INDEX IF NOT EXISTS idx_user_likes_user_id ON user_likes(user_id);
    `);
    
    await pool.query(`
      CREATE INDEX IF NOT EXISTS idx_user_likes_video_id ON user_likes(video_id);
    `);
    
    await pool.query(`
      CREATE INDEX IF NOT EXISTS idx_user_likes_created_at ON user_likes(created_at);
    `);
    
    console.log('索引创建完成');
    console.log('用户点赞表和索引创建完成');
  } catch (error) {
    console.error('创建用户点赞表时出错:', error);
  } finally {
    await pool.end();
  }
}

createUserLikesTable(); 