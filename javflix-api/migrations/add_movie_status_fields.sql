-- 影片状态管理数据库迁移
-- 为现有的movies表添加状态管理字段

-- 1. 添加影片状态字段
ALTER TABLE movies ADD COLUMN IF NOT EXISTS status VARCHAR(20) DEFAULT 'draft';
ALTER TABLE movies ADD COLUMN IF NOT EXISTS video_urls JSONB DEFAULT '{}';
ALTER TABLE movies ADD COLUMN IF NOT EXISTS processing_priority INTEGER DEFAULT 5;
ALTER TABLE movies ADD COLUMN IF NOT EXISTS published_at TIMESTAMP NULL;
ALTER TABLE movies ADD COLUMN IF NOT EXISTS processing_started_at TIMESTAMP NULL;
ALTER TABLE movies ADD COLUMN IF NOT EXISTS processing_completed_at TIMESTAMP NULL;

-- 2. 创建状态枚举约束
ALTER TABLE movies ADD CONSTRAINT movies_status_check 
CHECK (status IN ('draft', 'processing', 'processed', 'published', 'failed'));

-- 3. 添加索引优化查询
CREATE INDEX IF NOT EXISTS idx_movies_status ON movies(status);
CREATE INDEX IF NOT EXISTS idx_movies_published_at ON movies(published_at);
CREATE INDEX IF NOT EXISTS idx_movies_processing_priority ON movies(processing_priority);

-- 4. 更新现有数据（假设现有影片都是已发布状态）
UPDATE movies SET status = 'published', published_at = created_at WHERE status = 'draft';

-- 5. 创建视频处理任务表
CREATE TABLE IF NOT EXISTS video_processing_tasks (
  id SERIAL PRIMARY KEY,
  task_uuid VARCHAR(36) UNIQUE NOT NULL,
  movie_id INTEGER REFERENCES movies(id),
  task_type VARCHAR(50) NOT NULL,
  status VARCHAR(20) DEFAULT 'pending',
  progress INTEGER DEFAULT 0,
  priority INTEGER DEFAULT 5,
  
  -- 源文件信息
  source_url TEXT,
  magnet_link TEXT,
  original_filename VARCHAR(255),
  file_size BIGINT,
  
  -- 处理配置
  watermark_config JSONB DEFAULT '{}',
  slice_config JSONB DEFAULT '{}',
  quality_config JSONB DEFAULT '{}',
  
  -- 输出结果
  output_urls JSONB DEFAULT '{}',
  hls_playlist_url TEXT,
  thumbnail_urls JSONB DEFAULT '[]',
  
  -- 处理信息
  processing_server VARCHAR(50),
  error_message TEXT,
  retry_count INTEGER DEFAULT 0,
  max_retries INTEGER DEFAULT 3,
  
  -- 时间戳
  created_at TIMESTAMP DEFAULT NOW(),
  started_at TIMESTAMP,
  completed_at TIMESTAMP
);

-- 6. 添加视频处理任务表索引
CREATE INDEX IF NOT EXISTS idx_video_tasks_status ON video_processing_tasks(status);
CREATE INDEX IF NOT EXISTS idx_video_tasks_movie_id ON video_processing_tasks(movie_id);
CREATE INDEX IF NOT EXISTS idx_video_tasks_created_at ON video_processing_tasks(created_at);

-- 7. 创建视频处理日志表
CREATE TABLE IF NOT EXISTS video_processing_logs (
  id SERIAL PRIMARY KEY,
  task_id INTEGER REFERENCES video_processing_tasks(id),
  log_level VARCHAR(10) NOT NULL,
  message TEXT NOT NULL,
  details JSONB DEFAULT '{}',
  created_at TIMESTAMP DEFAULT NOW()
);

CREATE INDEX IF NOT EXISTS idx_video_logs_task_id ON video_processing_logs(task_id);
CREATE INDEX IF NOT EXISTS idx_video_logs_created_at ON video_processing_logs(created_at);

-- 8. 添加触发器：自动更新时间戳
CREATE OR REPLACE FUNCTION update_movie_timestamps()
RETURNS TRIGGER AS $$
BEGIN
    -- 当状态变为processing时，设置processing_started_at
    IF OLD.status != 'processing' AND NEW.status = 'processing' THEN
        NEW.processing_started_at = NOW();
    END IF;
    
    -- 当状态变为processed时，设置processing_completed_at
    IF OLD.status != 'processed' AND NEW.status = 'processed' THEN
        NEW.processing_completed_at = NOW();
    END IF;
    
    -- 当状态变为published时，设置published_at
    IF OLD.status != 'published' AND NEW.status = 'published' THEN
        NEW.published_at = NOW();
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS movie_status_timestamp_trigger ON movies;
CREATE TRIGGER movie_status_timestamp_trigger
    BEFORE UPDATE ON movies
    FOR EACH ROW
    EXECUTE FUNCTION update_movie_timestamps();