const { Pool } = require('pg');

async function createTables() {
  const pool = new Pool({
    host: process.env.PGHOST || 'localhost',
    port: parseInt(process.env.PGPORT || '5432'),
    database: process.env.PGDATABASE || 'javflix',
    user: process.env.PGUSER || 'postgres',
    password: process.env.PGPASSWORD || '',
  });

  try {
    console.log('开始创建用户偏好相关表...');

    // 创建用户偏好表
    await pool.query(`
      CREATE TABLE IF NOT EXISTS user_preferences (
        id SERIAL PRIMARY KEY,
        user_id INTEGER NOT NULL,
        theme VARCHAR(50) DEFAULT 'light',
        language VARCHAR(10) DEFAULT 'zh',
        autoplay BOOLEAN DEFAULT true,
        subtitle_enabled BOOLEAN DEFAULT true,
        video_quality VARCHAR(20) DEFAULT 'auto',
        notifications_enabled BOOLEAN DEFAULT true,
        genre_preferences JSONB DEFAULT '{}',
        created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        UNIQUE(user_id)
      );
    `);
    console.log('用户偏好表已创建');

    // 创建用户关注演员表
    await pool.query(`
      CREATE TABLE IF NOT EXISTS user_following (
        id SERIAL PRIMARY KEY,
        user_id INTEGER NOT NULL,
        star_id INTEGER NOT NULL,
        created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (star_id) REFERENCES stars(id) ON DELETE CASCADE,
        UNIQUE(user_id, star_id)
      );
    `);
    console.log('用户关注演员表已创建');

    // 创建用户屏蔽内容表
    await pool.query(`
      CREATE TABLE IF NOT EXISTS user_blocked (
        id SERIAL PRIMARY KEY,
        user_id INTEGER NOT NULL,
        content_type VARCHAR(20) NOT NULL, -- 'star', 'genre', 'movie', etc.
        content_id INTEGER NOT NULL,
        created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        UNIQUE(user_id, content_type, content_id)
      );
    `);
    console.log('用户屏蔽内容表已创建');

    // 创建推荐反馈表
    await pool.query(`
      CREATE TABLE IF NOT EXISTS recommendation_feedback (
        id SERIAL PRIMARY KEY,
        user_id INTEGER NOT NULL,
        item_id INTEGER NOT NULL,
        action VARCHAR(20) NOT NULL, -- 'like', 'dislike', 'skip', etc.
        created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        UNIQUE(user_id, item_id, action)
      );
    `);
    console.log('推荐反馈表已创建');

    console.log('表创建完成');
  } catch (error) {
    console.error('创建表时出错:', error);
  } finally {
    await pool.end();
  }
}

createTables(); 