const { Pool } = require('pg');
const fs = require('fs');
const path = require('path');
const Redis = require('ioredis');

// 数据库连接配置
const pool = new Pool({
  host: process.env.PGHOST || 'localhost',
  port: parseInt(process.env.PGPORT || '5432'),
  database: process.env.PGDATABASE || 'javflix',
  user: process.env.PGUSER || 'longgedemacminim4',
  password: process.env.PGPASSWORD || '',
});

// Redis连接配置
const redisClient = new Redis({
  host: process.env.REDIS_HOST || 'localhost',
  port: parseInt(process.env.REDIS_PORT || '6379'),
  password: process.env.REDIS_PASSWORD,
  db: parseInt(process.env.REDIS_DB || '0'),
  retryDelayOnFailover: 100,
  maxRetriesPerRequest: 3,
  lazyConnect: true,
});

async function cleanMoviesAndRedis() {
  try {
    console.log('🧹 开始清空影片数据和Redis统计...');
    
    // 1. 清空PostgreSQL数据库中的影片数据
    console.log('📁 读取SQL清理脚本...');
    const sqlFilePath = path.join(__dirname, '..', 'javflix', 'clean_movies.sql');
    const sqlScript = fs.readFileSync(sqlFilePath, 'utf8');
    
    console.log('🗄️ 执行PostgreSQL数据清理...');
    await pool.query(sqlScript);
    console.log('✅ PostgreSQL影片数据已清空!');
    
    // 2. 清空Redis中的所有相关数据
    console.log('🔍 查找Redis中的视频统计数据...');
    const videoStatsKeys = await redisClient.keys('video:stats:*');

    if (videoStatsKeys.length > 0) {
      console.log(`📊 找到 ${videoStatsKeys.length} 个视频统计键，正在删除...`);
      const deletedCount = await redisClient.del(...videoStatsKeys);
      console.log(`✅ 已删除 ${deletedCount} 个Redis视频统计键!`);
    } else {
      console.log('ℹ️ Redis中没有找到视频统计数据');
    }

    // 3. 清理视频缓存
    console.log('🔍 查找并清理视频缓存...');
    const videoCacheKeys = await redisClient.keys('javflix:videos:*');
    if (videoCacheKeys.length > 0) {
      const deletedCacheCount = await redisClient.del(...videoCacheKeys);
      console.log(`✅ 已删除 ${deletedCacheCount} 个视频缓存键!`);
    }

    // 4. 清理用户收藏相关缓存
    console.log('🔍 查找并清理用户收藏缓存...');
    const favoriteKeys = await redisClient.keys('user:favorites:*');
    if (favoriteKeys.length > 0) {
      const deletedFavoriteCount = await redisClient.del(...favoriteKeys);
      console.log(`✅ 已删除 ${deletedFavoriteCount} 个收藏缓存键!`);
    }

    // 5. 清理用户历史记录缓存
    console.log('🔍 查找并清理用户历史缓存...');
    const historyKeys = await redisClient.keys('user:history:*');
    if (historyKeys.length > 0) {
      const deletedHistoryCount = await redisClient.del(...historyKeys);
      console.log(`✅ 已删除 ${deletedHistoryCount} 个历史缓存键!`);
    }

    // 6. 清理其他可能的统计缓存
    console.log('🔍 查找并清理其他统计缓存...');
    const statsKeys = await redisClient.keys('stats:*');
    if (statsKeys.length > 0) {
      const deletedStatsCount = await redisClient.del(...statsKeys);
      console.log(`✅ 已删除 ${deletedStatsCount} 个统计缓存键!`);
    }

    // 7. 清理本地存储的统计数据
    console.log('🔍 清理本地存储的统计数据...');
    try {
      const fetch = (await import('node-fetch')).default;
      const response = await fetch('http://localhost:3000/api/admin/clear-local-stats', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        await response.json();
        console.log('✅ 本地存储统计数据已清空!');
      } else {
        console.warn('⚠️ 清理本地存储失败，但不影响主要清理流程');
      }
    } catch (localStorageError) {
      console.warn('⚠️ 无法连接到前端API清理本地存储，请手动刷新浏览器:', localStorageError.message);
    }

    console.log('🎉 所有影片数据和统计已成功清空!');

  } catch (err) {
    console.error('❌ 清空数据时出错:', err);
    process.exit(1);
  } finally {
    // 关闭数据库和Redis连接
    try {
      await pool.end();
      console.log('🔌 PostgreSQL连接已关闭');

      await redisClient.quit();
      console.log('🔌 Redis连接已关闭');
    } catch (closeErr) {
      console.error('❌ 关闭连接时出错:', closeErr);
    }
  }
}

// 运行清理脚本
cleanMoviesAndRedis();