const { Pool } = require('pg');
require('dotenv').config();

const pool = new Pool({
  host: process.env.PGHOST || 'localhost',
  port: process.env.PGPORT || 5432,
  database: process.env.PGDATABASE || 'javflix',
  user: process.env.PGUSER || 'longgedemacminim4',
  password: process.env.PGPASSWORD || '',
});

async function createMissingTables() {
  const client = await pool.connect();
  
  try {
    console.log('🔄 开始创建缺失的数据库表...');
    
    // 创建users表
    await client.query(`
      CREATE TABLE IF NOT EXISTS users (
        id SERIAL PRIMARY KEY,
        username VARCHAR(50) UNIQUE NOT NULL,
        email VARCHAR(100) UNIQUE NOT NULL,
        password VARCHAR(255) NOT NULL,
        is_admin BOOLEAN DEFAULT false,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        "lastLoginAt" TIMESTAMP,
        updated_at TIMESTAMP,
        "updatedAt" TIMESTAMP,
        "createdAt" TIMESTAMP,
        is_active BOOLEAN DEFAULT true
      );
    `);
    console.log('✅ users表创建成功');

    // 创建user_favorites表
    await client.query(`
      CREATE TABLE IF NOT EXISTS user_favorites (
        id SERIAL PRIMARY KEY,
        user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
        video_id VARCHAR(50) NOT NULL,
        note TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
    `);
    console.log('✅ user_favorites表创建成功');

    // 创建user_following表
    await client.query(`
      CREATE TABLE IF NOT EXISTS user_following (
        id SERIAL PRIMARY KEY,
        user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
        star_id INTEGER NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
    `);
    console.log('✅ user_following表创建成功');

    // 创建user_likes表
    await client.query(`
      CREATE TABLE IF NOT EXISTS user_likes (
        id SERIAL PRIMARY KEY,
        user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        video_id INTEGER NOT NULL REFERENCES movies(id) ON DELETE CASCADE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        UNIQUE(user_id, video_id)
      );
    `);
    console.log('✅ user_likes表创建成功');

    // 创建watch_history表
    await client.query(`
      CREATE TABLE IF NOT EXISTS watch_history (
        id SERIAL PRIMARY KEY,
        user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
        video_id INTEGER NOT NULL,
        progress INTEGER DEFAULT 0,
        completed BOOLEAN DEFAULT false,
        watched_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
    `);
    console.log('✅ watch_history表创建成功');

    // 创建video_stats表
    await client.query(`
      CREATE TABLE IF NOT EXISTS video_stats (
        id SERIAL PRIMARY KEY,
        views INTEGER DEFAULT 0,
        likes INTEGER DEFAULT 0,
        favorites INTEGER DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
    `);
    console.log('✅ video_stats表创建成功');

    // 创建movie_status_logs表
    await client.query(`
      CREATE TABLE IF NOT EXISTS movie_status_logs (
        id SERIAL PRIMARY KEY,
        movie_id INTEGER NOT NULL,
        old_status VARCHAR(20),
        new_status VARCHAR(20) NOT NULL,
        changed_by VARCHAR(100),
        reason TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );
    `);
    console.log('✅ movie_status_logs表创建成功');

    // 创建索引
    const indexes = [
      'CREATE INDEX IF NOT EXISTS idx_user_likes_created_at ON user_likes(created_at);',
      'CREATE INDEX IF NOT EXISTS idx_user_likes_user_id ON user_likes(user_id);',
      'CREATE INDEX IF NOT EXISTS idx_user_likes_video_id ON user_likes(video_id);',
      'CREATE INDEX IF NOT EXISTS idx_video_stats_views ON video_stats(views);',
      'CREATE INDEX IF NOT EXISTS idx_video_stats_likes ON video_stats(likes);',
      'CREATE INDEX IF NOT EXISTS idx_video_stats_favorites ON video_stats(favorites);',
      'CREATE INDEX IF NOT EXISTS idx_status_logs_movie_id ON movie_status_logs(movie_id);',
      'CREATE INDEX IF NOT EXISTS idx_status_logs_created_at ON movie_status_logs(created_at);'
    ];

    for (const indexQuery of indexes) {
      await client.query(indexQuery);
    }
    console.log('✅ 所有索引创建成功');

    // 验证表是否创建成功
    const result = await client.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      AND table_name IN ('users', 'user_favorites', 'user_following', 'user_likes', 'watch_history', 'video_stats', 'movie_status_logs')
      ORDER BY table_name;
    `);

    console.log('\n📊 创建的表列表:');
    result.rows.forEach(row => {
      console.log(`  - ${row.table_name}`);
    });

    console.log('\n🎉 所有缺失的表创建完成！');
    
  } catch (error) {
    console.error('❌ 创建表时出错:', error);
    throw error;
  } finally {
    client.release();
    await pool.end();
  }
}

// 运行脚本
createMissingTables()
  .then(() => {
    console.log('✅ 脚本执行完成');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ 脚本执行失败:', error);
    process.exit(1);
  });
