version: '3.8'

services:
  javbus-api:
    image: node:18-alpine
    container_name: javbus-api
    restart: unless-stopped
    working_dir: /app
    volumes:
      - ./javbus-api:/app
    ports:
      - "3001:3000"
    command: >
      sh -c "npm install && npm start"
    environment:
      - JAVBUS_HOST=https://www.javbus.com
      - NODE_ENV=production
      - PORT=3000

  # 主后端API服务
  javflix-api:
    build: .
    container_name: javflix-api
    restart: unless-stopped
    ports:
      - "4000:4000"
    depends_on:
      - javbus-api
    environment:
      - JAVBUS_API_URL=http://javbus-api:3000/api
      - PORT=4000
      # 其他环境变量...

networks:
  default:
    name: javflix-network 