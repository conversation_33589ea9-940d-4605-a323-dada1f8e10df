const { Pool } = require('pg');
const Redis = require('redis');

// 数据库连接配置
const pool = new Pool({
  host: process.env.PGHOST || 'localhost',
  port: parseInt(process.env.PGPORT || '5432'),
  database: process.env.PGDATABASE || 'javflix',
  user: process.env.PGUSER || 'longgedemacminim4',
  password: process.env.PGPASSWORD || '',
});

// Redis连接
const redisClient = Redis.createClient();

async function clearAllStats() {
  try {
    console.log('🧹 开始清空所有统计数据...');
    
    // 连接Redis
    await redisClient.connect();
    console.log('✅ Redis连接成功');
    
    // 1. 清空PostgreSQL中的view_count
    console.log('📊 清空PostgreSQL中的观看次数...');
    const pgResult = await pool.query('UPDATE movies SET view_count = 0 WHERE view_count > 0');
    console.log(`✅ PostgreSQL: 清空了 ${pgResult.rowCount} 个视频的观看次数`);
    
    // 2. 清空Redis中的所有视频统计数据
    console.log('🔄 清空Redis中的视频统计数据...');
    const keys = await redisClient.keys('video_stats:*');
    console.log(`📋 找到 ${keys.length} 个Redis统计键`);
    
    if (keys.length > 0) {
      await redisClient.del(keys);
      console.log(`✅ Redis: 删除了 ${keys.length} 个统计记录`);
    }
    
    // 3. 验证清空结果
    console.log('\n🔍 验证清空结果：');
    
    // 检查PostgreSQL
    const pgCheck = await pool.query('SELECT COUNT(*) as count FROM movies WHERE view_count > 0');
    console.log(`PostgreSQL中还有 ${pgCheck.rows[0].count} 个视频有观看次数`);
    
    // 检查Redis
    const redisCheck = await redisClient.keys('video_stats:*');
    console.log(`Redis中还有 ${redisCheck.length} 个统计记录`);
    
    if (pgCheck.rows[0].count === '0' && redisCheck.length === 0) {
      console.log('\n🎉 所有统计数据已成功清空！');
      console.log('💡 现在所有地方都会显示0观看次数，重新开始计数');
    } else {
      console.log('\n⚠️  清空可能不完整，请检查');
    }
    
  } catch (error) {
    console.error('❌ 清空失败:', error);
  } finally {
    // 关闭连接
    await redisClient.quit();
    await pool.end();
    console.log('🔌 连接已关闭');
  }
}

// 运行清空
clearAllStats(); 