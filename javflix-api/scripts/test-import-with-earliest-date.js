/**
 * 测试导入功能是否正确使用最早的磁力链接分享日期作为发行日期
 */

const axios = require('axios');

// JavBus API 基础 URL
const JAVBUS_API_URL = 'http://localhost:3000/api';
const JAVFLIX_API_URL = 'http://localhost:4000/api/javbus-admin';

/**
 * 测试导入单个影片
 */
async function testImportSingleMovie(movieId) {
  try {
    console.log(`测试导入影片: ${movieId}`);
    
    // 1. 先获取影片详情和磁力链接
    console.log('获取影片详情...');
    const detailResponse = await axios.get(`${JAVBUS_API_URL}/movies/${movieId}`);
    const movieDetail = detailResponse.data;
    
    console.log(`影片信息:`);
    console.log(`  标题: ${movieDetail.title}`);
    console.log(`  原始发行日期: ${movieDetail.date}`);
    
    // 2. 获取磁力链接
    console.log('获取磁力链接...');
    const magnetsResponse = await axios.get(`${JAVBUS_API_URL}/magnets/${movieId}`, {
      params: {
        gid: movieDetail.gid,
        uc: movieDetail.uc
      }
    });
    
    const magnets = magnetsResponse.data || [];
    console.log(`磁力链接列表 (${magnets.length} 个):`);
    
    // 显示所有磁力链接的分享日期
    magnets.forEach((magnet, index) => {
      console.log(`  ${index + 1}. ${magnet.title} - ${magnet.shareDate} (${magnet.size})`);
    });
    
    // 3. 计算最早的分享日期
    let earliestDate = null;
    for (const magnet of magnets) {
      if (magnet.shareDate) {
        const shareDate = new Date(magnet.shareDate);
        if (!isNaN(shareDate.getTime())) {
          if (!earliestDate || shareDate < earliestDate) {
            earliestDate = shareDate;
          }
        }
      }
    }
    
    if (earliestDate) {
      const earliestDateStr = earliestDate.toISOString().split('T')[0];
      console.log(`最早分享日期: ${earliestDateStr}`);
      console.log(`预期的发行日期: ${earliestDateStr} (而不是 ${movieDetail.date})`);
      
      // 4. 调用导入API
      console.log('开始测试导入...');
      const importResponse = await axios.get(`${JAVFLIX_API_URL}/import-movies`, {
        params: {
          count: 1,
          page: 1
        }
      });
      
      console.log('导入结果:', importResponse.data);
      
    } else {
      console.log('未找到有效的磁力链接分享日期');
    }
    
  } catch (error) {
    console.error('测试过程中发生错误:', error.message);
    if (error.response) {
      console.error('响应数据:', error.response.data);
    }
  }
}

/**
 * 验证数据库中的发行日期是否正确
 */
async function verifyDatabaseReleaseDate(movieId) {
  try {
    console.log('验证数据库中的发行日期...');
    
    // 使用测试脚本检查
    const { exec } = require('child_process');
    const util = require('util');
    const execAsync = util.promisify(exec);
    
    const { stdout } = await execAsync(`node scripts/update-earliest-release-date.js --test ${movieId}`);
    console.log('数据库验证结果:');
    console.log(stdout);
    
  } catch (error) {
    console.error('验证数据库时发生错误:', error.message);
  }
}

// 脚本入口
if (require.main === module) {
  const args = process.argv.slice(2);
  const movieId = args[0] || 'FNS-007';
  
  console.log('=========================================================');
  console.log('测试导入功能中的发行日期自动计算');
  console.log('=========================================================');
  
  testImportSingleMovie(movieId)
    .then(() => verifyDatabaseReleaseDate(movieId))
    .catch(console.error);
}