/**
 * 测试导入时磁力链接日期早于发行日期的情况
 */

const axios = require('axios');

const JAVBUS_API_URL = 'http://localhost:3000/api';

/**
 * 检查多个影片，找到磁力链接分享日期早于发行日期的例子
 */
async function findEarlyMagnetExample() {
  const testMovies = [
    'CAWD-780', 'CAWD-779', 'CAWD-778', 'CAWD-777', 'CAWD-776',
    'SSIS-950', 'SSIS-949', 'SSIS-948', 'SSIS-947', 'SSIS-946',
    'FSDSS-890', 'FSDSS-889', 'FSDSS-888', 'FSDSS-887', 'FSDSS-886'
  ];

  for (const movieId of testMovies) {
    try {
      console.log(`\n检查影片: ${movieId}`);
      
      // 获取影片详情
      const detailResponse = await axios.get(`${JAVBUS_API_URL}/movies/${movieId}`);
      const movieDetail = detailResponse.data;
      
      console.log(`  原始发行日期: ${movieDetail.date}`);
      
      // 获取磁力链接
      const magnetsResponse = await axios.get(`${JAVBUS_API_URL}/magnets/${movieId}`, {
        params: {
          gid: movieDetail.gid,
          uc: movieDetail.uc
        }
      });
      
      const magnets = magnetsResponse.data || [];
      
      if (magnets.length > 0) {
        // 计算最早的分享日期
        let earliestDate = null;
        for (const magnet of magnets) {
          if (magnet.shareDate) {
            const shareDate = new Date(magnet.shareDate);
            if (!isNaN(shareDate.getTime())) {
              if (!earliestDate || shareDate < earliestDate) {
                earliestDate = shareDate;
              }
            }
          }
        }
        
        if (earliestDate) {
          const earliestDateStr = earliestDate.toISOString().split('T')[0];
          const originalDate = new Date(movieDetail.date);
          
          console.log(`  最早磁力分享日期: ${earliestDateStr}`);
          
          if (earliestDate < originalDate) {
            console.log(`  ✅ 找到！磁力分享日期早于发行日期`);
            console.log(`  📅 预期使用日期: ${earliestDateStr} (而不是 ${movieDetail.date})`);
            return { movieId, originalDate: movieDetail.date, earliestMagnetDate: earliestDateStr };
          } else {
            console.log(`  ❌ 磁力分享日期不早于发行日期`);
          }
        } else {
          console.log(`  ⚠️ 没有有效的磁力分享日期`);
        }
      } else {
        console.log(`  ⚠️ 没有磁力链接`);
      }
      
    } catch (error) {
      console.log(`  ❌ 获取 ${movieId} 失败: ${error.message}`);
    }
  }
  
  return null;
}

/**
 * 测试导入指定影片
 */
async function testImportMovie(movieId) {
  try {
    console.log(`\n🚀 开始测试导入影片: ${movieId}`);
    
    const importResponse = await axios.get('http://localhost:4000/api/javbus-admin/import-movies', {
      params: {
        count: 1,
        page: 1
      }
    });
    
    console.log('导入结果:', importResponse.data?.data?.message);
    return importResponse.data;
    
  } catch (error) {
    console.error('导入失败:', error.message);
    return null;
  }
}

// 主程序
async function main() {
  console.log('='.repeat(60));
  console.log('寻找磁力链接分享日期早于发行日期的影片');
  console.log('='.repeat(60));
  
  const example = await findEarlyMagnetExample();
  
  if (example) {
    console.log(`\n🎯 测试影片: ${example.movieId}`);
    console.log(`📅 原始发行日期: ${example.originalDate}`);
    console.log(`🧲 最早磁力分享日期: ${example.earliestMagnetDate}`);
    
    // 测试导入
    await testImportMovie(example.movieId);
  } else {
    console.log('\n❌ 未找到合适的测试影片，所有检查的影片磁力分享日期都不早于发行日期');
  }
}

if (require.main === module) {
  main().catch(console.error);
}