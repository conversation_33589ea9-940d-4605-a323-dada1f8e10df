/**
 * 测试智能日期选择逻辑
 * 比较JAVBUS发行日期和磁力链接日期，选择较早的
 */

const axios = require('axios');

const JAVBUS_API_URL = 'http://localhost:3000/api';
const JAVFLIX_API_URL = 'http://localhost:4000/api/javbus-admin';

/**
 * 解析日期字符串
 */
function parseShareDate(shareDate) {
  if (!shareDate || typeof shareDate !== 'string') {
    return null;
  }
  
  const date = new Date(shareDate);
  return isNaN(date.getTime()) ? null : date;
}

/**
 * 测试日期比较逻辑
 */
async function testDateComparisonLogic() {
  console.log('🔍 测试智能日期选择逻辑...\n');
  
  const testCases = [
    'FNS-007',   // 磁力日期早于发行日期的情况
    'CAWD-780',  // 另一个测试案例
    'EROFV-316', // 可能磁力日期晚于发行日期的情况
  ];

  for (const movieId of testCases) {
    try {
      console.log(`📝 测试影片: ${movieId}`);
      
      // 1. 获取影片详情
      const detailResponse = await axios.get(`${JAVBUS_API_URL}/movies/${movieId}`);
      const movieDetail = detailResponse.data;
      
      console.log(`  JAVBUS API 发行日期: ${movieDetail.date}`);
      
      // 2. 获取磁力链接
      const magnetsResponse = await axios.get(`${JAVBUS_API_URL}/magnets/${movieId}`, {
        params: {
          gid: movieDetail.gid,
          uc: movieDetail.uc
        }
      });
      
      if (magnetsResponse.data && magnetsResponse.data.length > 0) {
        // 找到最早的磁力分享日期
        let earliestMagnetDate = null;
        const magnetDates = [];
        
        for (const magnet of magnetsResponse.data) {
          if (magnet.shareDate) {
            const shareDate = parseShareDate(magnet.shareDate);
            if (shareDate) {
              magnetDates.push(magnet.shareDate);
              if (!earliestMagnetDate || shareDate < earliestMagnetDate) {
                earliestMagnetDate = shareDate;
              }
            }
          }
        }
        
        console.log(`  磁力链接数量: ${magnetsResponse.data.length}`);
        console.log(`  磁力分享日期: ${magnetDates.join(', ')}`);
        console.log(`  最早磁力日期: ${earliestMagnetDate ? earliestMagnetDate.toISOString().split('T')[0] : '无'}`);
        
        // 3. 应用智能日期选择逻辑
        let finalReleaseDate = movieDetail.date;
        
        if (earliestMagnetDate && movieDetail.date) {
          const originalReleaseDate = parseShareDate(movieDetail.date);
          
          if (originalReleaseDate) {
            if (earliestMagnetDate < originalReleaseDate) {
              finalReleaseDate = earliestMagnetDate.toISOString().split('T')[0];
              console.log(`  ✅ 结果: 磁力日期(${finalReleaseDate})早于发行日期(${movieDetail.date})，使用磁力日期`);
            } else {
              finalReleaseDate = movieDetail.date;
              console.log(`  ✅ 结果: 发行日期(${movieDetail.date})早于等于磁力日期，使用发行日期`);
            }
          } else {
            finalReleaseDate = earliestMagnetDate.toISOString().split('T')[0];
            console.log(`  ✅ 结果: 发行日期无法解析，使用磁力日期: ${finalReleaseDate}`);
          }
        } else if (earliestMagnetDate && !movieDetail.date) {
          finalReleaseDate = earliestMagnetDate.toISOString().split('T')[0];
          console.log(`  ✅ 结果: 无原始发行日期，使用磁力日期: ${finalReleaseDate}`);
        } else {
          console.log(`  ✅ 结果: 使用原始发行日期: ${finalReleaseDate}`);
        }
        
        console.log(`  📅 最终选择的发行日期: ${finalReleaseDate}`);
        
      } else {
        console.log(`  ❌ 无磁力链接数据`);
      }
      
    } catch (error) {
      console.error(`  ❌ 测试影片 ${movieId} 失败:`, error.message);
    }
    
    console.log(); // 空行分隔
  }
}

/**
 * 测试导入一个新影片并验证日期逻辑
 */
async function testImportWithSmartDate() {
  console.log('🚀 测试导入新影片的智能日期选择...\n');
  
  try {
    // 导入一个影片
    const importResponse = await axios.get(`${JAVFLIX_API_URL}/import-movies`, {
      params: {
        count: 1,
        page: 1
      }
    });
    
    console.log('导入结果:', importResponse.data);
    
  } catch (error) {
    console.error('❌ 导入测试失败:', error.message);
  }
}

// 执行测试
async function runTests() {
  try {
    await testDateComparisonLogic();
    console.log('=' .repeat(60));
    await testImportWithSmartDate();
  } catch (error) {
    console.error('测试执行失败:', error);
  }
}

runTests();