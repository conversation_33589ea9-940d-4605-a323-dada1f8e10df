/**
 * 从磁力链接的分享日期中找到最早的日期，作为作品发行日期更新到数据库
 * 
 * 运行方式：
 * node scripts/update-earliest-release-date.js
 * 
 * 功能描述：
 * 1. 查询所有有磁力链接的影片
 * 2. 对每部影片，找到所有磁力链接中最早的分享日期
 * 3. 将最早的分享日期设置为该影片的发行日期
 * 4. 支持批量处理和进度显示
 */

const { Pool } = require('pg');
const fs = require('fs');
const path = require('path');

// 数据库配置
const pool = new Pool({
  user: process.env.DB_USER || 'longgedemacminim4',
  host: process.env.DB_HOST || 'localhost',
  database: process.env.DB_NAME || 'javflix',
  password: process.env.DB_PASSWORD || '',
  port: process.env.DB_PORT || 5432,
});

/**
 * 将分享日期字符串转换为标准日期格式
 * @param {string} shareDate - 分享日期字符串，如 "2025-04-28"
 * @returns {Date|null} - 解析后的日期对象，失败返回null
 */
function parseShareDate(shareDate) {
  if (!shareDate || typeof shareDate !== 'string') {
    return null;
  }
  
  // 支持多种日期格式
  const dateFormats = [
    /^(\d{4}-\d{2}-\d{2})$/,           // 2025-04-28
    /^(\d{4})\/(\d{2})\/(\d{2})$/,     // 2025/04/28
    /^(\d{2})\/(\d{2})\/(\d{4})$/,     // 28/04/2025
    /^(\d{4})年(\d{1,2})月(\d{1,2})日$/ // 2025年4月28日
  ];
  
  // 标准ISO格式
  if (dateFormats[0].test(shareDate)) {
    const date = new Date(shareDate);
    return isNaN(date.getTime()) ? null : date;
  }
  
  // 其他格式的解析可以在这里添加
  // 简单起见，先尝试直接解析
  const date = new Date(shareDate);
  return isNaN(date.getTime()) ? null : date;
}

/**
 * 更新单个影片的发行日期
 * @param {Object} client - 数据库客户端
 * @param {number} movieId - 影片数据库ID
 * @param {string} movieCode - 影片番号
 * @param {Date} earliestDate - 最早的分享日期
 */
async function updateMovieReleaseDate(client, movieId, movieCode, earliestDate) {
  try {
    const releaseDate = earliestDate.toISOString().split('T')[0]; // 格式化为 YYYY-MM-DD
    
    const result = await client.query(
      'UPDATE movies SET release_date = $1, updated_at = NOW() WHERE id = $2',
      [releaseDate, movieId]
    );
    
    if (result.rowCount > 0) {
      console.log(`✅ 已更新影片 ${movieCode} 的发行日期为: ${releaseDate}`);
      return true;
    } else {
      console.log(`⚠️ 影片 ${movieCode} 更新失败，未找到记录`);
      return false;
    }
  } catch (error) {
    console.error(`❌ 更新影片 ${movieCode} 发行日期失败:`, error.message);
    return false;
  }
}

/**
 * 获取影片的最早磁力链接分享日期
 * @param {Object} client - 数据库客户端
 * @param {number} movieId - 影片数据库ID
 * @returns {Date|null} - 最早的分享日期，没有有效日期返回null
 */
async function getEarliestMagnetDate(client, movieId) {
  try {
    const magnetsResult = await client.query(
      'SELECT share_date FROM magnets WHERE movie_id = $1 AND share_date IS NOT NULL',
      [movieId]
    );
    
    if (magnetsResult.rows.length === 0) {
      return null;
    }
    
    let earliestDate = null;
    
    for (const row of magnetsResult.rows) {
      const shareDate = parseShareDate(row.share_date);
      if (shareDate) {
        if (!earliestDate || shareDate < earliestDate) {
          earliestDate = shareDate;
        }
      }
    }
    
    return earliestDate;
  } catch (error) {
    console.error(`获取影片 ${movieId} 磁力链接日期失败:`, error.message);
    return null;
  }
}

/**
 * 主函数：批量更新所有影片的发行日期
 */
async function updateAllMoviesReleaseDate() {
  const client = await pool.connect();
  
  try {
    console.log('🚀 开始更新影片发行日期...');
    
    // 获取所有有磁力链接的影片
    const moviesResult = await client.query(`
      SELECT DISTINCT m.id, m.movie_id, m.title, m.release_date,
             COUNT(mag.id) as magnet_count
      FROM movies m 
      INNER JOIN magnets mag ON m.id = mag.movie_id 
      WHERE mag.share_date IS NOT NULL
      GROUP BY m.id, m.movie_id, m.title, m.release_date
      ORDER BY m.id
    `);
    
    console.log(`📊 找到 ${moviesResult.rows.length} 部有磁力链接的影片`);
    
    let processedCount = 0;
    let updatedCount = 0;
    let skippedCount = 0;
    let errorCount = 0;
    
    // 处理每部影片
    for (const movie of moviesResult.rows) {
      try {
        console.log(`\n📽️ 处理影片: ${movie.movie_id} - ${movie.title}`);
        console.log(`   当前发行日期: ${movie.release_date || '未设置'}`);
        console.log(`   磁力链接数量: ${movie.magnet_count}`);
        
        // 获取最早的磁力链接分享日期
        const earliestDate = await getEarliestMagnetDate(client, movie.id);
        
        if (!earliestDate) {
          console.log(`⚠️ 影片 ${movie.movie_id} 没有有效的磁力链接分享日期，跳过`);
          skippedCount++;
          continue;
        }
        
        const earliestDateStr = earliestDate.toISOString().split('T')[0];
        console.log(`   最早磁力分享日期: ${earliestDateStr}`);
        
        // 检查是否需要更新
        if (movie.release_date === earliestDateStr) {
          console.log(`✨ 影片 ${movie.movie_id} 发行日期已是最早日期，无需更新`);
          skippedCount++;
          continue;
        }
        
        // 更新发行日期
        const updateSuccess = await updateMovieReleaseDate(
          client, 
          movie.id, 
          movie.movie_id, 
          earliestDate
        );
        
        if (updateSuccess) {
          updatedCount++;
        } else {
          errorCount++;
        }
        
      } catch (movieError) {
        console.error(`❌ 处理影片 ${movie.movie_id} 时出错:`, movieError.message);
        errorCount++;
      } finally {
        processedCount++;
        
        // 显示进度
        const progress = ((processedCount / moviesResult.rows.length) * 100).toFixed(1);
        console.log(`📈 进度: ${processedCount}/${moviesResult.rows.length} (${progress}%)`);
      }
    }
    
    // 输出最终统计
    console.log('\n' + '='.repeat(50));
    console.log('🎯 更新完成！统计信息：');
    console.log(`📊 处理影片总数: ${processedCount}`);
    console.log(`✅ 成功更新数量: ${updatedCount}`);
    console.log(`⏭️ 跳过数量: ${skippedCount}`);
    console.log(`❌ 错误数量: ${errorCount}`);
    console.log('='.repeat(50));
    
    // 保存处理日志
    const logData = {
      timestamp: new Date().toISOString(),
      processed: processedCount,
      updated: updatedCount,
      skipped: skippedCount,
      errors: errorCount,
      movies: moviesResult.rows.map(m => ({
        id: m.movie_id,
        title: m.title,
        oldReleaseDate: m.release_date
      }))
    };
    
    const logDir = path.join(__dirname, '../logs');
    if (!fs.existsSync(logDir)) {
      fs.mkdirSync(logDir, { recursive: true });
    }
    
    const logFile = path.join(logDir, `release-date-update-${Date.now()}.json`);
    fs.writeFileSync(logFile, JSON.stringify(logData, null, 2));
    console.log(`📝 处理日志已保存到: ${logFile}`);
    
  } catch (error) {
    console.error('❌ 更新过程中发生错误:', error);
  } finally {
    client.release();
    await pool.end();
  }
}

/**
 * 测试单个影片的处理（用于调试）
 */
async function testSingleMovie(movieCode) {
  const client = await pool.connect();
  
  try {
    console.log(`🔍 测试影片: ${movieCode}`);
    
    const movieResult = await client.query(
      'SELECT id, movie_id, title, release_date FROM movies WHERE movie_id = $1',
      [movieCode]
    );
    
    if (movieResult.rows.length === 0) {
      console.log(`❌ 未找到影片: ${movieCode}`);
      return;
    }
    
    const movie = movieResult.rows[0];
    console.log(`找到影片: ${movie.title}`);
    console.log(`当前发行日期: ${movie.release_date || '未设置'}`);
    
    // 获取所有磁力链接
    const magnetsResult = await client.query(
      'SELECT share_date, title, size FROM magnets WHERE movie_id = $1 ORDER BY share_date',
      [movie.id]
    );
    
    console.log(`\n磁力链接列表 (${magnetsResult.rows.length} 个):`);
    magnetsResult.rows.forEach((magnet, index) => {
      console.log(`  ${index + 1}. ${magnet.title} - ${magnet.share_date} (${magnet.size})`);
    });
    
    const earliestDate = await getEarliestMagnetDate(client, movie.id);
    if (earliestDate) {
      console.log(`\n最早分享日期: ${earliestDate.toISOString().split('T')[0]}`);
    } else {
      console.log(`\n未找到有效的分享日期`);
    }
    
  } catch (error) {
    console.error('测试过程中发生错误:', error);
  } finally {
    client.release();
    await pool.end();
  }
}

// 脚本入口
if (require.main === module) {
  const args = process.argv.slice(2);
  
  if (args.length > 0 && args[0] === '--test') {
    // 测试模式
    const movieCode = args[1] || 'FNS-007';
    testSingleMovie(movieCode);
  } else {
    // 正常批量更新模式
    updateAllMoviesReleaseDate();
  }
}

module.exports = {
  updateAllMoviesReleaseDate,
  testSingleMovie,
  parseShareDate,
  getEarliestMagnetDate
};