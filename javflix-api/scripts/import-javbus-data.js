const axios = require('axios');
const fs = require('fs');
const path = require('path');
const { Pool } = require('pg');
const https = require('https');
const crypto = require('crypto');

// 创建数据库连接池
const pool = new Pool({
  host: process.env.PGHOST || 'localhost',
  port: parseInt(process.env.PGPORT || '5432'),
  database: process.env.PGDATABASE || 'javflix',
  user: process.env.PGUSER || 'longgedemacminim4',
  password: process.env.PGPASSWORD || '',
});

// JavBus API URL
const JAVBUS_API_URL = 'http://localhost:3000/api';

// 定义图片保存的路径
const IMAGE_BASE_DIR = path.join(__dirname, '../../javflix/public/images/javbus');

/**
 * 获取哈希文件名
 */
function getHashedFileName(url) {
  const hash = crypto.createHash('md5').update(url).digest('hex');
  const ext = path.extname(url) || '.jpg'; // 默认为jpg
  return `${hash}${ext}`;
}

/**
 * 确保目录存在
 */
function ensureDirExists(dirPath) {
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true });
  }
}

/**
 * 下载图片并保存到本地
 */
async function downloadImage(url, type = 'cover') {
  if (!url) return null;
  
  console.log(`正在下载图片: ${url}`);
  const imageDirPath = path.join(IMAGE_BASE_DIR, type);
  ensureDirExists(imageDirPath);
  
  const hashedFileName = getHashedFileName(url);
  const filePath = path.join(imageDirPath, hashedFileName);
  const publicPath = `/images/javbus/${type}/${hashedFileName}`;
  
  // 如果文件已存在，直接返回路径
  if (fs.existsSync(filePath)) {
    console.log(`图片已存在，跳过下载: ${publicPath}`);
    return publicPath;
  }
  
  return new Promise((resolve, reject) => {
    const file = fs.createWriteStream(filePath);
    
    https.get(url, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Referer': 'https://www.javbus.com/'
      },
      timeout: 15000 // 15秒超时
    }, (response) => {
      if (response.statusCode !== 200) {
        file.close();
        fs.unlink(filePath, () => {}); // 删除可能部分下载的文件
        console.error(`下载失败，状态码: ${response.statusCode}`);
        resolve(null);
        return;
      }
      
      response.pipe(file);
      
      file.on('finish', () => {
        file.close();
        console.log(`图片下载成功: ${publicPath}`);
        resolve(publicPath);
      });
    }).on('error', (err) => {
      file.close();
      fs.unlink(filePath, () => {});
      console.error('下载图片出错:', err.message);
      resolve(null);
    });
  });
}

/**
 * 从JavBus获取影片数据并保存到JSON文件和数据库
 */
async function importMovies(count = 5, page = 1) {
  try {
    console.log(`开始从JavBus API导入数据，数量：${count}，页码：${page}`);
    
    // 1. 从JavBus API获取影片列表
    console.log(`正在获取影片列表：${JAVBUS_API_URL}/movies?page=${page}&magnet=exist`);
    const javbusResponse = await axios.get(`${JAVBUS_API_URL}/movies?page=${page}&magnet=exist`);
    const movies = javbusResponse.data.movies.slice(0, parseInt(count));
    
    console.log(`成功获取${movies.length}部影片的基本信息`);
    
    // 确保图片目录存在
    ensureDirExists(IMAGE_BASE_DIR);
    
    // 确保数据目录存在
    const dataDir = path.join(__dirname, '../data');
    if (!fs.existsSync(dataDir)) {
      fs.mkdirSync(dataDir, { recursive: true });
    }
    
    // 创建连接池客户端
    const client = await pool.connect();
    
    try {
      // 确保表结构存在
      await createTablesIfNotExist(client);
      
      // 统计数据
      let successCount = 0;
      let censoredCount = 0;
      let uncensoredCount = 0;
      const importedMovies = [];
      
      // 2. 遍历每部影片，获取详情并立即写入数据库
      for (const movie of movies) {
        try {
          console.log(`正在获取影片详情：${movie.id}`);
          // 获取影片详情
          const detailResponse = await axios.get(`${JAVBUS_API_URL}/movies/${movie.id}`);
          const movieDetail = detailResponse.data;
          
          console.log(`正在获取磁力链接：${movie.id}`);
          // 获取磁力链接
          const magnetsResponse = await axios.get(`${JAVBUS_API_URL}/magnets/${movie.id}`, {
            params: {
              gid: movieDetail.gid,
              uc: movieDetail.uc
            }
          });
          
          // 下载封面图片
          console.log(`正在下载封面图片: ${movie.id}`);
          const localCoverPath = await downloadImage(movieDetail.img, 'cover');
          
          // 下载演员图片
          const processedStars = [];
          if (movieDetail.stars && movieDetail.stars.length > 0) {
            for (const star of movieDetail.stars) {
              if (star.img) {
                const localStarPath = await downloadImage(star.img, 'actress');
                processedStars.push({
                  id: star.id,
                  name: star.name,
                  image: localStarPath || null, // 只使用本地图片路径，没有则为null
                  localImage: localStarPath
                });
              } else {
                processedStars.push({
                  id: star.id,
                  name: star.name,
                  image: null,
                  localImage: null
                });
              }
            }
          }
          
          // 下载样品图片
          const processedSamples = [];
          if (movieDetail.samples && movieDetail.samples.length > 0) {
            for (const sample of movieDetail.samples) {
              // 不再下载样品图片和缩略图
              processedSamples.push({
                id: sample.id,
                alt: sample.alt
              });
            }
          }
          
          // 转换成我们需要的格式
          const processedMovie = {
            id: movie.id,
            title: movieDetail.title,
            imageUrl: localCoverPath || null, // 只使用本地图片路径，没有则为null
            localImageUrl: localCoverPath,
            coverImage: localCoverPath || null, // 只使用本地图片路径，没有则为null
            localCoverImage: localCoverPath,
            releaseDate: movieDetail.date,
            duration: movieDetail.videoLength ? `${movieDetail.videoLength}分钟` : '未知',
            description: `${movieDetail.title} - ${movie.id}`,
            // 添加导演信息
            director: movieDetail.director ? {
              id: movieDetail.director.id,
              name: movieDetail.director.name
            } : null,
            // 添加制作商信息
            producer: movieDetail.producer ? {
              id: movieDetail.producer.id,
              name: movieDetail.producer.name
            } : null,
            // 添加发行商信息
            publisher: movieDetail.publisher ? {
              id: movieDetail.publisher.id,
              name: movieDetail.publisher.name
            } : null,
            // 添加系列信息
            series: movieDetail.series ? {
              id: movieDetail.series.id,
              name: movieDetail.series.name
            } : null,
            stars: processedStars, // 使用处理过的带本地路径的演员数据
            genres: movieDetail.genres ? movieDetail.genres.map(genre => genre.name) : [],
            // 添加样品图片
            samples: processedSamples, // 使用处理过的带本地路径的样品数据
            // 添加相似影片
            similarMovies: movieDetail.similarMovies ? movieDetail.similarMovies.map(similar => ({
              id: similar.id,
              title: similar.title,
              imageUrl: null // 不再保存相似影片的外部URL
            })) : [],
            magnets: magnetsResponse.data ? magnetsResponse.data.map(magnet => ({
              id: magnet.id,
              link: magnet.link,
              title: magnet.title,
              size: magnet.size,
              isHD: magnet.isHD,
              hasSubtitle: magnet.hasSubtitle,
              shareDate: magnet.shareDate
            })) : []
          };
          
          // 立即保存到数据库
          console.log(`正在保存影片到数据库: ${movie.id}`);
          const result = await saveSingleMovieToDatabase(client, processedMovie);
          
          if (result.isUncensored) {
            uncensoredCount++;
          } else {
            censoredCount++;
          }
          
          successCount++;
          importedMovies.push(processedMovie);
          console.log(`成功处理并保存影片：${movie.id}`);
          
        } catch (detailError) {
          console.error(`获取或保存影片详情失败: ${movie.id}`, detailError);
        }
      }
      
      // 3. 最后一次性保存到JSON文件作为备份
      fs.writeFileSync(
        path.join(dataDir, 'javbus_movies.json'), 
        JSON.stringify(importedMovies, null, 2)
      );
      
      console.log(`成功保存${importedMovies.length}部影片到JSON文件`);
      
      // 4. 更新首页展示数据
      updateHomePageData(importedMovies);
      
      // 5. 更新分类计数
      await updateCategoryCounts(client, censoredCount, uncensoredCount);
      
      console.log('===== 分类统计 =====');
      console.log(`导入有码影片: ${censoredCount} 部`);
      console.log(`导入无码影片: ${uncensoredCount} 部`);
      console.log('===================');
      
      console.log(`成功导入${successCount}部影片到数据库`);
      return importedMovies;
    } finally {
      client.release();
    }
  } catch (error) {
    console.error('导入JavBus数据失败:', error);
    throw error;
  }
}

/**
 * 更新分类计数
 */
async function updateCategoryCounts(client, censoredCount, uncensoredCount) {
  // 确保有"有码"和"无码"两个分类
  const CENSORED_CATEGORY_ID = 83; // 有码分类ID
  const UNCENSORED_CATEGORY_ID = 77; // 无码分类ID
  
  await client.query(`UPDATE categories SET count = (
    SELECT COUNT(*) FROM video_categories WHERE category_id = ${CENSORED_CATEGORY_ID}
  ) WHERE id = ${CENSORED_CATEGORY_ID}`);
  
  await client.query(`UPDATE categories SET count = (
    SELECT COUNT(*) FROM video_categories WHERE category_id = ${UNCENSORED_CATEGORY_ID}
  ) WHERE id = ${UNCENSORED_CATEGORY_ID}`);
}

/**
 * 保存单部影片数据到数据库
 */
async function saveSingleMovieToDatabase(client, movie) {
  try {
    await client.query('BEGIN');
    
    // 确保有"有码"和"无码"两个分类
    const CENSORED_CATEGORY_ID = 83; // 有码分类ID
    const UNCENSORED_CATEGORY_ID = 77; // 无码分类ID
    
    // 1. 插入电影基本信息
    const movieResult = await client.query(
      `INSERT INTO movies 
       (movie_id, title, image_url, cover_image, cached_image_url, release_date, duration, description, created_at, updated_at) 
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8, NOW(), NOW())
       ON CONFLICT (movie_id) 
       DO UPDATE SET 
          title = $2, 
          image_url = $3, 
          cover_image = $4,
          cached_image_url = $5,
          release_date = $6, 
          duration = $7, 
          description = $8,
          updated_at = NOW()
       RETURNING id`,
      [
        movie.id, 
        movie.title, 
        movie.localCoverImage, // 只使用本地图片路径
        movie.localCoverImage, // 只使用本地图片路径
        movie.localCoverImage, // 本地图片路径
        movie.releaseDate, 
        movie.duration, 
        movie.description
      ]
    );
    
    const movieDbId = movieResult.rows[0].id;
    
    // 判断电影是有码还是无码
    const movieId = movie.id;
    // 常见无码ID格式: n1234, heyzo-1234, xxx-av-12345, k1234, 1pondo-123456_789 等
    const uncensoredPattern = /^(n\d+|heyzo-?\d+|xxx-av-?\d+|k\d+|\d{6}[-_]\d{3}|carib|pondo|gachi|1pon|mura|siro|fc2)/i;
    const isUncensored = uncensoredPattern.test(movieId);
    
    // 创建分类关联
    const categoryId = isUncensored ? UNCENSORED_CATEGORY_ID : CENSORED_CATEGORY_ID;
    
    // 插入电影分类关联
    await client.query(
      `INSERT INTO video_categories (video_id, category_id) 
       VALUES ($1, $2)
       ON CONFLICT (video_id, category_id) DO NOTHING`,
      [movieDbId, categoryId]
    );
    
    console.log(`影片 ${movieId} 已分类为: ${isUncensored ? '无码' : '有码'}`);
    
    // 2. 处理导演信息
    if (movie.director) {
      // 插入或更新导演
      const directorResult = await client.query(
        `INSERT INTO directors 
         (director_id, name, created_at, updated_at) 
         VALUES ($1, $2, NOW(), NOW())
         ON CONFLICT (director_id) 
         DO UPDATE SET 
            name = $2,
            updated_at = NOW()
         RETURNING id`,
        [movie.director.id, movie.director.name]
      );
      
      const directorDbId = directorResult.rows[0].id;
      
      // 建立电影与导演的关联
      await client.query(
        `INSERT INTO movie_directors (movie_id, director_id) 
         VALUES ($1, $2)
         ON CONFLICT (movie_id, director_id) DO NOTHING`,
        [movieDbId, directorDbId]
      );
    }
    
    // 3. 处理制作商信息
    if (movie.producer) {
      // 插入或更新制作商
      const producerResult = await client.query(
        `INSERT INTO producers 
         (producer_id, name, created_at, updated_at) 
         VALUES ($1, $2, NOW(), NOW())
         ON CONFLICT (producer_id) 
         DO UPDATE SET 
            name = $2,
            updated_at = NOW()
         RETURNING id`,
        [movie.producer.id, movie.producer.name]
      );
      
      // 更新电影的制作商关联
      await client.query(
        `UPDATE movies SET producer_id = $1 WHERE id = $2`,
        [producerResult.rows[0].id, movieDbId]
      );
    }
    
    // 4. 处理发行商信息
    if (movie.publisher) {
      // 插入或更新发行商
      const publisherResult = await client.query(
        `INSERT INTO publishers 
         (publisher_id, name, created_at, updated_at) 
         VALUES ($1, $2, NOW(), NOW())
         ON CONFLICT (publisher_id) 
         DO UPDATE SET 
            name = $2,
            updated_at = NOW()
         RETURNING id`,
        [movie.publisher.id, movie.publisher.name]
      );
      
      // 更新电影的发行商关联
      await client.query(
        `UPDATE movies SET publisher_id = $1 WHERE id = $2`,
        [publisherResult.rows[0].id, movieDbId]
      );
    }
    
    // 5. 处理系列信息
    if (movie.series) {
      // 插入或更新系列
      const seriesResult = await client.query(
        `INSERT INTO series 
         (series_id, name, created_at, updated_at) 
         VALUES ($1, $2, NOW(), NOW())
         ON CONFLICT (series_id) 
         DO UPDATE SET 
            name = $2,
            updated_at = NOW()
         RETURNING id`,
        [movie.series.id, movie.series.name]
      );
      
      // 更新电影的系列关联
      await client.query(
        `UPDATE movies SET series_id = $1 WHERE id = $2`,
        [seriesResult.rows[0].id, movieDbId]
      );
    }
    
    // 6. 处理演员信息
    if (movie.stars && movie.stars.length > 0) {
      // 先清除现有关联
      await client.query('DELETE FROM movie_stars WHERE movie_id = $1', [movieDbId]);
      
      for (const star of movie.stars) {
        // 插入或更新演员
        const starResult = await client.query(
          `INSERT INTO stars 
           (star_id, name, image_url, cached_image_url, created_at, updated_at) 
           VALUES ($1, $2, $3, $4, NOW(), NOW())
           ON CONFLICT (star_id) 
           DO UPDATE SET 
              name = $2, 
              image_url = $3,
              cached_image_url = COALESCE($4, stars.cached_image_url),
              updated_at = NOW()
           RETURNING id`,
          [star.id, star.name, star.localImage, star.localImage]
        );
        
        const starDbId = starResult.rows[0].id;
        
        // 建立电影与演员的关联
        await client.query(
          `INSERT INTO movie_stars (movie_id, star_id) VALUES ($1, $2)`,
          [movieDbId, starDbId]
        );
      }
    }
    
    // 7. 处理类型信息
    if (movie.genres && movie.genres.length > 0) {
      // 先清除现有关联
      await client.query('DELETE FROM movie_genres WHERE movie_id = $1', [movieDbId]);
      
      for (const genreName of movie.genres) {
        // 插入或获取类型
        const genreResult = await client.query(
          `INSERT INTO genres 
           (name, created_at, updated_at) 
           VALUES ($1, NOW(), NOW())
           ON CONFLICT (name) 
           DO UPDATE SET 
              updated_at = NOW()
           RETURNING id`,
          [genreName]
        );
        
        const genreDbId = genreResult.rows[0].id;
        
        // 建立电影与类型的关联
        await client.query(
          `INSERT INTO movie_genres (movie_id, genre_id) VALUES ($1, $2)`,
          [movieDbId, genreDbId]
        );
      }
    }
    
    // 8. 处理相似影片
    if (movie.similarMovies && movie.similarMovies.length > 0) {
      // 先清除现有相似影片
      await client.query('DELETE FROM similar_movies WHERE movie_id = $1', [movieDbId]);
      
      for (const similar of movie.similarMovies) {
        // 对于相似影片，尝试查找是否已经有本地缓存的图片
        const cachedImageResult = await client.query(
          `SELECT cached_image_url FROM movies WHERE movie_id = $1`,
          [similar.id]
        );
        
        // 如果数据库中已有这部电影，并且有本地图片，就使用本地图片
        const cachedImageUrl = cachedImageResult.rows.length > 0 && cachedImageResult.rows[0].cached_image_url 
          ? cachedImageResult.rows[0].cached_image_url 
          : null;
        
        await client.query(
          `INSERT INTO similar_movies 
           (movie_id, similar_movie_id, similar_title, similar_image, created_at, updated_at) 
           VALUES ($1, $2, $3, $4, NOW(), NOW())`,
          [movieDbId, similar.id, similar.title, cachedImageUrl]
        );
      }
    }
    
    // 10. 处理磁力链接
    if (movie.magnets && movie.magnets.length > 0) {
      // 先清除现有磁力链接
      await client.query('DELETE FROM magnets WHERE movie_id = $1', [movieDbId]);
      
      for (const magnet of movie.magnets) {
        await client.query(
          `INSERT INTO magnets 
           (movie_id, magnet_id, link, title, size, is_hd, has_subtitle, share_date, created_at, updated_at) 
           VALUES ($1, $2, $3, $4, $5, $6, $7, $8, NOW(), NOW())`,
          [
            movieDbId, 
            magnet.id, 
            magnet.link, 
            magnet.title, 
            magnet.size, 
            magnet.isHD, 
            magnet.hasSubtitle, 
            magnet.shareDate
          ]
        );
      }
    }
    
    await client.query('COMMIT');
    
    return { success: true, movieId: movieDbId, isUncensored };
  } catch (error) {
    await client.query('ROLLBACK');
    console.error(`保存影片 ${movie.id} 到数据库失败:`, error);
    throw error;
  }
}

/**
 * 更新首页展示数据
 */
function updateHomePageData(movies) {
  const dataDir = path.join(__dirname, '../data');
  
  // 确保目录存在
  if (!fs.existsSync(dataDir)) {
    fs.mkdirSync(dataDir, { recursive: true });
  }
  
  // 更新最新影片
  fs.writeFileSync(
    path.join(dataDir, 'recent_videos.json'), 
    JSON.stringify(movies.slice(0, 10), null, 2)
  );
  
  // 更新热门影片（按照磁力链接数量排序）
  const popularMovies = [...movies]
    .sort((a, b) => (b.magnets ? b.magnets.length : 0) - (a.magnets ? a.magnets.length : 0))
    .slice(0, 10);
  
  fs.writeFileSync(
    path.join(dataDir, 'popular_videos.json'), 
    JSON.stringify(popularMovies, null, 2)
  );
  
  // 更新推荐影片（随机选择）
  const shuffled = [...movies].sort(() => 0.5 - Math.random());
  
  fs.writeFileSync(
    path.join(dataDir, 'recommended_videos.json'), 
    JSON.stringify(shuffled.slice(0, 6), null, 2)
  );
  
  // 更新特色影片（高清的）
  const featuredMovies = movies
    .filter(movie => movie.magnets && movie.magnets.some(m => m.isHD))
    .slice(0, 4);
  
  fs.writeFileSync(
    path.join(dataDir, 'featured_videos.json'), 
    JSON.stringify(featuredMovies, null, 2)
  );
  
  console.log('成功更新首页展示数据');
}

/**
 * 确保需要的表结构存在
 */
async function createTablesIfNotExist(client) {
  // 先创建不依赖其他表的基础表
  
  // 创建演员表
  await client.query(`
    CREATE TABLE IF NOT EXISTS stars (
      id SERIAL PRIMARY KEY,
      star_id VARCHAR(50) UNIQUE NOT NULL,
      name VARCHAR(100) NOT NULL,
      image_url TEXT,
      cached_image_url TEXT,
      created_at TIMESTAMP NOT NULL,
      updated_at TIMESTAMP NOT NULL
    )
  `);
  
  // 创建类型表
  await client.query(`
    CREATE TABLE IF NOT EXISTS genres (
      id SERIAL PRIMARY KEY,
      name VARCHAR(100) UNIQUE NOT NULL,
      created_at TIMESTAMP NOT NULL,
      updated_at TIMESTAMP NOT NULL
    )
  `);
  
  // 创建导演表
  await client.query(`
    CREATE TABLE IF NOT EXISTS directors (
      id SERIAL PRIMARY KEY,
      director_id VARCHAR(50) UNIQUE NOT NULL,
      name VARCHAR(100) NOT NULL,
      created_at TIMESTAMP NOT NULL,
      updated_at TIMESTAMP NOT NULL
    )
  `);
  
  // 创建制作商表
  await client.query(`
    CREATE TABLE IF NOT EXISTS producers (
      id SERIAL PRIMARY KEY,
      producer_id VARCHAR(50) UNIQUE NOT NULL,
      name VARCHAR(100) NOT NULL,
      created_at TIMESTAMP NOT NULL,
      updated_at TIMESTAMP NOT NULL
    )
  `);
  
  // 创建发行商表
  await client.query(`
    CREATE TABLE IF NOT EXISTS publishers (
      id SERIAL PRIMARY KEY,
      publisher_id VARCHAR(50) UNIQUE NOT NULL,
      name VARCHAR(100) NOT NULL,
      created_at TIMESTAMP NOT NULL,
      updated_at TIMESTAMP NOT NULL
    )
  `);
  
  // 创建系列表
  await client.query(`
    CREATE TABLE IF NOT EXISTS series (
      id SERIAL PRIMARY KEY,
      series_id VARCHAR(50) UNIQUE NOT NULL,
      name VARCHAR(200) NOT NULL,
      created_at TIMESTAMP NOT NULL,
      updated_at TIMESTAMP NOT NULL
    )
  `);
  
  // 创建电影表 (依赖其他表的外键)
  await client.query(`
    CREATE TABLE IF NOT EXISTS movies (
      id SERIAL PRIMARY KEY,
      movie_id VARCHAR(50) UNIQUE NOT NULL,
      title TEXT NOT NULL,
      image_url TEXT,
      cover_image TEXT,
      cached_image_url TEXT,
      release_date VARCHAR(50),
      duration VARCHAR(50),
      description TEXT,
      director_id INTEGER REFERENCES directors(id) ON DELETE SET NULL,
      producer_id INTEGER REFERENCES producers(id) ON DELETE SET NULL,
      publisher_id INTEGER REFERENCES publishers(id) ON DELETE SET NULL,
      series_id INTEGER REFERENCES series(id) ON DELETE SET NULL,
      created_at TIMESTAMP NOT NULL,
      updated_at TIMESTAMP NOT NULL
    )
  `);
  
  // 创建样品图片表
  await client.query(`
    CREATE TABLE IF NOT EXISTS samples (
      id SERIAL PRIMARY KEY,
      movie_id INTEGER REFERENCES movies(id) ON DELETE CASCADE,
      sample_id VARCHAR(50),        
      alt TEXT,
      created_at TIMESTAMP NOT NULL,
      updated_at TIMESTAMP NOT NULL
    )
  `);
  
  // 创建磁力链接表
  await client.query(`
    CREATE TABLE IF NOT EXISTS magnets (
      id SERIAL PRIMARY KEY,
      movie_id INTEGER REFERENCES movies(id) ON DELETE CASCADE,
      magnet_id VARCHAR(100),
      link TEXT NOT NULL,
      title TEXT,
      size VARCHAR(50),
      is_hd BOOLEAN DEFAULT FALSE,
      has_subtitle BOOLEAN DEFAULT FALSE,
      share_date VARCHAR(50),
      created_at TIMESTAMP NOT NULL,
      updated_at TIMESTAMP NOT NULL
    )
  `);
  
  // 创建电影-演员关联表
  await client.query(`
    CREATE TABLE IF NOT EXISTS movie_stars (
      movie_id INTEGER REFERENCES movies(id) ON DELETE CASCADE,
      star_id INTEGER REFERENCES stars(id) ON DELETE CASCADE,
      PRIMARY KEY (movie_id, star_id)
    )
  `);
  
  // 创建电影-类型关联表
  await client.query(`
    CREATE TABLE IF NOT EXISTS movie_genres (
      movie_id INTEGER REFERENCES movies(id) ON DELETE CASCADE,
      genre_id INTEGER REFERENCES genres(id) ON DELETE CASCADE,
      PRIMARY KEY (movie_id, genre_id)
    )
  `);
  
  // 创建相似影片表
  await client.query(`
    CREATE TABLE IF NOT EXISTS similar_movies (
      id SERIAL PRIMARY KEY,
      movie_id INTEGER REFERENCES movies(id) ON DELETE CASCADE,
      similar_movie_id VARCHAR(50) NOT NULL,
      similar_title TEXT,
      similar_image TEXT,
      created_at TIMESTAMP NOT NULL,
      updated_at TIMESTAMP NOT NULL
    )
  `);
  
  // 创建电影-导演关联表
  await client.query(`
    CREATE TABLE IF NOT EXISTS movie_directors (
      movie_id INTEGER REFERENCES movies(id) ON DELETE CASCADE,
      director_id INTEGER REFERENCES directors(id) ON DELETE CASCADE,
      PRIMARY KEY (movie_id, director_id)
    )
  `);
  
  console.log('成功创建数据库表');
}

// 如果直接运行脚本，执行导入
if (require.main === module) {
  // 获取命令行参数
  const count = process.argv[2] || 5;
  const page = process.argv[3] || 1;
  
  console.log(`开始导入JavBus数据到数据库，数量：${count}，页码：${page}`);
  
  importMovies(count, page)
    .then(movies => {
      console.log(`成功导入${movies.length}部影片`);
      process.exit(0);
    })
    .catch(error => {
      console.error('导入失败:', error);
      process.exit(1);
    });
}
