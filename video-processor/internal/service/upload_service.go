package service

import (
	"context"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"sync"

	"github.com/aliyun/aliyun-oss-go-sdk/oss"
	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/credentials"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/s3"
	"github.com/sirupsen/logrus"

	"javflix-video-processor/internal/config"
)

type UploadService struct {
	config    *config.Config
	logger    *logrus.Logger
	ossClient *oss.Client
	s3Client  *s3.S3
	r2Client  *s3.S3
}

type UploadProgress func(progress int)

func NewUploadService(cfg *config.Config, logger *logrus.Logger) *UploadService {
	service := &UploadService{
		config: cfg,
		logger: logger,
	}

	// 初始化对应的云存储客户端
	switch cfg.CDNProvider {
	case "cloudflare_r2":
		service.initCloudflareR2()
	case "aliyun_oss":
		service.initAliyunOSS()
	case "aws_s3":
		service.initAWSS3()
	default:
		logger.Fatalf("不支持的CDN提供商: %s", cfg.CDNProvider)
	}

	return service
}

func (us *UploadService) initCloudflareR2() {
	if us.config.R2AccessKeyID == "" || us.config.R2SecretAccessKey == "" {
		us.logger.Fatal("Cloudflare R2配置不完整")
	}

	// Cloudflare R2使用S3兼容API
	sess, err := session.NewSession(&aws.Config{
		Endpoint: aws.String(us.config.R2Endpoint),
		Region:   aws.String("auto"), // R2使用auto区域
		Credentials: credentials.NewStaticCredentials(
			us.config.R2AccessKeyID,
			us.config.R2SecretAccessKey,
			"",
		),
		S3ForcePathStyle: aws.Bool(true), // R2需要路径样式
	})
	if err != nil {
		us.logger.Fatalf("初始化Cloudflare R2客户端失败: %v", err)
	}

	us.r2Client = s3.New(sess)
	us.logger.Info("Cloudflare R2客户端初始化成功")
}

func (us *UploadService) initAliyunOSS() {
	if us.config.AliyunOSSAccessKey == "" || us.config.AliyunOSSSecretKey == "" {
		us.logger.Fatal("阿里云OSS配置不完整")
	}

	client, err := oss.New(us.config.AliyunOSSEndpoint, us.config.AliyunOSSAccessKey, us.config.AliyunOSSSecretKey)
	if err != nil {
		us.logger.Fatalf("初始化阿里云OSS客户端失败: %v", err)
	}

	us.ossClient = client
	us.logger.Info("阿里云OSS客户端初始化成功")
}

func (us *UploadService) initAWSS3() {
	if us.config.AWSAccessKeyID == "" || us.config.AWSSecretAccessKey == "" {
		us.logger.Fatal("AWS S3配置不完整")
	}

	sess, err := session.NewSession(&aws.Config{
		Region: aws.String(us.config.AWSRegion),
		Credentials: credentials.NewStaticCredentials(
			us.config.AWSAccessKeyID,
			us.config.AWSSecretAccessKey,
			"",
		),
	})
	if err != nil {
		us.logger.Fatalf("初始化AWS S3会话失败: %v", err)
	}

	us.s3Client = s3.New(sess)
	us.logger.Info("AWS S3客户端初始化成功")
}

func (us *UploadService) UploadDirectory(ctx context.Context, taskUUID, dirPath string, progressCallback UploadProgress) (map[string]interface{}, error) {
	us.logger.WithFields(logrus.Fields{
		"taskId":   taskUUID,
		"dirPath":  dirPath,
		"provider": us.config.CDNProvider,
	}).Info("开始上传目录到CDN")

	// 收集所有需要上传的文件
	files, err := us.collectFiles(dirPath)
	if err != nil {
		return nil, fmt.Errorf("收集文件失败: %w", err)
	}

	if len(files) == 0 {
		return nil, fmt.Errorf("没有找到需要上传的文件")
	}

	us.logger.WithField("fileCount", len(files)).Info("开始并发上传文件")

	// 并发上传文件
	results, err := us.uploadFilesParallel(ctx, taskUUID, dirPath, files, progressCallback)
	if err != nil {
		return nil, err
	}

	// 构建返回的URL结构
	return us.buildVideoUrls(taskUUID, results), nil
}

func (us *UploadService) collectFiles(dirPath string) ([]string, error) {
	var files []string

	err := filepath.Walk(dirPath, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		if !info.IsDir() {
			files = append(files, path)
		}

		return nil
	})

	return files, err
}

func (us *UploadService) uploadFilesParallel(ctx context.Context, taskUUID, basePath string, files []string, progressCallback UploadProgress) (map[string]string, error) {
	results := make(map[string]string)
	var mu sync.Mutex
	var wg sync.WaitGroup

	// 创建信号量限制并发数
	semaphore := make(chan struct{}, us.config.MaxConcurrentUploads)

	// 进度跟踪
	completed := 0
	total := len(files)

	for _, file := range files {
		wg.Add(1)
		go func(filePath string) {
			defer wg.Done()

			// 获取信号量
			semaphore <- struct{}{}
			defer func() { <-semaphore }()

			// 计算相对路径作为对象键
			relPath, err := filepath.Rel(basePath, filePath)
			if err != nil {
				us.logger.WithError(err).Errorf("计算相对路径失败: %s", filePath)
				return
			}

			// 构建CDN对象键
			objectKey := fmt.Sprintf("videos/%s/%s", taskUUID, strings.ReplaceAll(relPath, "\\", "/"))

			// 上传文件
			var cdnUrl string
			switch us.config.CDNProvider {
			case "cloudflare_r2":
				cdnUrl, err = us.uploadToCloudflareR2(filePath, objectKey)
			case "aliyun_oss":
				cdnUrl, err = us.uploadToAliyunOSS(filePath, objectKey)
			case "aws_s3":
				cdnUrl, err = us.uploadToAWSS3(filePath, objectKey)
			}

			if err != nil {
				us.logger.WithError(err).Errorf("上传文件失败: %s", filePath)
				return
			}

			// 保存结果
			mu.Lock()
			results[relPath] = cdnUrl
			completed++
			progress := (completed * 100) / total
			mu.Unlock()

			// 更新进度
			if progressCallback != nil {
				progressCallback(progress)
			}

			us.logger.WithFields(logrus.Fields{
				"file":     relPath,
				"cdnUrl":   cdnUrl,
				"progress": progress,
			}).Info("文件上传成功")

		}(file)
	}

	wg.Wait()

	if len(results) != len(files) {
		return results, fmt.Errorf("部分文件上传失败，成功: %d/%d", len(results), len(files))
	}

	return results, nil
}

func (us *UploadService) uploadToCloudflareR2(filePath, objectKey string) (string, error) {
	file, err := os.Open(filePath)
	if err != nil {
		return "", fmt.Errorf("打开文件失败: %w", err)
	}
	defer file.Close()

	contentType := us.getContentType(filePath)

	input := &s3.PutObjectInput{
		Bucket:      aws.String(us.config.R2Bucket),
		Key:         aws.String(objectKey),
		Body:        file,
		ContentType: aws.String(contentType),
	}

	// 如果是视频文件，设置缓存控制
	if strings.Contains(contentType, "video") || strings.HasSuffix(filePath, ".m3u8") || strings.HasSuffix(filePath, ".ts") {
		input.CacheControl = aws.String("max-age=31536000") // 1年缓存
	}

	// 设置公共读取权限
	input.ACL = aws.String("public-read")

	_, err = us.r2Client.PutObject(input)
	if err != nil {
		return "", fmt.Errorf("上传到Cloudflare R2失败: %w", err)
	}

	// 构建公共CDN URL
	var cdnUrl string
	if us.config.R2PublicDomain != "" {
		// 使用自定义域名
		cdnUrl = fmt.Sprintf("%s/%s", strings.TrimRight(us.config.R2PublicDomain, "/"), objectKey)
	} else {
		// 使用R2的公共URL格式
		cdnUrl = fmt.Sprintf("https://pub-%s.r2.dev/%s", us.config.R2Bucket, objectKey)
	}

	return cdnUrl, nil
}

func (us *UploadService) uploadToAliyunOSS(filePath, objectKey string) (string, error) {
	bucket, err := us.ossClient.Bucket(us.config.AliyunOSSBucket)
	if err != nil {
		return "", fmt.Errorf("获取OSS bucket失败: %w", err)
	}

	// 设置内容类型
	contentType := us.getContentType(filePath)
	options := []oss.Option{
		oss.ContentType(contentType),
	}

	// 如果是视频文件，设置缓存控制
	if strings.Contains(contentType, "video") || strings.HasSuffix(filePath, ".m3u8") || strings.HasSuffix(filePath, ".ts") {
		options = append(options, oss.CacheControl("max-age=31536000")) // 1年缓存
	}

	err = bucket.PutObjectFromFile(objectKey, filePath, options...)
	if err != nil {
		return "", fmt.Errorf("上传到OSS失败: %w", err)
	}

	// 构建CDN URL
	cdnUrl := fmt.Sprintf("https://%s.%s/%s", us.config.AliyunOSSBucket, us.config.AliyunOSSEndpoint, objectKey)
	return cdnUrl, nil
}

func (us *UploadService) uploadToAWSS3(filePath, objectKey string) (string, error) {
	file, err := os.Open(filePath)
	if err != nil {
		return "", fmt.Errorf("打开文件失败: %w", err)
	}
	defer file.Close()

	contentType := us.getContentType(filePath)

	input := &s3.PutObjectInput{
		Bucket:      aws.String(us.config.AWSS3Bucket),
		Key:         aws.String(objectKey),
		Body:        file,
		ContentType: aws.String(contentType),
	}

	// 如果是视频文件，设置缓存控制
	if strings.Contains(contentType, "video") || strings.HasSuffix(filePath, ".m3u8") || strings.HasSuffix(filePath, ".ts") {
		input.CacheControl = aws.String("max-age=31536000") // 1年缓存
	}

	_, err = us.s3Client.PutObject(input)
	if err != nil {
		return "", fmt.Errorf("上传到S3失败: %w", err)
	}

	// 构建CDN URL
	cdnUrl := fmt.Sprintf("https://%s.s3.%s.amazonaws.com/%s", us.config.AWSS3Bucket, us.config.AWSRegion, objectKey)
	return cdnUrl, nil
}

func (us *UploadService) getContentType(filePath string) string {
	ext := strings.ToLower(filepath.Ext(filePath))

	contentTypes := map[string]string{
		".m3u8": "application/vnd.apple.mpegurl",
		".ts":   "video/mp2t",
		".mp4":  "video/mp4",
		".jpg":  "image/jpeg",
		".jpeg": "image/jpeg",
		".png":  "image/png",
		".webp": "image/webp",
	}

	if contentType, exists := contentTypes[ext]; exists {
		return contentType
	}

	return "application/octet-stream"
}

func (us *UploadService) buildVideoUrls(taskUUID string, uploadResults map[string]string) map[string]interface{} {
	videoUrls := map[string]interface{}{
		"qualities":  []map[string]interface{}{},
		"thumbnails": []string{},
	}

	qualities := []map[string]interface{}{}
	thumbnails := []string{}

	for relPath, cdnUrl := range uploadResults {
		if strings.HasSuffix(relPath, "playlist.m3u8") {
			// 这是一个质量的播放列表
			parts := strings.Split(relPath, string(filepath.Separator))
			if len(parts) >= 2 {
				resolution := parts[0] // 例如: "1080p/playlist.m3u8" -> "1080p"

				quality := map[string]interface{}{
					"resolution": resolution,
					"url":        cdnUrl,
				}

				// 尝试从分辨率推断比特率
				switch resolution {
				case "1080p":
					quality["bitrate"] = "5000k"
				case "720p":
					quality["bitrate"] = "3000k"
				case "480p":
					quality["bitrate"] = "1500k"
				}

				qualities = append(qualities, quality)
			}
		} else if strings.Contains(relPath, "thumbnails") && (strings.HasSuffix(relPath, ".jpg") || strings.HasSuffix(relPath, ".png")) {
			// 这是缩略图
			thumbnails = append(thumbnails, cdnUrl)
		}
	}

	videoUrls["qualities"] = qualities
	videoUrls["thumbnails"] = thumbnails

	us.logger.WithFields(logrus.Fields{
		"taskId":     taskUUID,
		"qualities":  len(qualities),
		"thumbnails": len(thumbnails),
	}).Info("视频URL构建完成")

	return videoUrls
}