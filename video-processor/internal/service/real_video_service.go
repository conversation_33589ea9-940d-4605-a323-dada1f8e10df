package service

import (
	"context"
	"fmt"
	"os"
	"path/filepath"
	"strings"

	"github.com/sirupsen/logrus"
	ffmpeg "github.com/u2takey/ffmpeg-go"

	"javflix-video-processor/internal/config"
)

// RealVideoService 真实的视频处理服务
type RealVideoService struct {
	config *config.Config
	logger *logrus.Logger
}

func NewRealVideoService(cfg *config.Config, logger *logrus.Logger) *RealVideoService {
	return &RealVideoService{
		config: cfg,
		logger: logger,
	}
}

// AddRealWatermark 真实的水印添加
func (rvs *RealVideoService) AddRealWatermark(ctx context.Context, task *ProcessTask, inputPath string) (string, error) {
	rvs.logger.WithField("taskId", task.TaskUUID).Info("开始真实水印处理")

	// 获取水印配置
	watermarkText := "JAVFLIX.TV"
	if text, ok := task.WatermarkConfig["text"].(string); ok && text != "" {
		watermarkText = text
	}

	position := "bottom-right"
	if pos, ok := task.WatermarkConfig["position"].(string); ok && pos != "" {
		position = pos
	}

	opacity := 0.8
	if op, ok := task.WatermarkConfig["opacity"].(float64); ok {
		opacity = op
	}

	fontSize := 24
	if fs, ok := task.WatermarkConfig["fontSize"].(float64); ok {
		fontSize = int(fs)
	}

	// 生成输出文件路径
	outputPath := rvs.generateOutputPath(inputPath, "_watermarked")

	// 构建水印位置参数
	positionArgs := rvs.getWatermarkPosition(position)

	rvs.logger.WithFields(logrus.Fields{
		"taskId":        task.TaskUUID,
		"watermarkText": watermarkText,
		"position":      position,
		"opacity":       opacity,
		"fontSize":      fontSize,
		"outputPath":    outputPath,
	}).Info("开始FFmpeg水印处理")

	// 使用FFmpeg添加水印
	err := ffmpeg.Input(inputPath).
		Filter("drawtext", ffmpeg.Args{
			fmt.Sprintf("text=%s", watermarkText),
			fmt.Sprintf("fontsize=%d", fontSize),
			fmt.Sprintf("fontcolor=white@%.1f", opacity),
			positionArgs,
		}).
		Output(outputPath).
		GlobalArgs("-c:v", "libx264", "-preset", "fast", "-crf", "23").
		GlobalArgs("-c:a", "copy"). // 复制音频流，不重新编码
		OverWriteOutput().
		Run()

	if err != nil {
		return "", fmt.Errorf("FFmpeg水印处理失败: %w", err)
	}

	rvs.logger.WithField("taskId", task.TaskUUID).Info("真实水印处理完成")
	return outputPath, nil
}

// CreateRealHLSSegments 真实的HLS切片
func (rvs *RealVideoService) CreateRealHLSSegments(ctx context.Context, task *ProcessTask, inputPath string) (string, error) {
	rvs.logger.WithField("taskId", task.TaskUUID).Info("开始真实HLS切片处理")

	// 创建输出目录
	outputDir := rvs.generateOutputDir(task.TaskUUID)
	if err := os.MkdirAll(outputDir, 0755); err != nil {
		return "", fmt.Errorf("创建输出目录失败: %w", err)
	}

	// 获取切片配置
	qualities := []map[string]interface{}{
		{"resolution": "1080p", "bitrate": "5000k", "scale": "1920:1080"},
		{"resolution": "720p", "bitrate": "3000k", "scale": "1280:720"},
	}
	if sliceConfig, ok := task.SliceConfig["qualities"].([]interface{}); ok {
		qualities = []map[string]interface{}{}
		for _, q := range sliceConfig {
			if qualityMap, ok := q.(map[string]interface{}); ok {
				qualities = append(qualities, qualityMap)
			}
		}
	}

	segmentDuration := 6 // 6秒，符合Apple推荐标准
	if duration, ok := task.SliceConfig["segmentDuration"].(float64); ok {
		segmentDuration = int(duration)
	}

	rvs.logger.WithFields(logrus.Fields{
		"taskId":          task.TaskUUID,
		"qualities":       len(qualities),
		"segmentDuration": segmentDuration,
	}).Info("开始真实多分辨率切片")

	// 为每个质量生成HLS文件
	for _, quality := range qualities {
		resolution := quality["resolution"].(string)
		bitrate := quality["bitrate"].(string)
		scale := quality["scale"].(string)

		qualityDir := filepath.Join(outputDir, resolution)
		if err := os.MkdirAll(qualityDir, 0755); err != nil {
			return "", fmt.Errorf("创建质量目录失败: %w", err)
		}

		// 使用FFmpeg进行真实的HLS切片
		playlistPath := filepath.Join(qualityDir, "playlist.m3u8")
		segmentPattern := filepath.Join(qualityDir, "segment_%03d.ts")

		rvs.logger.WithFields(logrus.Fields{
			"taskId":     task.TaskUUID,
			"resolution": resolution,
			"bitrate":    bitrate,
			"scale":      scale,
		}).Info("开始FFmpeg HLS切片")

		err := ffmpeg.Input(inputPath).
			Output(playlistPath).
			GlobalArgs(
				"-c:v", "libx264",
				"-preset", "fast",
				"-b:v", bitrate,
				"-vf", fmt.Sprintf("scale=%s", scale),
				"-c:a", "aac",
				"-b:a", "128k",
				"-hls_time", fmt.Sprintf("%d", segmentDuration),
				"-hls_playlist_type", "vod",
				"-hls_segment_filename", segmentPattern,
				"-f", "hls",
			).
			OverWriteOutput().
			Run()

		if err != nil {
			return "", fmt.Errorf("FFmpeg HLS切片失败 (%s): %w", resolution, err)
		}

		rvs.logger.WithFields(logrus.Fields{
			"taskId":     task.TaskUUID,
			"resolution": resolution,
		}).Info("真实分辨率切片完成")
	}

	rvs.logger.WithField("taskId", task.TaskUUID).Info("真实HLS切片处理完成")
	return outputDir, nil
}

// GenerateRealThumbnails 真实的缩略图生成
func (rvs *RealVideoService) GenerateRealThumbnails(taskUUID, inputPath, outputDir string) error {
	thumbnailDir := filepath.Join(outputDir, "thumbnails")
	if err := os.MkdirAll(thumbnailDir, 0755); err != nil {
		return fmt.Errorf("创建缩略图目录失败: %w", err)
	}

	rvs.logger.WithField("taskId", taskUUID).Info("开始生成真实缩略图")

	// 生成5个缩略图，每60秒一个
	for i := 1; i <= 5; i++ {
		thumbnailPath := filepath.Join(thumbnailDir, fmt.Sprintf("thumb_%03d.jpg", i))
		seekTime := fmt.Sprintf("%d", i*60) // 每60秒一个缩略图

		err := ffmpeg.Input(inputPath).
			Output(thumbnailPath).
			GlobalArgs(
				"-ss", seekTime,
				"-vframes", "1",
				"-q:v", "2",
				"-vf", "scale=320:240",
			).
			OverWriteOutput().
			Run()

		if err != nil {
			rvs.logger.WithError(err).Warnf("生成缩略图%d失败", i)
			continue
		}

		rvs.logger.WithFields(logrus.Fields{
			"taskId":       taskUUID,
			"thumbnailNum": i,
			"seekTime":     seekTime,
		}).Info("缩略图生成成功")
	}

	rvs.logger.WithField("taskId", taskUUID).Info("真实缩略图生成完成")
	return nil
}

// GetRealVideoDuration 获取真实的视频时长
func (rvs *RealVideoService) GetRealVideoDuration(inputPath string) (float64, error) {
	// 使用ffprobe获取真实视频时长
	// 这里简化实现，实际应该调用ffprobe命令
	
	// 检查文件是否存在
	if _, err := os.Stat(inputPath); os.IsNotExist(err) {
		return 0, fmt.Errorf("视频文件不存在: %s", inputPath)
	}

	// 获取文件信息进行估算
	fileInfo, err := os.Stat(inputPath)
	if err != nil {
		return 0, fmt.Errorf("无法获取文件信息: %w", err)
	}

	// 根据文件大小估算时长（粗略估算）
	fileSizeMB := float64(fileInfo.Size()) / (1024 * 1024)
	estimatedDuration := fileSizeMB * 8 // 假设平均码率为1Mbps

	// 设置合理的范围
	if estimatedDuration < 60 {
		estimatedDuration = 300 // 最少5分钟
	} else if estimatedDuration > 7200 {
		estimatedDuration = 7200 // 最多2小时
	}

	rvs.logger.WithFields(logrus.Fields{
		"inputPath":         inputPath,
		"fileSizeMB":        fileSizeMB,
		"estimatedDuration": estimatedDuration,
	}).Info("估算视频时长")

	return estimatedDuration, nil
}

// 辅助方法
func (rvs *RealVideoService) getWatermarkPosition(position string) string {
	switch position {
	case "top-left":
		return "x=10:y=10"
	case "top-right":
		return "x=w-tw-10:y=10"
	case "bottom-left":
		return "x=10:y=h-th-10"
	case "bottom-right":
		return "x=w-tw-10:y=h-th-10"
	case "center":
		return "x=(w-tw)/2:y=(h-th)/2"
	default:
		return "x=w-tw-10:y=h-th-10"
	}
}

func (rvs *RealVideoService) generateOutputPath(inputPath, suffix string) string {
	dir := filepath.Dir(inputPath)
	name := strings.TrimSuffix(filepath.Base(inputPath), filepath.Ext(inputPath))
	ext := filepath.Ext(inputPath)
	return filepath.Join(dir, name+suffix+ext)
}

func (rvs *RealVideoService) generateOutputDir(taskUUID string) string {
	return filepath.Join(rvs.config.TempDir, "output", taskUUID)
}
