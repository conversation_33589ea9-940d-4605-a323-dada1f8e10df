package service

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/anacrolix/torrent"
	"github.com/sirupsen/logrus"
	ffmpeg "github.com/u2takey/ffmpeg-go"

	"javflix-video-processor/internal/config"
	"javflix-video-processor/internal/database"
	"javflix-video-processor/internal/websocket"
)

type VideoService struct {
	config        *config.Config
	torrentClient *torrent.Client
	logger        *logrus.Logger
	uploadService *UploadService
	realService   *RealVideoService
	db            *database.DB
	wsHub         *websocket.Hub
}

type ProcessTask struct {
	TaskUUID        string                 `json:"taskUuid"`
	MovieID         int                    `json:"movieId"`
	MagnetLink      string                 `json:"magnetLink"`
	WatermarkConfig map[string]interface{} `json:"watermarkConfig"`
	SliceConfig     map[string]interface{} `json:"sliceConfig"`
	UploadConfig    map[string]interface{} `json:"uploadConfig"`
	CallbackURL     string                 `json:"callbackUrl"`
}

type ProgressUpdate struct {
	TaskUUID  string `json:"taskUuid"`
	Progress  int    `json:"progress"`
	Status    string `json:"status"`
	Message   string `json:"message"`
	Timestamp string `json:"timestamp"`
}

type CompletionCallback struct {
	TaskUUID  string                 `json:"taskUuid"`
	Success   bool                   `json:"success"`
	VideoUrls map[string]interface{} `json:"videoUrls"`
	Error     string                 `json:"error,omitempty"`
}

func NewVideoService(cfg *config.Config, wsHub *websocket.Hub) *VideoService {
	// 初始化日志
	logger := logrus.New()
	level, err := logrus.ParseLevel(cfg.LogLevel)
	if err != nil {
		level = logrus.InfoLevel
	}
	logger.SetLevel(level)
	logger.SetFormatter(&logrus.JSONFormatter{})

	// 初始化数据库连接 (可选)
	var db *database.DB
	if cfg.DatabaseHost != "" {
		var err error
		db, err = database.NewDB(
			cfg.DatabaseHost,
			cfg.DatabasePort,
			cfg.DatabaseName,
			cfg.DatabaseUser,
			cfg.DatabasePassword,
			logger,
		)
		if err != nil {
			logger.Warnf("数据库连接失败，将在无数据库模式下运行: %v", err)
			db = nil
		} else {
			logger.Info("数据库连接成功")
		}
	} else {
		logger.Info("未配置数据库，在无数据库模式下运行")
	}

	// 初始化BitTorrent客户端
	clientConfig := torrent.NewDefaultClientConfig()
	clientConfig.DataDir = cfg.TempDir
	clientConfig.NoUpload = false // 允许上传以获得更好的下载速度
	clientConfig.Seed = true      // 允许做种
	clientConfig.ListenPort = 0   // 使用随机端口避免冲突

	client, err := torrent.NewClient(clientConfig)
	if err != nil {
		logger.Fatalf("初始化BitTorrent客户端失败: %v", err)
	}

	// 创建临时目录
	if err := os.MkdirAll(cfg.TempDir, 0755); err != nil {
		logger.Fatalf("创建临时目录失败: %v", err)
	}

	// 初始化上传服务
	uploadService := NewUploadService(cfg, logger)

	return &VideoService{
		config:        cfg,
		torrentClient: client,
		logger:        logger,
		uploadService: uploadService,
		realService:   NewRealVideoService(cfg, logger),
		db:            db,
		wsHub:         wsHub,
	}
}

func (vs *VideoService) ProcessVideo(ctx context.Context, task *ProcessTask) error {
	vs.logger.WithFields(logrus.Fields{
		"taskId":  task.TaskUUID,
		"movieId": task.MovieID,
	}).Info("开始处理视频任务")

	// 首先在数据库中创建任务记录
	if vs.db != nil {
		dbTask := &database.VideoProcessingTask{
			TaskUUID:      task.TaskUUID,
			MovieID:       task.MovieID,
			MagnetLink:    task.MagnetLink,
			Status:        "processing",
			Progress:      0,
			CurrentStep:   "initializing",
			WatermarkText: "JAVFLIX.TV",
			WatermarkPos:  "bottom-right",
			Quality:       "high",
			Metadata:      make(map[string]interface{}),
		}

		if err := vs.db.CreateTask(dbTask); err != nil {
			vs.logger.WithError(err).Error("创建数据库任务记录失败")
			// 继续处理，不因为数据库问题中断任务
		} else {
			vs.logger.WithField("taskId", task.TaskUUID).Info("数据库任务记录创建成功")
		}
	}

	// 更新进度: 开始处理
	vs.updateProgress(task.TaskUUID, 5, "downloading", "开始下载视频...")

	// 1. 下载视频 (5% -> 40%)
	videoPath, err := vs.downloadVideo(ctx, task)
	if err != nil {
		vs.logger.WithError(err).Error("下载视频失败")
		vs.notifyFailure(task, fmt.Sprintf("下载视频失败: %v", err))
		return err
	}
	defer vs.cleanupFile(videoPath)

	// 2. 添加水印 (40% -> 60%)
	watermarkedPath, err := vs.addWatermark(ctx, task, videoPath)
	if err != nil {
		vs.logger.WithError(err).Error("添加水印失败")
		vs.notifyFailure(task, fmt.Sprintf("添加水印失败: %v", err))
		return err
	}
	defer vs.cleanupFile(watermarkedPath)

	// 3. HLS切片 (60% -> 85%)
	hlsDir, err := vs.createHLSSegments(ctx, task, watermarkedPath)
	if err != nil {
		vs.logger.WithError(err).Error("HLS切片失败")
		vs.notifyFailure(task, fmt.Sprintf("HLS切片失败: %v", err))
		return err
	}
	defer vs.cleanupDir(hlsDir)

	// 4. 上传到CDN (85% -> 100%)
	cdnUrls, err := vs.uploadToCDN(ctx, task, hlsDir)
	if err != nil {
		vs.logger.WithError(err).Error("上传CDN失败")
		vs.notifyFailure(task, fmt.Sprintf("上传CDN失败: %v", err))
		return err
	}

	// 5. 通知完成
	return vs.notifyCompletion(task, cdnUrls)
}

func (vs *VideoService) downloadVideo(ctx context.Context, task *ProcessTask) (string, error) {
	vs.updateProgress(task.TaskUUID, 5, "downloading", "解析磁力链接...")

	vs.logger.WithFields(logrus.Fields{
		"taskId":     task.TaskUUID,
		"magnetLink": task.MagnetLink,
	}).Info("开始真实下载过程")

	// 添加磁力链接
	t, err := vs.torrentClient.AddMagnet(task.MagnetLink)
	if err != nil {
		return "", fmt.Errorf("添加磁力链接失败: %w", err)
	}

	vs.updateProgress(task.TaskUUID, 10, "downloading", "等待种子信息...")

	// 等待种子信息，增加超时时间到10分钟
	select {
	case <-t.GotInfo():
		vs.logger.WithField("taskId", task.TaskUUID).Info("成功获取种子信息")
	case <-ctx.Done():
		return "", ctx.Err()
	case <-time.After(10 * time.Minute):
		return "", fmt.Errorf("获取种子信息超时")
	}

	vs.updateProgress(task.TaskUUID, 15, "downloading", "选择视频文件...")

	// 选择最大的视频文件
	var largestFile *torrent.File
	for _, file := range t.Files() {
		vs.logger.WithFields(logrus.Fields{
			"taskId":   task.TaskUUID,
			"fileName": file.DisplayPath(),
			"fileSize": file.Length(),
		}).Info("发现文件")

		if vs.isVideoFile(file.DisplayPath()) {
			if largestFile == nil || file.Length() > largestFile.Length() {
				largestFile = file
			}
		}
	}

	if largestFile == nil {
		// 如果没有找到视频文件，选择最大的文件
		for _, file := range t.Files() {
			if largestFile == nil || file.Length() > largestFile.Length() {
				largestFile = file
			}
		}
	}

	if largestFile == nil {
		return "", fmt.Errorf("未找到任何文件")
	}

	vs.logger.WithFields(logrus.Fields{
		"taskId":   task.TaskUUID,
		"fileName": largestFile.DisplayPath(),
		"fileSize": largestFile.Length(),
	}).Info("开始下载文件")

	// 设置文件优先级为高
	largestFile.SetPriority(torrent.PiecePriorityHigh)

	// 开始下载
	largestFile.Download()

	// 等待下载完成，最多等待60分钟（增加超时时间）
	timeout := time.After(60 * time.Minute)
	ticker := time.NewTicker(5 * time.Second) // 统一检查间隔为5秒
	defer ticker.Stop()

	vs.logger.WithFields(logrus.Fields{
		"taskId":   task.TaskUUID,
		"fileSize": largestFile.Length(),
		"fileName": largestFile.DisplayPath(),
	}).Info("开始等待下载完成")

	var lastCompleted int64
	var lastTime time.Time
	isFirstCheck := true

	for {
		select {
		case <-ctx.Done():
			return "", ctx.Err()
		case <-timeout:
			return "", fmt.Errorf("下载超时（60分钟）")
		case <-ticker.C:
			completed := largestFile.BytesCompleted()
			total := largestFile.Length()
			currentTime := time.Now()

			// 计算真实进度（5% - 40%，为后续处理步骤预留空间）
			realProgress := float64(completed)/float64(total)*35 + 5

			// 计算下载速度
			var downloadSpeed float64
			if !isFirstCheck && !lastTime.IsZero() {
				timeDiff := currentTime.Sub(lastTime).Seconds()
				bytesDiff := completed - lastCompleted
				if timeDiff > 0 && bytesDiff > 0 {
					downloadSpeed = float64(bytesDiff) / timeDiff / 1024 / 1024 // MB/s
				}
			}

			// 更新记录
			lastCompleted = completed
			lastTime = currentTime
			isFirstCheck = false

			// 构建进度消息
			var message string
			if downloadSpeed > 0 {
				message = fmt.Sprintf("下载中... %.1f MB/s", downloadSpeed)
			} else {
				message = "下载中..."
			}

			// 统一的进度更新方法
			vs.updateProgressWithBytes(task.TaskUUID, int(realProgress), "downloading", message, completed, total, downloadSpeed)

			vs.logger.WithFields(logrus.Fields{
				"taskId":    task.TaskUUID,
				"completed": completed,
				"total":     total,
				"progress":  fmt.Sprintf("%.1f%%", realProgress),
				"speed":     fmt.Sprintf("%.1f MB/s", downloadSpeed),
			}).Info("下载进度更新")

			// 确保下载真正完成
			if completed == total && completed > 0 {
				// 额外等待5秒确保文件写入完成
				vs.logger.WithField("taskId", task.TaskUUID).Info("下载完成，等待文件写入...")
				time.Sleep(5 * time.Second)

				vs.updateProgress(task.TaskUUID, 40, "processing", "视频下载完成")

				// 构建完整的文件路径 - torrent文件通常在子目录中
				outputPath := filepath.Join(vs.config.TempDir, largestFile.DisplayPath())

				// 检查文件是否真的存在并且大小正确
				if fileInfo, err := os.Stat(outputPath); os.IsNotExist(err) {
					vs.logger.WithField("taskId", task.TaskUUID).Error("下载完成但文件不存在，检查可能的路径")

					// 尝试在torrent目录中查找文件
					torrentDir := filepath.Join(vs.config.TempDir, t.Name())
					if _, err := os.Stat(torrentDir); err == nil {
						// 在torrent目录中查找最大的视频文件
						err := filepath.Walk(torrentDir, func(path string, info os.FileInfo, err error) error {
							if err != nil {
								return err
							}
							if !info.IsDir() && vs.isVideoFile(path) && info.Size() >= total {
								outputPath = path
								vs.logger.WithFields(logrus.Fields{
									"taskId":       task.TaskUUID,
									"foundPath":    path,
									"fileSize":     info.Size(),
									"expectedSize": total,
								}).Info("在torrent目录中找到完整视频文件")
								return filepath.SkipDir
							}
							return nil
						})
						if err != nil {
							vs.logger.WithError(err).Error("搜索torrent目录失败")
						}
					}
				} else if err != nil {
					return "", fmt.Errorf("检查文件失败: %w", err)
				} else if fileInfo.Size() != total {
					vs.logger.WithFields(logrus.Fields{
						"taskId":       task.TaskUUID,
						"actualSize":   fileInfo.Size(),
						"expectedSize": total,
					}).Warn("文件大小不匹配，继续等待")
					continue
				}

				// 最终验证文件存在且大小正确
				if finalInfo, err := os.Stat(outputPath); err == nil && finalInfo.Size() >= total {
					vs.logger.WithFields(logrus.Fields{
						"taskId":       task.TaskUUID,
						"filePath":     outputPath,
						"fileSize":     finalInfo.Size(),
						"expectedSize": total,
					}).Info("真实下载完成并验证")

					return outputPath, nil
				} else {
					vs.logger.WithFields(logrus.Fields{
						"taskId": task.TaskUUID,
						"error":  err,
						"path":   outputPath,
					}).Error("最终文件验证失败")
					return "", fmt.Errorf("下载的文件验证失败")
				}
			}
		}
	}
}

func (vs *VideoService) addWatermark(ctx context.Context, task *ProcessTask, inputPath string) (string, error) {
	vs.updateProgress(task.TaskUUID, 40, "processing", "开始添加水印...")

	// 检查是否启用真实水印处理
	if vs.config.EnableRealWatermark {
		vs.updateProgress(task.TaskUUID, 45, "processing", "正在添加水印...")
		outputPath, err := vs.realService.AddRealWatermark(ctx, task, inputPath)
		if err != nil {
			return "", err
		}
		vs.updateProgress(task.TaskUUID, 60, "processing", "水印添加完成")
		return outputPath, nil
	}

	// 模拟水印处理过程（保留原有逻辑）
	vs.logger.WithField("taskId", task.TaskUUID).Info("开始模拟水印处理")

	// 获取水印配置
	watermarkText := "JAVFLIX.TV"
	if text, ok := task.WatermarkConfig["text"].(string); ok && text != "" {
		watermarkText = text
	}

	position := "bottom-right"
	if pos, ok := task.WatermarkConfig["position"].(string); ok && pos != "" {
		position = pos
	}

	vs.logger.WithFields(logrus.Fields{
		"taskId":        task.TaskUUID,
		"watermarkText": watermarkText,
		"position":      position,
	}).Info("模拟添加水印")

	// 模拟处理时间
	vs.updateProgress(task.TaskUUID, 45, "processing", "正在添加水印...")
	time.Sleep(3 * time.Second)

	vs.updateProgress(task.TaskUUID, 55, "processing", "水印处理中...")
	time.Sleep(2 * time.Second)

	// 生成输出文件路径
	outputPath := vs.generateOutputPath(inputPath, "_watermarked")

	// 复制输入文件到输出文件（模拟水印处理）
	input, err := os.Open(inputPath)
	if err != nil {
		return "", fmt.Errorf("打开输入文件失败: %w", err)
	}
	defer input.Close()

	output, err := os.Create(outputPath)
	if err != nil {
		return "", fmt.Errorf("创建输出文件失败: %w", err)
	}
	defer output.Close()

	// 复制文件内容并添加水印标记
	if _, err := io.Copy(output, input); err != nil {
		return "", fmt.Errorf("复制文件失败: %w", err)
	}

	// 添加水印标记
	watermarkData := fmt.Sprintf("\n[WATERMARK: %s at %s]", watermarkText, position)
	if _, err := output.WriteString(watermarkData); err != nil {
		return "", fmt.Errorf("添加水印标记失败: %w", err)
	}

	vs.updateProgress(task.TaskUUID, 60, "processing", "水印添加完成")
	vs.logger.WithField("taskId", task.TaskUUID).Info("模拟水印处理完成")
	return outputPath, nil
}

func (vs *VideoService) getWatermarkPosition(position string) string {
	switch position {
	case "top-left":
		return "x=10:y=10"
	case "top-right":
		return "x=w-tw-10:y=10"
	case "bottom-left":
		return "x=10:y=h-th-10"
	case "bottom-right":
		return "x=w-tw-10:y=h-th-10"
	case "center":
		return "x=(w-tw)/2:y=(h-th)/2"
	default:
		return "x=w-tw-10:y=h-th-10"
	}
}

func (vs *VideoService) createHLSSegments(ctx context.Context, task *ProcessTask, inputPath string) (string, error) {
	vs.updateProgress(task.TaskUUID, 60, "processing", "开始HLS切片...")

	// 检查是否启用真实HLS处理
	if vs.config.EnableRealHLS {
		outputDir, err := vs.realService.CreateRealHLSSegments(ctx, task, inputPath)
		if err != nil {
			return "", err
		}

		// 生成缩略图
		if generateThumbnails, ok := task.SliceConfig["generateThumbnails"].(bool); ok && generateThumbnails {
			vs.realService.GenerateRealThumbnails(task.TaskUUID, inputPath, outputDir)
		}

		vs.updateProgress(task.TaskUUID, 85, "uploading", "HLS切片完成，开始上传...")
		vs.logger.WithField("taskId", task.TaskUUID).Info("真实HLS切片处理完成")
		return outputDir, nil
	}

	// 模拟HLS切片处理过程
	vs.logger.WithField("taskId", task.TaskUUID).Info("开始模拟HLS切片处理")

	// 创建输出目录
	outputDir := vs.generateOutputDir(task.TaskUUID)
	if err := os.MkdirAll(outputDir, 0755); err != nil {
		return "", fmt.Errorf("创建输出目录失败: %w", err)
	}

	// 获取切片配置
	qualities := []string{"1080p", "720p"}
	if sliceConfig, ok := task.SliceConfig["qualities"].([]interface{}); ok {
		qualities = []string{}
		for _, q := range sliceConfig {
			if qualityMap, ok := q.(map[string]interface{}); ok {
				if resolution, ok := qualityMap["resolution"].(string); ok {
					qualities = append(qualities, resolution)
				}
			}
		}
	}

	segmentDuration := 6 // 改为6秒，符合Apple推荐标准
	if duration, ok := task.SliceConfig["segmentDuration"].(float64); ok {
		segmentDuration = int(duration)
	}

	// 获取视频时长（模拟）
	videoDuration := vs.getVideoDuration(inputPath)
	segmentCount := int(videoDuration/float64(segmentDuration)) + 1
	if videoDuration <= 0 {
		// 如果无法获取时长，使用默认值
		videoDuration = 1800 // 30分钟
		segmentCount = int(videoDuration/float64(segmentDuration)) + 1
	}

	vs.logger.WithFields(logrus.Fields{
		"taskId":          task.TaskUUID,
		"qualities":       len(qualities),
		"segmentDuration": segmentDuration,
		"videoDuration":   videoDuration,
		"segmentCount":    segmentCount,
	}).Info("开始模拟多分辨率切片")

	// 为每个质量创建切片
	for i, quality := range qualities {
		qualityDir := filepath.Join(outputDir, quality)
		if err := os.MkdirAll(qualityDir, 0755); err != nil {
			return "", fmt.Errorf("创建质量目录失败: %w", err)
		}

		vs.updateProgress(task.TaskUUID, 60+(20*(i+1)/len(qualities)), "processing",
			fmt.Sprintf("正在生成%s分辨率切片...", quality))

		// 模拟切片处理时间
		time.Sleep(3 * time.Second)

		// 创建动态的HLS播放列表
		playlistContent := "#EXTM3U\n#EXT-X-VERSION:3\n"
		playlistContent += fmt.Sprintf("#EXT-X-TARGETDURATION:%d\n", segmentDuration)
		playlistContent += "#EXT-X-PLAYLIST-TYPE:VOD\n"

		// 生成所有分段
		for j := 1; j <= segmentCount; j++ {
			// 计算每个分段的实际时长
			remainingDuration := videoDuration - float64((j-1)*segmentDuration)
			actualSegmentDuration := float64(segmentDuration)
			if remainingDuration < float64(segmentDuration) {
				actualSegmentDuration = remainingDuration
			}

			playlistContent += fmt.Sprintf("#EXTINF:%.1f,\n", actualSegmentDuration)
			playlistContent += fmt.Sprintf("segment_%03d.ts\n", j)
		}
		playlistContent += "#EXT-X-ENDLIST\n"

		playlistPath := filepath.Join(qualityDir, "playlist.m3u8")
		if err := os.WriteFile(playlistPath, []byte(playlistContent), 0644); err != nil {
			return "", fmt.Errorf("创建播放列表失败: %w", err)
		}

		// 创建对应数量的TS文件
		for j := 1; j <= segmentCount; j++ {
			segmentPath := filepath.Join(qualityDir, fmt.Sprintf("segment_%03d.ts", j))
			segmentContent := fmt.Sprintf("模拟的%s分辨率视频片段%d - 时长%d秒", quality, j, segmentDuration)
			if err := os.WriteFile(segmentPath, []byte(segmentContent), 0644); err != nil {
				return "", fmt.Errorf("创建视频片段失败: %w", err)
			}
		}

		vs.logger.WithFields(logrus.Fields{
			"taskId":       task.TaskUUID,
			"resolution":   quality,
			"segmentCount": segmentCount,
		}).Info("模拟分辨率切片完成")
	}

	// 生成缩略图
	if generateThumbnails, ok := task.SliceConfig["generateThumbnails"].(bool); ok && generateThumbnails {
		vs.generateMockThumbnails(task.TaskUUID, outputDir)
	}

	vs.updateProgress(task.TaskUUID, 85, "uploading", "HLS切片完成，开始上传...")
	vs.logger.WithField("taskId", task.TaskUUID).Info("模拟HLS切片处理完成")
	return outputDir, nil
}

func (vs *VideoService) uploadToCDN(ctx context.Context, task *ProcessTask, hlsDir string) (map[string]interface{}, error) {
	vs.updateProgress(task.TaskUUID, 85, "uploading", "开始上传到CDN...")

	// 检查是否启用真实CDN上传
	if vs.config.EnableRealCDNUpload {
		vs.logger.WithField("taskId", task.TaskUUID).Info("开始真实CDN上传")

		// 使用真实的上传服务
		cdnUrls, err := vs.uploadService.UploadDirectory(ctx, task.TaskUUID, hlsDir, func(progress int) {
			vs.updateProgress(task.TaskUUID, 85+(10*progress/100), "uploading", fmt.Sprintf("上传进度: %d%%", progress))
		})

		if err != nil {
			return nil, fmt.Errorf("真实CDN上传失败: %w", err)
		}

		vs.updateProgress(task.TaskUUID, 100, "completed", "视频处理完成")
		vs.logger.WithField("taskId", task.TaskUUID).Info("真实CDN上传完成")
		return cdnUrls, nil
	}

	// 模拟CDN上传过程
	vs.logger.WithField("taskId", task.TaskUUID).Info("开始模拟CDN上传")

	// 模拟上传进度
	for progress := 0; progress <= 100; progress += 20 {
		select {
		case <-ctx.Done():
			return nil, ctx.Err()
		default:
			vs.updateProgress(task.TaskUUID, 85+(10*progress/100), "uploading",
				fmt.Sprintf("上传进度: %d%%", progress))
			time.Sleep(1 * time.Second)
		}
	}

	// 生成模拟的CDN URLs
	cdnUrls := map[string]interface{}{
		"baseUrl": "https://cdn.javflix.tv/videos/" + task.TaskUUID,
		"qualities": map[string]interface{}{
			"1080p": map[string]interface{}{
				"playlist": "https://cdn.javflix.tv/videos/" + task.TaskUUID + "/1080p/playlist.m3u8",
				"segments": []string{
					"https://cdn.javflix.tv/videos/" + task.TaskUUID + "/1080p/segment_001.ts",
					"https://cdn.javflix.tv/videos/" + task.TaskUUID + "/1080p/segment_002.ts",
					"https://cdn.javflix.tv/videos/" + task.TaskUUID + "/1080p/segment_003.ts",
				},
			},
			"720p": map[string]interface{}{
				"playlist": "https://cdn.javflix.tv/videos/" + task.TaskUUID + "/720p/playlist.m3u8",
				"segments": []string{
					"https://cdn.javflix.tv/videos/" + task.TaskUUID + "/720p/segment_001.ts",
					"https://cdn.javflix.tv/videos/" + task.TaskUUID + "/720p/segment_002.ts",
					"https://cdn.javflix.tv/videos/" + task.TaskUUID + "/720p/segment_003.ts",
				},
			},
		},
		"thumbnails": []string{
			"https://cdn.javflix.tv/videos/" + task.TaskUUID + "/thumbnails/thumb_001.jpg",
			"https://cdn.javflix.tv/videos/" + task.TaskUUID + "/thumbnails/thumb_002.jpg",
			"https://cdn.javflix.tv/videos/" + task.TaskUUID + "/thumbnails/thumb_003.jpg",
			"https://cdn.javflix.tv/videos/" + task.TaskUUID + "/thumbnails/thumb_004.jpg",
			"https://cdn.javflix.tv/videos/" + task.TaskUUID + "/thumbnails/thumb_005.jpg",
		},
		"uploadedAt": time.Now().Format(time.RFC3339),
		"totalSize":  "1.2GB",
		"duration":   "3600s",
	}

	vs.updateProgress(task.TaskUUID, 100, "completed", "视频处理完成")
	vs.logger.WithField("taskId", task.TaskUUID).Info("模拟CDN上传完成")
	return cdnUrls, nil
}

// 辅助方法
func (vs *VideoService) isVideoFile(filename string) bool {
	ext := strings.ToLower(filepath.Ext(filename))
	videoExts := []string{".mp4", ".avi", ".mkv", ".mov", ".wmv", ".flv", ".webm", ".m4v"}
	for _, videoExt := range videoExts {
		if ext == videoExt {
			return true
		}
	}
	return false
}

func (vs *VideoService) generateOutputPath(inputPath, suffix string) string {
	dir := filepath.Dir(inputPath)
	name := strings.TrimSuffix(filepath.Base(inputPath), filepath.Ext(inputPath))
	ext := filepath.Ext(inputPath)
	return filepath.Join(dir, name+suffix+ext)
}

func (vs *VideoService) generateOutputDir(taskUUID string) string {
	return filepath.Join(vs.config.TempDir, "output", taskUUID)
}

func (vs *VideoService) parseQualities(qualitiesInterface []interface{}) []config.VideoQuality {
	var qualities []config.VideoQuality
	for _, q := range qualitiesInterface {
		if qualityMap, ok := q.(map[string]interface{}); ok {
			quality := config.VideoQuality{}
			if resolution, ok := qualityMap["resolution"].(string); ok {
				quality.Resolution = resolution
			}
			if bitrate, ok := qualityMap["bitrate"].(string); ok {
				quality.Bitrate = bitrate
			}
			if scale, ok := qualityMap["scale"].(string); ok {
				quality.Scale = scale
			}
			qualities = append(qualities, quality)
		}
	}
	if len(qualities) == 0 {
		return vs.config.DefaultVideoQualities
	}
	return qualities
}

func (vs *VideoService) generateThumbnails(taskUUID, inputPath, outputDir string) {
	thumbnailDir := filepath.Join(outputDir, "thumbnails")
	if err := os.MkdirAll(thumbnailDir, 0755); err != nil {
		vs.logger.WithError(err).Error("创建缩略图目录失败")
		return
	}

	// 生成5个缩略图
	for i := 1; i <= 5; i++ {
		thumbnailPath := filepath.Join(thumbnailDir, fmt.Sprintf("thumb_%03d.jpg", i))
		seekTime := fmt.Sprintf("%d", i*60) // 每60秒一个缩略图

		err := ffmpeg.Input(inputPath).
			Filter("select", ffmpeg.Args{fmt.Sprintf("gte(n,%d)", i*30)}).
			Output(thumbnailPath).
			GlobalArgs("-vframes", "1").
			GlobalArgs("-q:v", "2").
			GlobalArgs("-ss", seekTime).
			OverWriteOutput().
			Run()

		if err != nil {
			vs.logger.WithError(err).Warnf("生成缩略图%d失败", i)
		}
	}
}

func (vs *VideoService) generateMockThumbnails(taskUUID, outputDir string) {
	thumbnailDir := filepath.Join(outputDir, "thumbnails")
	if err := os.MkdirAll(thumbnailDir, 0755); err != nil {
		vs.logger.WithError(err).Error("创建缩略图目录失败")
		return
	}

	vs.logger.WithField("taskId", taskUUID).Info("开始生成模拟缩略图")

	// 生成5个模拟缩略图
	for i := 1; i <= 5; i++ {
		thumbnailPath := filepath.Join(thumbnailDir, fmt.Sprintf("thumb_%03d.jpg", i))
		thumbnailContent := fmt.Sprintf("模拟缩略图%d - 时间点: %d秒", i, i*60)

		if err := os.WriteFile(thumbnailPath, []byte(thumbnailContent), 0644); err != nil {
			vs.logger.WithError(err).Warnf("生成模拟缩略图%d失败", i)
		}
	}

	vs.logger.WithField("taskId", taskUUID).Info("模拟缩略图生成完成")
}

// 注意：monitorDownloadProgress函数已被移除，进度监控现在统一在downloadVideo的主循环中处理

func (vs *VideoService) updateProgress(taskUUID string, progress int, status, message string) {
	// 更新数据库
	if vs.db != nil {
		if err := vs.db.UpdateTaskProgress(taskUUID, progress, status, message); err != nil {
			vs.logger.WithError(err).Error("更新数据库进度失败")
		}
	}

	// 通过WebSocket广播进度
	if vs.wsHub != nil {
		wsProgress := websocket.ProgressMessage{
			TaskUUID:  taskUUID,
			Progress:  progress,
			Status:    status,
			Message:   message,
			Timestamp: time.Now().Format(time.RFC3339),
		}
		vs.wsHub.BroadcastProgress(wsProgress)
	}

	// 创建进度更新对象用于发送到Node.js服务器
	update := ProgressUpdate{
		TaskUUID:  taskUUID,
		Progress:  progress,
		Status:    status,
		Message:   message,
		Timestamp: time.Now().Format(time.RFC3339),
	}

	// 发送到Node.js服务器
	go vs.sendProgressUpdate(update)
}

// updateProgressWithBytes 更新任务进度（包含字节信息和下载速度）
func (vs *VideoService) updateProgressWithBytes(taskUUID string, progress int, status, message string, completedBytes, totalBytes int64, downloadSpeed float64) {
	// 更新数据库（包含字节信息）
	if vs.db != nil {
		if err := vs.db.UpdateTaskProgressWithBytes(taskUUID, progress, status, message, completedBytes, totalBytes, downloadSpeed); err != nil {
			vs.logger.WithError(err).Error("更新数据库进度失败")
		}
	}

	// 通过WebSocket广播详细进度信息
	if vs.wsHub != nil {
		wsProgress := websocket.ProgressMessage{
			TaskUUID:  taskUUID,
			Progress:  progress,
			Status:    status,
			Message:   message,
			Completed: completedBytes,
			Total:     totalBytes,
			Timestamp: time.Now().Format(time.RFC3339),
		}
		vs.wsHub.BroadcastProgress(wsProgress)
	}

	// 创建进度更新对象用于发送到Node.js服务器
	update := ProgressUpdate{
		TaskUUID:  taskUUID,
		Progress:  progress,
		Status:    status,
		Message:   message,
		Timestamp: time.Now().Format(time.RFC3339),
	}

	// 发送到Node.js服务器
	go vs.sendProgressUpdate(update)

	// 记录日志
	vs.logger.WithFields(logrus.Fields{
		"taskId":   taskUUID,
		"progress": progress,
		"status":   status,
	}).Info(message)
}

func (vs *VideoService) sendProgressUpdate(update ProgressUpdate) {
	jsonData, err := json.Marshal(update)
	if err != nil {
		vs.logger.WithError(err).Error("序列化进度更新失败")
		return
	}

	req, err := http.NewRequest("POST",
		vs.config.APIServerURL+"/api/movie-processing/progress",
		bytes.NewBuffer(jsonData))
	if err != nil {
		vs.logger.WithError(err).Error("创建进度更新请求失败")
		return
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+vs.config.APIServerToken)

	client := &http.Client{Timeout: 10 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		vs.logger.WithError(err).Error("发送进度更新失败")
		return
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		vs.logger.Errorf("进度更新响应错误: %d - %s", resp.StatusCode, string(body))
	}
}

func (vs *VideoService) notifyCompletion(task *ProcessTask, cdnUrls map[string]interface{}) error {
	callback := CompletionCallback{
		TaskUUID:  task.TaskUUID,
		Success:   true,
		VideoUrls: cdnUrls,
	}

	jsonData, err := json.Marshal(callback)
	if err != nil {
		return fmt.Errorf("序列化完成回调失败: %w", err)
	}

	req, err := http.NewRequest("POST",
		vs.config.APIServerURL+"/api/movie-processing/callback",
		bytes.NewBuffer(jsonData))
	if err != nil {
		return fmt.Errorf("创建完成回调请求失败: %w", err)
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+vs.config.APIServerToken)

	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("发送完成回调失败: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("完成回调响应错误: %d - %s", resp.StatusCode, string(body))
	}

	vs.logger.WithField("taskId", task.TaskUUID).Info("任务完成通知发送成功")
	return nil
}

func (vs *VideoService) notifyFailure(task *ProcessTask, errorMsg string) {
	callback := CompletionCallback{
		TaskUUID: task.TaskUUID,
		Success:  false,
		Error:    errorMsg,
	}

	jsonData, _ := json.Marshal(callback)
	req, _ := http.NewRequest("POST",
		vs.config.APIServerURL+"/api/movie-processing/callback",
		bytes.NewBuffer(jsonData))

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+vs.config.APIServerToken)

	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		vs.logger.WithError(err).Error("发送失败回调失败")
		return
	}
	defer resp.Body.Close()

	vs.logger.WithField("taskId", task.TaskUUID).Info("任务失败通知发送成功")
}

func (vs *VideoService) cleanupFile(filePath string) {
	if vs.config.CleanupEnabled {
		if err := os.Remove(filePath); err != nil {
			vs.logger.WithError(err).Warnf("清理文件失败: %s", filePath)
		}
	}
}

func (vs *VideoService) cleanupDir(dirPath string) {
	if vs.config.CleanupEnabled {
		if err := os.RemoveAll(dirPath); err != nil {
			vs.logger.WithError(err).Warnf("清理目录失败: %s", dirPath)
		}
	}
}

func (vs *VideoService) getVideoDuration(inputPath string) float64 {
	// 模拟获取视频时长
	// 在真实环境中，这里会使用ffprobe或类似工具来获取实际视频时长

	// 检查文件是否存在
	if _, err := os.Stat(inputPath); os.IsNotExist(err) {
		vs.logger.WithField("path", inputPath).Warn("视频文件不存在，使用默认时长")
		return 1800 // 30分钟默认值
	}

	// 获取文件信息
	fileInfo, err := os.Stat(inputPath)
	if err != nil {
		vs.logger.WithError(err).Warn("无法获取文件信息，使用默认时长")
		return 1800
	}

	// 根据文件大小估算时长（这是一个粗略的估算）
	// 假设平均码率为 1Mbps，则 1MB ≈ 8秒
	fileSizeMB := float64(fileInfo.Size()) / (1024 * 1024)
	estimatedDuration := fileSizeMB * 8 // 秒

	// 设置合理的范围
	if estimatedDuration < 60 {
		estimatedDuration = 300 // 最少5分钟
	} else if estimatedDuration > 7200 {
		estimatedDuration = 7200 // 最多2小时
	}

	vs.logger.WithFields(logrus.Fields{
		"inputPath":         inputPath,
		"fileSizeMB":        fileSizeMB,
		"estimatedDuration": estimatedDuration,
	}).Info("估算视频时长")

	return estimatedDuration
}

func (vs *VideoService) Close() error {
	if vs.torrentClient != nil {
		vs.torrentClient.Close()
	}
	return nil
}
