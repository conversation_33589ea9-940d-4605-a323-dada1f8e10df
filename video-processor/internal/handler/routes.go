package handler

import (
	"fmt"
	"net/http"
	"runtime"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"

	"javflix-video-processor/internal/config"
	"javflix-video-processor/internal/middleware"
	"javflix-video-processor/internal/service"
	"javflix-video-processor/internal/worker"
)

type Handler struct {
	config       *config.Config
	logger       *logrus.Logger
	videoService *service.VideoService
	taskWorker   *worker.TaskWorker
	startTime    time.Time
}

func NewHandler(cfg *config.Config, videoService *service.VideoService, taskWorker *worker.TaskWorker) *Handler {
	logger := logrus.New()
	level, err := logrus.ParseLevel(cfg.LogLevel)
	if err != nil {
		level = logrus.InfoLevel
	}
	logger.SetLevel(level)

	return &Handler{
		config:       cfg,
		logger:       logger,
		videoService: videoService,
		taskWorker:   taskWorker,
		startTime:    time.Now(),
	}
}

func RegisterRoutes(r *gin.Engine, cfg *config.Config, videoService *service.VideoService, taskWorker *worker.TaskWorker) {
	handler := NewHandler(cfg, videoService, taskWorker)

	// 中间件
	r.Use(middleware.LoggingMiddleware())
	r.Use(middleware.CORSMiddleware())

	// 健康检查 (无需认证)
	r.GET("/health", handler.healthCheck)
	r.GET("/metrics", handler.metrics)

	// API路由 (需要认证)
	api := r.Group("/api/v1")
	api.Use(middleware.JWTAuthMiddleware(cfg.JWTSecret))
	api.Use(middleware.IPWhitelistMiddleware(cfg.AllowedIPs))
	{
		api.POST("/process-video", handler.processVideo)
		api.GET("/tasks/:taskUuid/status", handler.getTaskStatus)
		api.GET("/queue/info", handler.getQueueInfo)
		api.PUT("/tasks/:taskUuid/pause", handler.pauseTask)
		api.PUT("/tasks/:taskUuid/resume", handler.resumeTask)
		api.DELETE("/tasks/:taskUuid", handler.cancelTask)
	}
}

func (h *Handler) healthCheck(c *gin.Context) {
	var m runtime.MemStats
	runtime.ReadMemStats(&m)

	uptime := time.Since(h.startTime)

	// 获取队列信息
	queueInfo, err := h.taskWorker.GetQueueInfo()
	if err != nil {
		h.logger.WithError(err).Error("获取队列信息失败")
	}

	var activeJobs, queueLength int
	if queueInfo != nil {
		if total, ok := queueInfo["total"].(map[string]interface{}); ok {
			if active, ok := total["active"].(int); ok {
				activeJobs = active
			}
			if pending, ok := total["pending"].(int); ok {
				queueLength = pending
			}
		}
	}

	response := gin.H{
		"status":      "healthy",
		"server":      "go-video-processor-01",
		"version":     "1.0.0",
		"uptime":      uptime.String(),
		"activeJobs":  activeJobs,
		"queueLength": queueLength,
		"systemInfo": gin.H{
			"cpuCores":    runtime.NumCPU(),
			"goroutines":  runtime.NumGoroutine(),
			"memoryUsage": formatBytes(m.Alloc),
			"totalAlloc":  formatBytes(m.TotalAlloc),
			"sys":         formatBytes(m.Sys),
			"gcRuns":      m.NumGC,
		},
	}

	c.JSON(http.StatusOK, response)
}

func (h *Handler) metrics(c *gin.Context) {
	var m runtime.MemStats
	runtime.ReadMemStats(&m)

	queueInfo, _ := h.taskWorker.GetQueueInfo()

	metrics := gin.H{
		"timestamp": time.Now().Unix(),
		"uptime":    time.Since(h.startTime).Seconds(),
		"memory": gin.H{
			"alloc":      m.Alloc,
			"totalAlloc": m.TotalAlloc,
			"sys":        m.Sys,
			"numGC":      m.NumGC,
		},
		"runtime": gin.H{
			"goroutines": runtime.NumGoroutine(),
			"cpuCores":   runtime.NumCPU(),
		},
		"queues": queueInfo,
	}

	c.JSON(http.StatusOK, metrics)
}

func (h *Handler) processVideo(c *gin.Context) {
	var task service.ProcessTask
	if err := c.ShouldBindJSON(&task); err != nil {
		h.logger.WithError(err).Error("解析请求失败")
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "请求格式错误: " + err.Error(),
		})
		return
	}

	h.logger.WithFields(logrus.Fields{
		"taskId":  task.TaskUUID,
		"movieId": task.MovieID,
	}).Info("接收到视频处理任务")

	// 获取优先级
	priority := 5 // 默认优先级
	if p, exists := c.GetQuery("priority"); exists {
		if pInt := parseInt(p); pInt > 0 {
			priority = pInt
		}
	}

	// 将任务加入队列
	if err := h.taskWorker.EnqueueVideoProcessTask(&task, priority); err != nil {
		h.logger.WithError(err).Error("任务入队失败")
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "任务入队失败: " + err.Error(),
		})
		return
	}

	// 获取队列位置
	queueInfo, _ := h.taskWorker.GetQueueInfo()
	queuePosition := 1
	if queueInfo != nil {
		if total, ok := queueInfo["total"].(map[string]interface{}); ok {
			if pending, ok := total["pending"].(int); ok {
				queuePosition = pending
			}
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "任务已接收并加入队列",
		"data": gin.H{
			"taskUuid":           task.TaskUUID,
			"queuePosition":      queuePosition,
			"estimatedStartTime": time.Now().Add(time.Duration(queuePosition*10) * time.Minute).Format(time.RFC3339),
		},
	})
}

func (h *Handler) getTaskStatus(c *gin.Context) {
	taskUuid := c.Param("taskUuid")

	// 这里应该从数据库或Redis中获取任务状态
	// 暂时返回模拟数据
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"taskUuid": taskUuid,
			"status":   "processing",
			"progress": 45,
			"message":  "正在处理视频...",
		},
	})
}

func (h *Handler) getQueueInfo(c *gin.Context) {
	queueInfo, err := h.taskWorker.GetQueueInfo()
	if err != nil {
		h.logger.WithError(err).Error("获取队列信息失败")
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "获取队列信息失败",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    queueInfo,
	})
}

func (h *Handler) pauseTask(c *gin.Context) {
	taskUuid := c.Param("taskUuid")

	// TODO: 实现任务暂停逻辑
	h.logger.WithField("taskId", taskUuid).Info("暂停任务")

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "任务已暂停",
	})
}

func (h *Handler) resumeTask(c *gin.Context) {
	taskUuid := c.Param("taskUuid")

	// TODO: 实现任务恢复逻辑
	h.logger.WithField("taskId", taskUuid).Info("恢复任务")

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "任务已恢复",
	})
}

func (h *Handler) cancelTask(c *gin.Context) {
	taskUuid := c.Param("taskUuid")

	// TODO: 实现任务取消逻辑
	h.logger.WithField("taskId", taskUuid).Info("取消任务")

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "任务已取消",
	})
}

// 辅助函数
func formatBytes(b uint64) string {
	const unit = 1024
	if b < unit {
		return fmt.Sprintf("%d B", b)
	}
	div, exp := int64(unit), 0
	for n := b / unit; n >= unit; n /= unit {
		div *= unit
		exp++
	}
	units := []string{"KB", "MB", "GB", "TB"}
	return fmt.Sprintf("%.1f %s", float64(b)/float64(div), units[exp])
}

func parseInt(s string) int {
	// 简单的字符串转整数
	result := 0
	for _, r := range s {
		if r >= '0' && r <= '9' {
			result = result*10 + int(r-'0')
		} else {
			return 0
		}
	}
	return result
}
