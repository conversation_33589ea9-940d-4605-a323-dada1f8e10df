package database

import (
	"database/sql"
	"encoding/json"
	"fmt"
	"time"

	_ "github.com/lib/pq"
	"github.com/sirupsen/logrus"
)

// DB 数据库连接包装器
type DB struct {
	conn   *sql.DB
	logger *logrus.Logger
}

// VideoProcessingTask 视频处理任务结构
type VideoProcessingTask struct {
	ID            int                    `json:"id"`
	TaskUUID      string                 `json:"taskUuid"`
	MovieID       int                    `json:"movieId"`
	MagnetLink    string                 `json:"magnetLink"`
	Status        string                 `json:"status"`
	Progress      int                    `json:"progress"`
	CurrentStep   string                 `json:"currentStep"`
	ErrorMessage  string                 `json:"errorMessage"`
	CreatedAt     time.Time              `json:"createdAt"`
	StartedAt     *time.Time             `json:"startedAt"`
	CompletedAt   *time.Time             `json:"completedAt"`
	UpdatedAt     time.Time              `json:"updatedAt"`
	DownloadPath  string                 `json:"downloadPath"`
	OutputPath    string                 `json:"outputPath"`
	VideoURL      string                 `json:"videoUrl"`
	ThumbnailURL  string                 `json:"thumbnailUrl"`
	Duration      int                    `json:"duration"`
	FileSize      int64                  `json:"fileSize"`
	WatermarkText string                 `json:"watermarkText"`
	WatermarkPos  string                 `json:"watermarkPosition"`
	Quality       string                 `json:"quality"`
	Metadata      map[string]interface{} `json:"metadata"`
}

// NewDB 创建新的数据库连接
func NewDB(host, port, database, user, password string, logger *logrus.Logger) (*DB, error) {
	// 如果没有密码，不包含password参数
	var dsn string
	if password == "" {
		dsn = fmt.Sprintf("host=%s port=%s dbname=%s user=%s sslmode=disable",
			host, port, database, user)
	} else {
		dsn = fmt.Sprintf("host=%s port=%s dbname=%s user=%s password=%s sslmode=disable",
			host, port, database, user, password)
	}

	conn, err := sql.Open("postgres", dsn)
	if err != nil {
		return nil, fmt.Errorf("连接数据库失败: %w", err)
	}

	// 测试连接
	if err := conn.Ping(); err != nil {
		return nil, fmt.Errorf("数据库ping失败: %w", err)
	}

	// 设置连接池参数
	conn.SetMaxOpenConns(25)
	conn.SetMaxIdleConns(5)
	conn.SetConnMaxLifetime(5 * time.Minute)

	logger.Info("数据库连接成功")

	return &DB{
		conn:   conn,
		logger: logger,
	}, nil
}

// Close 关闭数据库连接
func (db *DB) Close() error {
	return db.conn.Close()
}

// CreateTask 创建新的视频处理任务
func (db *DB) CreateTask(task *VideoProcessingTask) error {
	metadataJSON, err := json.Marshal(task.Metadata)
	if err != nil {
		return fmt.Errorf("序列化metadata失败: %w", err)
	}

	query := `
		INSERT INTO video_processing_tasks (
			task_uuid, movie_id, magnet_link, status, progress, current_step,
			watermark_text, watermark_position, quality, metadata
		) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
		RETURNING id, created_at, updated_at
	`

	err = db.conn.QueryRow(
		query,
		task.TaskUUID, task.MovieID, task.MagnetLink, task.Status, task.Progress,
		task.CurrentStep, task.WatermarkText, task.WatermarkPos, task.Quality,
		metadataJSON,
	).Scan(&task.ID, &task.CreatedAt, &task.UpdatedAt)

	if err != nil {
		return fmt.Errorf("创建任务失败: %w", err)
	}

	db.logger.WithFields(logrus.Fields{
		"taskId":  task.TaskUUID,
		"movieId": task.MovieID,
	}).Info("任务创建成功")

	return nil
}

// UpdateTaskProgress 更新任务进度
func (db *DB) UpdateTaskProgress(taskUUID string, progress int, status, currentStep string) error {
	query := `
		UPDATE video_processing_tasks 
		SET progress = $2, status = $3, current_step = $4, updated_at = CURRENT_TIMESTAMP
		WHERE task_uuid = $1
	`

	result, err := db.conn.Exec(query, taskUUID, progress, status, currentStep)
	if err != nil {
		return fmt.Errorf("更新任务进度失败: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("获取影响行数失败: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("任务不存在: %s", taskUUID)
	}

	return nil
}

// UpdateTaskStatus 更新任务状态
func (db *DB) UpdateTaskStatus(taskUUID, status string) error {
	var query string
	var args []interface{}

	if status == "processing" {
		query = `
			UPDATE video_processing_tasks 
			SET status = $2, started_at = CURRENT_TIMESTAMP, updated_at = CURRENT_TIMESTAMP
			WHERE task_uuid = $1
		`
		args = []interface{}{taskUUID, status}
	} else if status == "completed" || status == "failed" {
		query = `
			UPDATE video_processing_tasks 
			SET status = $2, completed_at = CURRENT_TIMESTAMP, updated_at = CURRENT_TIMESTAMP
			WHERE task_uuid = $1
		`
		args = []interface{}{taskUUID, status}
	} else {
		query = `
			UPDATE video_processing_tasks 
			SET status = $2, updated_at = CURRENT_TIMESTAMP
			WHERE task_uuid = $1
		`
		args = []interface{}{taskUUID, status}
	}

	result, err := db.conn.Exec(query, args...)
	if err != nil {
		return fmt.Errorf("更新任务状态失败: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("获取影响行数失败: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("任务不存在: %s", taskUUID)
	}

	return nil
}

// UpdateTaskError 更新任务错误信息
func (db *DB) UpdateTaskError(taskUUID, errorMessage string) error {
	query := `
		UPDATE video_processing_tasks 
		SET status = 'failed', error_message = $2, completed_at = CURRENT_TIMESTAMP, updated_at = CURRENT_TIMESTAMP
		WHERE task_uuid = $1
	`

	result, err := db.conn.Exec(query, taskUUID, errorMessage)
	if err != nil {
		return fmt.Errorf("更新任务错误失败: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("获取影响行数失败: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("任务不存在: %s", taskUUID)
	}

	return nil
}

// UpdateTaskResult 更新任务结果
func (db *DB) UpdateTaskResult(taskUUID, videoURL, thumbnailURL string, duration int, fileSize int64) error {
	query := `
		UPDATE video_processing_tasks 
		SET video_url = $2, thumbnail_url = $3, duration = $4, file_size = $5, 
		    status = 'completed', completed_at = CURRENT_TIMESTAMP, updated_at = CURRENT_TIMESTAMP
		WHERE task_uuid = $1
	`

	result, err := db.conn.Exec(query, taskUUID, videoURL, thumbnailURL, duration, fileSize)
	if err != nil {
		return fmt.Errorf("更新任务结果失败: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("获取影响行数失败: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("任务不存在: %s", taskUUID)
	}

	return nil
}

// GetTask 获取任务详情
func (db *DB) GetTask(taskUUID string) (*VideoProcessingTask, error) {
	query := `
		SELECT id, task_uuid, movie_id, magnet_link, status, progress, current_step,
		       error_message, created_at, started_at, completed_at, updated_at,
		       download_path, output_path, video_url, thumbnail_url, duration, file_size,
		       watermark_text, watermark_position, quality, metadata
		FROM video_processing_tasks 
		WHERE task_uuid = $1
	`

	task := &VideoProcessingTask{}
	var metadataJSON []byte

	err := db.conn.QueryRow(query, taskUUID).Scan(
		&task.ID, &task.TaskUUID, &task.MovieID, &task.MagnetLink, &task.Status,
		&task.Progress, &task.CurrentStep, &task.ErrorMessage, &task.CreatedAt,
		&task.StartedAt, &task.CompletedAt, &task.UpdatedAt, &task.DownloadPath,
		&task.OutputPath, &task.VideoURL, &task.ThumbnailURL, &task.Duration,
		&task.FileSize, &task.WatermarkText, &task.WatermarkPos, &task.Quality,
		&metadataJSON,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("任务不存在: %s", taskUUID)
		}
		return nil, fmt.Errorf("获取任务失败: %w", err)
	}

	// 解析metadata
	if len(metadataJSON) > 0 {
		if err := json.Unmarshal(metadataJSON, &task.Metadata); err != nil {
			db.logger.WithError(err).Warn("解析任务metadata失败")
			task.Metadata = make(map[string]interface{})
		}
	} else {
		task.Metadata = make(map[string]interface{})
	}

	return task, nil
}

// GetTasksByStatus 根据状态获取任务列表
func (db *DB) GetTasksByStatus(status string, limit, offset int) ([]*VideoProcessingTask, error) {
	query := `
		SELECT id, task_uuid, movie_id, magnet_link, status, progress, current_step,
		       error_message, created_at, started_at, completed_at, updated_at,
		       download_path, output_path, video_url, thumbnail_url, duration, file_size,
		       watermark_text, watermark_position, quality, metadata
		FROM video_processing_tasks 
		WHERE status = $1
		ORDER BY created_at DESC
		LIMIT $2 OFFSET $3
	`

	rows, err := db.conn.Query(query, status, limit, offset)
	if err != nil {
		return nil, fmt.Errorf("查询任务失败: %w", err)
	}
	defer rows.Close()

	var tasks []*VideoProcessingTask

	for rows.Next() {
		task := &VideoProcessingTask{}
		var metadataJSON []byte

		err := rows.Scan(
			&task.ID, &task.TaskUUID, &task.MovieID, &task.MagnetLink, &task.Status,
			&task.Progress, &task.CurrentStep, &task.ErrorMessage, &task.CreatedAt,
			&task.StartedAt, &task.CompletedAt, &task.UpdatedAt, &task.DownloadPath,
			&task.OutputPath, &task.VideoURL, &task.ThumbnailURL, &task.Duration,
			&task.FileSize, &task.WatermarkText, &task.WatermarkPos, &task.Quality,
			&metadataJSON,
		)

		if err != nil {
			return nil, fmt.Errorf("扫描任务行失败: %w", err)
		}

		// 解析metadata
		if len(metadataJSON) > 0 {
			if err := json.Unmarshal(metadataJSON, &task.Metadata); err != nil {
				db.logger.WithError(err).Warn("解析任务metadata失败")
				task.Metadata = make(map[string]interface{})
			}
		} else {
			task.Metadata = make(map[string]interface{})
		}

		tasks = append(tasks, task)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("遍历任务行失败: %w", err)
	}

	return tasks, nil
}

// GetAllTasks 获取所有任务
func (db *DB) GetAllTasks(limit, offset int) ([]*VideoProcessingTask, error) {
	query := `
		SELECT id, task_uuid, movie_id, magnet_link, status, progress, current_step,
		       error_message, created_at, started_at, completed_at, updated_at,
		       download_path, output_path, video_url, thumbnail_url, duration, file_size,
		       watermark_text, watermark_position, quality, metadata
		FROM video_processing_tasks 
		ORDER BY created_at DESC
		LIMIT $1 OFFSET $2
	`

	rows, err := db.conn.Query(query, limit, offset)
	if err != nil {
		return nil, fmt.Errorf("查询所有任务失败: %w", err)
	}
	defer rows.Close()

	var tasks []*VideoProcessingTask

	for rows.Next() {
		task := &VideoProcessingTask{}
		var metadataJSON []byte

		err := rows.Scan(
			&task.ID, &task.TaskUUID, &task.MovieID, &task.MagnetLink, &task.Status,
			&task.Progress, &task.CurrentStep, &task.ErrorMessage, &task.CreatedAt,
			&task.StartedAt, &task.CompletedAt, &task.UpdatedAt, &task.DownloadPath,
			&task.OutputPath, &task.VideoURL, &task.ThumbnailURL, &task.Duration,
			&task.FileSize, &task.WatermarkText, &task.WatermarkPos, &task.Quality,
			&metadataJSON,
		)

		if err != nil {
			return nil, fmt.Errorf("扫描任务行失败: %w", err)
		}

		// 解析metadata
		if len(metadataJSON) > 0 {
			if err := json.Unmarshal(metadataJSON, &task.Metadata); err != nil {
				db.logger.WithError(err).Warn("解析任务metadata失败")
				task.Metadata = make(map[string]interface{})
			}
		} else {
			task.Metadata = make(map[string]interface{})
		}

		tasks = append(tasks, task)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("遍历任务行失败: %w", err)
	}

	return tasks, nil
}
