package websocket

import (
	"encoding/json"
	"log"
	"net/http"
	"sync"

	"github.com/gorilla/websocket"
)

var upgrader = websocket.Upgrader{
	CheckOrigin: func(r *http.Request) bool {
		// 允许所有来源，生产环境中应该更严格
		return true
	},
}

// Hub 管理WebSocket连接
type Hub struct {
	// 注册的客户端连接
	clients map[*Client]bool

	// 从客户端接收的消息
	broadcast chan []byte

	// 注册客户端连接
	register chan *Client

	// 注销客户端连接
	unregister chan *Client

	// 互斥锁保护clients map
	mutex sync.RWMutex
}

// Client 表示一个WebSocket客户端
type Client struct {
	hub  *Hub
	conn *websocket.Conn
	send chan []byte
}

// Message 表示WebSocket消息
type Message struct {
	Type string      `json:"type"`
	Data interface{} `json:"data"`
}

// ProgressMessage 进度更新消息
type ProgressMessage struct {
	TaskUUID  string `json:"taskUuid"`
	Progress  int    `json:"progress"`
	Status    string `json:"status"`
	Message   string `json:"message"`
	Completed int64  `json:"completed,omitempty"`
	Total     int64  `json:"total,omitempty"`
	Timestamp string `json:"timestamp"`
}

// TaskCompletedMessage 任务完成消息
type TaskCompletedMessage struct {
	TaskUUID  string                 `json:"taskUuid"`
	Success   bool                   `json:"success"`
	VideoUrls map[string]interface{} `json:"videoUrls,omitempty"`
	Error     string                 `json:"error,omitempty"`
}

// TaskFailedMessage 任务失败消息
type TaskFailedMessage struct {
	TaskUUID     string `json:"taskUuid"`
	Error        string `json:"error"`
	ErrorMessage string `json:"errorMessage"`
}

// TaskStartedMessage 任务开始消息
type TaskStartedMessage struct {
	TaskUUID   string `json:"taskUuid"`
	MovieID    int    `json:"movieId"`
	MagnetLink string `json:"magnetLink"`
	Status     string `json:"status"`
}

// NewHub 创建新的Hub
func NewHub() *Hub {
	return &Hub{
		clients:    make(map[*Client]bool),
		broadcast:  make(chan []byte),
		register:   make(chan *Client),
		unregister: make(chan *Client),
	}
}

// Run 启动Hub
func (h *Hub) Run() {
	for {
		select {
		case client := <-h.register:
			h.mutex.Lock()
			h.clients[client] = true
			h.mutex.Unlock()
			log.Printf("WebSocket客户端已连接，当前连接数: %d", len(h.clients))

		case client := <-h.unregister:
			h.mutex.Lock()
			if _, ok := h.clients[client]; ok {
				delete(h.clients, client)
				close(client.send)
			}
			h.mutex.Unlock()
			log.Printf("WebSocket客户端已断开，当前连接数: %d", len(h.clients))

		case message := <-h.broadcast:
			h.mutex.RLock()
			for client := range h.clients {
				select {
				case client.send <- message:
				default:
					delete(h.clients, client)
					close(client.send)
				}
			}
			h.mutex.RUnlock()
		}
	}
}

// BroadcastProgress 广播进度更新
func (h *Hub) BroadcastProgress(progress ProgressMessage) {
	message := Message{
		Type: "video-task-progress",
		Data: progress,
	}

	data, err := json.Marshal(message)
	if err != nil {
		log.Printf("序列化进度消息失败: %v", err)
		return
	}

	h.broadcast <- data
}

// BroadcastTaskCompleted 广播任务完成
func (h *Hub) BroadcastTaskCompleted(completed TaskCompletedMessage) {
	message := Message{
		Type: "video-task-completed",
		Data: completed,
	}

	data, err := json.Marshal(message)
	if err != nil {
		log.Printf("序列化完成消息失败: %v", err)
		return
	}

	h.broadcast <- data
}

// BroadcastTaskFailed 广播任务失败
func (h *Hub) BroadcastTaskFailed(failed TaskFailedMessage) {
	message := Message{
		Type: "video-task-failed",
		Data: failed,
	}

	data, err := json.Marshal(message)
	if err != nil {
		log.Printf("序列化失败消息失败: %v", err)
		return
	}

	h.broadcast <- data
}

// BroadcastTaskStarted 广播任务开始
func (h *Hub) BroadcastTaskStarted(started TaskStartedMessage) {
	message := Message{
		Type: "video-task-started",
		Data: started,
	}

	data, err := json.Marshal(message)
	if err != nil {
		log.Printf("序列化开始消息失败: %v", err)
		return
	}

	h.broadcast <- data
}

// ServeWS 处理WebSocket连接
func (h *Hub) ServeWS(w http.ResponseWriter, r *http.Request) {
	conn, err := upgrader.Upgrade(w, r, nil)
	if err != nil {
		log.Printf("WebSocket升级失败: %v", err)
		return
	}

	client := &Client{
		hub:  h,
		conn: conn,
		send: make(chan []byte, 256),
	}

	client.hub.register <- client

	// 启动goroutines处理读写
	go client.writePump()
	go client.readPump()
}

// readPump 处理从WebSocket连接读取消息
func (c *Client) readPump() {
	defer func() {
		c.hub.unregister <- c
		c.conn.Close()
	}()

	for {
		_, _, err := c.conn.ReadMessage()
		if err != nil {
			if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
				log.Printf("WebSocket读取错误: %v", err)
			}
			break
		}
	}
}

// writePump 处理向WebSocket连接写入消息
func (c *Client) writePump() {
	defer c.conn.Close()

	for {
		select {
		case message, ok := <-c.send:
			if !ok {
				c.conn.WriteMessage(websocket.CloseMessage, []byte{})
				return
			}

			if err := c.conn.WriteMessage(websocket.TextMessage, message); err != nil {
				log.Printf("WebSocket写入错误: %v", err)
				return
			}
		}
	}
}

// GetConnectedClients 获取连接的客户端数量
func (h *Hub) GetConnectedClients() int {
	h.mutex.RLock()
	defer h.mutex.RUnlock()
	return len(h.clients)
}
