package middleware

import (
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/golang-jwt/jwt/v5"
)

func JWTAuthMiddleware(jwtSecret string) gin.HandlerFunc {
	return gin.HandlerFunc(func(c *gin.Context) {
		authHeader := c.<PERSON>("Authorization")
		if authHeader == "" {
			c.J<PERSON>(http.StatusUnauthorized, gin.H{
				"success": false,
				"error":   "Missing authorization header",
			})
			c.Abort()
			return
		}

		// 检查Bearer前缀
		tokenString := strings.TrimPrefix(authHeader, "Bearer ")
		if tokenString == authHeader {
			c.JSON(http.StatusUnauthorized, gin.H{
				"success": false,
				"error":   "Invalid authorization format",
			})
			c.Abort()
			return
		}

		// 简单的token验证 - 检查是否匹配预设的token
		expectedToken := "go-video-processor-token-123"
		if tokenString == expectedToken {
			c.Set("authenticated", true)
			c.Next()
			return
		}

		// 如果不是预设token，尝试JWT验证
		token, err := jwt.Parse(tokenString, func(token *jwt.Token) (interface{}, error) {
			// 验证签名方法
			if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
				return nil, jwt.ErrSignatureInvalid
			}
			return []byte(jwtSecret), nil
		})

		if err != nil || !token.Valid {
			c.JSON(http.StatusUnauthorized, gin.H{
				"success": false,
				"error":   "Invalid token",
			})
			c.Abort()
			return
		}

		// 提取claims
		if claims, ok := token.Claims.(jwt.MapClaims); ok {
			c.Set("user_claims", claims)
		}

		c.Next()
	})
}

func IPWhitelistMiddleware(allowedIPs []string) gin.HandlerFunc {
	return gin.HandlerFunc(func(c *gin.Context) {
		// 如果没有配置IP白名单，则跳过检查
		if len(allowedIPs) == 0 || (len(allowedIPs) == 1 && allowedIPs[0] == "") {
			c.Next()
			return
		}

		clientIP := c.ClientIP()

		// 检查IP是否在白名单中
		for _, allowedIP := range allowedIPs {
			if allowedIP == clientIP {
				c.Next()
				return
			}
		}

		c.JSON(http.StatusForbidden, gin.H{
			"success": false,
			"error":   "IP address not allowed",
		})
		c.Abort()
	})
}
