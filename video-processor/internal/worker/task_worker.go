package worker

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/hibiken/asynq"
	"github.com/sirupsen/logrus"

	"javflix-video-processor/internal/config"
	"javflix-video-processor/internal/service"
)

type TaskWorker struct {
	config       *config.Config
	logger       *logrus.Logger
	videoService *service.VideoService
	server       *asynq.Server
	client       *asynq.Client
}

const (
	TypeVideoProcess = "video:process"
)

func NewTaskWorker(cfg *config.Config, videoService *service.VideoService) *TaskWorker {
	// Redis连接配置
	redisOpt := asynq.RedisClientOpt{
		Addr:     cfg.RedisURL[8:], // 移除 "redis://" 前缀
		Password: cfg.RedisPassword,
		DB:       cfg.RedisDB,
	}

	// 创建Asynq服务器
	server := asynq.NewServer(
		redisOpt,
		asynq.Config{
			Concurrency: cfg.WorkerPoolSize,
			Queues: map[string]int{
				"critical": 6, // 高优先级队列
				"default":  3, // 默认队列
				"low":      1, // 低优先级队列
			},
			Logger: logrus.New(),
		},
	)

	// 创建Asynq客户端
	client := asynq.NewClient(redisOpt)

	logger := logrus.New()
	level, err := logrus.ParseLevel(cfg.LogLevel)
	if err != nil {
		level = logrus.InfoLevel
	}
	logger.SetLevel(level)

	return &TaskWorker{
		config:       cfg,
		logger:       logger,
		videoService: videoService,
		server:       server,
		client:       client,
	}
}

func (tw *TaskWorker) Start() {
	tw.logger.Info("启动任务工作池...")

	// 注册任务处理器
	mux := asynq.NewServeMux()
	mux.HandleFunc(TypeVideoProcess, tw.handleVideoProcessTask)

	// 启动服务器
	if err := tw.server.Run(mux); err != nil {
		tw.logger.Fatalf("任务工作池启动失败: %v", err)
	}
}

func (tw *TaskWorker) Stop() {
	tw.logger.Info("停止任务工作池...")
	tw.server.Shutdown()
	tw.client.Close()
}

func (tw *TaskWorker) EnqueueVideoProcessTask(task *service.ProcessTask, priority int) error {
	payload, err := json.Marshal(task)
	if err != nil {
		return fmt.Errorf("序列化任务失败: %w", err)
	}

	// 根据优先级选择队列
	var queue string
	switch {
	case priority >= 8:
		queue = "critical"
	case priority >= 5:
		queue = "default"
	default:
		queue = "low"
	}

	// 创建任务
	asynqTask := asynq.NewTask(TypeVideoProcess, payload)

	// 设置任务选项
	opts := []asynq.Option{
		asynq.Queue(queue),
		asynq.MaxRetry(3),
		asynq.Timeout(2 * time.Hour), // 2小时超时
	}

	// 入队任务
	info, err := tw.client.Enqueue(asynqTask, opts...)
	if err != nil {
		return fmt.Errorf("任务入队失败: %w", err)
	}

	tw.logger.WithFields(logrus.Fields{
		"taskId":   task.TaskUUID,
		"queue":    queue,
		"priority": priority,
		"id":       info.ID,
	}).Info("视频处理任务已入队")

	return nil
}

func (tw *TaskWorker) handleVideoProcessTask(ctx context.Context, t *asynq.Task) error {
	var task service.ProcessTask
	if err := json.Unmarshal(t.Payload(), &task); err != nil {
		return fmt.Errorf("反序列化任务失败: %w", err)
	}

	tw.logger.WithFields(logrus.Fields{
		"taskId":  task.TaskUUID,
		"movieId": task.MovieID,
	}).Info("开始处理视频任务")

	// 处理视频
	if err := tw.videoService.ProcessVideo(ctx, &task); err != nil {
		tw.logger.WithError(err).Errorf("视频处理失败: %s", task.TaskUUID)
		return fmt.Errorf("视频处理失败: %w", err)
	}

	tw.logger.WithField("taskId", task.TaskUUID).Info("视频处理完成")
	return nil
}

func (tw *TaskWorker) GetQueueInfo() (map[string]interface{}, error) {
	inspector := asynq.NewInspector(asynq.RedisClientOpt{
		Addr:     tw.config.RedisURL[8:],
		Password: tw.config.RedisPassword,
		DB:       tw.config.RedisDB,
	})
	defer inspector.Close()

	// 获取队列统计信息
	stats, err := inspector.GetQueueInfo("default")
	if err != nil {
		return nil, fmt.Errorf("获取队列信息失败: %w", err)
	}

	criticalStats, _ := inspector.GetQueueInfo("critical")
	lowStats, _ := inspector.GetQueueInfo("low")

	// 安全处理可能为nil的stats
	var criticalPending, criticalActive, criticalScheduled, criticalRetry, criticalArchived int
	if criticalStats != nil {
		criticalPending = criticalStats.Pending
		criticalActive = criticalStats.Active
		criticalScheduled = criticalStats.Scheduled
		criticalRetry = criticalStats.Retry
		criticalArchived = criticalStats.Archived
	}

	var lowPending, lowActive, lowScheduled, lowRetry, lowArchived int
	if lowStats != nil {
		lowPending = lowStats.Pending
		lowActive = lowStats.Active
		lowScheduled = lowStats.Scheduled
		lowRetry = lowStats.Retry
		lowArchived = lowStats.Archived
	}

	info := map[string]interface{}{
		"queues": map[string]interface{}{
			"critical": map[string]interface{}{
				"pending":   criticalPending,
				"active":    criticalActive,
				"scheduled": criticalScheduled,
				"retry":     criticalRetry,
				"archived":  criticalArchived,
			},
			"default": map[string]interface{}{
				"pending":   stats.Pending,
				"active":    stats.Active,
				"scheduled": stats.Scheduled,
				"retry":     stats.Retry,
				"archived":  stats.Archived,
			},
			"low": map[string]interface{}{
				"pending":   lowPending,
				"active":    lowActive,
				"scheduled": lowScheduled,
				"retry":     lowRetry,
				"archived":  lowArchived,
			},
		},
		"total": map[string]interface{}{
			"pending":   stats.Pending + criticalPending + lowPending,
			"active":    stats.Active + criticalActive + lowActive,
			"scheduled": stats.Scheduled + criticalScheduled + lowScheduled,
			"retry":     stats.Retry + criticalRetry + lowRetry,
			"archived":  stats.Archived + criticalArchived + lowArchived,
		},
	}

	return info, nil
}
