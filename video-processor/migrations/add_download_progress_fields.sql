-- 添加下载进度相关字段到video_processing_tasks表
-- 执行时间：2024年

-- 添加字节级进度跟踪字段
ALTER TABLE video_processing_tasks 
ADD COLUMN IF NOT EXISTS completed_bytes BIGINT DEFAULT 0,
ADD COLUMN IF NOT EXISTS total_bytes BIGINT DEFAULT 0,
ADD COLUMN IF NOT EXISTS download_speed DECIMAL(10,2) DEFAULT 0.0;

-- 添加当前步骤字段（如果不存在）
ALTER TABLE video_processing_tasks 
ADD COLUMN IF NOT EXISTS current_step VARCHAR(100) DEFAULT '';

-- 添加磁力链接字段（如果不存在）
ALTER TABLE video_processing_tasks 
ADD COLUMN IF NOT EXISTS magnet_link TEXT DEFAULT '';

-- 添加水印相关字段（如果不存在）
ALTER TABLE video_processing_tasks 
ADD COLUMN IF NOT EXISTS watermark_text VARCHAR(255) DEFAULT '',
ADD COLUMN IF NOT EXISTS watermark_position VARCHAR(50) DEFAULT 'bottom-right';

-- 添加质量设置字段（如果不存在）
ALTER TABLE video_processing_tasks 
ADD COLUMN IF NOT EXISTS quality VARCHAR(20) DEFAULT '720p';

-- 添加元数据字段（如果不存在）
ALTER TABLE video_processing_tasks 
ADD COLUMN IF NOT EXISTS metadata JSONB DEFAULT '{}';

-- 添加视频相关结果字段（如果不存在）
ALTER TABLE video_processing_tasks 
ADD COLUMN IF NOT EXISTS video_url TEXT DEFAULT '',
ADD COLUMN IF NOT EXISTS thumbnail_url TEXT DEFAULT '',
ADD COLUMN IF NOT EXISTS duration INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS file_size BIGINT DEFAULT 0;

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_video_tasks_completed_bytes ON video_processing_tasks(completed_bytes);
CREATE INDEX IF NOT EXISTS idx_video_tasks_total_bytes ON video_processing_tasks(total_bytes);
CREATE INDEX IF NOT EXISTS idx_video_tasks_download_speed ON video_processing_tasks(download_speed);
CREATE INDEX IF NOT EXISTS idx_video_tasks_current_step ON video_processing_tasks(current_step);

-- 添加注释
COMMENT ON COLUMN video_processing_tasks.completed_bytes IS '已下载字节数';
COMMENT ON COLUMN video_processing_tasks.total_bytes IS '总字节数';
COMMENT ON COLUMN video_processing_tasks.download_speed IS '下载速度(MB/s)';
COMMENT ON COLUMN video_processing_tasks.current_step IS '当前处理步骤';
COMMENT ON COLUMN video_processing_tasks.magnet_link IS '磁力链接';
COMMENT ON COLUMN video_processing_tasks.watermark_text IS '水印文本';
COMMENT ON COLUMN video_processing_tasks.watermark_position IS '水印位置';
COMMENT ON COLUMN video_processing_tasks.quality IS '视频质量';
COMMENT ON COLUMN video_processing_tasks.metadata IS '任务元数据';
COMMENT ON COLUMN video_processing_tasks.video_url IS '处理后的视频URL';
COMMENT ON COLUMN video_processing_tasks.thumbnail_url IS '缩略图URL';
COMMENT ON COLUMN video_processing_tasks.duration IS '视频时长(秒)';
COMMENT ON COLUMN video_processing_tasks.file_size IS '文件大小(字节)';
