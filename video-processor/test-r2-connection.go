package main

import (
	"fmt"
	"log"
	"os"
	"strings"

	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/credentials"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/s3"
	"github.com/joho/godotenv"
)

func main() {
	// 加载环境变量
	if err := godotenv.Load(); err != nil {
		log.Printf("警告: 无法加载.env文件: %v", err)
	}

	// 获取R2配置
	endpoint := os.Getenv("R2_ENDPOINT")
	accessKeyID := os.Getenv("R2_ACCESS_KEY_ID")
	secretAccessKey := os.Getenv("R2_SECRET_ACCESS_KEY")
	bucket := os.Getenv("R2_BUCKET")
	publicDomain := os.Getenv("R2_PUBLIC_DOMAIN")

	fmt.Println("=== Cloudflare R2 配置测试 ===")
	fmt.Printf("端点: %s\n", endpoint)
	fmt.Printf("访问密钥ID: %s\n", maskString(accessKeyID))
	fmt.Printf("机密访问密钥: %s\n", maskString(secretAccessKey))
	fmt.Printf("存储桶: %s\n", bucket)
	fmt.Printf("公共域名: %s\n", publicDomain)
	fmt.Println()

	// 验证配置
	if endpoint == "" || accessKeyID == "" || secretAccessKey == "" || bucket == "" {
		log.Fatal("❌ R2配置不完整，请检查.env文件")
	}

	// 创建S3客户端（R2兼容S3 API）
	sess, err := session.NewSession(&aws.Config{
		Endpoint: aws.String(endpoint),
		Region:   aws.String("auto"), // R2使用auto区域
		Credentials: credentials.NewStaticCredentials(
			accessKeyID,
			secretAccessKey,
			"",
		),
		S3ForcePathStyle: aws.Bool(true), // R2需要路径样式
	})
	if err != nil {
		log.Fatalf("❌ 创建AWS会话失败: %v", err)
	}

	client := s3.New(sess)

	fmt.Println("=== 测试R2连接 ===")

	// 测试1: 列出存储桶
	fmt.Print("1. 测试列出存储桶... ")
	listBucketsResult, err := client.ListBuckets(&s3.ListBucketsInput{})
	if err != nil {
		fmt.Printf("❌ 失败: %v\n", err)
	} else {
		fmt.Printf("✅ 成功，找到 %d 个存储桶\n", len(listBucketsResult.Buckets))
		for _, b := range listBucketsResult.Buckets {
			fmt.Printf("   - %s\n", *b.Name)
		}
	}

	// 测试2: 检查指定存储桶是否存在
	fmt.Printf("2. 测试存储桶 '%s' 是否存在... ", bucket)
	_, err = client.HeadBucket(&s3.HeadBucketInput{
		Bucket: aws.String(bucket),
	})
	if err != nil {
		fmt.Printf("❌ 失败: %v\n", err)
		fmt.Printf("   提示: 请确保存储桶 '%s' 已创建\n", bucket)
	} else {
		fmt.Println("✅ 存储桶存在")
	}

	// 测试3: 列出存储桶中的对象
	fmt.Printf("3. 测试列出存储桶 '%s' 中的对象... ", bucket)
	listObjectsResult, err := client.ListObjectsV2(&s3.ListObjectsV2Input{
		Bucket:  aws.String(bucket),
		MaxKeys: aws.Int64(10), // 只列出前10个对象
	})
	if err != nil {
		fmt.Printf("❌ 失败: %v\n", err)
	} else {
		fmt.Printf("✅ 成功，找到 %d 个对象\n", len(listObjectsResult.Contents))
		for _, obj := range listObjectsResult.Contents {
			fmt.Printf("   - %s (大小: %d 字节)\n", *obj.Key, *obj.Size)
		}
	}

	// 测试4: 测试上传权限（创建一个测试文件）
	fmt.Print("4. 测试上传权限... ")
	testKey := "test/connection-test.txt"
	testContent := "这是一个连接测试文件"
	
	_, err = client.PutObject(&s3.PutObjectInput{
		Bucket:      aws.String(bucket),
		Key:         aws.String(testKey),
		Body:        strings.NewReader(testContent),
		ContentType: aws.String("text/plain"),
	})
	if err != nil {
		fmt.Printf("❌ 失败: %v\n", err)
	} else {
		fmt.Println("✅ 上传成功")
		
		// 构建访问URL
		var testUrl string
		if publicDomain != "" {
			testUrl = fmt.Sprintf("%s/%s", strings.TrimRight(publicDomain, "/"), testKey)
		} else {
			testUrl = fmt.Sprintf("https://pub-%s.r2.dev/%s", bucket, testKey)
		}
		fmt.Printf("   测试文件URL: %s\n", testUrl)
		
		// 清理测试文件
		fmt.Print("5. 清理测试文件... ")
		_, err = client.DeleteObject(&s3.DeleteObjectInput{
			Bucket: aws.String(bucket),
			Key:    aws.String(testKey),
		})
		if err != nil {
			fmt.Printf("❌ 清理失败: %v\n", err)
		} else {
			fmt.Println("✅ 清理成功")
		}
	}

	fmt.Println()
	fmt.Println("=== 测试完成 ===")
	fmt.Println("如果所有测试都通过，说明R2配置正确，可以开始使用视频处理服务。")
}

// maskString 遮盖字符串的中间部分，用于安全显示
func maskString(s string) string {
	if len(s) <= 8 {
		return strings.Repeat("*", len(s))
	}
	return s[:4] + strings.Repeat("*", len(s)-8) + s[len(s)-4:]
}
