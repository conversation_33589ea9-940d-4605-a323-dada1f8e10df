const { Pool } = require('pg');
require('dotenv').config();

const pool = new Pool({
  host: process.env.PGHOST || 'localhost',
  port: process.env.PGPORT || 5432,
  database: process.env.PGDATABASE || 'javflix',
  user: process.env.PGUSER || 'longgedemacminim4',
  password: process.env.PGPASSWORD || '',
});

async function fixDatabaseSchema() {
  const client = await pool.connect();
  
  try {
    console.log('🔄 开始修复数据库表结构...');
    
    // 添加缺少的字段
    const alterQueries = [
      'ALTER TABLE video_processing_tasks ADD COLUMN IF NOT EXISTS current_step VARCHAR(255);',
      'ALTER TABLE video_processing_tasks ADD COLUMN IF NOT EXISTS download_path TEXT;',
      'ALTER TABLE video_processing_tasks ADD COLUMN IF NOT EXISTS output_path TEXT;',
      'ALTER TABLE video_processing_tasks ADD COLUMN IF NOT EXISTS video_url TEXT;',
      'ALTER TABLE video_processing_tasks ADD COLUMN IF NOT EXISTS thumbnail_url TEXT;',
      'ALTER TABLE video_processing_tasks ADD COLUMN IF NOT EXISTS duration INTEGER;',
      'ALTER TABLE video_processing_tasks ADD COLUMN IF NOT EXISTS file_size BIGINT;',
      'ALTER TABLE video_processing_tasks ADD COLUMN IF NOT EXISTS watermark_text VARCHAR(255) DEFAULT \'JAVFLIX.TV\';',
      'ALTER TABLE video_processing_tasks ADD COLUMN IF NOT EXISTS watermark_position VARCHAR(20) DEFAULT \'bottom-right\';',
      'ALTER TABLE video_processing_tasks ADD COLUMN IF NOT EXISTS quality VARCHAR(20) DEFAULT \'high\';',
      'ALTER TABLE video_processing_tasks ADD COLUMN IF NOT EXISTS metadata JSONB DEFAULT \'{}\'::jsonb;'
    ];

    for (const query of alterQueries) {
      try {
        await client.query(query);
        console.log(`✅ 执行成功: ${query.substring(0, 50)}...`);
      } catch (error) {
        if (error.message.includes('already exists')) {
          console.log(`⚠️  字段已存在: ${query.substring(0, 50)}...`);
        } else {
          console.error(`❌ 执行失败: ${query}`, error.message);
        }
      }
    }

    // 验证表结构
    const result = await client.query(`
      SELECT column_name, data_type, is_nullable, column_default
      FROM information_schema.columns 
      WHERE table_name = 'video_processing_tasks'
      ORDER BY ordinal_position;
    `);

    console.log('\n📊 当前video_processing_tasks表结构:');
    result.rows.forEach(row => {
      console.log(`  - ${row.column_name}: ${row.data_type} ${row.is_nullable === 'NO' ? 'NOT NULL' : ''} ${row.column_default ? `DEFAULT ${row.column_default}` : ''}`);
    });

    console.log('\n🎉 数据库表结构修复完成！');
    
  } catch (error) {
    console.error('❌ 修复表结构时出错:', error);
    throw error;
  } finally {
    client.release();
    await pool.end();
  }
}

// 运行脚本
fixDatabaseSchema()
  .then(() => {
    console.log('✅ 脚本执行完成');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ 脚本执行失败:', error);
    process.exit(1);
  });
