# JAVFLIX.TV - API文档

JAVFLIX.TV是一个视频流媒体平台，提供了丰富的API接口用于视频内容管理和用户交互。

## API概览

所有API接口均可通过以下方式访问：
- **开发环境前端代理**: `http://localhost:3001/api`（推荐使用）
- **直接后端API**: `http://localhost:4000/api`（仅后端开发时使用）

### 架构设计说明

当前系统采用前端代理API的设计，原因如下：

1. **避免跨域问题**: 前端(3001) → 后端(4000) 会产生跨域请求
2. **统一API入口**: 前端只需要连接到自己的域名
3. **代理功能增强**: 可以在代理层添加认证、缓存、错误处理等功能
4. **简化部署**: 生产环境中前端和API可以使用同一个域名

### API请求流程

```
用户请求 → 前端(3001) → 前端代理(/api/*) → 后端API(4000) → 返回结果
```

因此，前端代码应该使用相对路径调用API：

```javascript
// ✅ 推荐：使用相对路径，通过前端代理
const response = await fetch('/api/videos/popular?limit=10');

// ❌ 不推荐：直接调用后端API（会有跨域问题）
const response = await fetch('http://localhost:4000/api/videos/popular?limit=10');
```

### 视频相关API

| 路径 | 方法 | 描述 | 参数 |
|------|------|------|------|
| `/videos` | GET | 获取视频列表 | `page`, `limit` |
| `/videos/popular` | GET | 获取热门视频 | `limit` |
| `/videos/recent` | GET | 获取最新视频 | `limit` |
| `/videos/:id` | GET | 通过ID获取视频详情 | - |
| `/videos/slug/:slug` | GET | 通过Slug获取视频详情 | - |
| `/videos/search` | GET | 搜索视频 | `q`, `page`, `limit` |
| `/videos/stars/:id` | GET | 获取演员详情 | - |

### 用户相关API

| 路径 | 方法 | 描述 | 参数 |
|------|------|------|------|
| `/users/register` | POST | 用户注册 | `username`, `email`, `password` |
| `/users/login` | POST | 用户登录 | `email`, `password` |
| `/users/profile` | GET | 获取用户信息 | 需要认证 |
| `/users/profile` | PUT | 更新用户信息 | 需要认证 |
| `/users/favorites` | GET | 获取收藏列表 | 需要认证, `page`, `limit` |
| `/users/favorites/:videoId` | POST | 添加收藏 | 需要认证 |
| `/users/favorites/:videoId` | DELETE | 取消收藏 | 需要认证 |

### 🔥 实时视频统计系统（Redis+PostgreSQL+Socket.IO）

**核心特性**：融合统计架构 + 跨页面实时同步 + 智能数据管理

#### 📊 统计API端点

| 路径 | 方法 | 描述 | 参数 | 状态 |
|------|------|------|------|------|
| `/video-stats/fused/:videoId` | GET | **融合统计数据**（DB基础+Redis增量） | - | ✅ **核心API** |
| `/video-stats/:videoId/increment` | POST | 增加视频统计 | `statType`(`views`/`likes`/`favorites`), `delta` | ✅ **已实现** |
| `/video-stats/:videoId` | GET | 获取Redis统计 | - | ✅ **已实现** |
| `/video-stats/batch` | POST | 批量获取统计 | `videoIds[]` | ✅ **已实现** |
| `/proxy/users/likes/:videoId` | POST/DELETE | 用户点赞操作 | - | ✅ **同步管理器** |
| `/proxy/users/favorites/:videoId` | POST/DELETE | 用户收藏操作 | - | ✅ **同步管理器** |

#### 🏗️ 系统架构设计

```
┌─────────────────────────────────────────────────────────────────┐
│                    JAVFLIX 实时统计系统                         │
├─────────────────────────────────────────────────────────────────┤
│  🎬 播放页面                    📱 个人资料页面                  │
│  ┌─────────────────────┐      ┌─────────────────────┐           │
│  │ useVideoStats Hook  │      │ sync-manager        │           │
│  │ • 融合API获取       │◄────►│ • 本地缓存优先      │           │
│  │ • Socket.IO实时     │      │ • 事件驱动同步      │           │
│  │ • 乐观更新策略      │      │ • 智能降级处理      │           │
│  └─────────────────────┘      └─────────────────────┘           │
├─────────────────────────────────────────────────────────────────┤
│  🔄 跨页面同步机制                                               │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │ CustomEvent('likeStatusChanged') ◄──► localStorage缓存     │ │
│  │ CustomEvent('favoriteStatusChanged') ◄──► 实时事件广播    │ │
│  └─────────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────────┤
│  ⚡ 后端服务层                                                   │
│  ┌─────────────────────┐      ┌─────────────────────┐           │
│  │ Redis (实时层)      │      │ PostgreSQL (持久层) │           │
│  │ • Lua原子操作       │◄────►│ • 用户操作记录      │           │
│  │ • 增量统计缓存      │      │ • 基础观看统计      │           │
│  │ • Socket.IO发布     │      │ • 关系型查询        │           │
│  └─────────────────────┘      └─────────────────────┘           │
└─────────────────────────────────────────────────────────────────┘
```

#### 🎯 核心技术特性

1. **📈 融合统计API** - 读时融合策略
   ```javascript
   // 核心融合逻辑
   const fusedStats = {
     views: dbStats.view_count + redisStats.views,
     likes: dbStats.likes_count + redisStats.likes,
     favorites: dbStats.favorites_count + redisStats.favorites
   };
   ```

2. **🔄 跨页面实时同步**
   ```javascript
   // 播放页操作 → 个人页实时更新
   播放页点赞 → sync-manager → localStorage + 事件 → 个人页刷新
   个人页操作 → sync-manager → 后端API → Socket.IO → 播放页更新
   ```

3. **⚡ 智能数据管理**
   - **本地优先**：用户操作立即反映在本地
   - **后端同步**：异步同步到服务器
   - **事件驱动**：跨页面状态同步
   - **错误容错**：网络问题时保持本地状态

#### 🚀 页面互通机制

##### 播放页面 → 个人页面
```typescript
// 1. 用户在播放页点赞
useVideoStats.incrementLikes() 
  → sync-manager.likeVideo()
  → localStorage更新 + 事件触发
  → 个人页监听事件自动刷新

// 2. Socket.IO实时广播
Redis PUBLISH → Socket.IO → 所有连接的客户端同步更新
```

##### 个人页面 → 播放页面  
```typescript
// 1. 个人页面操作
sync-manager操作 
  → 后端API调用
  → Redis更新 + Socket.IO广播
  → 播放页实时接收更新

// 2. 本地事件同步
localStorage变化 → CustomEvent → 播放页监听器 → UI更新
```

#### 📊 数据流程详解

```
用户点赞操作完整流程：
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│  用户点击   │───►│ 同步管理器  │───►│ 本地缓存    │
│  点赞按钮   │    │ 处理操作    │    │ 立即更新    │
└─────────────┘    └─────────────┘    └─────────────┘
                           │                   │
                           ▼                   ▼
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│ 后端API     │◄───│ 异步同步    │    │ 事件触发    │
│ 持久化存储  │    │ 到服务器    │    │ 跨页面通知  │
└─────────────┘    └─────────────┘    └─────────────┘
        │                                      │
        ▼                                      ▼
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│ Redis更新   │───►│ Socket.IO   │───►│ 其他页面    │
│ 实时统计    │    │ 实时广播    │    │ 同步更新    │
└─────────────┘    └─────────────┘    └─────────────┘
```



**当前限制**:
1. **中文搜索限制** - 对于中文关键词，搜索功能可能仍有限制。建议使用英文关键词或影片ID进行搜索以获得最佳结果。
2. **网络依赖** - API依赖外部数据源，网络问题可能导致某些请求失败。

### 推荐系统API

| 路径 | 方法 | 描述 | 参数 |
|------|------|------|------|
| `/recommendations` | GET | 获取推荐视频 | `userId?`, `limit`, `locale` |
| `/recommendations/featured` | GET | 获取精选视频 | `limit` |

## 认证

认证基于JWT令牌，客户端需要在HTTP请求头中包含以下内容：

```
Authorization: Bearer {token}
```

## 响应格式

所有API响应都使用以下标准格式：

```json
{
  "success": true|false,
  "message": "操作结果描述",
  "data": {},
  "code": 200
}
```

## 前端调用示例

```javascript
// 获取热门视频 - 使用前端代理
async function fetchPopularVideos() {
  try {
    const response = await fetch('/api/videos/popular?limit=10');
    const data = await response.json();
    return data.data;
  } catch (error) {
    console.error('获取热门视频失败', error);
    return [];
  }
}

// 🔥 实时视频统计系统使用示例

// 1. 播放页面 - 使用 useVideoStats Hook
import { useVideoStats } from '@/hooks/useVideoStats';

function VideoPlayer({ videoId }) {
  const { stats, actions, loading, isConnected } = useVideoStats({
    videoId,
    enableRealtime: true,    // 启用Socket.IO实时更新
    useFusedApi: true        // 使用融合API (DB+Redis)
  });

  const handleLike = async () => {
    try {
      await actions.incrementLikes();
      // 自动触发跨页面同步事件
    } catch (error) {
      console.error('点赞失败:', error);
    }
  };

  return (
    <div>
      <div>观看: {stats.views} | 点赞: {stats.likes} | 收藏: {stats.favorites}</div>
      <button onClick={handleLike}>点赞</button>
      <div>实时连接: {isConnected ? '✅' : '❌'}</div>
    </div>
  );
}

// 2. 个人页面 - 使用同步管理器
import { getUserLikes, getUserFavorites, startSyncManager } from '@/lib/sync-manager';

function ProfilePage() {
  const [likedVideos, setLikedVideos] = useState([]);
  
  useEffect(() => {
    // 启动同步管理器
    startSyncManager();
    
    // 获取用户点赞列表（本地缓存 + 服务器数据合并）
    const fetchLikes = async () => {
      const result = await getUserLikes(1, 12);
      setLikedVideos(result.data);
    };
    
    fetchLikes();
    
    // 监听跨页面同步事件
    const handleLikeChange = () => {
      console.log('检测到点赞状态变化，刷新列表');
      fetchLikes(); // 自动刷新
    };
    
    window.addEventListener('likeStatusChanged', handleLikeChange);
    return () => window.removeEventListener('likeStatusChanged', handleLikeChange);
  }, []);

  return (
    <div>
      <h2>我的点赞 ({likedVideos.length})</h2>
      {likedVideos.map(video => (
        <VideoCard key={video.id} video={video} />
      ))}
    </div>
  );
}

// 3. 获取融合统计数据
async function getFusedVideoStats(videoId) {
  try {
    const response = await fetch(`/api/video-stats/fused/${videoId}`);
    const data = await response.json();
    return data.data; 
    // 返回: { 
    //   views: 150, likes: 25, favorites: 8,
    //   dbBase: { views: 100, likes: 20, favorites: 5 },
    //   redisIncrement: { views: 50, likes: 5, favorites: 3 }
    // }
  } catch (error) {
    console.error('获取融合统计失败', error);
    return null;
  }
}

// 4. 手动触发跨页面同步事件
function triggerSyncEvent(videoId, isLiked) {
  window.dispatchEvent(new CustomEvent('likeStatusChanged', {
    detail: { videoId, isLiked }
  }));
}

// 5. Socket.IO实时通信示例（已集成在useVideoStats中）
import { getSocketClient } from '@/lib/socket-client';

function setupRealtimeStats(videoId) {
  const socketClient = getSocketClient();
  
  // 订阅视频统计更新
  socketClient.subscribeToStats(videoId, (stats) => {
    console.log('收到实时统计更新:', stats);
    // 自动更新UI显示
    updateStatsDisplay(videoId, stats);
  });
  
  // 检查连接状态
  const status = socketClient.getConnectionStatus();
  console.log('Socket连接状态:', status);
  
  return () => {
    socketClient.unsubscribeFromStats(videoId);
  };
}

// 6. 完整的跨页面同步示例
function setupCrossPageSync() {
  // 监听所有同步事件
  const events = ['likeStatusChanged', 'favoriteStatusChanged'];
  
  events.forEach(eventName => {
    window.addEventListener(eventName, (event) => {
      console.log(`跨页面同步事件: ${eventName}`, event.detail);
      
      // 根据事件类型更新相应的UI
      if (eventName === 'likeStatusChanged') {
        updateLikeStatus(event.detail.videoId, event.detail.isLiked);
      } else if (eventName === 'favoriteStatusChanged') {
        updateFavoriteStatus(event.detail.videoId, event.detail.isFavorited);
      }
    });
  });
}

// 用户登录 - 使用前端代理
async function login(email, password) {
  try {
    const response = await fetch('/api/users/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ email, password })
    });
    const data = await response.json();
    
    if (data.success) {
      // 保存token到localStorage
      localStorage.setItem('auth_token', data.data.token);
    }
    
    return data;
  } catch (error) {
    console.error('登录失败', error);
    return { success: false, message: '登录失败' };
  }
}

// 点赞视频 - 使用前端代理
async function likeVideo(videoId) {
  try {
    const token = localStorage.getItem('auth_token');
    const response = await fetch(`/api/proxy/users/likes/${videoId}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      }
    });
    const data = await response.json();
    return data.success;
  } catch (error) {
    console.error('点赞失败', error);
    return false;
  }
}

// 收藏视频 - 使用前端代理
async function favoriteVideo(videoId) {
  try {
    const token = localStorage.getItem('auth_token');
    const response = await fetch(`/api/proxy/users/favorites/${videoId}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      }
    });
    const data = await response.json();
    return data.success;
  } catch (error) {
    console.error('收藏失败', error);
    return false;
  }
}
```

### 代理API vs 直接API调用

| 使用场景 | 调用方式 | 示例 |
|---------|---------|------|
| 前端组件调用 | 代理API | `fetch('/api/videos/popular')` |
| 用户相关功能 | 专用代理 | `fetch('/api/proxy/users/likes/123')` |
| 后端服务间调用 | 直接API | `fetch('http://localhost:4000/api/videos')` |
| 外部服务调用 | 直接API | `fetch('http://localhost:4000/api/videos')` |

## 项目结构

- `javflix-api/`: 后端API服务器（端口4000）
- `javflix/`: 前端应用（端口3001）
- `admin-panel/`: 管理员控制面板
- `javbus-api/`: 第三方数据源API（端口3000）

## 系统架构 🚀

JAVFLIX.TV采用现代化的微服务架构设计，集成了实时通信、缓存和统计功能：

### ✨ 新架构特性（2024年最新版）

- **🔄 实时视频统计系统**：基于Redis + Socket.IO的实时数据同步
- **⚡ 高性能缓存**：Redis缓存层，支持原子操作和增量更新  
- **🔗 WebSocket实时通信**：Socket.IO房间管理，多用户实时交互
- **⏰ 定时数据同步**：Redis到PostgreSQL的批量同步（30秒间隔）
- **🛡️ TypeScript支持**：完整的类型安全和开发体验
- **📊 优化的前端更新**：乐观更新 + 实时推送的混合模式
- **🔄 跨页面数据同步**：播放页面与个人资料页面完全互通
- **🎯 智能同步管理器**：本地缓存优先 + 事件驱动的数据一致性
- **📈 融合统计API**：读时融合PostgreSQL基础数据与Redis增量数据

### 🏗️ 系统组件架构

```
                  ┌─────────────┐    
                  │             │    
┌─────────────┐   │ JavFlix前端 │    
│             │   │  (端口3001) │    
│ 管理员面板  │   │             │    
│ (端口8000)  │   └──────┬──────┘    
│             │          │           
└──────┬──────┘          │           
       │                 │           
       │                 │ (代理API)
┌──────▼─────────────────▼──────┐    
│                                │    
│       JavFlix-API服务器        │    
│         (端口4000)             │    
│                                │    
└──────┬─────────────────┬───────┘    
       │                 │           
       │                 │           
┌──────▼──────┐  ┌───────▼───────┐   
│             │  │               │   
│  JavBus-API │  │ PostgreSQL    │   
│  (端口3000) │  │ 数据库        │   
│             │  │               │   
└─────────────┘  └───────────────┘   
```

### 🔧 主要组件说明

1. **🎯 JavFlix-API (端口4000) - 核心服务器**
   - **TypeScript + Express**：类型安全的后端服务
   - **Redis集成**：Lua脚本支持的原子操作缓存
   - **Socket.IO服务器**：实时WebSocket通信
   - **PostgreSQL连接**：主数据库存储
   - **定时同步服务**：Redis → PostgreSQL数据同步
   - **视频统计API**：观看、点赞、收藏实时统计
   - **JWT认证**：用户身份验证和授权
   - **媒体文件管理**：图片下载和存储

2. **🌐 JavFlix前端 (端口3001) - 用户界面**
   - **Next.js框架**：现代React应用
   - **Socket.IO客户端**：实时数据接收
   - **优化更新策略**：乐观更新 + 实时同步
   - **响应式设计**：支持多种设备
   - **API代理功能**：统一API调用入口
   - **实时统计组件**：多浏览器同步的统计显示
   - **🎬 播放页面系统**：
     - `useVideoStats` Hook：融合API + Socket.IO实时更新
     - 自动加入视频房间，接收实时统计广播
     - 乐观更新策略，操作立即反映
   - **📱 个人资料页面系统**：
     - `sync-manager`：智能数据同步管理器
     - 本地缓存优先策略，离线操作支持
     - 跨页面事件监听，实时状态同步
   - **🔄 跨页面互通机制**：
     - CustomEvent事件系统：`likeStatusChanged`, `favoriteStatusChanged`
     - localStorage状态缓存：用户操作立即本地化
     - 智能数据合并：本地状态 + 服务器数据融合

3. **📡 JavBus-API (端口3000) - 数据源服务**
   - **外部数据获取**：影片和演员信息爬取
   - **搜索功能**：关键词和筛选搜索
   - **无状态设计**：不存储持久化数据
   - **数据转换**：标准化外部数据格式

4. **⚙️ 管理员面板 (端口8000) - 后台管理**
   - **数据导入工具**：JavBus → 本地数据库
   - **用户权限管理**：管理员功能控制
   - **系统监控**：服务状态和性能监控

### 数据流向

1. **数据获取流程**：管理员面板 → JavBus-API → JavFlix-API → 数据库
2. **用户访问流程**：用户 → 前端 → **前端代理API** → JavFlix-API → 数据库
3. **媒体访问流程**：用户 → 前端 → **前端代理API** → JavFlix-API → 文件系统

### 前端代理API的作用

前端(3001)内置了API代理功能，主要解决以下问题：

1. **跨域问题**：避免前端直接访问4000端口产生的CORS错误
2. **认证统一**：在代理层统一处理用户认证token
3. **错误处理**：提供统一的错误处理和重试机制
4. **开发便利**：前端开发者只需要关心一个API域名

### 架构设计总结

这种架构设计的核心思想是：

- **前端(3001)**：负责用户界面和API代理，所有前端请求都通过内置代理转发
- **后端(4000)**：专注于业务逻辑，不需要处理跨域问题
- **外部API(3000)**：专门的数据获取服务，解耦数据源和业务逻辑

好处：
- ✅ 前端开发简单，只需要相对路径调用API
- ✅ 后端专注业务逻辑，不需要配置CORS
- ✅ 便于添加认证、缓存、限流等中间件功能
- ✅ 生产环境部署简单，前后端可以使用同一域名

## JavBus-API 详细文档

JavBus-API是一个专门负责从外部数据源获取影片数据的服务。它运行在端口3000上，提供了丰富的数据获取API。

### API端点

基础URL: `http://localhost:3000/api`

#### 影片相关API

| 路径 | 方法 | 描述 | 参数 | 状态 |
|------|------|------|------|------|
| `/movies` | GET | 获取影片列表 | `page`, `magnet`(`exist`/`all`), `type`(`normal`/`uncensored`) | ✅ 正常 |
| `/movies/search` | GET | 搜索影片 | `keyword`, `page`, `magnet`, `type` | ✅ 正常 |
| `/movies/:id` | GET | 获取影片详情 | - | ✅ 正常 |
| `/magnets/:movieId` | GET | 获取影片磁力链接 | `gid`, `uc`, `sortBy`, `sortOrder` | ✅ 正常 |

#### 演员相关API

| 路径 | 方法 | 描述 | 参数 | 状态 |
|------|------|------|------|------|
| `/stars/:id` | GET | 获取演员详情 | `type`(可选) | ✅ 正常 |

### 特殊筛选参数

除了基本参数外，还可以使用以下筛选条件：

| 参数 | 说明 | 示例 |
|------|------|------|
| `filterType` | 筛选类型 | `star`, `genre`, `director`, `studio`, `label`, `series` |
| `filterValue` | 筛选值 | 对应类型的ID，如演员ID、类型ID等 |

示例：
```
/movies?filterType=star&filterValue=6z2
```

### 目前已知问题

**已修复问题**:
1. **无码影片API已修复** - 现在无码影片API (`/movies?type=uncensored`) 正常工作。
2. **搜索功能增强** - 已增强搜索API以支持更多关键词和搜索方式。

**当前限制**:
1. **中文搜索限制** - 对于中文关键词，搜索功能可能仍有限制。建议使用英文关键词或影片ID进行搜索以获得最佳结果。
2. **网络依赖** - API依赖外部数据源，网络问题可能导致某些请求失败。

### 数据响应格式示例

#### 获取影片列表

```json
{
  "movies": [
    {
      "date": "2024-05-01",
      "id": "ABC-123",
      "img": "https://example.com/images/abc-123.jpg",
      "title": "影片标题",
      "tags": ["高清", "字幕"]
    }
  ],
  "pagination": {
    "currentPage": 1,
    "hasNextPage": true,
    "nextPage": 2,
    "pages": [1, 2, 3, 4, 5]
  }
}
```

#### 获取影片详情

```json
{
  "id": "ABC-123",
  "title": "影片标题",
  "img": "https://example.com/images/abc-123.jpg",
  "date": "2024-05-01",
  "videoLength": 120,
  "director": { "id": "d123", "name": "导演名" },
  "producer": { "id": "m456", "name": "制作商" },
  "publisher": { "id": "l789", "name": "发行商" },
  "genres": [
    { "id": "g1", "name": "类型1" },
    { "id": "g2", "name": "类型2" }
  ],
  "stars": [
    { "id": "s1", "name": "演员1" },
    { "id": "s2", "name": "演员2" }
  ],
  "samples": [
    { 
      "id": "sample1",
      "alt": "样品图片1", 
      "src": "https://example.com/samples/1.jpg",
      "thumbnail": "https://example.com/samples/thumb/1.jpg" 
    }
  ],
  "gid": "123456789",
  "uc": "0"
}
```

#### 获取磁力链接

```json
[
  {
    "id": "ABCDEF1234567890",
    "link": "magnet:?xt=urn:btih:ABCDEF1234567890",
    "isHD": true,
    "title": "影片标题",
    "size": "4.52GB",
    "numberSize": 4852620738,
    "shareDate": "2024-05-01",
    "hasSubtitle": true
  }
]
```

#### 获取演员信息

```json
{
  "avatar": "https://example.com/stars/s1.jpg",
  "id": "s1",
  "name": "演员名称",
  "birthday": "1990-01-01",
  "age": 34,
  "height": 165,
  "bust": "90cm",
  "waistline": "60cm",
  "hipline": "85cm",
  "birthplace": "东京",
  "hobby": "旅行"
}
```

### 错误处理

API可能返回以下错误：

1. **404 Not Found** - 请求的资源不存在
   ```json
   {"error":"Not Found","messages":[]}
   ```

2. **400 Bad Request** - 请求参数无效
   ```json
   {"error":"Bad Request","messages":["Invalid parameter"]}
   ```

### 使用示例

```javascript
// 获取影片列表
async function fetchMovies() {
  try {
    // 基本使用方法
    const response = await fetch('http://localhost:3000/api/movies?page=1&magnet=exist');
    
    // 获取无码影片
    // const response = await fetch('http://localhost:3000/api/movies?page=1&magnet=exist&type=uncensored');
    
    // 使用筛选条件
    // const response = await fetch('http://localhost:3000/api/movies?filterType=star&filterValue=6z2');
    
    const data = await response.json();
    return data.movies;
  } catch (error) {
    console.error('获取影片列表失败', error);
    return [];
  }
}

// 搜索影片 - 使用具体关键词获得更好结果
async function searchMovies(keyword) {
  try {
    // 推荐使用英文关键词或影片ID
    const response = await fetch(`http://localhost:3000/api/movies/search?keyword=${encodeURIComponent(keyword)}&page=1`);
    const data = await response.json();
    return data.movies;
  } catch (error) {
    console.error('搜索影片失败', error);
    return [];
  }
}

// 获取影片详情
async function getMovieDetail(id) {
  try {
    const response = await fetch(`http://localhost:3000/api/movies/${id}`);
    return await response.json();
  } catch (error) {
    console.error('获取影片详情失败', error);
    return null;
  }
}

// 获取磁力链接
async function getMovieMagnets(movieId, gid, uc) {
  try {
    const response = await fetch(`http://localhost:3000/api/magnets/${movieId}?gid=${gid}&uc=${uc}`);
    return await response.json();
  } catch (error) {
    console.error('获取磁力链接失败', error);
    return [];
  }
}
```

## 代理API说明

JavFlix-API（端口4000）中的`/api/javbus/`路径下的所有接口都是对JavBus-API（端口3000）的代理转发。这种设计有以下几个目的：

1. **统一接口管理**：通过javflix-api提供统一的API入口，前端只需要连接到一个API服务器
2. **认证与权限控制**：可以在代理层添加认证和权限检查，而不需要修改原始API
3. **数据转换**：可以在转发过程中进行数据格式转换或过滤
4. **跨域问题解决**：避免前端直接访问不同域的API服务产生的跨域问题

### 代理API工作原理

1. 客户端发送请求到`http://localhost:4000/api/javbus/*`
2. javflix-api服务器接收请求
3. javflix-api将请求转发到`http://localhost:3000/api/*`（去掉了'javbus'路径）
4. javflix-api接收javbus-api的响应
5. javflix-api将响应原样返回给客户端

例如，当客户端请求`http://localhost:4000/api/javbus/movies?limit=10`时：
- javflix-api将请求转发到`http://localhost:3000/api/movies?limit=10`
- 然后将返回的数据直接传递给客户端

### 代理API配置

代理设置在`javflix-api/src/controllers/javbusController.js`中定义：

```javascript
const JAVBUS_API_BASE_URL = process.env.JAVBUS_API_URL || 'http://localhost:3000/api';
```

可以通过环境变量`JAVBUS_API_URL`修改JavBus-API的地址。

### 使用代理API的好处

1. 简化前端访问 - 只需连接单一API服务器
2. 增强安全性 - 可以在代理层添加额外的安全措施
3. 便于迁移和扩展 - 后端服务结构变化时，前端无需修改
4. 集中式日志和监控 - 所有API请求都经过同一服务器处理

## 数据库API

以下API用于访问JAVFLIX.TV自己的数据库，获取已导入和处理过的数据。**与代理API不同，这些接口直接查询本地PostgreSQL数据库**。

⚠️ **重要说明：**
- 🔄 **JavBus代理API** (`/api/javbus/*`): 获取外部实时数据，适合浏览最新内容
- 💾 **数据库API** (`/api/db/*`): 查询本地已处理数据，适合个性化功能和快速查询
- 📊 建议优先使用数据库API，它包含了已清洗和优化的数据，响应更快

### 女优/演员数据API ⭐

| 路径 | 方法 | 描述 | 参数 | 状态 |
|------|------|------|------|------|
| `/db/stars` | GET | 获取数据库中的演员列表 | `page`, `limit`, `sort`, `order` | ✅ **已实现** |
| `/db/stars/search` | GET | 搜索数据库中的演员 | `keyword`, `page`, `limit` | ✅ **已实现** |
| `/db/stars/:id` | GET | 获取数据库中的演员详情 | - | ✅ **已实现** |
| `/db/stars/popular` | GET | 获取数据库中的热门演员 | `limit` | ✅ **已实现** |

### 分类/标签数据API 🏷️

| 路径 | 方法 | 描述 | 参数 | 状态 |
|------|------|------|------|------|
| `/db/genres` | GET | 获取数据库中的类型列表 | `sort`, `limit` | ✅ **已实现** |
| `/db/genres/:id` | GET | 获取数据库中的类型详情 | - | ✅ **已实现** |
| `/db/genres/popular` | GET | 获取数据库中的热门类型 | `limit` | ✅ **已实现** |

### 影片数据API

| 路径 | 方法 | 描述 | 参数 |
|------|------|------|------|
| `/db/movies` | GET | 获取数据库中的影片列表 | `page`, `limit`, `sort`, `order` |
| `/db/movies/search` | GET | 搜索数据库中的影片 | `keyword`, `page`, `limit` |
| `/db/movies/:id` | GET | 获取数据库中的影片详情 | - |
| `/db/movies/popular` | GET | 获取数据库中的热门影片 | `limit` |
| `/db/movies/recent` | GET | 获取数据库中的最新影片 | `limit` |
| `/db/movies/genre/:genreId` | GET | 获取指定类型的影片 | `page`, `limit` |
| `/db/movies/star/:starId` | GET | 获取指定演员的影片 | `page`, `limit` |
| `/db/movies/:movieId/magnets` | GET | 获取影片的磁力链接 | - |

### 数据统计API

| 路径 | 方法 | 描述 | 参数 |
|------|------|------|------|
| `/db/stats/movies` | GET | 获取影片统计信息 | - |
| `/db/stats/stars` | GET | 获取演员统计信息 | - |
| `/db/stats/genres` | GET | 获取类型统计信息 | - |
| `/db/stats/magnets` | GET | 获取磁力链接统计信息 | - |

## 🚨 故障排除

### 常见问题和解决方案

#### 1. 服务器启动失败
```bash
# 检查端口占用
lsof -i :4000

# 检查Redis连接
redis-cli ping

# 检查PostgreSQL连接
psql -h localhost -p 5432 -U your_username -d javflix
```

#### 2. API请求404错误
确保使用正确的启动方式：
```bash
# ✅ 正确：使用新的TypeScript服务器
npm run dev  # 或 npm start

# ❌ 错误：旧的启动方式（已废弃）
npm run dev:old
```

#### 3. 实时统计不工作
检查以下服务状态：
```bash
# 检查健康状态
curl http://localhost:4000/api/users/stats/health

# 预期输出应包含：
# - videoStats.status: "healthy"
# - socket.status: "healthy"
```

#### 4. 前端无法连接Socket.IO
确认防火墙和代理设置：
```javascript
// 前端Socket.IO连接配置
const socket = io('http://localhost:4000', {
  transports: ['websocket', 'polling']
});
```

### 🔧 开发工具

#### 实时监控Redis数据
```bash
# 监控Redis中的视频统计
redis-cli monitor

# 查看特定视频的统计
redis-cli hgetall video:stats:123

# 查看待同步的视频列表
redis-cli zrange modified_videos 0 -1
```

#### 数据库调试
```sql
-- 检查用户统计表
SELECT * FROM user_favorites LIMIT 10;
SELECT * FROM user_likes LIMIT 10;
SELECT * FROM user_history LIMIT 10;

-- 检查视频统计
SELECT m.movie_id, m.title, 
       COUNT(DISTINCT uf.user_id) as favorites,
       COUNT(DISTINCT ul.user_id) as likes,
       COUNT(DISTINCT uh.user_id) as views
FROM movies m
LEFT JOIN user_favorites uf ON m.id = uf.movie_id
LEFT JOIN user_likes ul ON m.id = ul.movie_id  
LEFT JOIN user_history uh ON m.id = uh.movie_id
GROUP BY m.id, m.movie_id, m.title
ORDER BY favorites DESC
LIMIT 10;
```

### 🚀 生产环境部署

#### 环境变量配置
```bash
# .env文件示例
NODE_ENV=production
PORT=4000

# 数据库配置
PGHOST=your_postgres_host
PGPORT=5432
PGDATABASE=javflix
PGUSER=your_username
PGPASSWORD=your_password

# Redis配置
REDIS_URL=redis://your_redis_host:6379

# JWT密钥
JWT_SECRET=your_jwt_secret_key

# 其他配置
CORS_ORIGIN=https://your-domain.com
```

#### 生产环境启动
```bash
# 构建和启动
npm run build
npm start

# 使用PM2管理进程（推荐）
npm install -g pm2
pm2 start dist/server.js --name javflix-api
pm2 save
pm2 startup
```

### 数据库模型结构

以下是主要数据库表及其关键字段：

#### 电影表 (movies)
- **关键字段**：`id`, `movie_id`, `title`, `image_url`, `release_date`, `duration`, `description`
- **外键关联**：`director_id`, `producer_id`, `publisher_id`, `series_id`

#### 演员表 (stars)
- **关键字段**：`id`, `star_id`, `name`, `image_url`

#### 类型表 (genres)
- **关键字段**：`id`, `name`

#### 磁力链接表 (magnets)
- **关键字段**：`id`, `movie_id`, `link`, `title`, `size`, `is_hd`, `has_subtitle`

#### 关联表
- **movie_stars**：电影-演员关联
- **movie_genres**：电影-类型关联

### 数据响应格式

所有数据库API均使用统一的响应格式：

```json
{
  "success": true,
  "data": {
    "items": [...],  // 数据项数组
    "pagination": {
      "currentPage": 1,
      "totalPages": 10,
      "totalItems": 100,
      "limit": 10
    }
  },
  "message": "操作成功",
  "code": 200
}
```

### 使用示例

```javascript
// 获取数据库中的影片列表 - 前端调用（推荐）
async function fetchDatabaseMovies() {
  try {
    const response = await fetch('/api/db/movies?page=1&limit=20&sort=release_date&order=desc');
    const result = await response.json();
    return result.data.items;
  } catch (error) {
    console.error('获取数据库影片失败', error);
    return [];
  }
}

// 获取数据库中的影片列表 - 后端服务间调用
async function fetchDatabaseMoviesBackend() {
  try {
    const response = await fetch('http://localhost:4000/api/db/movies?page=1&limit=20&sort=release_date&order=desc');
    const result = await response.json();
    return result.data.items;
  } catch (error) {
    console.error('获取数据库影片失败', error);
    return [];
  }
}

// 搜索数据库中的演员 - 前端调用（推荐）
async function searchDatabaseStars(keyword) {
  try {
    const response = await fetch(`/api/db/stars/search?keyword=${encodeURIComponent(keyword)}&page=1&limit=10`);
    const result = await response.json();
    return result.data.items;
  } catch (error) {
    console.error('搜索演员失败', error);
    return [];
  }
}

// 获取热门类型 - 前端调用（推荐）
async function fetchPopularGenres() {
  try {
    const response = await fetch('/api/db/genres/popular?limit=20');
    const result = await response.json();
    return result.data.items;
  } catch (error) {
    console.error('获取热门类型失败', error);
    return [];
  }
}

// 获取影片磁力链接 - 前端调用（推荐）
async function getMovieMagnets(movieId) {
  try {
    const response = await fetch(`/api/db/movies/${movieId}/magnets`);
    const result = await response.json();
    return result.data.items;
  } catch (error) {
    console.error('获取影片磁力链接失败', error);
    return [];
  }
}
```




## 本地开发

### 前置要求
- Node.js (v16+)
- PostgreSQL 数据库
- Redis 服务器

### 启动步骤

1. **安装依赖**：
   ```bash
   # 安装后端依赖
   cd javflix-api
   npm install
   
   # 安装前端依赖  
   cd ../javflix
   npm install
   ```

2. **配置环境变量**：
   ```bash
   # 在javflix-api目录下创建.env文件
   cp .env.example .env
   # 配置数据库和Redis连接信息
   ```

3. **编译TypeScript代码**：
   ```bash
   cd javflix-api
   npm run build
   ```

4. **启动服务**：
   ```bash
   # 启动后端API服务器（包含Socket.IO、Redis、视频统计功能）
   cd javflix-api
   npm start  # 在端口4000运行新的TypeScript服务器
   
   # 启动前端应用
   cd ../javflix
   npm run dev  # 在端口3001运行
   ```

5. **可选：启动JavBus API**（用于数据导入）：
   ```bash
   cd javbus-api
   npm run dev  # 在端口3000运行
   ```

### 开发模式启动（推荐）

```bash
# 后端开发模式（自动重编译和重启）
cd javflix-api
npm run dev:ts  # 如果有此脚本，否则使用 npm run build && npm start

# 前端开发模式
cd javflix  
npm run dev
```

## 未来API规划

以下是计划在未来版本中添加的API端点和功能：

### 个人收藏与历史增强API

| 路径 | 方法 | 描述 | 参数 | 状态 |
|------|------|------|------|------|
| `/users/favorites/categories` | GET | 获取收藏分类列表 | - | ✅ 已实现 |
| `/users/favorites/categories` | POST | 创建收藏分类 | `name`, `description` | ✅ 已实现 |
| `/users/favorites/categories/:id` | PUT | 修改收藏分类 | `name`, `description` | ✅ 已实现 |
| `/users/favorites/categories/:id` | DELETE | 删除收藏分类 | - | ✅ 已实现 |
| `/users/favorites/categories/:id/items` | GET | 获取分类内收藏 | `page`, `limit` | ✅ 已实现 |
| `/users/favorites/:videoId/note` | PUT | 为收藏添加备注 | `note` | ✅ 已实现 |
| `/users/favorites/sort` | PUT | 重新排序收藏 | `items`, `order` | ✅ 已实现 |
| `/users/favorites/stats` | GET | 收藏统计信息 | - | ✅ 已实现 |
| `/users/watch-history` | GET | 获取用户观看历史 | `page`, `limit` | ✅ 已实现 |
| `/users/watch-history` | POST | 添加观看历史记录 | `videoId`, `progress` | ✅ 已实现 |
| `/users/watch-history/:videoId` | DELETE | 删除观看历史记录 | - | ✅ 已实现 |
| `/users/watch-history/clear` | POST | 清空观看历史 | - | ✅ 已实现 |
| `/users/watch-later` | GET | 获取稍后观看列表 | `page`, `limit` | ✅ 已实现 |
| `/users/watch-later` | POST | 添加到稍后观看 | `videoId` | ✅ 已实现 |
| `/users/watch-later/:videoId` | DELETE | 从稍后观看中移除 | - | ✅ 已实现 |
| `/users/favorites/categories/:id/items` | POST | 将视频添加到收藏分类 | `videoId` | ✅ 已实现 |
| `/users/favorites/categories/:id/items/:videoId` | DELETE | 从收藏分类中移除视频 | - | ✅ 已实现 |
| `/users/favorites/batch` | POST | 批量操作收藏 | `action`, `videoIds`, `categoryId?` | ✅ 已实现 |
| `/users/favorites/export` | GET | 导出收藏数据 | `format` | ✅ 已实现 |
| `/users/favorites/import` | POST | 导入收藏数据 | 收藏数据 | ✅ 已实现 |
| `/users/watch-history/analytics` | GET | 获取观看历史分析 | `period` | ✅ 已实现 |
| `/users/sync` | POST | 跨设备同步用户数据 | `data`, `device` | ✅ 已实现 |
| `/users/watch-status` | GET | 获取视频观看状态 | `videoIds` | ✅ 已实现 |

### 用户偏好与个性化API

| 路径 | 方法 | 描述 | 参数 | 状态 |
|------|------|------|------|------|
| `/users/preferences` | GET | 获取用户偏好设置 | - | ✅ 已实现 |
| `/users/preferences` | PUT | 更新用户偏好设置 | 偏好数据 | ✅ 已实现 |
| `/users/following` | GET | 获取用户关注的演员列表 | `page`, `limit` | ✅ 已实现 |
| `/users/following/:starId` | POST | 关注演员 | - | ✅ 已实现 |
| `/users/following/:starId` | DELETE | 取消关注演员 | - | ✅ 已实现 |
| `/users/blocked` | GET | 获取屏蔽内容列表 | `type`, `page`, `limit` | ✅ 已实现 |
| `/users/blocked` | POST | 添加屏蔽内容 | `type`, `id` | ✅ 已实现 |
| `/users/blocked/:id` | DELETE | 移除屏蔽内容 | - | ✅ 已实现 |
| `/users/recommendations` | GET | 获取个性化推荐 | `type`, `limit` | ✅ 已实现 |
| `/users/recommendations/feedback` | POST | 提交推荐反馈 | `itemId`, `action` | ✅ 已实现 |

### 高级搜索API

| 路径 | 方法 | 描述 | 参数 | 状态 |
|------|------|------|------|------|
| `/search/advanced` | GET | 高级搜索功能 | 多种筛选条件 | 🚧 计划中 |
| `/search/suggest` | GET | 搜索建议 | `keyword`, `limit`

### 数据库API使用示例 📝

#### 获取女优/演员数据

```javascript
// 前端调用演员列表（推荐使用）
async function fetchStarsFromDatabase() {
  try {
    const response = await fetch('/api/db/stars?page=1&limit=20&sort=name&order=asc');
    const result = await response.json();
    console.log('演员列表:', result.data.items);
    return result.data.items;
  } catch (error) {
    console.error('获取演员列表失败', error);
    return [];
  }
}

// 搜索特定演员
async function searchStars(keyword) {
  try {
    const response = await fetch(`/api/db/stars/search?keyword=${encodeURIComponent(keyword)}&page=1&limit=10`);
    const result = await response.json();
    console.log('搜索结果:', result.data.items);
    return result.data.items;
  } catch (error) {
    console.error('搜索演员失败', error);
    return [];
  }
}

// 获取热门演员
async function getPopularStars() {
  try {
    const response = await fetch('/api/db/stars/popular?limit=10');
    const result = await response.json();
    console.log('热门演员:', result.data.items);
    return result.data.items;
  } catch (error) {
    console.error('获取热门演员失败', error);
    return [];
  }
}
```

#### 获取分类/标签数据

```javascript
// 获取所有分类标签
async function fetchGenresFromDatabase() {
  try {
    const response = await fetch('/api/db/genres?limit=100&sort=name');
    const result = await response.json();
    console.log('分类列表:', result.data.items);
    return result.data.items;
  } catch (error) {
    console.error('获取分类失败', error);
    return [];
  }
}

// 获取热门分类
async function getPopularGenres() {
  try {
    const response = await fetch('/api/db/genres/popular?limit=20');
    const result = await response.json();
    console.log('热门分类:', result.data.items);
    return result.data.items;
  } catch (error) {
    console.error('获取热门分类失败', error);
    return [];
  }
}

// 获取分类详情及示例影片
async function getGenreDetail(genreId) {
  try {
    const response = await fetch(`/api/db/genres/${genreId}`);
    const result = await response.json();
    console.log('分类详情:', result.data);
    console.log('示例影片:', result.data.sample_movies);
    return result.data;
  } catch (error) {
    console.error('获取分类详情失败', error);
    return null;
  }
}
```

#### 综合使用示例：构建演员和分类筛选器

```javascript
// 构建完整的筛选器组件
async function buildFilterComponents() {
  try {
    // 并行获取演员和分类数据
    const [stars, genres] = await Promise.all([
      fetch('/api/db/stars/popular?limit=50').then(r => r.json()),
      fetch('/api/db/genres/popular?limit=30').then(r => r.json())
    ]);

    const filterData = {
      popularStars: stars.data.items,
      popularGenres: genres.data.items
    };

    console.log('筛选器数据准备完成:', filterData);
    return filterData;
  } catch (error) {
    console.error('构建筛选器失败', error);
    return { popularStars: [], popularGenres: [] };
  }
}

// 根据演员和分类获取相关影片
async function getMoviesByFilters(starId, genreId) {
  try {
    let url = '/api/db/movies?page=1&limit=20';
    
    if (starId) {
      url = `/api/db/movies/star/${starId}?page=1&limit=20`;
    } else if (genreId) {
      url = `/api/db/movies/genre/${genreId}?page=1&limit=20`;
    }
    
    const response = await fetch(url);
    const result = await response.json();
    return result.data.items;
  } catch (error) {
    console.error('获取筛选影片失败', error);
    return [];
  }
}
```

### 数据库模型结构