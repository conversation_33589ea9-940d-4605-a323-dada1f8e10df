{"timestamp": "2025-05-25T16:36:50.511Z", "summary": {"totalPages": 16, "overallScore": 80, "avgResponseTime": 251, "criticalIssues": 1}, "results": [{"name": "🏠 首页", "url": "/", "category": "core", "priority": "high", "statusCode": 307, "responseTime": 9, "ttfb": 9, "responseSize": 6, "downloadSpeed": 636, "score": 60, "timestamp": "2025-05-25T16:36:43.894Z"}, {"name": "🔍 搜索页", "url": "/zh-CN/search?q=TOTTE-218", "category": "core", "priority": "high", "statusCode": 200, "responseTime": 629, "ttfb": 625, "responseSize": 14624, "downloadSpeed": 23243, "score": 85, "timestamp": "2025-05-25T16:36:44.517Z"}, {"name": "📂 分类页", "url": "/zh-CN/categories", "category": "core", "priority": "medium", "statusCode": 404, "responseTime": 628, "ttfb": 562, "responseSize": 6351, "downloadSpeed": 10112, "score": 55, "timestamp": "2025-05-25T16:36:44.517Z"}, {"name": "🎬 播放页 - 热门视频", "url": "/zh-CN/video/SSIS-016", "category": "video", "priority": "high", "statusCode": 200, "responseTime": 114, "ttfb": 109, "responseSize": 14420, "downloadSpeed": 126230, "score": 100, "timestamp": "2025-05-25T16:36:45.634Z"}, {"name": "🎬 播放页 - 搜索结果", "url": "/zh-CN/video/TOTTE-218", "category": "video", "priority": "high", "statusCode": 200, "responseTime": 114, "ttfb": 108, "responseSize": 14422, "downloadSpeed": 127032, "score": 100, "timestamp": "2025-05-25T16:36:45.634Z"}, {"name": "🎬 视频详情页", "url": "/zh-CN/movie/test-movie-id", "category": "video", "priority": "medium", "statusCode": 404, "responseTime": 51, "ttfb": 45, "responseSize": 6359, "downloadSpeed": 125362, "score": 70, "timestamp": "2025-05-25T16:36:45.571Z"}, {"name": "📡 API-首页数据", "url": "/api/popular-videos?locale=zh-CN&limit=8", "category": "api", "priority": "high", "statusCode": 200, "responseTime": 276, "ttfb": 276, "responseSize": 6761, "downloadSpeed": 24487, "score": 95, "timestamp": "2025-05-25T16:36:46.912Z"}, {"name": "📡 API-搜索数据", "url": "/api/search?q=test&locale=zh-CN", "category": "api", "priority": "high", "statusCode": 200, "responseTime": 275, "ttfb": 275, "responseSize": 198, "downloadSpeed": 720, "score": 95, "timestamp": "2025-05-25T16:36:46.911Z"}, {"name": "📡 API-分类数据", "url": "/api/categories", "category": "api", "priority": "medium", "statusCode": 200, "responseTime": 251, "ttfb": 251, "responseSize": 2209, "downloadSpeed": 8785, "score": 95, "timestamp": "2025-05-25T16:36:46.888Z"}, {"name": "📡 API-视频详情", "url": "/api/video/details?id=test", "category": "api", "priority": "medium", "statusCode": 200, "responseTime": 444, "ttfb": 444, "responseSize": 683, "downloadSpeed": 1538, "score": 85, "timestamp": "2025-05-25T16:36:48.357Z"}, {"name": "👤 用户中心", "url": "/zh-CN/profile", "category": "user", "priority": "low", "statusCode": 200, "responseTime": 494, "ttfb": 490, "responseSize": 14511, "downloadSpeed": 29385, "score": 95, "timestamp": "2025-05-25T16:36:48.407Z"}, {"name": "⭐ 收藏页面", "url": "/zh-CN/favorites", "category": "user", "priority": "low", "statusCode": 404, "responseTime": 492, "ttfb": 437, "responseSize": 6356, "downloadSpeed": 12916, "score": 65, "timestamp": "2025-05-25T16:36:48.406Z"}, {"name": "📜 历史记录", "url": "/zh-CN/history", "category": "user", "priority": "low", "statusCode": 404, "responseTime": 71, "ttfb": 69, "responseSize": 6351, "downloadSpeed": 89418, "score": 70, "timestamp": "2025-05-25T16:36:49.480Z"}, {"name": "🖼️ 静态图片", "url": "/images/logo.png", "category": "static", "priority": "medium", "statusCode": 404, "responseTime": 71, "ttfb": 51, "responseSize": 6351, "downloadSpeed": 89989, "score": 70, "timestamp": "2025-05-25T16:36:49.480Z"}, {"name": "📄 样式文件", "url": "/_next/static/css/app.css", "category": "static", "priority": "medium", "statusCode": 404, "responseTime": 70, "ttfb": 51, "responseSize": 6355, "downloadSpeed": 90397, "score": 70, "timestamp": "2025-05-25T16:36:49.480Z"}, {"name": "⚡ JS文件", "url": "/_next/static/chunks/main.js", "category": "static", "priority": "medium", "statusCode": 404, "responseTime": 28, "ttfb": 26, "responseSize": 6357, "downloadSpeed": 226683, "score": 70, "timestamp": "2025-05-25T16:36:50.510Z"}], "categories": {"core": {"name": "core", "count": 3, "totalScore": 200, "avgResponseTime": 422, "avgTtfb": 399, "totalResponseTime": 1266, "totalTtfb": 1196, "highPriorityIssues": 1, "issues": [{"name": "🏠 首页", "score": 60, "responseTime": 9, "issue": "性能需要优化"}], "avgScore": 67}, "video": {"name": "video", "count": 3, "totalScore": 270, "avgResponseTime": 93, "avgTtfb": 87, "totalResponseTime": 279, "totalTtfb": 262, "highPriorityIssues": 0, "issues": [], "avgScore": 90}, "api": {"name": "api", "count": 4, "totalScore": 370, "avgResponseTime": 312, "avgTtfb": 312, "totalResponseTime": 1246, "totalTtfb": 1246, "highPriorityIssues": 0, "issues": [], "avgScore": 93}, "user": {"name": "user", "count": 3, "totalScore": 230, "avgResponseTime": 352, "avgTtfb": 332, "totalResponseTime": 1057, "totalTtfb": 996, "highPriorityIssues": 0, "issues": [], "avgScore": 77}, "static": {"name": "static", "count": 3, "totalScore": 210, "avgResponseTime": 56, "avgTtfb": 43, "totalResponseTime": 169, "totalTtfb": 128, "highPriorityIssues": 0, "issues": [], "avgScore": 70}}, "suggestions": [{"priority": "🔥 紧急", "category": "核心页面优化", "description": "发现 1 个高优先级性能问题", "issues": [{"page": "🏠 首页", "problem": "响应时间 9ms，评分 60/100", "solution": "需要代码和查询优化"}]}, {"priority": "⚡ 高优先级", "category": "API性能优化", "description": "1 个API端点需要优化", "solutions": ["实施Redis缓存策略", "优化数据库查询和索引", "启用API响应压缩", "实施分页和数据限制"]}]}