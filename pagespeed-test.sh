#!/bin/bash

# JAVFLIX.TV PageSpeed Insights 测试脚本
# 使用Google PageSpeed Insights API进行性能测试

echo "🚀 JAVFLIX.TV PageSpeed 性能测试"
echo "=================================="

# 配置
BASE_URL="http://localhost:3001"
API_KEY="your_api_key_here" # 可选，用于更高的API限制
REPORT_DIR="./pagespeed-reports"

# 创建报告目录
mkdir -p "$REPORT_DIR"

# 测试页面列表
declare -a pages=(
    "首页:/"
    "搜索页:/zh-CN/search?q=test"
    "分类页:/zh-CN/categories"
)

# PageSpeed API测试函数
test_pagespeed() {
    local page_name="$1"
    local page_url="$2"
    local full_url="${BASE_URL}${page_url}"
    
    echo ""
    echo "📊 测试 $page_name..."
    echo "URL: $full_url"
    
    # 构建API请求URL
    local api_url="https://www.googleapis.com/pagespeedonline/v5/runPagespeed"
    api_url="${api_url}?url=${full_url}"
    api_url="${api_url}&strategy=desktop"
    api_url="${api_url}&category=performance"
    
    if [ ! -z "$API_KEY" ] && [ "$API_KEY" != "your_api_key_here" ]; then
        api_url="${api_url}&key=${API_KEY}"
    fi
    
    # 发送请求并保存结果
    local timestamp=$(date +"%Y%m%d-%H%M%S")
    local report_file="${REPORT_DIR}/pagespeed-${page_name}-${timestamp}.json"
    
    echo "⏳ 正在分析性能..."
    
    if curl -s "$api_url" -o "$report_file"; then
        # 解析结果
        local performance_score=$(cat "$report_file" | python3 -c "
import sys, json
try:
    data = json.load(sys.stdin)
    score = data['lighthouseResult']['categories']['performance']['score']
    print(int(score * 100) if score else 0)
except:
    print(0)
" 2>/dev/null)
        
        local lcp=$(cat "$report_file" | python3 -c "
import sys, json
try:
    data = json.load(sys.stdin)
    lcp = data['lighthouseResult']['audits']['largest-contentful-paint']['displayValue']
    print(lcp)
except:
    print('N/A')
" 2>/dev/null)
        
        local fid=$(cat "$report_file" | python3 -c "
import sys, json
try:
    data = json.load(sys.stdin)
    fid = data['lighthouseResult']['audits']['max-potential-fid']['displayValue']
    print(fid)
except:
    print('N/A')
" 2>/dev/null)
        
        local cls=$(cat "$report_file" | python3 -c "
import sys, json
try:
    data = json.load(sys.stdin)
    cls = data['lighthouseResult']['audits']['cumulative-layout-shift']['displayValue']
    print(cls)
except:
    print('N/A')
" 2>/dev/null)
        
        echo "✅ $page_name 测试完成"
        echo "   性能评分: $performance_score/100"
        echo "   LCP: $lcp"
        echo "   FID: $fid"
        echo "   CLS: $cls"
        echo "   报告保存至: $report_file"
        
        # 返回评分用于汇总
        echo $performance_score
    else
        echo "❌ $page_name 测试失败"
        echo "0"
    fi
}

# 检查服务器是否运行
echo "🔍 检查服务器状态..."
if curl -s --head "$BASE_URL" | head -n 1 | grep -q "200 OK"; then
    echo "✅ 服务器运行正常"
else
    echo "❌ 服务器未运行或无法访问"
    echo "请确保 npm run dev 已启动"
    exit 1
fi

# 运行所有测试
total_score=0
test_count=0

for page in "${pages[@]}"; do
    IFS=':' read -ra ADDR <<< "$page"
    page_name="${ADDR[0]}"
    page_url="${ADDR[1]}"
    
    score=$(test_pagespeed "$page_name" "$page_url")
    total_score=$((total_score + score))
    test_count=$((test_count + 1))
done

# 生成总结报告
echo ""
echo "=================================================="
echo "🎯 JAVFLIX.TV 性能测试总结"
echo "=================================================="

if [ $test_count -gt 0 ]; then
    avg_score=$((total_score / test_count))
    echo "📊 平均性能评分: $avg_score/100"
    
    if [ $avg_score -ge 90 ]; then
        echo "🏆 性能等级: A (优秀)"
        echo "💡 建议: 保持当前优化水平"
    elif [ $avg_score -ge 80 ]; then
        echo "🥈 性能等级: B (良好)"
        echo "💡 建议: 可进一步优化图片和缓存"
    elif [ $avg_score -ge 70 ]; then
        echo "🥉 性能等级: C (一般)"
        echo "💡 建议: 需要优化加载速度和资源大小"
    elif [ $avg_score -ge 60 ]; then
        echo "⚠️ 性能等级: D (较差)"
        echo "💡 建议: 急需实施静态化和缓存策略"
    else
        echo "🚨 性能等级: F (差)"
        echo "💡 建议: 立即实施性能优化方案"
    fi
fi

echo ""
echo "📁 所有详细报告保存在: $REPORT_DIR/"
echo "🌐 在线查看: https://pagespeed.web.dev/"
echo ""
echo "🚀 下一步行动:"
echo "1. 查看详细报告分析具体问题"
echo "2. 实施静态页面生成(SSG)"
echo "3. 启用多层缓存策略"
echo "4. 优化图片和资源加载"
echo ""

echo "✅ 性能测试完成！"