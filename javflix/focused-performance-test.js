#!/usr/bin/env node

/**
 * 🎯 JAVFLIX.TV 专项性能测试工具
 * 基于 Medium 和 freeCodeCamp 的 Next.js 优化最佳实践
 * 专注测试非分类页面的性能优化
 */

const fs = require('fs').promises;
const path = require('path');

// 🎯 测试配置
const BASE_URL = 'http://localhost:3000';
const OUTPUT_DIR = './performance-reports';

// 🔧 测试页面配置 - 排除分类页面
const FOCUS_TEST_PAGES = [
  // 🏠 核心页面
  { 
    name: '首页', 
    url: `${BASE_URL}/`, 
    category: 'core',
    expectedScore: 80,
    critical: true
  },
  
  // 🎬 视频页面 - 重点优化
  { 
    name: '播放页-示例1', 
    url: `${BASE_URL}/video/sample1`, 
    category: 'video',
    expectedScore: 80,
    critical: true
  },
  { 
    name: '播放页-示例2', 
    url: `${BASE_URL}/video/sample2`, 
    category: 'video',
    expectedScore: 80,
    critical: false
  },
  
  // 🔍 搜索功能
  { 
    name: '搜索页面', 
    url: `${BASE_URL}/search?q=test`, 
    category: 'search',
    expectedScore: 75,
    critical: true
  },
  
  // 🔗 API端点 - 重点优化
  { 
    name: 'API-首页数据', 
    url: `${BASE_URL}/api/popular-videos?limit=20`, 
    category: 'api',
    expectedScore: 85,
    critical: true,
    isAPI: true
  },
  { 
    name: 'API-搜索数据', 
    url: `${BASE_URL}/api/search?q=高清&limit=20`, 
    category: 'api',
    expectedScore: 85,
    critical: true,
    isAPI: true
  },
  { 
    name: 'API-视频详情', 
    url: `${BASE_URL}/api/video/details?id=SSIS-001`, 
    category: 'api',
    expectedScore: 85,
    critical: true,
    isAPI: true
  },
  { 
    name: 'API-推荐视频', 
    url: `${BASE_URL}/api/recommendations?id=SSIS-001&limit=12`, 
    category: 'api',
    expectedScore: 80,
    critical: false,
    isAPI: true
  },
  
  // 👤 用户页面
  { 
    name: '用户中心', 
    url: `${BASE_URL}/zh-CN/user/profile`, 
    category: 'user',
    expectedScore: 75,
    critical: false
  },
  { 
    name: '收藏页面', 
    url: `${BASE_URL}/zh-CN/user/favorites`, 
    category: 'user',
    expectedScore: 75,
    critical: false
  },
  { 
    name: '观看历史', 
    url: `${BASE_URL}/zh-CN/user/history`, 
    category: 'user',
    expectedScore: 75,
    critical: false
  },
  
  // 📄 静态页面
  { 
    name: '关于页面', 
    url: `${BASE_URL}/zh-CN/about`, 
    category: 'static',
    expectedScore: 90,
    critical: false
  },
  { 
    name: '隐私政策', 
    url: `${BASE_URL}/zh-CN/privacy`, 
    category: 'static',
    expectedScore: 90,
    critical: false
  },
  { 
    name: '使用条款', 
    url: `${BASE_URL}/zh-CN/terms`, 
    category: 'static',
    expectedScore: 90,
    critical: false
  },
  
  // 🏷️ 标签和分类详情页
  { 
    name: '标签页-高清', 
    url: `${BASE_URL}/zh-CN/tag/hd`, 
    category: 'tag',
    expectedScore: 82,
    critical: false
  },
  { 
    name: '标签页-巨乳', 
    url: `${BASE_URL}/zh-CN/tag/big-breasts`, 
    category: 'tag',
    expectedScore: 82,
    critical: false
  },
  
  // 📊 数据页面
  { 
    name: '排行榜', 
    url: `${BASE_URL}/zh-CN/ranking`, 
    category: 'data',
    expectedScore: 78,
    critical: false
  },
  { 
    name: '最新更新', 
    url: `${BASE_URL}/zh-CN/latest`, 
    category: 'data',
    expectedScore: 78,
    critical: false
  }
];

// 📊 性能统计
let requestCount = 0;
let totalResponseTime = 0;

// 🚀 API性能测试函数
async function testAPIPerformance(url, name) {
  const startTime = Date.now();
  requestCount++;
  
  try {
    // 🎯 发送请求并记录详细信息
    const response = await fetch(url, {
      headers: {
        'User-Agent': 'JAVFLIX-Performance-Test/1.0',
        'Accept': 'application/json',
        'Cache-Control': 'no-cache'
      }
    });
    
    const responseTime = Date.now() - startTime;
    totalResponseTime += responseTime;
    
    let data = null;
    let contentLength = 0;
    
    try {
      const text = await response.text();
      contentLength = text.length;
      data = JSON.parse(text);
    } catch (e) {
      // 非JSON响应
    }
    
    // 🎯 API性能评分算法 - 基于 freeCodeCamp 最佳实践
    let score = 100;
    
    // 响应时间评分 (权重50%)
    if (responseTime > 1000) score -= 40;
    else if (responseTime > 500) score -= 30;
    else if (responseTime > 300) score -= 20;
    else if (responseTime > 200) score -= 10;
    else if (responseTime > 100) score -= 5;
    
    // 状态码评分 (权重25%)
    if (response.status !== 200) score -= 30;
    
    // 数据质量评分 (权重15%)
    if (data && !data.success) score -= 15;
    if (!data || !data.data) score -= 10;
    
    // 缓存和性能头评分 (权重10%)
    const cacheControl = response.headers.get('cache-control');
    const responseTimeHeader = response.headers.get('x-response-time');
    if (!cacheControl || !cacheControl.includes('max-age')) score -= 8;
    if (!responseTimeHeader) score -= 5;
    
    return {
      name,
      url,
      score: Math.max(0, Math.round(score)),
      responseTime,
      status: response.status,
      contentLength,
      cacheControl: cacheControl || 'none',
      serverTime: responseTimeHeader || 'not provided',
      dataValid: data ? data.success || false : false,
      success: response.ok,
      timestamp: new Date().toISOString(),
      
      // 📊 性能等级
      performanceGrade: score >= 90 ? 'A+' : score >= 85 ? 'A' : score >= 80 ? 'B+' : score >= 70 ? 'B' : score >= 60 ? 'C' : 'D'
    };
    
  } catch (error) {
    return {
      name,
      url,
      score: 0,
      responseTime: Date.now() - startTime,
      error: error.message,
      success: false,
      performanceGrade: 'F',
      timestamp: new Date().toISOString()
    };
  }
}

// 🚀 页面响应时间测试
async function testPageResponseTime(url, name) {
  const startTime = Date.now();
  
  try {
    const response = await fetch(url, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.109 Safari/537.36'
      }
    });
    
    const responseTime = Date.now() - startTime;
    const contentLength = response.headers.get('content-length') || 'unknown';
    
    // 🎯 页面性能评分 - 基于 Medium 文章建议
    let score = 100;
    
    // 响应时间评分
    if (responseTime > 3000) score -= 50;
    else if (responseTime > 2000) score -= 40;
    else if (responseTime > 1000) score -= 30;
    else if (responseTime > 500) score -= 15;
    else if (responseTime > 300) score -= 8;
    
    // 状态码评分
    if (response.status !== 200) score -= 40;
    
    // 内容大小评分
    const size = parseInt(contentLength) || 0;
    if (size > 1000000) score -= 20; // 1MB+
    else if (size > 500000) score -= 15; // 500KB+
    else if (size > 200000) score -= 10; // 200KB+
    
    return {
      name,
      url,
      score: Math.max(0, Math.round(score)),
      responseTime,
      status: response.status,
      contentLength,
      contentType: response.headers.get('content-type') || 'unknown',
      success: response.ok,
      performanceGrade: score >= 90 ? 'A+' : score >= 85 ? 'A' : score >= 80 ? 'B+' : score >= 70 ? 'B' : score >= 60 ? 'C' : 'D',
      timestamp: new Date().toISOString()
    };
    
  } catch (error) {
    return {
      name,
      url,
      score: 0,
      responseTime: Date.now() - startTime,
      error: error.message,
      success: false,
      performanceGrade: 'F',
      timestamp: new Date().toISOString()
    };
  }
}

// 📊 生成HTML报告
async function generateHTMLReport(results) {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const reportPath = path.join(OUTPUT_DIR, `focused-performance-report-${timestamp}.html`);
  
  // 📈 计算统计数据
  const stats = {
    total: results.length,
    passed: results.filter(r => r.score >= (FOCUS_TEST_PAGES.find(p => p.name === r.name)?.expectedScore || 70)).length,
    failed: 0,
    avgScore: Math.round(results.reduce((sum, r) => sum + (r.score || 0), 0) / results.length),
    avgResponseTime: Math.round(totalResponseTime / requestCount),
    criticalIssues: results.filter(r => {
      const page = FOCUS_TEST_PAGES.find(p => p.name === r.name);
      return page?.critical && r.score < (page.expectedScore || 70);
    }).length,
    
    // 📊 性能等级分布
    gradeDistribution: {
      'A+': results.filter(r => r.performanceGrade === 'A+').length,
      'A': results.filter(r => r.performanceGrade === 'A').length,
      'B+': results.filter(r => r.performanceGrade === 'B+').length,
      'B': results.filter(r => r.performanceGrade === 'B').length,
      'C': results.filter(r => r.performanceGrade === 'C').length,
      'D': results.filter(r => r.performanceGrade === 'D').length,
      'F': results.filter(r => r.performanceGrade === 'F').length
    }
  };
  stats.failed = stats.total - stats.passed;
  
  // 📊 按分类分组
  const categories = {};
  results.forEach(result => {
    const page = FOCUS_TEST_PAGES.find(p => p.name === result.name);
    const category = page?.category || 'other';
    if (!categories[category]) categories[category] = [];
    categories[category].push(result);
  });

  const html = `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JAVFLIX.TV 专项性能测试报告</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        .container { 
            max-width: 1400px; 
            margin: 0 auto; 
            background: white; 
            border-radius: 15px; 
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header { 
            background: linear-gradient(135deg, #ff6b6b, #ee5a52);
            color: white; 
            padding: 30px; 
            text-align: center;
        }
        .header h1 { font-size: 2.5em; margin-bottom: 10px; }
        .stats { 
            display: grid; 
            grid-template-columns: repeat(auto-fit, minmax(180px, 1fr)); 
            gap: 20px; 
            padding: 30px;
            background: #f8f9fa;
        }
        .stat-card { 
            background: white; 
            padding: 20px; 
            border-radius: 10px; 
            text-align: center; 
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .stat-number { font-size: 2em; font-weight: bold; margin-bottom: 5px; }
        .stat-label { color: #666; font-size: 0.9em; }
        .grade-excellent { color: #28a745; }
        .grade-good { color: #17a2b8; }
        .grade-warning { color: #ffc107; }
        .grade-poor { color: #dc3545; }
        .results { padding: 20px; }
        .category-section { margin-bottom: 40px; }
        .category-title { 
            font-size: 1.5em; 
            color: #333; 
            margin-bottom: 20px; 
            padding-bottom: 10px; 
            border-bottom: 3px solid #ff6b6b;
        }
        .result-grid { 
            display: grid; 
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr)); 
            gap: 20px; 
        }
        .result-card { 
            background: white; 
            border-radius: 10px; 
            padding: 20px; 
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            border-left: 5px solid #ddd;
        }
        .result-card.excellent { border-left-color: #28a745; }
        .result-card.good { border-left-color: #17a2b8; }
        .result-card.warning { border-left-color: #ffc107; }
        .result-card.poor { border-left-color: #dc3545; }
        .result-title { font-weight: bold; margin-bottom: 10px; font-size: 1.1em; }
        .result-score { font-size: 2em; font-weight: bold; margin: 10px 0; }
        .result-details { font-size: 0.9em; color: #666; }
        .result-metrics { 
            display: grid; 
            grid-template-columns: 1fr 1fr; 
            gap: 10px; 
            margin-top: 15px; 
        }
        .metric { 
            background: #f8f9fa; 
            padding: 8px; 
            border-radius: 5px; 
            text-align: center; 
        }
        .metric-label { font-size: 0.8em; color: #666; }
        .metric-value { font-weight: bold; color: #333; }
        .footer { 
            text-align: center; 
            padding: 20px; 
            color: #666; 
            background: #f8f9fa; 
        }
        .critical-badge { 
            background: #dc3545; 
            color: white; 
            padding: 2px 8px; 
            border-radius: 10px; 
            font-size: 0.8em; 
            margin-left: 10px; 
        }
        .api-badge { 
            background: #6f42c1; 
            color: white; 
            padding: 2px 8px; 
            border-radius: 10px; 
            font-size: 0.8em; 
            margin-left: 5px; 
        }
        .grade-badge {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 15px;
            font-weight: bold;
            font-size: 0.9em;
            margin-left: 10px;
        }
        .grade-A-plus { background: #28a745; color: white; }
        .grade-A { background: #20c997; color: white; }
        .grade-B-plus { background: #17a2b8; color: white; }
        .grade-B { background: #007bff; color: white; }
        .grade-C { background: #ffc107; color: black; }
        .grade-D { background: #fd7e14; color: white; }
        .grade-F { background: #dc3545; color: white; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 JAVFLIX.TV 专项性能测试报告</h1>
            <p>基于 Medium 和 freeCodeCamp 的 Next.js 优化最佳实践</p>
            <p>测试时间: ${new Date().toLocaleString('zh-CN')}</p>
            <p>专注测试：视频页面、搜索功能、API接口等核心功能</p>
        </div>
        
        <div class="stats">
            <div class="stat-card">
                <div class="stat-number ${stats.avgScore >= 85 ? 'grade-excellent' : stats.avgScore >= 70 ? 'grade-good' : stats.avgScore >= 60 ? 'grade-warning' : 'grade-poor'}">${stats.avgScore}</div>
                <div class="stat-label">平均分数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number grade-excellent">${stats.passed}</div>
                <div class="stat-label">通过测试</div>
            </div>
            <div class="stat-card">
                <div class="stat-number grade-poor">${stats.failed}</div>
                <div class="stat-label">测试失败</div>
            </div>
            <div class="stat-card">
                <div class="stat-number ${stats.criticalIssues === 0 ? 'grade-excellent' : 'grade-poor'}">${stats.criticalIssues}</div>
                <div class="stat-label">关键问题</div>
            </div>
            <div class="stat-card">
                <div class="stat-number grade-good">${stats.avgResponseTime}ms</div>
                <div class="stat-label">平均响应时间</div>
            </div>
            <div class="stat-card">
                <div class="stat-number grade-good">${stats.gradeDistribution['A+'] + stats.gradeDistribution['A']}</div>
                <div class="stat-label">A级页面数</div>
            </div>
        </div>
        
        <div class="results">
            ${Object.keys(categories).map(category => `
                <div class="category-section">
                    <h2 class="category-title">📊 ${getCategoryName(category)} (${categories[category].length}个测试)</h2>
                    <div class="result-grid">
                        ${categories[category].map(result => {
                          const page = FOCUS_TEST_PAGES.find(p => p.name === result.name);
                          const scoreClass = result.score >= 85 ? 'excellent' : result.score >= 70 ? 'good' : result.score >= 60 ? 'warning' : 'poor';
                          const scoreColor = result.score >= 85 ? 'grade-excellent' : result.score >= 70 ? 'grade-good' : result.score >= 60 ? 'grade-warning' : 'grade-poor';
                          const gradeClass = `grade-${result.performanceGrade?.replace('+', '-plus') || 'F'}`;
                          
                          return `
                            <div class="result-card ${scoreClass}">
                                <div class="result-title">
                                    ${result.name}
                                    ${page?.critical ? '<span class="critical-badge">关键</span>' : ''}
                                    ${page?.isAPI ? '<span class="api-badge">API</span>' : ''}
                                    <span class="grade-badge ${gradeClass}">${result.performanceGrade || 'F'}</span>
                                </div>
                                <div class="result-score ${scoreColor}">${result.score || 0}</div>
                                <div class="result-details">
                                    <strong>URL:</strong> ${result.url}<br>
                                    ${result.error ? `<strong style="color: #dc3545;">错误:</strong> ${result.error}` : ''}
                                </div>
                                <div class="result-metrics">
                                    <div class="metric">
                                        <div class="metric-label">响应时间</div>
                                        <div class="metric-value">${result.responseTime}ms</div>
                                    </div>
                                    ${result.status ? `
                                        <div class="metric">
                                            <div class="metric-label">状态码</div>
                                            <div class="metric-value">${result.status}</div>
                                        </div>
                                    ` : ''}
                                    ${result.contentLength ? `
                                        <div class="metric">
                                            <div class="metric-label">内容大小</div>
                                            <div class="metric-value">${result.contentLength}</div>
                                        </div>
                                    ` : ''}
                                    ${result.cacheControl && result.cacheControl !== 'none' ? `
                                        <div class="metric">
                                            <div class="metric-label">缓存策略</div>
                                            <div class="metric-value">已配置</div>
                                        </div>
                                    ` : ''}
                                </div>
                            </div>
                          `;
                        }).join('')}
                    </div>
                </div>
            `).join('')}
        </div>
        
        <div class="footer">
            <p>🎯 由 JAVFLIX.TV 性能优化团队生成 | 基于 Medium 和 freeCodeCamp 最佳实践</p>
            <p>💡 专注测试核心功能页面，排除分类页面的性能表现</p>
        </div>
    </div>
    
    <script>
        function getCategoryName(category) {
            const names = {
                'core': '核心页面',
                'video': '视频页面', 
                'search': '搜索功能',
                'api': 'API接口',
                'user': '用户页面',
                'static': '静态页面',
                'tag': '标签页面',
                'data': '数据页面',
                'other': '其他页面'
            };
            return names[category] || category;
        }
    </script>
</body>
</html>`;

  await fs.writeFile(reportPath, html);
  console.log(`📊 HTML报告已生成: ${reportPath}`);
  return reportPath;
}

// 🚀 主测试函数
async function runFocusedPerformanceTest() {
  console.log('🎯 启动JAVFLIX.TV专项性能测试...');
  console.log('🚀 专注测试：视频页面、搜索功能、API接口等核心功能\n');
  
  // 📁 确保输出目录存在
  try {
    await fs.mkdir(OUTPUT_DIR, { recursive: true });
  } catch (error) {
    // 目录已存在，忽略错误
  }
  
  const results = [];
  
  console.log('🌐 开始测试各个页面和API...\n');
  
  for (let i = 0; i < FOCUS_TEST_PAGES.length; i++) {
    const page = FOCUS_TEST_PAGES[i];
    const progress = `(${i + 1}/${FOCUS_TEST_PAGES.length})`;
    
    console.log(`🔍 ${progress} 测试: ${page.name}${page.critical ? ' [关键]' : ''}${page.isAPI ? ' [API]' : ''}`);
    
    let result;
    if (page.isAPI) {
      result = await testAPIPerformance(page.url, page.name);
    } else {
      result = await testPageResponseTime(page.url, page.name);
    }
    
    results.push(result);
    
    // 🎯 显示结果
    if (result.error) {
      console.log(`   ❌ 测试失败: ${result.error}`);
    } else {
      const emoji = result.score >= 85 ? '🟢' : result.score >= 70 ? '🟡' : '🔴';
      const expectedScore = page.expectedScore || 70;
      const status = result.score >= expectedScore ? '✅ 达标' : '❌ 未达标';
      
      console.log(`   ${emoji} 分数: ${result.score}/100 (${result.performanceGrade}) ${status} (期望≥${expectedScore})`);
      console.log(`   ⏱️  响应时间: ${result.responseTime}ms`);
      
      if (page.critical && result.score < expectedScore) {
        console.log(`   🚨 关键页面性能不达标!`);
      }
    }
    
    console.log('');
    
    // 🔄 避免过于频繁的请求
    await new Promise(resolve => setTimeout(resolve, 200));
  }
  
  // 📊 生成报告
  console.log('📊 生成测试报告...\n');
  
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const jsonReportPath = path.join(OUTPUT_DIR, `focused-performance-data-${timestamp}.json`);
  
  // 💾 保存JSON数据
  await fs.writeFile(jsonReportPath, JSON.stringify(results, null, 2));
  console.log(`💾 JSON数据已保存: ${jsonReportPath}`);
  
  // 🎨 生成HTML报告
  const htmlReportPath = await generateHTMLReport(results);
  
  // 📈 显示总结
  const totalScore = Math.round(results.reduce((sum, r) => sum + (r.score || 0), 0) / results.length);
  const passedTests = results.filter(r => {
    const page = FOCUS_TEST_PAGES.find(p => p.name === r.name);
    return r.score >= (page?.expectedScore || 70);
  }).length;
  const criticalIssues = results.filter(r => {
    const page = FOCUS_TEST_PAGES.find(p => p.name === r.name);
    return page?.critical && r.score < (page.expectedScore || 70);
  }).length;
  
  // 📊 性能等级统计
  const gradeStats = results.reduce((acc, r) => {
    acc[r.performanceGrade] = (acc[r.performanceGrade] || 0) + 1;
    return acc;
  }, {});
  
  console.log('\n' + '='.repeat(60));
  console.log('📊 专项测试总结');
  console.log('='.repeat(60));
  console.log(`🎯 平均分数: ${totalScore}/100`);
  console.log(`✅ 通过测试: ${passedTests}/${results.length}`);
  console.log(`🚨 关键问题: ${criticalIssues}个`);
  console.log(`⚡ 平均响应时间: ${Math.round(totalResponseTime / requestCount)}ms`);
  console.log('');
  console.log('📈 性能等级分布:');
  Object.entries(gradeStats).forEach(([grade, count]) => {
    console.log(`   ${grade}: ${count}个页面`);
  });
  console.log(`📋 详细报告: ${htmlReportPath}`);
  
  // 🎉 最终评估
  if (totalScore >= 85 && criticalIssues === 0) {
    console.log('\n🎉 优秀! 核心功能性能表现出色!');
  } else if (totalScore >= 75 && criticalIssues <= 1) {
    console.log('\n👍 良好! 大部分功能性能达标，有少量改进空间。');
  } else {
    console.log('\n⚠️  警告! 部分核心功能性能需要优化，请查看详细报告。');
  }
  
  console.log('\n💡 建议优先优化关键页面和API接口的性能。');
  
  return {
    averageScore: totalScore,
    passedTests,
    totalTests: results.length,
    criticalIssues,
    averageResponseTime: Math.round(totalResponseTime / requestCount),
    gradeStats,
    htmlReport: htmlReportPath,
    jsonReport: jsonReportPath
  };
}

// 🚀 启动测试
if (require.main === module) {
  runFocusedPerformanceTest().catch(error => {
    console.error('❌ 测试执行失败:', error);
    process.exit(1);
  });
}

module.exports = { runFocusedPerformanceTest };

function getCategoryName(category) {
  const names = {
    'core': '核心页面',
    'video': '视频页面', 
    'search': '搜索功能',
    'api': 'API接口',
    'user': '用户页面',
    'static': '静态页面',
    'tag': '标签页面',
    'data': '数据页面',
    'other': '其他页面'
  };
  return names[category] || category;
}