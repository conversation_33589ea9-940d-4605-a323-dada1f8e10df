# SSR 问题修复总结

## 问题描述
在服务端渲染(SSR)环境中，出现了 `window is not defined` 错误，导致应用无法正常运行。

## 修复的文件和问题

### 1. `src/hooks/useEnhancedAnimations.ts`
**问题**: 直接访问 `window.matchMedia` 和 `navigator` 对象
**修复**: 添加类型检查
```typescript
// 修复前
const prefersReducedMotion = useCallback(() => {
  return window.matchMedia('(prefers-reduced-motion: reduce)').matches;
}, []);

// 修复后
const prefersReducedMotion = useCallback(() => {
  if (typeof window === 'undefined') return false;
  return window.matchMedia('(prefers-reduced-motion: reduce)').matches;
}, []);

// 设备性能检测也添加了类型检查
const isLowPerformanceDevice = useCallback(() => {
  if (typeof window === 'undefined' || typeof navigator === 'undefined') return false;
  // ... 其余代码
}, []);
```

### 2. `src/components/LoginPromptModal.tsx`
**问题**: 直接访问 `document` 对象进行 DOM 操作
**修复**: 添加类型检查
```typescript
// 动画效果 useEffect
useEffect(() => {
  if (typeof document === 'undefined') return;
  // ... DOM 操作代码
}, [isOpen]);

// ESC键监听 useEffect
useEffect(() => {
  if (typeof document === 'undefined') return;
  // ... 事件监听代码
}, [isOpen, onClose]);
```

### 3. `src/components/VideoStatsDisplay.tsx`
**问题**: 在 useEffect 中直接访问 `localStorage`
**修复**: 添加类型检查
```typescript
React.useEffect(() => {
  if (typeof window === 'undefined') return;
  // ... localStorage 操作代码
}, [autoIncrementViews, videoId, actions]);
```

### 4. `src/components/LoginPromptModal.tsx` - 功能列表显示问题
**问题**: 使用 `Object.entries()` 遍历国际化对象时，文本被当作字符串处理，导致显示为单个字母
**修复**: 直接访问具体的国际化键值
```typescript
// 修复前 - 会显示单个字母
{Object.entries(t('auth.login.loginRequired.benefits')).map(([key, value]) => ...)}

// 修复后 - 直接访问具体键值
<div className="flex items-center text-gray-300 text-sm">
  <FiHeart className="w-4 h-4 text-red-400" />
  <span className="ml-3">{t('auth.login.loginRequired.benefits.like')}</span>
</div>
```

## SSR 最佳实践

### 1. 浏览器 API 检查
在使用任何浏览器特有的 API 之前，都要进行类型检查：
```typescript
if (typeof window !== 'undefined') {
  // 使用 window 相关 API
}

if (typeof document !== 'undefined') {
  // 使用 document 相关 API
}

if (typeof navigator !== 'undefined') {
  // 使用 navigator 相关 API
}
```

### 2. useEffect 中的 DOM 操作
所有 DOM 操作都应该在 useEffect 中进行，并添加类型检查：
```typescript
useEffect(() => {
  if (typeof document === 'undefined') return;
  
  // DOM 操作代码
  document.body.style.overflow = 'hidden';
  
  return () => {
    document.body.style.overflow = 'unset';
  };
}, []);
```

### 3. 本地存储访问
访问 localStorage 或 sessionStorage 时要检查环境：
```typescript
const getStorageItem = (key: string) => {
  if (typeof window === 'undefined') return null;
  return localStorage.getItem(key);
};
```

### 4. 事件监听器
添加事件监听器时要确保在正确的环境中：
```typescript
useEffect(() => {
  if (typeof document === 'undefined') return;
  
  const handleKeyDown = (e: KeyboardEvent) => {
    // 处理逻辑
  };
  
  document.addEventListener('keydown', handleKeyDown);
  
  return () => {
    document.removeEventListener('keydown', handleKeyDown);
  };
}, []);
```

## 测试验证
修复后，应用应该能够：
1. 在服务端正常渲染，不出现 `window is not defined` 错误
2. 在客户端正常运行所有交互功能
3. 登录提示模态框正确显示功能列表文本
4. 所有动画和交互效果正常工作

## 注意事项
- 这些修复确保了代码在 SSR 环境中的兼容性
- 不会影响客户端的功能和性能
- 遵循了 Next.js 的 SSR 最佳实践
- 提高了应用的稳定性和可靠性
