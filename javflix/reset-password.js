// 重置用户密码的工具
const { Pool } = require('pg');
const bcrypt = require('bcrypt');

// 配置
const SALT_ROUNDS = 10;
const USERNAME = 'aokuao1';
const NEW_PASSWORD = '123456'; // 简单易记的密码

async function resetPassword() {
  console.log(`开始重置用户 ${USERNAME} 的密码...`);
  
  // 创建数据库连接
  const pool = new Pool({
    host: process.env.PGHOST || 'localhost',
    port: parseInt(process.env.PGPORT || '5432'),
    database: process.env.PGDATABASE || 'javflix',
    user: process.env.PGUSER || 'longgedemacminim4',
    password: process.env.PGPASSWORD || '',
  });

  try {
    // 检查用户是否存在
    const userCheck = await pool.query(
      'SELECT * FROM users WHERE username = $1',
      [USERNAME]
    );
    
    if (userCheck.rows.length === 0) {
      console.error(`用户 ${USERNAME} 不存在！`);
      return;
    }
    
    const user = userCheck.rows[0];
    console.log(`找到用户: ${user.username} (ID: ${user.id})`);
    
    // 生成新密码的哈希
    const hashedPassword = await bcrypt.hash(NEW_PASSWORD, SALT_ROUNDS);
    console.log('新密码哈希已生成');
    
    // 更新用户密码
    await pool.query(
      'UPDATE users SET password = $1, updated_at = NOW() WHERE id = $2',
      [hashedPassword, user.id]
    );
    
    console.log(`密码已成功重置! 新密码: ${NEW_PASSWORD}`);
    console.log('您现在可以使用这个新密码登录了');
    
    // 验证新密码
    const updatedUser = await pool.query(
      'SELECT * FROM users WHERE id = $1',
      [user.id]
    );
    
    if (updatedUser.rows.length > 0) {
      const isMatch = await bcrypt.compare(NEW_PASSWORD, updatedUser.rows[0].password);
      console.log(`新密码验证: ${isMatch ? '成功' : '失败'}`);
    }
    
  } catch (error) {
    console.error('重置密码时出错:', error);
  } finally {
    await pool.end();
  }
}

// 执行密码重置
resetPassword().catch(console.error); 