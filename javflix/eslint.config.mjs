import path from 'path';
import { fileURLToPath } from 'url';
import { FlatCompat } from '@eslint/eslintrc';

const __dirname = path.dirname(fileURLToPath(import.meta.url));
const compat = new FlatCompat({
    baseDirectory: __dirname,
    recommendedConfig: {},
});

export default [
    ...compat.extends('next/core-web-vitals'),
    {
        ignores: [
            "**/node_modules/",
            ".next/"
        ]
    },
    {
        files: ['src/app/search/advanced/page.tsx', 'src/app/video/[slug]/page.tsx'],
        rules: {
            '@typescript-eslint/no-unused-vars': 'off'
        }
    }
];
