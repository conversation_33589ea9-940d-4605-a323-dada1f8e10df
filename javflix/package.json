{"name": "javflix", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 3001", "build": "next build", "build:standalone": "next build && node scripts/post-build-standalone.js", "build:optimized": "node scripts/optimize-build.js", "start": "next start -p 3001", "start:production": "./scripts/start-production.sh", "start:standalone": "PORT=3001 node .next/standalone/start.js", "lint": "next lint", "performance:test": "node ../frontend-performance-test.js", "performance:build": "npm run build:optimized && npm run performance:test", "test-db": "tsx src/lib/db-test.ts", "seed-videos": "tsx scripts/seed-videos.ts", "db:seed": "tsx scripts/seed-videos.ts", "seed-search": "tsx scripts/seed-search-terms.ts", "migrate:users": "tsx src/lib/migrate-users.ts", "migrate:content": "tsx src/lib/migrate-content.ts", "migrate:user-relations": "tsx scripts/create-user-relations.ts", "seed:user-data": "tsx scripts/seed-user-data.ts", "setup:user-features": "npm run migrate:user-relations && npm run seed:user-data", "migrate:db": "node scripts/run-migrations.js", "check:schema": "tsx scripts/check-table-schema.ts", "verify-redirects": "node scripts/verify-redirects.js", "check-categories": "ts-node scripts/check-categories.ts", "dev:setup": "node scripts/deploy.js", "dev:reset": "rm -f .env.local && npm run dev:setup", "test:api": "node test-proxy-api.js", "test:config": "node -e \"console.log('🔧 当前环境:', process.env.NODE_ENV); require('./src/lib/api-config.ts')\"", "deploy:production": "node scripts/deploy.js --env production --mode single-domain", "deploy:docker": "node scripts/deploy.js --env production --mode docker --api-url http://backend:4000", "health:check": "curl -f http://localhost:3001/api/videos/popular?limit=1 || echo '❌ 健康检查失败'"}, "dependencies": {"@faker-js/faker": "^9.8.0", "@formatjs/intl-localematcher": "^0.6.1", "@neondatabase/serverless": "^1.0.0", "@tailwindcss/postcss": "^4.1.7", "@types/jsonwebtoken": "^9.0.9", "@types/pg": "^8.15.2", "bcrypt": "^6.0.0", "canvas": "^3.1.0", "dotenv": "^16.5.0", "drizzle-orm": "^0.43.1", "gsap": "^3.13.0", "ioredis": "^5.6.1", "jsonwebtoken": "^9.0.2", "negotiator": "^1.0.0", "next": "^15.3.2", "node-fetch": "^2.7.0", "pg": "^8.16.0", "plyr": "^3.7.8", "plyr-react": "^5.3.0", "postcss": "^8.5.3", "postgres": "^3.4.5", "react": "^19.1.0", "react-dom": "^19.1.0", "react-icons": "5.5.0", "react-intersection-observer": "^9.16.0", "slugify": "^1.6.6", "use-debounce": "^10.0.4"}, "devDependencies": {"@eslint/eslintrc": "3.3.1", "@types/negotiator": "^0.6.3", "@types/node": "22.13.9", "@types/node-fetch": "^2.6.12", "@types/react": "19.0.11", "@types/react-dom": "19.0.4", "autoprefixer": "^10.4.21", "axios": "^1.9.0", "chrome-launcher": "^1.2.0", "eslint": "9.22.0", "eslint-config-next": "15.2.3", "eslint-plugin-react": "^7.33.2", "lighthouse": "^12.6.0", "socket.io-client": "^4.8.1", "tailwindcss": "^4.1.7", "tsx": "^4.19.4", "typescript": "5.4.3"}}