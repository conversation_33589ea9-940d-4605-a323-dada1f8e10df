#!/usr/bin/env node

/**
 * 🚀 JAVFLIX.TV 增强性能测试工具
 * 基于 Pieces.app 和 Sematext 的性能监控最佳实践
 * 用于验证优化效果和持续监控
 */

const lighthouse = require('lighthouse');
const chromeLauncher = require('chrome-launcher');
const fs = require('fs').promises;
const path = require('path');

// 🎯 测试配置
const BASE_URL = 'http://localhost:3000';
const OUTPUT_DIR = './performance-reports';

// 🔧 增强的测试页面配置
const TEST_PAGES = [
  // 🏠 核心页面
  { 
    name: '首页-优化后', 
    url: `${BASE_URL}/zh-CN`, 
    category: 'core',
    expectedScore: 85,
    critical: true
  },
  { 
    name: '分类页-新优化', 
    url: `${BASE_URL}/zh-CN/category`, 
    category: 'core',
    expectedScore: 90,
    critical: true
  },
  
  // 🎬 视频页面
  { 
    name: '播放页-示例1', 
    url: `${BASE_URL}/zh-CN/video/SSIS-001`, 
    category: 'video',
    expectedScore: 85,
    critical: true
  },
  { 
    name: '播放页-示例2', 
    url: `${BASE_URL}/zh-CN/video/SSNI-999`, 
    category: 'video',
    expectedScore: 85,
    critical: false
  },
  
  // 🔍 搜索功能
  { 
    name: '搜索页面', 
    url: `${BASE_URL}/zh-CN/search?q=高清`, 
    category: 'search',
    expectedScore: 80,
    critical: true
  },
  
  // 📚 分类详情
  { 
    name: '分类详情-高清', 
    url: `${BASE_URL}/zh-CN/category/hd`, 
    category: 'category',
    expectedScore: 82,
    critical: false
  },
  { 
    name: '分类详情-巨乳', 
    url: `${BASE_URL}/zh-CN/category/big-breasts`, 
    category: 'category',
    expectedScore: 82,
    critical: false
  },
  
  // 🔗 API端点
  { 
    name: 'API-分类优化版', 
    url: `${BASE_URL}/api/categories-optimized?limit=24`, 
    category: 'api',
    expectedScore: 95,
    critical: true,
    isAPI: true
  },
  { 
    name: 'API-首页数据', 
    url: `${BASE_URL}/api/popular-videos?limit=20`, 
    category: 'api',
    expectedScore: 85,
    critical: true,
    isAPI: true
  },
  { 
    name: 'API-搜索数据', 
    url: `${BASE_URL}/api/search?q=高清&limit=20`, 
    category: 'api',
    expectedScore: 85,
    critical: true,
    isAPI: true
  },
  
  // 👤 用户页面
  { 
    name: '用户中心', 
    url: `${BASE_URL}/zh-CN/user/profile`, 
    category: 'user',
    expectedScore: 75,
    critical: false
  },
  { 
    name: '收藏页面', 
    url: `${BASE_URL}/zh-CN/user/favorites`, 
    category: 'user',
    expectedScore: 75,
    critical: false
  },
  
  // 📄 静态页面
  { 
    name: '关于页面', 
    url: `${BASE_URL}/zh-CN/about`, 
    category: 'static',
    expectedScore: 90,
    critical: false
  },
  { 
    name: '隐私政策', 
    url: `${BASE_URL}/zh-CN/privacy`, 
    category: 'static',
    expectedScore: 90,
    critical: false
  },
  
  // 📱 移动端测试
  { 
    name: '首页-移动端', 
    url: `${BASE_URL}/zh-CN`, 
    category: 'mobile',
    expectedScore: 80,
    critical: true,
    mobile: true
  },
  { 
    name: '分类页-移动端', 
    url: `${BASE_URL}/zh-CN/category`, 
    category: 'mobile',
    expectedScore: 85,
    critical: true,
    mobile: true
  }
];

// 🎯 Lighthouse 配置
const LIGHTHOUSE_CONFIG = {
  logLevel: 'info',
  output: 'json',
  onlyCategories: ['performance', 'accessibility', 'best-practices', 'seo'],
  settings: {
    formFactor: 'desktop',
    throttling: {
      rttMs: 40,
      throughputKbps: 10240,
      cpuSlowdownMultiplier: 1,
      requestLatencyMs: 0,
      downloadThroughputKbps: 0,
      uploadThroughputKbps: 0
    },
    screenEmulation: {
      mobile: false,
      width: 1350,
      height: 940,
      deviceScaleFactor: 1,
      disabled: false,
    },
    emulatedUserAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.109 Safari/537.36'
  }
};

// 📱 移动端配置
const MOBILE_CONFIG = {
  ...LIGHTHOUSE_CONFIG,
  settings: {
    ...LIGHTHOUSE_CONFIG.settings,
    formFactor: 'mobile',
    screenEmulation: {
      mobile: true,
      width: 375,
      height: 667,
      deviceScaleFactor: 2,
      disabled: false,
    },
    throttling: {
      rttMs: 150,
      throughputKbps: 1638.4,
      cpuSlowdownMultiplier: 4,
      requestLatencyMs: 0,
      downloadThroughputKbps: 0,
      uploadThroughputKbps: 0
    }
  }
};

// 🚀 API性能测试函数
async function testAPIPerformance(url, name) {
  const startTime = Date.now();
  
  try {
    const response = await fetch(url);
    const responseTime = Date.now() - startTime;
    const data = await response.json();
    
    // 🎯 API性能评分算法
    let score = 100;
    
    // 响应时间评分 (权重40%)
    if (responseTime > 500) score -= 30;
    else if (responseTime > 300) score -= 20;
    else if (responseTime > 200) score -= 10;
    else if (responseTime > 100) score -= 5;
    
    // 状态码评分 (权重20%)
    if (response.status !== 200) score -= 25;
    
    // 数据质量评分 (权重20%)
    if (!data.success) score -= 15;
    if (!data.data) score -= 10;
    
    // 缓存头评分 (权重10%)
    const cacheControl = response.headers.get('cache-control');
    if (!cacheControl || !cacheControl.includes('max-age')) score -= 10;
    
    // 性能头评分 (权重10%)
    const responseTimeHeader = response.headers.get('x-response-time');
    if (!responseTimeHeader) score -= 5;
    
    return {
      name,
      url,
      score: Math.max(0, Math.round(score)),
      responseTime,
      status: response.status,
      contentLength: response.headers.get('content-length') || 'unknown',
      cacheControl: cacheControl || 'none',
      serverTime: responseTimeHeader || 'not provided',
      success: response.ok,
      timestamp: new Date().toISOString()
    };
    
  } catch (error) {
    return {
      name,
      url,
      score: 0,
      responseTime: Date.now() - startTime,
      error: error.message,
      success: false,
      timestamp: new Date().toISOString()
    };
  }
}

// 🚀 页面性能测试函数
async function testPagePerformance(chrome, url, name, mobile = false) {
  try {
    const config = mobile ? MOBILE_CONFIG : LIGHTHOUSE_CONFIG;
    const runnerResult = await lighthouse(url, {
      port: chrome.port,
      disableDeviceEmulation: false,
      chromeFlags: ['--disable-dev-shm-usage']
    }, config);

    const { lhr } = runnerResult;
    
    // 📊 提取关键指标
    const metrics = {
      name,
      url,
      score: Math.round(lhr.categories.performance.score * 100),
      accessibility: Math.round(lhr.categories.accessibility.score * 100),
      bestPractices: Math.round(lhr.categories['best-practices'].score * 100),
      seo: Math.round(lhr.categories.seo.score * 100),
      
      // 🎯 关键性能指标
      fcp: lhr.audits['first-contentful-paint']?.displayValue || 'N/A',
      lcp: lhr.audits['largest-contentful-paint']?.displayValue || 'N/A',
      cls: lhr.audits['cumulative-layout-shift']?.displayValue || 'N/A',
      fid: lhr.audits['max-potential-fid']?.displayValue || 'N/A',
      ttfb: lhr.audits['server-response-time']?.displayValue || 'N/A',
      
      // 📈 性能机会
      opportunities: lhr.audits['unused-css-rules']?.details?.overallSavingsMs || 0,
      imageOptimization: lhr.audits['uses-optimized-images']?.score === 1,
      compressionEnabled: lhr.audits['uses-text-compression']?.score === 1,
      
      // 🌍 网络相关
      totalBlockingTime: lhr.audits['total-blocking-time']?.displayValue || 'N/A',
      speedIndex: lhr.audits['speed-index']?.displayValue || 'N/A',
      
      // 📱 设备类型
      deviceType: mobile ? 'mobile' : 'desktop',
      timestamp: new Date().toISOString()
    };
    
    return metrics;
    
  } catch (error) {
    console.error(`测试失败 ${name}:`, error.message);
    return {
      name,
      url,
      score: 0,
      error: error.message,
      deviceType: mobile ? 'mobile' : 'desktop',
      timestamp: new Date().toISOString()
    };
  }
}

// 📊 生成HTML报告
async function generateHTMLReport(results) {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const reportPath = path.join(OUTPUT_DIR, `enhanced-performance-report-${timestamp}.html`);
  
  // 📈 计算统计数据
  const stats = {
    total: results.length,
    passed: results.filter(r => r.score >= (TEST_PAGES.find(p => p.name === r.name)?.expectedScore || 70)).length,
    failed: 0,
    avgScore: Math.round(results.reduce((sum, r) => sum + (r.score || 0), 0) / results.length),
    criticalIssues: results.filter(r => {
      const page = TEST_PAGES.find(p => p.name === r.name);
      return page?.critical && r.score < (page.expectedScore || 70);
    }).length
  };
  stats.failed = stats.total - stats.passed;
  
  // 📊 按分类分组
  const categories = {};
  results.forEach(result => {
    const page = TEST_PAGES.find(p => p.name === result.name);
    const category = page?.category || 'other';
    if (!categories[category]) categories[category] = [];
    categories[category].push(result);
  });

  const html = `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JAVFLIX.TV 增强性能测试报告</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        .container { 
            max-width: 1400px; 
            margin: 0 auto; 
            background: white; 
            border-radius: 15px; 
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header { 
            background: linear-gradient(135deg, #ff6b6b, #ee5a52);
            color: white; 
            padding: 30px; 
            text-align: center;
        }
        .header h1 { font-size: 2.5em; margin-bottom: 10px; }
        .stats { 
            display: grid; 
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); 
            gap: 20px; 
            padding: 30px;
            background: #f8f9fa;
        }
        .stat-card { 
            background: white; 
            padding: 20px; 
            border-radius: 10px; 
            text-align: center; 
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .stat-number { font-size: 2em; font-weight: bold; margin-bottom: 5px; }
        .stat-label { color: #666; font-size: 0.9em; }
        .score-excellent { color: #28a745; }
        .score-good { color: #17a2b8; }
        .score-warning { color: #ffc107; }
        .score-poor { color: #dc3545; }
        .results { padding: 20px; }
        .category-section { margin-bottom: 40px; }
        .category-title { 
            font-size: 1.5em; 
            color: #333; 
            margin-bottom: 20px; 
            padding-bottom: 10px; 
            border-bottom: 3px solid #ff6b6b;
        }
        .result-grid { 
            display: grid; 
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr)); 
            gap: 20px; 
        }
        .result-card { 
            background: white; 
            border-radius: 10px; 
            padding: 20px; 
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            border-left: 5px solid #ddd;
        }
        .result-card.excellent { border-left-color: #28a745; }
        .result-card.good { border-left-color: #17a2b8; }
        .result-card.warning { border-left-color: #ffc107; }
        .result-card.poor { border-left-color: #dc3545; }
        .result-title { font-weight: bold; margin-bottom: 10px; font-size: 1.1em; }
        .result-score { font-size: 2em; font-weight: bold; margin: 10px 0; }
        .result-details { font-size: 0.9em; color: #666; }
        .result-metrics { 
            display: grid; 
            grid-template-columns: 1fr 1fr; 
            gap: 10px; 
            margin-top: 15px; 
        }
        .metric { 
            background: #f8f9fa; 
            padding: 8px; 
            border-radius: 5px; 
            text-align: center; 
        }
        .metric-label { font-size: 0.8em; color: #666; }
        .metric-value { font-weight: bold; color: #333; }
        .footer { 
            text-align: center; 
            padding: 20px; 
            color: #666; 
            background: #f8f9fa; 
        }
        .critical-badge { 
            background: #dc3545; 
            color: white; 
            padding: 2px 8px; 
            border-radius: 10px; 
            font-size: 0.8em; 
            margin-left: 10px; 
        }
        .mobile-badge { 
            background: #17a2b8; 
            color: white; 
            padding: 2px 8px; 
            border-radius: 10px; 
            font-size: 0.8em; 
            margin-left: 5px; 
        }
        .api-badge { 
            background: #6f42c1; 
            color: white; 
            padding: 2px 8px; 
            border-radius: 10px; 
            font-size: 0.8em; 
            margin-left: 5px; 
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 JAVFLIX.TV 增强性能测试报告</h1>
            <p>基于 Pieces.app 和 Sematext 的性能监控最佳实践</p>
            <p>测试时间: ${new Date().toLocaleString('zh-CN')}</p>
        </div>
        
        <div class="stats">
            <div class="stat-card">
                <div class="stat-number ${stats.avgScore >= 85 ? 'score-excellent' : stats.avgScore >= 70 ? 'score-good' : stats.avgScore >= 60 ? 'score-warning' : 'score-poor'}">${stats.avgScore}</div>
                <div class="stat-label">平均分数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number score-excellent">${stats.passed}</div>
                <div class="stat-label">通过测试</div>
            </div>
            <div class="stat-card">
                <div class="stat-number score-poor">${stats.failed}</div>
                <div class="stat-label">测试失败</div>
            </div>
            <div class="stat-card">
                <div class="stat-number ${stats.criticalIssues === 0 ? 'score-excellent' : 'score-poor'}">${stats.criticalIssues}</div>
                <div class="stat-label">关键问题</div>
            </div>
        </div>
        
        <div class="results">
            ${Object.keys(categories).map(category => `
                <div class="category-section">
                    <h2 class="category-title">📊 ${getCategoryName(category)} (${categories[category].length}个测试)</h2>
                    <div class="result-grid">
                        ${categories[category].map(result => {
                          const page = TEST_PAGES.find(p => p.name === result.name);
                          const scoreClass = result.score >= 85 ? 'excellent' : result.score >= 70 ? 'good' : result.score >= 60 ? 'warning' : 'poor';
                          const scoreColor = result.score >= 85 ? 'score-excellent' : result.score >= 70 ? 'score-good' : result.score >= 60 ? 'score-warning' : 'score-poor';
                          
                          return `
                            <div class="result-card ${scoreClass}">
                                <div class="result-title">
                                    ${result.name}
                                    ${page?.critical ? '<span class="critical-badge">关键</span>' : ''}
                                    ${result.deviceType === 'mobile' ? '<span class="mobile-badge">移动端</span>' : ''}
                                    ${page?.isAPI ? '<span class="api-badge">API</span>' : ''}
                                </div>
                                <div class="result-score ${scoreColor}">${result.score || 0}</div>
                                <div class="result-details">
                                    <strong>URL:</strong> ${result.url}<br>
                                    ${result.error ? `<strong style="color: #dc3545;">错误:</strong> ${result.error}` : ''}
                                </div>
                                ${result.responseTime ? `
                                    <div class="result-metrics">
                                        <div class="metric">
                                            <div class="metric-label">响应时间</div>
                                            <div class="metric-value">${result.responseTime}ms</div>
                                        </div>
                                        ${result.status ? `
                                            <div class="metric">
                                                <div class="metric-label">状态码</div>
                                                <div class="metric-value">${result.status}</div>
                                            </div>
                                        ` : ''}
                                        ${result.fcp ? `
                                            <div class="metric">
                                                <div class="metric-label">FCP</div>
                                                <div class="metric-value">${result.fcp}</div>
                                            </div>
                                        ` : ''}
                                        ${result.lcp ? `
                                            <div class="metric">
                                                <div class="metric-label">LCP</div>
                                                <div class="metric-value">${result.lcp}</div>
                                            </div>
                                        ` : ''}
                                        ${result.cls ? `
                                            <div class="metric">
                                                <div class="metric-label">CLS</div>
                                                <div class="metric-value">${result.cls}</div>
                                            </div>
                                        ` : ''}
                                        ${result.ttfb ? `
                                            <div class="metric">
                                                <div class="metric-label">TTFB</div>
                                                <div class="metric-value">${result.ttfb}</div>
                                            </div>
                                        ` : ''}
                                    </div>
                                ` : ''}
                            </div>
                          `;
                        }).join('')}
                    </div>
                </div>
            `).join('')}
        </div>
        
        <div class="footer">
            <p>🚀 由 JAVFLIX.TV 性能优化团队生成 | 基于 Lighthouse 和自定义API测试</p>
            <p>💡 建议定期运行此测试以监控网站性能</p>
        </div>
    </div>
    
    <script>
        function getCategoryName(category) {
            const names = {
                'core': '核心页面',
                'video': '视频页面', 
                'search': '搜索功能',
                'category': '分类页面',
                'api': 'API接口',
                'user': '用户页面',
                'static': '静态页面',
                'mobile': '移动端页面',
                'other': '其他页面'
            };
            return names[category] || category;
        }
    </script>
</body>
</html>`;

  await fs.writeFile(reportPath, html);
  console.log(`📊 HTML报告已生成: ${reportPath}`);
  return reportPath;
}

// 🚀 主测试函数
async function runEnhancedPerformanceTest() {
  console.log('🚀 启动JAVFLIX.TV增强性能测试...\n');
  
  // 📁 确保输出目录存在
  try {
    await fs.mkdir(OUTPUT_DIR, { recursive: true });
  } catch (error) {
    // 目录已存在，忽略错误
  }
  
  const results = [];
  const chrome = await chromeLauncher.launch({ chromeFlags: ['--headless', '--disable-dev-shm-usage'] });
  
  try {
    console.log('🌐 开始测试各个页面和API...\n');
    
    for (let i = 0; i < TEST_PAGES.length; i++) {
      const page = TEST_PAGES[i];
      const progress = `(${i + 1}/${TEST_PAGES.length})`;
      
      console.log(`🔍 ${progress} 测试: ${page.name}${page.critical ? ' [关键]' : ''}${page.mobile ? ' [移动端]' : ''}${page.isAPI ? ' [API]' : ''}`);
      
      let result;
      if (page.isAPI) {
        result = await testAPIPerformance(page.url, page.name);
      } else {
        result = await testPagePerformance(chrome, page.url, page.name, page.mobile);
      }
      
      results.push(result);
      
      // 🎯 显示结果
      if (result.error) {
        console.log(`   ❌ 测试失败: ${result.error}`);
      } else {
        const emoji = result.score >= 85 ? '🟢' : result.score >= 70 ? '🟡' : '🔴';
        const expectedScore = page.expectedScore || 70;
        const status = result.score >= expectedScore ? '✅ 达标' : '❌ 未达标';
        
        console.log(`   ${emoji} 分数: ${result.score}/100 ${status} (期望≥${expectedScore})`);
        
        if (result.responseTime) {
          console.log(`   ⏱️  响应时间: ${result.responseTime}ms`);
        }
        
        if (page.critical && result.score < expectedScore) {
          console.log(`   🚨 关键页面性能不达标!`);
        }
      }
      
      console.log('');
    }
    
  } finally {
    await chrome.kill();
  }
  
  // 📊 生成报告
  console.log('📊 生成测试报告...\n');
  
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const jsonReportPath = path.join(OUTPUT_DIR, `enhanced-performance-data-${timestamp}.json`);
  
  // 💾 保存JSON数据
  await fs.writeFile(jsonReportPath, JSON.stringify(results, null, 2));
  console.log(`💾 JSON数据已保存: ${jsonReportPath}`);
  
  // 🎨 生成HTML报告
  const htmlReportPath = await generateHTMLReport(results);
  
  // 📈 显示总结
  const totalScore = Math.round(results.reduce((sum, r) => sum + (r.score || 0), 0) / results.length);
  const passedTests = results.filter(r => {
    const page = TEST_PAGES.find(p => p.name === r.name);
    return r.score >= (page?.expectedScore || 70);
  }).length;
  const criticalIssues = results.filter(r => {
    const page = TEST_PAGES.find(p => p.name === r.name);
    return page?.critical && r.score < (page.expectedScore || 70);
  }).length;
  
  console.log('\n' + '='.repeat(60));
  console.log('📊 测试总结');
  console.log('='.repeat(60));
  console.log(`🎯 平均分数: ${totalScore}/100`);
  console.log(`✅ 通过测试: ${passedTests}/${results.length}`);
  console.log(`🚨 关键问题: ${criticalIssues}个`);
  console.log(`📋 详细报告: ${htmlReportPath}`);
  
  // 🎉 最终评估
  if (totalScore >= 85 && criticalIssues === 0) {
    console.log('\n🎉 优秀! 网站性能表现出色!');
  } else if (totalScore >= 70 && criticalIssues <= 1) {
    console.log('\n👍 良好! 网站性能基本达标，有少量改进空间。');
  } else {
    console.log('\n⚠️  警告! 网站性能需要优化，请查看详细报告。');
  }
  
  console.log('\n💡 建议定期运行此测试以监控性能变化。');
  
  return {
    averageScore: totalScore,
    passedTests,
    totalTests: results.length,
    criticalIssues,
    htmlReport: htmlReportPath,
    jsonReport: jsonReportPath
  };
}

// 🚀 启动测试
if (require.main === module) {
  runEnhancedPerformanceTest().catch(error => {
    console.error('❌ 测试执行失败:', error);
    process.exit(1);
  });
}

module.exports = { runEnhancedPerformanceTest };

function getCategoryName(category) {
  const names = {
    'core': '核心页面',
    'video': '视频页面', 
    'search': '搜索功能',
    'category': '分类页面',
    'api': 'API接口',
    'user': '用户页面',
    'static': '静态页面',
    'mobile': '移动端页面',
    'other': '其他页面'
  };
  return names[category] || category;
}