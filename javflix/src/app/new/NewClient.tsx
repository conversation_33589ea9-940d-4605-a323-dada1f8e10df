'use client';

import { Suspense, useEffect, useRef, useState, useCallback } from 'react';
import gsap from 'gsap';
import { useClientTranslations } from '@/components/TranslationsProvider';
import { useParams } from 'next/navigation';
import VideoCard from '@/components/VideoCard';
import { buildApiUrl } from '@/lib/api-config';
import { FiChevronLeft, FiChevronRight, FiClock } from 'react-icons/fi';

interface Video {
  id: string | number;
  movie_id: string;
  code: string;
  title: string;
  description?: string;
  duration: string;
  cover_image?: string;
  image_url?: string;
  release_date: string;
  views?: number;
  view_count?: number;
  timeAgo?: string;
}

interface VideoAPIResponse {
  success: boolean;
  data: {
    items: Video[];
    total: number;
    page: number;
    limit: number;
    total_pages: number;
  };
  message?: string;
}

export default function NewClient() {
  const { t } = useClientTranslations();
  const params = useParams();
  const locale = params?.locale || 'zh';
  
  // 状态管理
  const [videos, setVideos] = useState<Video[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalCount, setTotalCount] = useState(0);
  
  // 动画refs
  const titleRef = useRef<HTMLHeadingElement>(null);
  const subtitleRef = useRef<HTMLParagraphElement>(null);
  const gridRef = useRef<HTMLDivElement>(null);
  const paginationRef = useRef<HTMLDivElement>(null);
  
  const itemsPerPage = 24;

  // 格式化数量显示
  const formatCount = (count: number): string => {
    if (count >= 1000000) {
      return `${(count / 1000000).toFixed(1)}M`;
    }
    if (count >= 1000) {
      return `${(count / 1000).toFixed(1)}K`;
    }
    return count.toString();
  };

  // 获取最新视频数据
  const fetchVideos = useCallback(async (page: number = 1) => {
    try {
      setLoading(true);
      setError(null);

      const apiUrl = buildApiUrl(`/api/recent-videos?page=${page}&limit=${itemsPerPage}`);
      const response = await fetch(apiUrl, {
        cache: 'no-store'
      });

      if (!response.ok) {
        throw new Error(`${t('common.failedToLoadVideos')}: ${response.status}`);
      }

      const result: VideoAPIResponse = await response.json();
      
      if (result.success && result.data && Array.isArray(result.data.items)) {
        setVideos(result.data.items);
        setTotalPages(result.data.total_pages || 1);
        setTotalCount(result.data.total || 0);
      } else {
        setError(t('common.errorLoadingVideos'));
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : t('common.failedToLoadVideos'));
    } finally {
      setLoading(false);
    }
  }, [t]);

  // 页面切换处理
  const handlePageChange = (page: number) => {
    if (page >= 1 && page <= totalPages && page !== currentPage) {
      setCurrentPage(page);
      window.scrollTo({ top: 0, behavior: 'smooth' });
    }
  };

  // 渲染分页组件
  const renderPagination = () => {
    if (totalPages <= 1) return null;

    const showPages = 5;
    const startPage = Math.max(1, currentPage - Math.floor(showPages / 2));
    const endPage = Math.min(totalPages, startPage + showPages - 1);

    return (
      <div ref={paginationRef} className="flex justify-center space-x-2 mt-12">
        {/* 上一页 */}
        {currentPage > 1 && (
          <button
            key="prev"
            onClick={() => handlePageChange(currentPage - 1)}
            className="w-10 h-10 flex items-center justify-center rounded-full bg-gray-800/50 hover:bg-gray-700/50 transition-all duration-200 text-gray-300 hover:text-white"
          >
            <FiChevronLeft size={16} />
          </button>
        )}

        {/* 页码 */}
        {Array.from({ length: endPage - startPage + 1 }, (_, index) => {
          const pageNum = startPage + index;
          return (
            <button
              key={pageNum}
              onClick={() => handlePageChange(pageNum)}
              className={`w-10 h-10 flex items-center justify-center rounded-full transition-all duration-200 text-sm font-medium ${
                currentPage === pageNum
                  ? 'bg-red-500 text-white shadow-lg shadow-red-500/25'
                  : 'bg-gray-800/50 hover:bg-gray-700/50 text-gray-300 hover:text-white'
              }`}
            >
              {pageNum}
            </button>
          );
        })}

        {/* 下一页 */}
        {currentPage < totalPages && (
          <button
            key="next"
            onClick={() => handlePageChange(currentPage + 1)}
            className="w-10 h-10 flex items-center justify-center rounded-full bg-gray-800/50 hover:bg-gray-700/50 transition-all duration-200 text-gray-300 hover:text-white"
          >
            <FiChevronRight size={16} />
          </button>
        )}
      </div>
    );
  };
  
  useEffect(() => {
    // 页面加载动画
    if (titleRef.current) {
      gsap.fromTo(
        titleRef.current,
        { y: -30, opacity: 0 },
        { y: 0, opacity: 1, duration: 0.8, ease: 'power3.out' }
      );
    }
    
    if (subtitleRef.current) {
      gsap.fromTo(
        subtitleRef.current,
        { y: -20, opacity: 0 },
        { y: 0, opacity: 1, duration: 0.8, delay: 0.2, ease: 'power3.out' }
      );
    }
    
    if (gridRef.current) {
      gsap.fromTo(
        gridRef.current,
        { y: 30, opacity: 0 },
        { y: 0, opacity: 1, duration: 0.8, delay: 0.4, ease: 'power2.out' }
      );
    }
  }, []);

  // 获取视频数据
  useEffect(() => {
    fetchVideos(currentPage);
  }, [currentPage, fetchVideos]);
  
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-800 text-white pt-20 pb-12">
      {/* 背景装饰 - 参考首页Hero组件 */}
      <div className="absolute inset-0 pointer-events-none">
        <div className="absolute top-1/4 left-1/4 w-64 h-64 bg-red-500/10 rounded-full filter blur-3xl"></div>
        <div className="absolute bottom-1/3 right-1/3 w-96 h-96 bg-blue-500/8 rounded-full filter blur-3xl"></div>
        <div className="absolute top-1/2 left-1/2 w-48 h-48 bg-purple-500/12 rounded-full filter blur-2xl"></div>
      </div>

      <div className="relative container mx-auto px-4">
        {/* 现代化页面标题 - 参考首页设计风格 */}
        <div className="text-center mb-16">
          <div className="relative">
            <h1 
              ref={titleRef}
              className="text-5xl md:text-7xl font-bold mb-6 text-white leading-tight"
            >
              {t('nav.new') || '最新视频'}
            </h1>
          </div>
          
          <div 
            ref={subtitleRef}
            className="space-y-2"
          >
            {totalCount > 0 && (
              <p className="text-xl text-gray-300 font-light">
                {t('common.discoveredVideos').replace('{{count}}', formatCount(totalCount))}
              </p>
            )}
            {currentPage > 1 && (
              <p className="text-gray-400">
                {t('common.pageInfo').replace('{{current}}', currentPage.toString()).replace('{{total}}', totalPages.toString())}
              </p>
            )}
          </div>
        </div>
        
        {/* 现代化视频网格 - 参考首页卡片样式 */}
        {loading ? (
          <div className="flex justify-center items-center min-h-[500px]">
            <div className="text-center">
              <div className="relative">
                <div className="w-16 h-16 border-4 border-gray-700 border-t-white rounded-full animate-spin mx-auto mb-6"></div>
                <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-8 h-8 bg-white/20 rounded-full animate-pulse"></div>
              </div>
              <p className="text-gray-300 text-lg font-light">{t('common.loading')}</p>
            </div>
          </div>
        ) : error ? (
          <div className="text-center py-24">
            <div className="relative inline-block">
              <div className="text-8xl mb-6 filter drop-shadow-lg">😞</div>
              <div className="absolute -top-2 -right-2 w-6 h-6 bg-red-500 rounded-full animate-ping"></div>
            </div>
            <h3 className="text-2xl font-medium mb-3 text-gray-200">{t('common.loadingFailed')}</h3>
            <p className="text-gray-400 text-lg">{error}</p>
            <button 
              onClick={() => fetchVideos(currentPage)}
              className="mt-6 px-6 py-3 bg-red-600 hover:bg-red-700 text-white rounded-xl transition-all duration-300 transform hover:scale-105"
            >
              {t('common.retry')}
            </button>
          </div>
        ) : videos.length > 0 ? (
          <div ref={gridRef} className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {videos.map((video, index) => (
              <div
                key={video.id}
                className="group cursor-pointer transform transition-all duration-500 hover:scale-105"
                style={{
                  animationDelay: `${index * 50}ms`
                }}
              >
                <VideoCard
                  video={video}
                  locale={locale as string}
                  size="small"
                />
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-24">
            <div className="relative inline-block mb-8">
              <div className="text-8xl filter drop-shadow-lg">📹</div>
              <div className="absolute -bottom-2 left-1/2 transform -translate-x-1/2 w-12 h-1 bg-gradient-to-r from-transparent via-red-500 to-transparent"></div>
            </div>
            <h3 className="text-2xl font-medium mb-3 text-gray-200">{t('common.noNewVideos')}</h3>
            <p className="text-gray-400 text-lg">{t('common.comingSoon')}</p>
          </div>
        )}

        {/* 分页 */}
        {renderPagination()}
      </div>
    </div>
  );
} 