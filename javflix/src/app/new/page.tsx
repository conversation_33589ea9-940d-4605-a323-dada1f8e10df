import type { Metadata } from 'next';
import { generateMetadata as createMetadata } from '@/lib/seo';
import { Suspense } from 'react';
import NewClient from './NewClient';
import { RealtimeStatsProvider } from '@/contexts/RealtimeStatsContext';

// 导出静态元数据
export const metadata: Metadata = createMetadata({
  title: '最新上架视频',
  description: '浏览JAVFLIX最新上架的高清日本AV视频，每日更新，提供中文字幕、无码有码高清资源',
  path: '/new',
  keywords: [
    '最新AV', '日本AV', '在线观看', '高清无码', '中文字幕', 
    '每日更新', '新片', 'JAV', '成人视频'
  ],
});

// 加载占位符组件
function NewLoading() {
  return (
    <div className="min-h-screen bg-gray-900 text-white pt-20 pb-12">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <div className="h-12 bg-gray-800 rounded-lg animate-pulse mb-4 mx-auto max-w-md"></div>
          <div className="h-6 bg-gray-700 rounded-lg animate-pulse mx-auto max-w-sm"></div>
        </div>
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-6">
          {[...Array(20)].map((_, i) => (
            <div key={i} className="bg-gray-800 rounded-lg p-4 animate-pulse">
              <div className="h-48 bg-gray-700 rounded-lg mb-4"></div>
              <div className="h-4 bg-gray-700 rounded mb-2"></div>
              <div className="h-3 bg-gray-600 rounded w-3/4"></div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}

// 主页面是服务器组件，只用于包装客户端组件
export default function NewPage() {
  return (
    <RealtimeStatsProvider>
      <Suspense fallback={<NewLoading />}>
        <NewClient />
      </Suspense>
    </RealtimeStatsProvider>
  );
} 