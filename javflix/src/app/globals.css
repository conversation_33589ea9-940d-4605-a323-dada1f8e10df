@import "tailwindcss";
@import 'plyr/dist/plyr.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

/* 添加自定义动画和工具类 */
@layer utilities {
  /* 基础动画 */
  .animate-fadeIn {
    animation: fadeIn 0.3s ease-in-out;
  }

  .animate-slideUp {
    animation: slideUp 0.4s cubic-bezier(0.16, 1, 0.3, 1);
  }

  .animate-scaleIn {
    animation: scaleIn 0.3s cubic-bezier(0.16, 1, 0.3, 1);
  }

  .animate-bounceIn {
    animation: bounceIn 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
  }

  /* 文本工具类 */
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  /* 滚动条隐藏 */
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  /* 增强的悬停效果 */
  .hover-lift {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    will-change: transform;
  }

  .hover-lift:hover {
    transform: translateY(-4px) scale(1.02);
  }

  /* 微交互按钮效果 */
  .btn-micro {
    position: relative;
    overflow: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    will-change: transform, box-shadow;
  }

  .btn-micro::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.6s, height 0.6s;
  }

  .btn-micro:active::before {
    width: 300px;
    height: 300px;
  }

  .btn-micro:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
  }

  .btn-micro:active {
    transform: translateY(0);
  }

  /* 增强的发光效果 */
  .glow-subtle {
    box-shadow: 0 0 10px rgba(220, 38, 38, 0.2);
    transition: box-shadow 0.3s ease;
  }

  .glow-subtle:hover {
    box-shadow: 0 0 20px rgba(220, 38, 38, 0.4), 0 0 40px rgba(220, 38, 38, 0.1);
  }

  .glow-blue {
    box-shadow: 0 0 10px rgba(59, 130, 246, 0.2);
    transition: box-shadow 0.3s ease;
  }

  .glow-blue:hover {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.4), 0 0 40px rgba(59, 130, 246, 0.1);
  }

  /* 高性能渐进显示动画 */
  .animate-fade-in-up {
    opacity: 0;
    transform: translate3d(0, 20px, 0);
    animation: fadeInUp 0.6s cubic-bezier(0.16, 1, 0.3, 1) forwards;
  }

  .animate-fade-in-down {
    opacity: 0;
    transform: translate3d(0, -20px, 0);
    animation: fadeInDown 0.6s cubic-bezier(0.16, 1, 0.3, 1) forwards;
  }

  .animate-fade-in-left {
    opacity: 0;
    transform: translate3d(-20px, 0, 0);
    animation: fadeInLeft 0.6s cubic-bezier(0.16, 1, 0.3, 1) forwards;
  }

  .animate-fade-in-right {
    opacity: 0;
    transform: translate3d(20px, 0, 0);
    animation: fadeInRight 0.6s cubic-bezier(0.16, 1, 0.3, 1) forwards;
  }

  /* 卡片增强效果 */
  .card-enhanced {
    position: relative;
    background: rgba(31, 41, 55, 0.8);
    backdrop-filter: blur(12px) saturate(180%);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 16px;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    will-change: transform, box-shadow, border-color;
  }

  .card-enhanced::before {
    content: '';
    position: absolute;
    inset: 0;
    border-radius: inherit;
    padding: 1px;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), transparent, rgba(255, 255, 255, 0.05));
    mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    mask-composite: xor;
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  .card-enhanced:hover::before {
    opacity: 1;
  }

  .card-enhanced:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow:
      0 20px 40px rgba(0, 0, 0, 0.4),
      0 0 0 1px rgba(255, 255, 255, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.2);
  }

  /* 毛玻璃效果增强 */
  .glass-effect {
    background: rgba(31, 41, 55, 0.7);
    backdrop-filter: blur(16px) saturate(180%);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  .glass-effect-strong {
    background: rgba(17, 24, 39, 0.8);
    backdrop-filter: blur(24px) saturate(200%);
    border: 1px solid rgba(255, 255, 255, 0.15);
  }

  /* 渐变背景增强 */
  .gradient-bg-primary {
    background: linear-gradient(135deg,
      rgba(220, 38, 38, 0.1) 0%,
      rgba(239, 68, 68, 0.05) 50%,
      rgba(185, 28, 28, 0.1) 100%);
  }

  .gradient-bg-secondary {
    background: linear-gradient(135deg,
      rgba(59, 130, 246, 0.1) 0%,
      rgba(147, 51, 234, 0.05) 50%,
      rgba(236, 72, 153, 0.1) 100%);
  }

  /* 阴影系统 */
  .shadow-soft {
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  }

  .shadow-medium {
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.25);
  }

  .shadow-strong {
    box-shadow: 0 16px 50px rgba(0, 0, 0, 0.35);
  }

  .shadow-glow-red {
    box-shadow: 0 0 20px rgba(220, 38, 38, 0.3);
  }

  .shadow-glow-blue {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
  }

  /* 性能优化 - 减少重绘 */
  .gpu-accelerated {
    transform: translate3d(0, 0, 0);
    will-change: transform;
  }

  /* 响应式动画控制 */
  @media (prefers-reduced-motion: reduce) {
    .animate-fadeIn,
    .animate-slideUp,
    .animate-scaleIn,
    .animate-bounceIn,
    .animate-fade-in-up,
    .animate-fade-in-down,
    .animate-fade-in-left,
    .animate-fade-in-right,
    .hover-lift,
    .btn-micro,
    .card-enhanced {
      animation: none !important;
      transition: none !important;
      transform: none !important;
    }
  }

  /* 移动端优化 */
  @media (max-width: 768px) {
    .hover-lift:hover,
    .card-enhanced:hover {
      transform: none;
    }

    .btn-micro:hover {
      transform: none;
      box-shadow: none;
    }
  }

  /* 动画完成后清理will-change */
  .animate-fade-in-up.animation-complete {
    will-change: auto;
  }

  /* Hero Banner 专用动画 */
  .hero-title {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
  }

  .hero-description {
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  }

  .hero-badge {
    box-shadow: 0 4px 12px rgba(220, 38, 38, 0.3);
  }

  /* 浮动元素动画 */
  .floating-element {
    animation: float 6s ease-in-out infinite;
  }

  .floating-element:nth-child(2) {
    animation-delay: -2s;
  }

  .floating-element:nth-child(3) {
    animation-delay: -4s;
  }

  .floating-element:nth-child(4) {
    animation-delay: -1s;
  }

  @keyframes float {
    0%, 100% {
      transform: translateY(0px) rotate(0deg);
    }
    33% {
      transform: translateY(-10px) rotate(1deg);
    }
    66% {
      transform: translateY(5px) rotate(-1deg);
    }
  }

  /* 背景图片增强效果 */
  .hero-bg-image {
    filter: contrast(1.1) saturate(1.2) brightness(0.9);
    transition: filter 0.3s ease;
  }

  .hero-bg-image:hover {
    filter: contrast(1.15) saturate(1.3) brightness(0.95);
  }

  /* 按钮悬停效果增强 */
  .group:hover .group-hover\:scale-110 {
    transform: scale(1.1);
  }

  .group:hover .group-hover\:rotate-90 {
    transform: rotate(90deg);
  }

  /* 渐变文字效果 */
  .bg-gradient-to-r.bg-clip-text {
    background-size: 200% 200%;
    animation: gradientShift 3s ease infinite;
  }

  @keyframes gradientShift {
    0%, 100% {
      background-position: 0% 50%;
    }
    50% {
      background-position: 100% 50%;
    }
  }

  /* 毛玻璃效果增强 */
  .backdrop-blur-md {
    backdrop-filter: blur(12px) saturate(180%);
  }

  .backdrop-blur-xl {
    backdrop-filter: blur(24px) saturate(180%);
  }

  /* 响应式优化 */
  @media (max-width: 768px) {
    .hero-title {
      font-size: clamp(2rem, 8vw, 4rem);
    }
    
    .hero-description {
      font-size: clamp(1rem, 4vw, 1.25rem);
    }
  }

  /* 延迟动画类 - 用于错开显示 */
  .animate-delay-100 { animation-delay: 0.1s; }
  .animate-delay-200 { animation-delay: 0.2s; }
  .animate-delay-300 { animation-delay: 0.3s; }
  .animate-delay-400 { animation-delay: 0.4s; }

  /* 旧的fade-in-up保持兼容性 */
  .fade-in-up {
    opacity: 0;
    transform: translateY(20px);
    animation: fadeInUp 0.5s ease-out forwards;
  }

  .fade-in-up:nth-child(1) { animation-delay: 0.1s; }
  .fade-in-up:nth-child(2) { animation-delay: 0.2s; }
  .fade-in-up:nth-child(3) { animation-delay: 0.3s; }
  .fade-in-up:nth-child(4) { animation-delay: 0.4s; }

  /* 进度条 */
  .progress-bar {
    background: linear-gradient(90deg, #dc2626 0%, #b91c1c 100%);
    position: relative;
    overflow: hidden;
  }

  /* 等级徽章 */
  .level-badge {
    background: linear-gradient(135deg, #374151 0%, #1f2937 100%);
    border: 1px solid #dc2626;
  }

  /* 成就样式 */
  .achievement-item {
    transition: all 0.2s ease;
  }
  
  .achievement-item:hover {
    transform: scale(1.02);
  }

  /* 统一的区块样式 */
  .section-container {
    background: rgba(17, 24, 39, 0.2);
    border: 1px solid rgba(55, 65, 81, 0.5);
    border-radius: 1rem;
    backdrop-filter: blur(8px);
    transition: all 0.3s ease;
  }

  .section-container:hover {
    border-color: rgba(220, 38, 38, 0.3);
    box-shadow: 0 4px 20px rgba(220, 38, 38, 0.1);
  }

  /* 统一的标题样式 */
  .section-title {
    color: white;
    font-weight: bold;
    display: flex;
    align-items: center;
  }

  .section-title-indicator {
    width: 4px;
    height: 24px;
    background-color: #dc2626;
    border-radius: 9999px;
    margin-right: 12px;
  }

  /* 统一的查看更多链接样式 */
  .view-more-link {
    color: #9ca3af;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
  }

  .view-more-link:hover {
    color: #dc2626;
  }

  .view-more-link svg {
    transition: transform 0.2s ease;
  }

  .view-more-link:hover svg {
    transform: translateX(4px);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 增强的关键帧动画 */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translate3d(0, 30px, 0);
  }
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translate3d(0, 20px, 0);
  }
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translate3d(0, -20px, 0);
  }
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translate3d(-20px, 0, 0);
  }
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translate3d(20px, 0, 0);
  }
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}

:root {
  --color-gray-300: #d1d5db;
  --foreground-rgb: 255, 255, 255;
  --background-color: #121212;
  --red-primary: #dc2626;
  --red-hover: #b91c1c;
  --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

html,
body {
  height: 100%;
}

body {
  color: rgb(var(--foreground-rgb));
  background-color: var(--background-color);
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

main {
  flex: 1 0 auto;
}

footer {
  flex-shrink: 0;
}

/* 基本样式 */
.nav-link {
  color: var(--color-gray-300);
  transition-property: color;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.nav-link:hover {
  color: #ffffff;
}

/* 文本截断 */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  line-clamp: 2;
}

/* 美化滚动条 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(15, 15, 15, 0.3);
  border-radius: 10px;
}

::-webkit-scrollbar-thumb {
  background: rgba(220, 38, 38, 0.5);
  border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(220, 38, 38, 0.7);
}

/* 视频卡片悬停效果 */
.video-card {
  transition: var(--transition-smooth);
  transform: translateY(0);
}

.video-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 20px -5px rgba(0, 0, 0, 0.3);
}

/* 自定义按钮样式 */
.btn-primary {
  background-color: var(--red-primary);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  font-weight: 500;
  transition: var(--transition-smooth);
}

.btn-primary:hover {
  background-color: var(--red-hover);
  transform: translateY(-1px);
}

/* 自定义标签样式 */
.badge {
  padding: 0.25rem 0.5rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
}

.badge-red {
  background-color: var(--red-primary);
  color: white;
}

.badge-blue {
  background-color: #3b82f6;
  color: white;
}

/* 图片悬停效果 */
.img-hover-zoom {
  overflow: hidden;
  border-radius: 0.5rem;
}

.img-hover-zoom img {
  transition: transform 0.3s ease;
}

.img-hover-zoom:hover img {
  transform: scale(1.05);
}

/* 卡片悬停效果 */
.card-hover-effect {
  transition: all 0.2s ease;
}

.card-hover-effect:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.btn-interaction {
  transition: all 0.2s;
}

.btn-interaction:active {
  transform: scale(0.95);
}

/* 响应式 */
@media (max-width: 640px) {
  .responsive-padding {
    padding-left: 1rem;
    padding-right: 1rem;
  }
  
  h1, h2 {
    font-size: 1.25rem;
    line-height: 1.75rem;
  }
}

@media (max-width: 768px) {
  .hover-lift:hover {
    transform: none;
  }
}

.drawer-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 50;
}

.drawer-content {
  transform: translateX(-100%);
  transition: transform 0.3s ease-in-out;
}

/* Responsive HTML5 Video Player Styles */
video {
  /* Make responsive by removing fixed dimensions */
  width: 100% !important;
  height: auto !important;
  max-width: 100%;
  display: block;
}

/* Video container aspect ratio maintenance */
.video-container {
  position: relative;
  width: 100%;
  height: 0;
  padding-bottom: 56.25%; /* 16:9 aspect ratio */
}

.video-container video {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: contain;
}

/* Custom video control styling for dark theme */
video::-webkit-media-controls-panel {
  background-color: rgba(0, 0, 0, 0.8);
}

video::-webkit-media-controls-play-button {
  color: #ef4444;
}

video::-webkit-media-controls-timeline {
  color: #ef4444;
}

video::-webkit-media-controls-volume-slider {
  color: #ef4444;
}

/* Ensure video doesn't break out of container on mobile */
@media (max-width: 768px) {
  video {
    width: 100% !important;
    height: auto !important;
  }
}
