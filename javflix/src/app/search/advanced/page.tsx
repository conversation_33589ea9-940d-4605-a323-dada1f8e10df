'use client';

import { useState, useEffect, useRef, Suspense } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { useSearchParams } from 'next/navigation';
import { 
  FiFilter, FiSearch, FiCalendar, FiClock, FiX, 
  FiChevronDown, FiStar
} from 'react-icons/fi';
import gsap from 'gsap';

// 模拟分类数据
const categories = [
  { id: 1, name: '制服', count: 526 },
  { id: 2, name: '女教师', count: 245 },
  { id: 3, name: '情侣', count: 327 },
  { id: 4, name: '巨乳', count: 842 },
  { id: 5, name: '萝莉', count: 431 },
  { id: 6, name: '御姐', count: 278 },
  { id: 7, name: '护士', count: 198 },
  { id: 8, name: '姐妹', count: 167 },
  { id: 9, name: '奴隶', count: 213 },
  { id: 10, name: '女仆', count: 226 },
  { id: 11, name: '痴汉', count: 194 },
  { id: 12, name: '多人', count: 356 },
];

// 模拟热门女优数据
const actresses = [
  { id: 1, name: '三上悠亚', videos: 86, imageUrl: '/images/1Ayvrd.jpg' },
  { id: 2, name: '深田咏美', videos: 94, imageUrl: '/images/2mKN5y.jpg' },
  { id: 3, name: '桥本有菜', videos: 78, imageUrl: '/images/3nydbz.jpg' },
  { id: 4, name: '水卜樱', videos: 65, imageUrl: '/images/4DKBXJ.jpg' },
  { id: 5, name: '明日花绮罗', videos: 120, imageUrl: '/images/5nK11a.jpg' },
  { id: 6, name: '葵司', videos: 83, imageUrl: '/images/6deyV7.jpg' },
  { id: 7, name: '天使萌', videos: 92, imageUrl: '/images/7yKgJ1.jpg' },
  { id: 8, name: '松下纱荣子', videos: 76, imageUrl: '/images/82Ez9x.jpg' },
];

// 视频时长选项
const durationOptions = [
  { label: '全部时长', value: '' },
  { label: '短片 (< 30分钟)', value: 'short' },
  { label: '中长 (30-60分钟)', value: 'medium' },
  { label: '长片 (1-2小时)', value: 'long' },
  { label: '超长 (> 2小时)', value: 'extra-long' }
];

// 排序选项
const sortOptions = [
  { label: '最新上传', value: 'latest' },
  { label: '观看最多', value: 'views' },
  { label: '评分最高', value: 'rating' },
  { label: '最受欢迎', value: 'popularity' }
];

// 定义查询参数类型
interface SearchParams {
  query: string;
  category: string;
  duration: string;
  date: string;
  sort: string;
}

// 模拟搜索结果数据和类型
interface SearchResult {
  id: number;
  title: string;
  imageUrl: string;
  duration: string;
  views: string;
  rating: string;
  date: string;
  actress: string;
  slug: string;
}

const getSearchResults = (_params: SearchParams): SearchResult[] => {
  // 模拟搜索结果，实际应该根据搜索条件从API获取
  return [
    {
      id: 1,
      title: '高级搜索结果1',
      imageUrl: '/images/a8BGN3.jpg',
      duration: '2:25:10',
      views: '215K',
      rating: '98%',
      date: '2023-05-15',
      actress: '三上悠亚',
      slug: 'search-result-1'
    },
    {
      id: 2,
      title: '高级搜索结果2',
      imageUrl: '/images/Ww3pkp.jpg',
      duration: '1:58:30',
      views: '186K',
      rating: '96%',
      date: '2023-04-20',
      actress: '深田咏美',
      slug: 'search-result-2'
    }
    // 更多结果...
  ];
};

// 搜索表单组件，使用 useSearchParams
function SearchForm() {
  const searchParams = useSearchParams();
  
  // 搜索参数状态
  const [searchQuery, setSearchQuery] = useState(searchParams.get('keyword') || '');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [selectedDuration, setSelectedDuration] = useState('all');
  const [selectedDate, setSelectedDate] = useState('all');
  const [selectedSort, setSelectedSort] = useState('relevant');
  const [isLoading, setIsLoading] = useState(false);
  const [showFilters, setShowFilters] = useState(true);
  
  // 创建默认参数
  const defaultParams: SearchParams = {
    query: '',
    category: '',
    duration: '',
    date: '',
    sort: ''
  };

  // 搜索结果
  const [searchResults, setSearchResults] = useState(getSearchResults(defaultParams));
  
  // Refs for animations
  const headerRef = useRef<HTMLDivElement>(null);
  const filtersRef = useRef<HTMLDivElement>(null);
  const resultsRef = useRef<HTMLDivElement>(null);
  const resultRefs = useRef<Array<HTMLDivElement | null>>([]);
  
  useEffect(() => {
    resultRefs.current = resultRefs.current.slice(0, searchResults.length);
    
    // 初始动画
    const tl = gsap.timeline({ defaults: { ease: 'power3.out' } });
    
    tl.fromTo(
      headerRef.current,
      { y: -20, opacity: 0 },
      { y: 0, opacity: 1, duration: 0.6 }
    );
    
    tl.fromTo(
      filtersRef.current,
      { y: 20, opacity: 0 },
      { y: 0, opacity: 1, duration: 0.6 },
      '-=0.3'
    );
    
    tl.fromTo(
      resultsRef.current,
      { y: 20, opacity: 0 },
      { y: 0, opacity: 1, duration: 0.6 },
      '-=0.3'
    );
    
    // 结果项动画
    resultRefs.current.forEach((ref, index) => {
      if (ref) {
        gsap.fromTo(
          ref,
          { opacity: 0, y: 20 },
          { 
            opacity: 1, 
            y: 0, 
            duration: 0.4, 
            delay: 0.6 + index * 0.05 
          }
        );
      }
    });
  }, [searchResults.length]);
  
  const handleCategoryToggle = (id: number) => {
    setSelectedCategory(prev => 
      prev === id.toString() ? 'all' : id.toString()
    );
  };
  
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    
    // 构建搜索参数
    const params = new URLSearchParams();
    if (searchQuery) params.set('keyword', searchQuery);
    if (selectedCategory !== 'all') params.set('category', selectedCategory);
    if (selectedDuration !== 'all') params.set('duration', selectedDuration);
    if (selectedDate !== 'all') params.set('date', selectedDate);
    if (selectedSort !== 'relevant') params.set('sort', selectedSort);
    
    // 从URLSearchParams创建SearchParams
    const searchParamsObj: SearchParams = {
      query: params.get('keyword') || '',
      category: params.get('category') || '',
      duration: params.get('duration') || '',
      date: params.get('date') || '',
      sort: params.get('sort') || ''
    };
    
    // 模拟搜索请求
    setTimeout(() => {
      setIsLoading(false);
      setSearchResults(getSearchResults(searchParamsObj));
      
      // 实际应用中，这里会使用router.push更新URL
      // router.push(`/search/advanced?${params.toString()}`);
    }, 800);
  };
  
  const clearFilters = () => {
    setSearchQuery('');
    setSelectedCategory('all');
    setSelectedDuration('all');
    setSelectedDate('all');
    setSelectedSort('relevant');
  };
  
  const setResultRef = (el: HTMLDivElement | null, index: number) => {
    resultRefs.current[index] = el;
  };
  
  const toggleFilters = () => {
    setShowFilters(!showFilters);
    
    if (filtersRef.current) {
      gsap.to(filtersRef.current, {
        height: showFilters ? 0 : 'auto',
        opacity: showFilters ? 0 : 1,
        duration: 0.4,
        ease: 'power2.inOut',
        onComplete: () => {
          if (!showFilters) {
            gsap.set(filtersRef.current, { height: 'auto' });
          }
        }
      });
    }
  };
  
  return (
    <div className="min-h-screen pt-20 pb-16">
      <div className="container mx-auto px-4">
        {/* 页面标题 */}
        <div ref={headerRef} className="mb-8">
          <h1 className="text-3xl font-bold text-white">高级搜索</h1>
          <p className="text-gray-400 mt-2">使用多种条件组合，找到您想要的视频</p>
        </div>
        
        {/* 搜索表单 */}
        <form onSubmit={handleSearch}>
          {/* 搜索框 */}
          <div className="bg-gray-800 rounded-xl p-4 mb-6">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-grow">
                <div className="relative">
                  <input
                    type="text"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    placeholder="输入关键词、番号或女优名称..."
                    className="w-full bg-gray-700 text-white rounded-lg py-3 px-4 pl-12 focus:outline-none focus:ring-2 focus:ring-red-500"
                  />
                  <FiSearch className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
                </div>
              </div>
              <div className="flex gap-2">
                <button
                  type="button"
                  onClick={toggleFilters}
                  className="bg-gray-700 text-white px-4 py-3 rounded-lg flex items-center hover:bg-gray-600 transition-colors"
                >
                  <FiFilter className="mr-2" />
                  筛选
                  <FiChevronDown className={`ml-2 transform transition-transform ${showFilters ? 'rotate-180' : ''}`} />
                </button>
                <button
                  type="submit"
                  className="bg-red-600 hover:bg-red-700 text-white px-8 py-3 rounded-lg flex items-center transition-colors"
                  disabled={isLoading}
                >
                  {isLoading ? (
                    <>
                      <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                      搜索中...
                    </>
                  ) : (
                    <>
                      <FiSearch className="mr-2" />
                      搜索
                    </>
                  )}
                </button>
              </div>
            </div>
            
            {/* 筛选选项 */}
            <div 
              ref={filtersRef} 
              className={`mt-4 border-t border-gray-700 pt-4 ${showFilters ? '' : 'hidden'}`}
            >
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                {/* 分类筛选 */}
                <div>
                  <label className="block text-gray-400 mb-2 text-sm">分类</label>
                  <select
                    value={selectedCategory}
                    onChange={(e) => setSelectedCategory(e.target.value)}
                    className="w-full bg-gray-700 text-white rounded-lg py-2 px-3 focus:outline-none focus:ring-2 focus:ring-red-500"
                  >
                    <option value="all">全部分类</option>
                    {categories.map(category => (
                      <option key={category.id} value={category.id.toString()}>
                        {category.name} ({category.count})
                      </option>
                    ))}
                  </select>
                </div>
                
                {/* 时长筛选 */}
                <div>
                  <label className="block text-gray-400 mb-2 text-sm">时长</label>
                  <select
                    value={selectedDuration}
                    onChange={(e) => setSelectedDuration(e.target.value)}
                    className="w-full bg-gray-700 text-white rounded-lg py-2 px-3 focus:outline-none focus:ring-2 focus:ring-red-500"
                  >
                    {durationOptions.map((option, index) => (
                      <option key={index} value={option.value || 'all'}>
                        {option.label}
                      </option>
                    ))}
                  </select>
                </div>
                
                {/* 日期筛选 */}
                <div>
                  <label className="block text-gray-400 mb-2 text-sm">日期</label>
                  <select
                    value={selectedDate}
                    onChange={(e) => setSelectedDate(e.target.value)}
                    className="w-full bg-gray-700 text-white rounded-lg py-2 px-3 focus:outline-none focus:ring-2 focus:ring-red-500"
                  >
                    <option value="all">全部时间</option>
                    <option value="today">今天</option>
                    <option value="week">本周</option>
                    <option value="month">本月</option>
                    <option value="year">今年</option>
                  </select>
                </div>
                
                {/* 排序方式 */}
                <div>
                  <label className="block text-gray-400 mb-2 text-sm">排序方式</label>
                  <select
                    value={selectedSort}
                    onChange={(e) => setSelectedSort(e.target.value)}
                    className="w-full bg-gray-700 text-white rounded-lg py-2 px-3 focus:outline-none focus:ring-2 focus:ring-red-500"
                  >
                    <option value="relevant">相关性</option>
                    {sortOptions.map((option, index) => (
                      <option key={index} value={option.value}>
                        {option.label}
                      </option>
                    ))}
                  </select>
                </div>
              </div>
              
              {/* 热门女优标签 */}
              <div className="mt-4">
                <div className="flex justify-between items-center mb-2">
                  <label className="text-gray-400 text-sm">热门女优</label>
                  <button 
                    type="button" 
                    onClick={clearFilters}
                    className="text-xs text-red-500 hover:text-red-400 flex items-center"
                  >
                    <FiX className="mr-1" size={12} />
                    清除筛选
                  </button>
                </div>
                <div className="flex flex-wrap gap-2">
                  {actresses.slice(0, 8).map(actress => (
                    <div 
                      key={actress.id}
                      className="flex items-center bg-gray-700 rounded-full pl-1 pr-3 py-1 hover:bg-gray-600 transition-colors cursor-pointer"
                      onClick={() => setSearchQuery(prev => 
                        prev.includes(actress.name) ? 
                        prev.replace(actress.name, '').trim() : 
                        `${prev} ${actress.name}`.trim()
                      )}
                    >
                      <div className="w-6 h-6 rounded-full overflow-hidden mr-2">
                        <Image 
                          src={actress.imageUrl} 
                          alt={actress.name}
                          width={24}
                          height={24}
                          className="w-full h-full object-cover"
                        />
                      </div>
                      <span className="text-sm text-white">{actress.name}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </form>
        
        {/* 搜索结果 */}
        <div ref={resultsRef}>
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-xl font-bold text-white">搜索结果</h2>
            <span className="text-gray-400 text-sm">找到 {searchResults.length} 个结果</span>
          </div>
          
          {searchResults.length === 0 ? (
            <div className="bg-gray-800 rounded-xl p-8 text-center">
              <p className="text-gray-400 mb-4">没有找到匹配的结果</p>
              <p className="text-sm text-gray-500">尝试使用不同的关键词或减少筛选条件</p>
            </div>
          ) : (
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-5">
              {searchResults.map((result, index) => (
                <div 
                  key={result.id}
                  ref={(el) => setResultRef(el, index)}
                  className="flex flex-col space-y-2"
                >
                  {/* 视频图片区块 */}
                  <Link href={`/video/${result.slug}`} className="block">
                    <div className="relative overflow-hidden rounded-xl group">
                      <div className="relative h-44 md:h-48 overflow-hidden">
                        <Image
                          src={result.imageUrl}
                          alt={result.title}
                          fill
                          className="object-cover transition-transform duration-500 group-hover:scale-110"
                          sizes="(max-width: 768px) 100vw, 400px"
                        />
                        <div className="absolute inset-0 bg-gradient-to-t from-black to-transparent opacity-70 group-hover:opacity-60 transition-opacity duration-300"></div>
                        
                        {/* 时长标签 */}
                        <div className="absolute top-2 right-2 bg-red-600 text-white text-xs px-2 py-1 rounded font-medium flex items-center">
                          <FiClock className="mr-1" size={10} />
                          {result.duration}
                        </div>
                        
                        {/* 评分标签 */}
                        <div className="absolute bottom-2 right-2 flex items-center bg-black bg-opacity-60 text-white text-xs px-2 py-1 rounded">
                          <FiStar className="text-yellow-500 mr-1" size={12} />
                          {result.rating}
                        </div>
                      </div>
                    </div>
                  </Link>
                  
                  {/* 视频信息区块 */}
                  <div className="flex flex-col">
                    <Link href={`/video/${result.slug}`} className="block">
                      <h3 className="text-white font-semibold text-base truncate hover:text-red-500 transition-colors duration-300">
                        {result.title}
                      </h3>
                    </Link>
                    <div className="flex justify-between items-center mt-1">
                      <p className="text-gray-400 text-xs">{result.views} 次观看</p>
                      <span className="text-xs text-gray-400 flex items-center">
                        <FiCalendar className="mr-1" size={10} />
                        {result.date}
                      </span>
                    </div>
                    <Link href={`/actress/${result.actress}`} className="text-xs text-blue-400 hover:text-blue-300 mt-1">
                      {result.actress}
                    </Link>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

export default function AdvancedSearchPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-800 pt-20 pb-16 flex items-center justify-center">
        {/* 背景装饰 */}
        <div className="absolute inset-0 pointer-events-none">
          <div className="absolute top-1/4 left-1/4 w-64 h-64 bg-red-500/10 rounded-full filter blur-3xl"></div>
          <div className="absolute bottom-1/3 right-1/3 w-96 h-96 bg-blue-500/8 rounded-full filter blur-3xl"></div>
          <div className="absolute top-1/2 left-1/2 w-48 h-48 bg-purple-500/12 rounded-full filter blur-2xl"></div>
        </div>

        <div className="relative text-center">
          <div className="relative">
            <div className="border-4 border-gray-700 border-t-white rounded-full animate-spin mx-auto mb-6 w-16 h-16"></div>
            <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-8 h-8 bg-white/20 rounded-full animate-pulse"></div>
          </div>
          <p className="text-lg font-light text-gray-300">正在加载...</p>
        </div>
      </div>
    }>
      <SearchForm />
    </Suspense>
  );
}

