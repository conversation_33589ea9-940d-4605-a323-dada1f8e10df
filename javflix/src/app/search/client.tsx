'use client';

import { useEffect, useState, useRef } from 'react';
import { useRouter } from 'next/navigation';
import Image from 'next/image';
import Link from 'next/link';
import { FiClock, FiEye, FiSearch, FiFilter, FiX, FiChevronDown, FiChevronUp, FiHeart, FiStar, FiCalendar, FiVideo, FiTrendingUp, FiBookmark, FiThumbsUp } from 'react-icons/fi';
import gsap from 'gsap';
import { useClientTranslations } from '@/components/TranslationsProvider';

interface Video {
  id: string | number;
  title: string;
  thumbnail: string;
  duration: string;
  views: number | null;
  timeAgo: string;
  slug: string;
}

interface SearchClientProps {
  initialQuery: string;
  initialCategory: string;
  initialDuration: string;
  initialTime: string;
  initialQuality: string;
  initialSort: string;
  initialPage: number;
}

// 分类选项
const categories = [
  { value: 'all', label: '全部分类' },
  { value: 'drama', label: '剧情' },
  { value: 'uniform', label: '制服' },
  { value: 'schoolgirl', label: '学生' },
  { value: 'mature', label: '熟女' },
  { value: 'idol', label: '偶像' },
  { value: 'cosplay', label: '角色扮演' },
  { value: 'outdoor', label: '户外' },
];

// 视频时长选项
const durations = [
  { value: 'all', label: '全部时长' },
  { value: 'short', label: '短片 (< 30分钟)' },
  { value: 'medium', label: '中长 (30-60分钟)' },
  { value: 'long', label: '长片 (60-120分钟)' },
  { value: 'movie', label: '电影 (> 120分钟)' },
];

// 上传时间选项
const uploadTimes = [
  { value: 'all', label: '全部时间' },
  { value: 'today', label: '今天' },
  { value: 'week', label: '本周' },
  { value: 'month', label: '本月' },
  { value: 'year', label: '今年' },
];

// 清晰度选项
const qualities = [
  { value: 'all', label: '全部清晰度' },
  { value: 'hd', label: '高清 720P' },
  { value: 'fullhd', label: '全高清 1080P' },
  { value: '4k', label: '超高清 4K' },
];

export function SearchClient({
  initialQuery,
  initialCategory,
  initialDuration,
  initialTime,
  initialQuality,
  initialSort,
  initialPage
}: SearchClientProps) {
  const router = useRouter();
  const { t } = useClientTranslations();
  
  const [query, setQuery] = useState(initialQuery);
  const [category, setCategory] = useState(initialCategory);
  const [duration, setDuration] = useState(initialDuration);
  const [time, setTime] = useState(initialTime);
  const [quality, setQuality] = useState(initialQuality);
  const [sort, setSort] = useState(initialSort);
  const [page, setPage] = useState(initialPage);

  const [loading, setLoading] = useState(true);
  const [results, setResults] = useState<Video[]>([]);
  const [totalResults, setTotalResults] = useState(0);
  const [totalPages, setTotalPages] = useState(0);
  const [showFilters, setShowFilters] = useState(false);
  const [isMobileFilterOpen, setIsMobileFilterOpen] = useState(false);
  const [expandedSections, setExpandedSections] = useState({
    category: true,
    duration: true, 
    time: true,
    quality: true
  });
  
  // 添加搜索词推荐相关状态
  const [searchTerm, setSearchTerm] = useState(initialQuery);
  const [suggestions, setSuggestions] = useState<string[]>([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [isInputFocused, setIsInputFocused] = useState(false);
  const suggestionsRef = useRef<HTMLDivElement>(null);
  const searchInputRef = useRef<HTMLInputElement>(null);
  const suggestionDebounceRef = useRef<NodeJS.Timeout | null>(null);
  
  const videoRefs = useRef<Array<HTMLDivElement | null>>([]);
  const filtersRef = useRef<HTMLDivElement>(null);

  // 侧边栏收起/展开逻辑
  const toggleSection = (section: 'category' | 'duration' | 'time' | 'quality') => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  // 构建查询参数
  const buildQueryParams = (params: Record<string, string>) => {
    const newParams = new URLSearchParams();
    
    // 始终包含查询词
    if (query) {
      newParams.set('q', query);
    }
    
    // 添加当前参数
    if (category && category !== 'all') newParams.set('category', category);
    if (duration && duration !== 'all') newParams.set('duration', duration);
    if (time && time !== 'all') newParams.set('time', time);
    if (quality && quality !== 'all') newParams.set('quality', quality);
    if (sort && sort !== 'relevance') newParams.set('sort', sort);
    if (page > 1) newParams.set('page', page.toString());
    
    // 添加新参数
    Object.entries(params).forEach(([key, value]) => {
      if (value && value !== 'all') {
        newParams.set(key, value);
      } else {
        newParams.delete(key);
      }
    });
    
    return newParams.toString();
  };

  // 应用筛选器
  const applyFilter = (filterType: string, value: string) => {
    const params: Record<string, string> = {};
    
    switch (filterType) {
      case 'category':
        params.category = value;
        setCategory(value);
        break;
      case 'duration':
        params.duration = value;
        setDuration(value);
        break;
      case 'time':
        params.time = value;
        setTime(value);
        break;
      case 'quality':
        params.quality = value;
        setQuality(value);
        break;
      case 'sort':
        params.sort = value;
        setSort(value);
        break;
    }
    
    // 重置页码
    params.page = '1';
    setPage(1);
    
    const queryString = buildQueryParams(params);
    router.push(`/search?${queryString}`);
  };

  // 清除所有筛选器
  const clearAllFilters = () => {
    setCategory('all');
    setDuration('all');
    setTime('all');
    setQuality('all');
    setSort('relevance');
    setPage(1);
    
    router.push(`/search?q=${encodeURIComponent(query)}`);
  };

  // 获取搜索建议
  const fetchSuggestions = async (term: string) => {
    if (term.trim().length === 0) {
      setSuggestions([]);
      setShowSuggestions(false);
      return;
    }
    
    try {
      const response = await fetch(`/api/search-suggestions?q=${encodeURIComponent(term)}`);
      if (response.ok) {
        const data = await response.json();
        if (data.status === 'success' && Array.isArray(data.data)) {
          setSuggestions(data.data);
          setShowSuggestions(true);
        }
      }
    } catch (error) {
      console.error('获取搜索建议失败:', error);
    }
  };
  
  // 记录搜索词
  const recordSearch = async (term: string) => {
    if (term.trim().length === 0) return;
    
    try {
      // 记录搜索词到全局搜索建议
      await fetch('/api/search-suggestions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ term })
      });
      
      // 通过新的API记录搜索
      const userId = localStorage.getItem('user_id');
      if (userId) {
        await fetch('/api/search/record', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({ userId, term })
        });
      } else {
        // 如果没有用户ID，创建一个
        const newUserId = `u_${Math.random().toString(36).substring(2, 15)}`;
        localStorage.setItem('user_id', newUserId);
        await fetch('/api/search/record', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({ userId: newUserId, term })
        });
      }
    } catch (error) {
      console.error('记录搜索词失败:', error);
    }
  };

  // 处理搜索输入变化
  const handleSearchInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchTerm(value);
    
    // 使用防抖优化请求频率
    if (suggestionDebounceRef.current) {
      clearTimeout(suggestionDebounceRef.current);
    }
    
    suggestionDebounceRef.current = setTimeout(() => {
      fetchSuggestions(value);
    }, 300);
  };
  
  // 处理搜索建议点击
  const handleSuggestionClick = (suggestion: string) => {
    setSearchTerm(suggestion);
    setShowSuggestions(false);
    recordSearch(suggestion);
    
    // 重置所有筛选器并更新查询
    setCategory('all');
    setDuration('all');
    setTime('all');
    setQuality('all');
    setSort('relevance');
    setPage(1);
    setQuery(suggestion);
    
    // 跳转到搜索结果页
    router.push(`/search?q=${encodeURIComponent(suggestion)}`);
  };
  
  // 处理搜索表单提交
  const handleSearchSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchTerm.trim()) {
      recordSearch(searchTerm);
      
      // 重置所有筛选器并更新查询
      setCategory('all');
      setDuration('all');
      setTime('all');
      setQuality('all');
      setSort('relevance');
      setPage(1);
      setQuery(searchTerm);
      
      router.push(`/search?q=${encodeURIComponent(searchTerm)}`);
    }
  };
  
  // 点击外部关闭建议
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        suggestionsRef.current && 
        !suggestionsRef.current.contains(event.target as Node) &&
        searchInputRef.current &&
        !searchInputRef.current.contains(event.target as Node)
      ) {
        setShowSuggestions(false);
      }
    };
    
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);
  
  // 获取搜索结果
  useEffect(() => {
    const fetchResults = async () => {
      if (!query) {
        setResults([]);
        setLoading(false);
        return;
      }
      
      setLoading(true);
      console.log('开始获取搜索结果:', query);
      
      try {
        // 构建API URL
        const apiUrl = new URL('/api/search', window.location.origin);
        if (query) apiUrl.searchParams.append('q', query);
        if (category !== 'all') apiUrl.searchParams.append('category', category);
        if (duration !== 'all') apiUrl.searchParams.append('duration', duration);
        if (time !== 'all') apiUrl.searchParams.append('time', time);
        if (quality !== 'all') apiUrl.searchParams.append('quality', quality);
        if (sort !== 'relevance') apiUrl.searchParams.append('sort', sort);
        apiUrl.searchParams.append('page', page.toString());
        
        // 添加用户ID用于记录搜索
        const userId = localStorage.getItem('user_id');
        if (userId) {
          apiUrl.searchParams.append('userId', userId);
        }
        
        console.log('发送搜索请求:', apiUrl.toString());
        const response = await fetch(apiUrl.toString());
        
        if (response.ok) {
          const data = await response.json();
          console.log('搜索API响应:', data);
          if (data.status === 'success') {
            setResults(data.data.results);
            setTotalResults(data.data.total);
            setTotalPages(data.data.totalPages);
            console.log('获取到', data.data.results.length, '条搜索结果');
          } else {
            console.error('搜索API返回错误:', data.message);
            setResults([]);
          }
        } else {
          console.error('搜索请求失败:', response.statusText);
          setResults([]);
        }
      } catch (error) {
        console.error('获取搜索结果时出错:', error);
        setResults([]);
      } finally {
        setLoading(false);
      }
    };
    
    fetchResults();
  }, [query, category, duration, time, quality, sort, page]);
  
  // 动画效果
  useEffect(() => {
    if (!loading && results.length > 0) {
      videoRefs.current = videoRefs.current.slice(0, results.length);
      
      gsap.fromTo(
        videoRefs.current,
        { 
          opacity: 0,
          y: 20 
        },
        { 
          opacity: 1,
          y: 0,
          stagger: 0.05,
          duration: 0.4,
          ease: 'power2.out'
        }
      );
    }
  }, [loading, results]);
  
  // 设置引用
  const setRef = (el: HTMLDivElement | null, index: number) => {
    videoRefs.current[index] = el;
  };
  
  // 如果正在搜索中且没有结果，显示加载中
  if (loading) {
    return (
      <div className="container mx-auto px-4 py-10 relative">
        {/* 背景装饰 */}
        <div className="absolute inset-0 pointer-events-none">
          <div className="absolute top-1/4 left-1/4 w-32 h-32 bg-red-500/10 rounded-full filter blur-2xl"></div>
          <div className="absolute bottom-1/3 right-1/3 w-48 h-48 bg-blue-500/8 rounded-full filter blur-2xl"></div>
        </div>

        <h2 className="text-2xl font-bold mb-6 text-white">搜索中...</h2>
        <div className="flex justify-center items-center min-h-[300px]">
          <div className="text-center">
            <div className="relative">
              <div className="border-4 border-gray-700 border-t-white rounded-full animate-spin mx-auto mb-6 w-16 h-16"></div>
              <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-8 h-8 bg-white/20 rounded-full animate-pulse"></div>
            </div>
            <p className="text-gray-300 font-light">正在搜索...</p>
          </div>
        </div>
      </div>
    );
  }

  // 如果没有搜索结果
  if (query && !loading && results.length === 0) {
    return (
      <div className="container mx-auto px-4 py-10">
        <h2 className="text-2xl font-bold mb-6">搜索结果: {query}</h2>
        <div className="text-center py-20">
          <FiSearch size={50} className="mx-auto text-gray-600 mb-4" />
          <h3 className="text-xl font-medium mb-2">没有找到匹配的内容</h3>
          <p className="text-gray-400 mb-6">
            尝试使用不同的关键词或浏览我们的分类
          </p>
          <Link 
            href="/category/all" 
            className="px-5 py-2.5 bg-red-600 hover:bg-red-700 rounded-md text-white font-medium transition-all"
          >
            浏览全部分类
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-900 text-white pb-20">
      <div className="container mx-auto px-4 py-8">
        {/* 搜索表单 */}
        <div className="relative max-w-3xl mx-auto mb-8">
          <form onSubmit={handleSearchSubmit} className="relative">
            <div className="relative">
              <input
                ref={searchInputRef}
                type="text"
                value={searchTerm}
                onChange={handleSearchInputChange}
                onFocus={() => {
                  setIsInputFocused(true);
                  if (searchTerm.trim()) {
                    fetchSuggestions(searchTerm);
                  }
                }}
                placeholder="搜索你想看的内容..."
                className="w-full bg-gray-800 border border-gray-700 rounded-full pl-12 pr-4 py-3 outline-none focus:border-pink-500 transition-all"
              />
              <FiSearch className="absolute left-4 top-1/2 -translate-y-1/2 text-gray-400 text-xl" />
              {searchTerm && (
                <button
                  type="button"
                  onClick={() => setSearchTerm('')}
                  className="absolute right-16 top-1/2 -translate-y-1/2 text-gray-400 hover:text-white"
                >
                  <FiX />
                </button>
              )}
              <button
                type="submit"
                className="absolute right-3 top-1/2 -translate-y-1/2 bg-pink-600 hover:bg-pink-700 text-white rounded-full p-2 transition-colors"
              >
                <FiSearch className="text-lg" />
              </button>
            </div>
            
            {/* 搜索建议 */}
            {showSuggestions && suggestions.length > 0 && (
              <div 
                ref={suggestionsRef}
                className="absolute z-50 top-full left-0 right-0 mt-1 rounded-xl overflow-hidden bg-gray-800 border border-gray-700 shadow-xl"
              >
                <ul className="py-2">
                  {suggestions.map((suggestion, index) => (
                    <li key={index}>
                      <button
                        type="button"
                        onClick={() => handleSuggestionClick(suggestion)}
                        className="w-full text-left px-4 py-2 hover:bg-gray-700 flex items-center"
                      >
                        <FiSearch className="mr-2 text-gray-400" />
                        {suggestion}
                      </button>
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </form>
        </div>
        
        {/* 搜索结果 */}
        <div className="relative">
          {/* 筛选条件和排序 - 桌面版 */}
          <div className="md:flex mb-6 items-center justify-between">
            <div className="flex items-center space-x-4 mb-4 md:mb-0">
              <h1 className="text-xl font-bold">
                {query ? (
                  <>搜索 &quot;<span className="text-pink-500">{query}</span>&quot; 的结果</>
                ) : '搜索'}
              </h1>
              {query && <span className="text-gray-400">找到 {totalResults} 个结果</span>}
            </div>
            
            <div className="flex items-center justify-between md:justify-end w-full md:w-auto space-x-4">
              <button
                onClick={() => setShowFilters(!showFilters)}
                className="flex items-center space-x-2 text-sm bg-gray-800 hover:bg-gray-700 px-3 py-2 rounded-lg transition-colors"
              >
                <FiFilter />
                <span>{t('search.filters')}</span>
              </button>
              
              <div className="relative inline-block text-left">
                <select 
                  value={sort}
                  onChange={(e) => applyFilter('sort', e.target.value)}
                  className="bg-gray-800 border border-gray-700 text-white rounded-lg px-3 py-2 appearance-none cursor-pointer text-sm"
                >
                  <option value="relevance">{t('search.relevance')}</option>
                  <option value="recent">{t('search.recent')}</option>
                  <option value="views">{t('search.views')}</option>
                  <option value="oldest">{t('search.oldest')}</option>
                </select>
                <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-white">
                  <FiChevronDown className="text-gray-400" />
                </div>
              </div>
            </div>
          </div>
          
          {/* 显示筛选器 */}
          {showFilters && (
            <div 
              ref={filtersRef}
              className="lg:flex gap-8 mb-8 p-6 bg-gray-800 rounded-xl"
            >
              <div className="lg:w-3/4 grid sm:grid-cols-2 lg:grid-cols-4 gap-6">
                {/* 分类 */}
                <div>
                  <div 
                    onClick={() => toggleSection('category')}
                    className="flex justify-between items-center mb-3 cursor-pointer"
                  >
                    <h3 className="font-medium">{t('search.category')}</h3>
                    {expandedSections.category ? <FiChevronUp /> : <FiChevronDown />}
                  </div>
                  
                  {expandedSections.category && (
                    <div className="space-y-2">
                      {categories.map(cat => (
                        <div key={cat.value} className="flex items-center">
                          <input
                            type="radio"
                            id={`category-${cat.value}`}
                            name="category"
                            value={cat.value}
                            checked={category === cat.value}
                            onChange={() => applyFilter('category', cat.value)}
                            className="mr-2 accent-pink-500"
                          />
                          <label 
                            htmlFor={`category-${cat.value}`}
                            className="text-sm cursor-pointer"
                          >
                            {cat.label}
                          </label>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
                
                {/* 时长 */}
                <div>
                  <div 
                    onClick={() => toggleSection('duration')}
                    className="flex justify-between items-center mb-3 cursor-pointer"
                  >
                    <h3 className="font-medium">{t('search.duration')}</h3>
                    {expandedSections.duration ? <FiChevronUp /> : <FiChevronDown />}
                  </div>
                  
                  {expandedSections.duration && (
                    <div className="space-y-2">
                      {durations.map(dur => (
                        <div key={dur.value} className="flex items-center">
                          <input
                            type="radio"
                            id={`duration-${dur.value}`}
                            name="duration"
                            value={dur.value}
                            checked={duration === dur.value}
                            onChange={() => applyFilter('duration', dur.value)}
                            className="mr-2 accent-pink-500"
                          />
                          <label 
                            htmlFor={`duration-${dur.value}`}
                            className="text-sm cursor-pointer"
                          >
                            {dur.label}
                          </label>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
                
                {/* 上传时间 */}
                <div>
                  <div 
                    onClick={() => toggleSection('time')}
                    className="flex justify-between items-center mb-3 cursor-pointer"
                  >
                    <h3 className="font-medium">{t('search.uploadTime')}</h3>
                    {expandedSections.time ? <FiChevronUp /> : <FiChevronDown />}
                  </div>
                  
                  {expandedSections.time && (
                    <div className="space-y-2">
                      {uploadTimes.map(tim => (
                        <div key={tim.value} className="flex items-center">
                          <input
                            type="radio"
                            id={`time-${tim.value}`}
                            name="time"
                            value={tim.value}
                            checked={time === tim.value}
                            onChange={() => applyFilter('time', tim.value)}
                            className="mr-2 accent-pink-500"
                          />
                          <label 
                            htmlFor={`time-${tim.value}`}
                            className="text-sm cursor-pointer"
                          >
                            {tim.label}
                          </label>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
                
                {/* 清晰度 */}
                <div>
                  <div 
                    onClick={() => toggleSection('quality')}
                    className="flex justify-between items-center mb-3 cursor-pointer"
                  >
                    <h3 className="font-medium">{t('search.quality')}</h3>
                    {expandedSections.quality ? <FiChevronUp /> : <FiChevronDown />}
                  </div>
                  
                  {expandedSections.quality && (
                    <div className="space-y-2">
                      {qualities.map(qual => (
                        <div key={qual.value} className="flex items-center">
                          <input
                            type="radio"
                            id={`quality-${qual.value}`}
                            name="quality"
                            value={qual.value}
                            checked={quality === qual.value}
                            onChange={() => applyFilter('quality', qual.value)}
                            className="mr-2 accent-pink-500"
                          />
                          <label 
                            htmlFor={`quality-${qual.value}`}
                            className="text-sm cursor-pointer"
                          >
                            {qual.label}
                          </label>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>
              
              <div className="mt-6 lg:mt-0 flex justify-center lg:justify-end">
                <button
                  onClick={clearAllFilters}
                  className="text-sm bg-gray-700 hover:bg-gray-600 px-4 py-2 rounded-lg transition-colors"
                >
                  {t('search.clearAll')}
                </button>
              </div>
            </div>
          )}
          
          {/* 搜索结果内容 */}
          {loading ? (
            <div className="py-16 text-center">
              <div className="inline-block w-12 h-12 border-4 border-t-pink-500 border-gray-600 rounded-full animate-spin"></div>
              <p className="mt-4 text-gray-400">{t('search.searching')}</p>
            </div>
          ) : (
            <>
              {results.length > 0 ? (
                <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
                  {results.map((video, index) => (
                    <div 
                      key={video.id}
                      ref={(el) => setRef(el, index)}
                      className="bg-gray-800 rounded-lg overflow-hidden hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1"
                    >
                      <Link href={`/video/${video.slug}`}>
                        <div className="relative">
                          <Image 
                            src={video.thumbnail || '/images/placeholder.jpg'} 
                            alt={video.title}
                            width={320}
                            height={180}
                            className="w-full h-40 object-cover"
                          />
                          <div className="absolute bottom-2 right-2 bg-black bg-opacity-70 text-white text-xs px-2 py-1 rounded">
                            {video.duration}
                          </div>
                        </div>
                        <div className="p-3">
                          <h3 className="text-sm font-medium line-clamp-2 h-10">{video.title}</h3>
                          <div className="flex items-center text-xs text-gray-400 mt-2">
                            <div className="flex items-center mr-3">
                              <FiEye className="mr-1" /> 
                              {video.views?.toLocaleString() || 0}
                            </div>
                            <div className="flex items-center">
                              <FiClock className="mr-1" /> 
                              {video.timeAgo}
                            </div>
                          </div>
                        </div>
                      </Link>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="py-16 text-center">
                  {query ? (
                    <div>
                      <FiSearch className="text-5xl text-gray-500 mx-auto mb-4" />
                      <h2 className="text-xl font-bold mb-2">没有找到相关内容</h2>
                      <p className="text-gray-400 mb-4">尝试使用不同的关键词或者检查拼写</p>
                      <div className="flex flex-wrap justify-center gap-2 max-w-lg mx-auto">
                        <button 
                          onClick={() => handleSuggestionClick('新片')}
                          className="bg-gray-800 hover:bg-gray-700 px-4 py-2 rounded-full transition-colors"
                        >
                          新片
                        </button>
                        <button 
                          onClick={() => handleSuggestionClick('高清')}
                          className="bg-gray-800 hover:bg-gray-700 px-4 py-2 rounded-full transition-colors"
                        >
                          高清
                        </button>
                        <button 
                          onClick={() => handleSuggestionClick('制服')}
                          className="bg-gray-800 hover:bg-gray-700 px-4 py-2 rounded-full transition-colors"
                        >
                          制服
                        </button>
                        <button 
                          onClick={() => handleSuggestionClick('偶像')}
                          className="bg-gray-800 hover:bg-gray-700 px-4 py-2 rounded-full transition-colors"
                        >
                          偶像
                        </button>
                      </div>
                    </div>
                  ) : (
                    <div>
                      <FiSearch className="text-5xl text-gray-500 mx-auto mb-4" />
                      <h2 className="text-xl font-bold mb-2">输入关键词开始搜索</h2>
                      <p className="text-gray-400">在上方搜索框中输入你感兴趣的内容</p>
                    </div>
                  )}
                </div>
              )}
              
              {/* 分页 */}
              {totalPages > 1 && (
                <div className="mt-8 flex justify-center">
                  <div className="flex space-x-2">
                    {page > 1 && (
                      <button
                        onClick={() => {
                          setPage(page - 1);
                          const params = buildQueryParams({ page: (page - 1).toString() });
                          router.push(`/search?${params}`);
                        }}
                        className="px-4 py-2 bg-gray-800 hover:bg-gray-700 rounded-lg transition-colors"
                      >
                        上一页
                      </button>
                    )}
                    
                    {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                      const pageNum = i + 1;
                      return (
                        <button
                          key={i}
                          onClick={() => {
                            setPage(pageNum);
                            const params = buildQueryParams({ page: pageNum.toString() });
                            router.push(`/search?${params}`);
                          }}
                          className={`px-4 py-2 rounded-lg transition-colors ${
                            page === pageNum 
                              ? 'bg-pink-600 text-white' 
                              : 'bg-gray-800 hover:bg-gray-700'
                          }`}
                        >
                          {pageNum}
                        </button>
                      );
                    })}
                    
                    {page < totalPages && (
                      <button
                        onClick={() => {
                          setPage(page + 1);
                          const params = buildQueryParams({ page: (page + 1).toString() });
                          router.push(`/search?${params}`);
                        }}
                        className="px-4 py-2 bg-gray-800 hover:bg-gray-700 rounded-lg transition-colors"
                      >
                        下一页
                      </button>
                    )}
                  </div>
                </div>
              )}
            </>
          )}
        </div>
      </div>
    </div>
  );
} 