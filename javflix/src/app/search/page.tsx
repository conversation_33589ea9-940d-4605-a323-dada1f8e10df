import { Suspense } from 'react';
import { Metadata } from 'next';
import { SearchClient } from './client';

export const metadata: Metadata = {
  title: '搜索 - JAVFLIX',
  description: '搜索成千上万的高质量影片',
};

// 服务器端搜索页面
export default async function SearchPage({
  searchParams,
}: {
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>;
}) {
  // 在Next.js 15中，正确处理searchParams
  const params = await searchParams;
  const query = typeof params.q === 'string' ? params.q : '';
  const category = typeof params.category === 'string' ? params.category : 'all';
  const duration = typeof params.duration === 'string' ? params.duration : 'all';
  const time = typeof params.time === 'string' ? params.time : 'all';
  const quality = typeof params.quality === 'string' ? params.quality : 'all';
  const sort = typeof params.sort === 'string' ? params.sort : 'relevance';
  const page = typeof params.page === 'string' ? parseInt(params.page) : 1;

  return (
    <Suspense fallback={<div className="min-h-screen flex items-center justify-center">加载中...</div>}>
      <SearchClient 
        initialQuery={query}
        initialCategory={category}
        initialDuration={duration}
        initialTime={time}
        initialQuality={quality}
        initialSort={sort}
        initialPage={page}
      />
    </Suspense>
  );
} 