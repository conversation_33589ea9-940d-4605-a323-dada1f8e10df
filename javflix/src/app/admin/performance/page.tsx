/**
 * JAVFLIX.TV 性能监控仪表板
 * 基于专业性能监控最佳实践
 */

'use client';

import { useState, useEffect, useRef } from 'react';
import { FiMonitor, FiActivity, FiClock, FiDatabase, FiZap, FiTrendingUp, FiRefreshCw } from 'react-icons/fi';

interface PerformanceMetrics {
  responseTime: number;
  dbTime: number;
  cacheHitRate: number;
  throughput: number;
  errorRate: number;
  activeConnections: number;
  memoryUsage: number;
  cpuUsage: number;
}

interface APIEndpoint {
  path: string;
  method: string;
  avgResponseTime: number;
  requestCount: number;
  errorCount: number;
  cacheHits: number;
  status: 'healthy' | 'warning' | 'error';
}

export default function PerformanceDashboard() {
  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    responseTime: 0,
    dbTime: 0,
    cacheHitRate: 0,
    throughput: 0,
    errorRate: 0,
    activeConnections: 0,
    memoryUsage: 0,
    cpuUsage: 0
  });

  const [apiEndpoints, setApiEndpoints] = useState<APIEndpoint[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [lastUpdate, setLastUpdate] = useState<Date>(new Date());
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  // 获取性能指标
  const fetchMetrics = async () => {
    try {
      const response = await fetch('/api/admin/performance/metrics');
      if (response.ok) {
        const data = await response.json();
        setMetrics(data.metrics);
        setApiEndpoints(data.endpoints);
      }
    } catch (error) {
      console.error('获取性能指标失败:', error);
    } finally {
      setIsLoading(false);
      setLastUpdate(new Date());
    }
  };

  // 运行性能测试
  const runPerformanceTest = async () => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/admin/performance/test', {
        method: 'POST'
      });
      if (response.ok) {
        await fetchMetrics();
      }
    } catch (error) {
      console.error('性能测试失败:', error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchMetrics();
    
    // 每30秒自动刷新
    intervalRef.current = setInterval(fetchMetrics, 30000);
    
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, []);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy': return 'text-green-500';
      case 'warning': return 'text-yellow-500';
      case 'error': return 'text-red-500';
      default: return 'text-gray-500';
    }
  };

  const getResponseTimeColor = (time: number) => {
    if (time < 100) return 'text-green-500';
    if (time < 300) return 'text-yellow-500';
    return 'text-red-500';
  };

  return (
    <div className="min-h-screen bg-gray-900 text-white p-6">
      <div className="max-w-7xl mx-auto">
        {/* 标题区域 */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold flex items-center gap-3">
              <FiMonitor className="text-blue-500" />
              性能监控仪表板
            </h1>
            <p className="text-gray-400 mt-2">实时监控 JAVFLIX.TV 的系统性能指标</p>
          </div>
          
          <div className="flex gap-4">
            <button
              onClick={runPerformanceTest}
              disabled={isLoading}
              className="flex items-center gap-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded-lg transition-colors disabled:opacity-50"
            >
              <FiActivity />
              {isLoading ? '测试中...' : '运行测试'}
            </button>
            
            <button
              onClick={fetchMetrics}
              disabled={isLoading}
              className="flex items-center gap-2 px-4 py-2 bg-gray-700 hover:bg-gray-600 rounded-lg transition-colors disabled:opacity-50"
            >
              <FiRefreshCw className={isLoading ? 'animate-spin' : ''} />
              刷新
            </button>
          </div>
        </div>

        {/* 核心指标卡片 */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div className="bg-gray-800 rounded-xl p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm">平均响应时间</p>
                <p className={`text-2xl font-bold ${getResponseTimeColor(metrics.responseTime)}`}>
                  {metrics.responseTime}ms
                </p>
              </div>
              <FiClock className="text-blue-500 text-2xl" />
            </div>
            <div className="mt-2">
              <div className="w-full bg-gray-700 rounded-full h-2">
                <div 
                  className="bg-blue-500 h-2 rounded-full transition-all duration-500"
                  style={{ width: `${Math.min(metrics.responseTime / 5, 100)}%` }}
                ></div>
              </div>
            </div>
          </div>

          <div className="bg-gray-800 rounded-xl p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm">数据库查询时间</p>
                <p className={`text-2xl font-bold ${getResponseTimeColor(metrics.dbTime)}`}>
                  {metrics.dbTime}ms
                </p>
              </div>
              <FiDatabase className="text-green-500 text-2xl" />
            </div>
            <div className="mt-2">
              <div className="w-full bg-gray-700 rounded-full h-2">
                <div 
                  className="bg-green-500 h-2 rounded-full transition-all duration-500"
                  style={{ width: `${Math.min(metrics.dbTime / 3, 100)}%` }}
                ></div>
              </div>
            </div>
          </div>

          <div className="bg-gray-800 rounded-xl p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm">缓存命中率</p>
                <p className="text-2xl font-bold text-purple-500">
                  {metrics.cacheHitRate}%
                </p>
              </div>
              <FiZap className="text-purple-500 text-2xl" />
            </div>
            <div className="mt-2">
              <div className="w-full bg-gray-700 rounded-full h-2">
                <div 
                  className="bg-purple-500 h-2 rounded-full transition-all duration-500"
                  style={{ width: `${metrics.cacheHitRate}%` }}
                ></div>
              </div>
            </div>
          </div>

          <div className="bg-gray-800 rounded-xl p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm">请求吞吐量</p>
                <p className="text-2xl font-bold text-yellow-500">
                  {metrics.throughput} req/s
                </p>
              </div>
              <FiTrendingUp className="text-yellow-500 text-2xl" />
            </div>
            <div className="mt-2">
              <div className="w-full bg-gray-700 rounded-full h-2">
                <div 
                  className="bg-yellow-500 h-2 rounded-full transition-all duration-500"
                  style={{ width: `${Math.min(metrics.throughput / 10, 100)}%` }}
                ></div>
              </div>
            </div>
          </div>
        </div>

        {/* API端点性能表 */}
        <div className="bg-gray-800 rounded-xl p-6 mb-8">
          <h2 className="text-xl font-bold mb-4">API端点性能</h2>
          <div className="overflow-x-auto">
            <table className="w-full text-sm">
              <thead>
                <tr className="border-b border-gray-700">
                  <th className="text-left py-3 px-4">端点</th>
                  <th className="text-left py-3 px-4">方法</th>
                  <th className="text-left py-3 px-4">平均响应时间</th>
                  <th className="text-left py-3 px-4">请求次数</th>
                  <th className="text-left py-3 px-4">错误率</th>
                  <th className="text-left py-3 px-4">缓存命中</th>
                  <th className="text-left py-3 px-4">状态</th>
                </tr>
              </thead>
              <tbody>
                {apiEndpoints.map((endpoint, index) => (
                  <tr key={index} className="border-b border-gray-700/50">
                    <td className="py-3 px-4 font-mono text-blue-400">{endpoint.path}</td>
                    <td className="py-3 px-4">
                      <span className="px-2 py-1 bg-gray-700 rounded text-xs">
                        {endpoint.method}
                      </span>
                    </td>
                    <td className={`py-3 px-4 font-bold ${getResponseTimeColor(endpoint.avgResponseTime)}`}>
                      {endpoint.avgResponseTime}ms
                    </td>
                    <td className="py-3 px-4">{endpoint.requestCount.toLocaleString()}</td>
                    <td className="py-3 px-4">
                      <span className={endpoint.errorCount > 0 ? 'text-red-500' : 'text-green-500'}>
                        {((endpoint.errorCount / endpoint.requestCount) * 100).toFixed(1)}%
                      </span>
                    </td>
                    <td className="py-3 px-4 text-purple-400">
                      {endpoint.cacheHits}
                    </td>
                    <td className="py-3 px-4">
                      <span className={`px-2 py-1 rounded text-xs ${
                        endpoint.status === 'healthy' ? 'bg-green-900 text-green-300' :
                        endpoint.status === 'warning' ? 'bg-yellow-900 text-yellow-300' :
                        'bg-red-900 text-red-300'
                      }`}>
                        {endpoint.status === 'healthy' ? '正常' :
                         endpoint.status === 'warning' ? '警告' : '错误'}
                      </span>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* 性能建议 */}
        <div className="bg-gray-800 rounded-xl p-6">
          <h2 className="text-xl font-bold mb-4">性能优化建议</h2>
          <div className="space-y-3">
            {metrics.responseTime > 300 && (
              <div className="flex items-start gap-3 p-3 bg-red-900/20 border border-red-800 rounded">
                <FiActivity className="text-red-500 mt-1" />
                <div>
                  <p className="font-semibold text-red-400">响应时间过长</p>
                  <p className="text-sm text-gray-300">当前平均响应时间 {metrics.responseTime}ms，建议优化数据库查询和启用缓存</p>
                </div>
              </div>
            )}
            
            {metrics.cacheHitRate < 70 && (
              <div className="flex items-start gap-3 p-3 bg-yellow-900/20 border border-yellow-800 rounded">
                <FiZap className="text-yellow-500 mt-1" />
                <div>
                  <p className="font-semibold text-yellow-400">缓存命中率偏低</p>
                  <p className="text-sm text-gray-300">当前缓存命中率 {metrics.cacheHitRate}%，建议增加缓存策略和时间</p>
                </div>
              </div>
            )}

            {metrics.dbTime > 200 && (
              <div className="flex items-start gap-3 p-3 bg-orange-900/20 border border-orange-800 rounded">
                <FiDatabase className="text-orange-500 mt-1" />
                <div>
                  <p className="font-semibold text-orange-400">数据库查询需要优化</p>
                  <p className="text-sm text-gray-300">数据库查询时间 {metrics.dbTime}ms，建议添加索引和优化SQL查询</p>
                </div>
              </div>
            )}

            {metrics.responseTime < 200 && metrics.cacheHitRate > 80 && (
              <div className="flex items-start gap-3 p-3 bg-green-900/20 border border-green-800 rounded">
                <FiTrendingUp className="text-green-500 mt-1" />
                <div>
                  <p className="font-semibold text-green-400">性能表现优秀</p>
                  <p className="text-sm text-gray-300">当前系统性能良好，响应时间和缓存命中率都在理想范围内</p>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* 更新时间 */}
        <div className="text-center text-gray-400 text-sm mt-6">
          最后更新: {lastUpdate.toLocaleString('zh-CN')}
        </div>
      </div>
    </div>
  );
}