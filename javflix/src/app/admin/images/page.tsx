'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';

export default function ImagesManagementPage() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [results, setResults] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);
  
  // 采集图片
  const handleScrapeImages = async (type: string) => {
    setIsLoading(true);
    setError(null);
    setResults(null);
    
    try {
      const response = await fetch(`/api/image-scraper?type=${type}`);
      const data = await response.json();
      
      if (data.status === 'success') {
        setResults(data.results);
      } else {
        setError(data.message || '采集图片失败');
      }
    } catch (err) {
      setError('请求失败: ' + (err instanceof Error ? err.message : String(err)));
    } finally {
      setIsLoading(false);
    }
  };
  
  // 更新缓存URL
  const handleUpdateCacheUrls = async (type: string = 'all') => {
    setIsLoading(true);
    setError(null);
    setResults(null);
    
    try {
      const response = await fetch(`/api/cache-update?type=${type}`);
      const data = await response.json();
      
      if (data.status === 'success') {
        setResults(data.results);
      } else {
        setError(data.message || '更新缓存URL失败');
      }
    } catch (err) {
      setError('请求失败: ' + (err instanceof Error ? err.message : String(err)));
    } finally {
      setIsLoading(false);
    }
  };
  
  return (
    <div className="container mx-auto p-4">
      <h1 className="text-2xl font-bold mb-6 text-white">图片管理</h1>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* 图片采集卡片 */}
        <div className="bg-gray-800 rounded-lg p-6 shadow-lg">
          <h2 className="text-xl font-semibold mb-4 text-white">图片采集</h2>
          <p className="text-gray-300 mb-4">从JAVBUS采集图片并缓存到本地</p>
          
          <div className="flex flex-wrap gap-2 mb-4">
            <button
              className="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded disabled:opacity-50"
              onClick={() => handleScrapeImages('actress')}
              disabled={isLoading}
            >
              采集女优图片
            </button>
            
            <button
              className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded disabled:opacity-50"
              onClick={() => handleScrapeImages('cover')}
              disabled={isLoading}
            >
              采集封面图片
            </button>
            
            <button
              className="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded disabled:opacity-50"
              onClick={() => handleScrapeImages('sample')}
              disabled={isLoading}
            >
              采集样品图片
            </button>
            
            <button
              className="px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded disabled:opacity-50"
              onClick={() => handleScrapeImages('all')}
              disabled={isLoading}
            >
              采集全部图片
            </button>
          </div>
        </div>
        
        {/* 缓存URL更新卡片 */}
        <div className="bg-gray-800 rounded-lg p-6 shadow-lg">
          <h2 className="text-xl font-semibold mb-4 text-white">缓存URL更新</h2>
          <p className="text-gray-300 mb-4">扫描已缓存图片，更新数据库中的缓存URL</p>
          
          <div className="flex flex-wrap gap-2 mb-4">
            <button
              className="px-4 py-2 bg-yellow-600 hover:bg-yellow-700 text-white rounded disabled:opacity-50"
              onClick={() => handleUpdateCacheUrls('actress')}
              disabled={isLoading}
            >
              更新女优图片URL
            </button>
            
            <button
              className="px-4 py-2 bg-orange-600 hover:bg-orange-700 text-white rounded disabled:opacity-50"
              onClick={() => handleUpdateCacheUrls('cover')}
              disabled={isLoading}
            >
              更新封面图片URL
            </button>
            
            <button
              className="px-4 py-2 bg-teal-600 hover:bg-teal-700 text-white rounded disabled:opacity-50"
              onClick={() => handleUpdateCacheUrls()}
              disabled={isLoading}
            >
              更新所有缓存URL
            </button>
          </div>
        </div>
      </div>
      
      {/* 加载中提示 */}
      {isLoading && (
        <div className="mt-6 p-4 bg-gray-800 rounded-lg">
          <p className="text-white">处理中，请稍候...</p>
        </div>
      )}
      
      {/* 错误信息 */}
      {error && (
        <div className="mt-6 p-4 bg-red-900 rounded-lg">
          <h3 className="text-lg font-medium text-white mb-2">出错了</h3>
          <p className="text-red-100">{error}</p>
        </div>
      )}
      
      {/* 结果显示 */}
      {results && (
        <div className="mt-6 p-4 bg-gray-800 rounded-lg">
          <h3 className="text-lg font-medium text-white mb-2">处理结果</h3>
          
          <div className="overflow-x-auto">
            <pre className="text-gray-300 text-sm">
              {JSON.stringify(results, null, 2)}
            </pre>
          </div>
        </div>
      )}
    </div>
  );
} 