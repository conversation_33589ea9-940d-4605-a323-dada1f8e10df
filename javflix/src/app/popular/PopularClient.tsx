'use client';

import { useEffect, useState, useRef } from 'react';
import { usePathname, useParams } from 'next/navigation';
import VideoCard from '@/components/VideoCard';
import { useClientTranslations } from '@/components/TranslationsProvider';
import gsap from 'gsap';
import { FiTrendingUp, FiFilter } from 'react-icons/fi';

// 模拟视频数据
const MOCK_VIDEOS = [
  { id: '1', title: 'SSIS-862 暴风雨后和女上司二人独处的夜晚', coverImage: '/images/1Ayvrd.jpg', duration: '2:12:45', views: 125600, likes: 5240, releaseDate: '2023-11-15', code: 'SSIS-862' },
  { id: '2', title: 'JUL-868 夫の上司に犯され続けて7日目、私は理性を失った', coverImage: '/images/2mKN5y.jpg', duration: '1:58:32', views: 98700, likes: 4320, releaseDate: '2023-11-12', code: 'JUL-868' },
  { id: '3', title: 'IPX-939 超高級泡店に売られた人気ファッションモデル', coverImage: '/images/3nydbz.jpg', duration: '1:54:18', views: 112400, likes: 6750, releaseDate: '2023-11-08', code: 'IPX-939' },
  { id: '4', title: 'MIDE-633 ずるむけ半熟子宮 お姉さんにオギャりたい', coverImage: '/images/4DKBXJ.jpg', duration: '2:01:24', views: 87500, likes: 3980, releaseDate: '2023-11-05', code: 'MIDE-633' },
  { id: '5', title: 'FSDSS-388 腹筋バキバキ強さと美しさ両立の元アスリート現役女子大生AV', coverImage: '/images/5nK11a.jpg', duration: '1:49:52', views: 152300, likes: 8940, releaseDate: '2023-11-01', code: 'FSDSS-388' },
  { id: '6', title: 'PRED-350 最高級おもてなしソープ嬢 ローションぬるぬる恥虐の幼妻', coverImage: '/images/6deyV7.jpg', duration: '2:05:37', views: 67800, likes: 2890, releaseDate: '2023-10-28', code: 'PRED-350' },
  { id: '7', title: 'STARS-508 揮発性誘惑 書き下ろしが刺激的過ぎて', coverImage: '/images/7yKgJ1.jpg', duration: '1:58:10', views: 93600, likes: 4120, releaseDate: '2023-10-25', code: 'STARS-508' },
  { id: '8', title: 'ABW-078 神対応ギャルまりんにひたすら犯されたい', coverImage: '/images/82Ez9x.jpg', duration: '2:10:43', views: 74900, likes: 3560, releaseDate: '2023-10-22', code: 'ABW-078' },
  { id: '9', title: 'SSNI-954 逆NTR媚薬 家庭教師の僕に依存してハマる若妻', coverImage: '/images/96e0Bg.jpg', duration: '1:53:21', views: 118700, likes: 7240, releaseDate: '2023-10-19', code: 'SSNI-954' },
  { id: '10', title: 'MEYD-722 溜池ゴロー特選 人妻官能占い師', coverImage: '/images/0ey9pq.jpg', duration: '2:07:09', views: 86300, likes: 4780, releaseDate: '2023-10-15', code: 'MEYD-722' },
];

const PopularClient = () => {
  const { t } = useClientTranslations();
  const params = useParams();
  const locale = params?.locale || 'zh-CN';
  
  const [videos, setVideos] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [sortBy, setSortBy] = useState('views');
  const pathname = usePathname();
  
  // 动画refs
  const titleRef = useRef<HTMLHeadingElement>(null);
  const subtitleRef = useRef<HTMLParagraphElement>(null);
  const controlsRef = useRef<HTMLDivElement>(null);
  const gridRef = useRef<HTMLDivElement>(null);
  
  // 从路径中提取子类型
  const getPopularType = () => {
    // 提取类型 - 例如从 '/zh-CN/popular/weekly' 中提取 'weekly'
    const segments = pathname.split('/');
    const type = segments.length > 3 ? segments[3] : 'default';
    
    // 有效的类型
    const validTypes = ['weekly', 'monthly', 'yearly', 'newcomers', 'hd', '4k'];
    
    // 如果类型有效，返回该类型，否则返回默认
    return validTypes.includes(type) ? type : 'default';
  };
  
  // 获取当前热门类型
  const popularType = getPopularType();
  
  useEffect(() => {
    // 页面加载动画
    gsap.fromTo(
      titleRef.current,
      { y: -30, opacity: 0 },
      { y: 0, opacity: 1, duration: 0.8, ease: 'power3.out' }
    );
    
    gsap.fromTo(
      subtitleRef.current,
      { y: -20, opacity: 0 },
      { y: 0, opacity: 1, duration: 0.8, delay: 0.2, ease: 'power3.out' }
    );
    
    gsap.fromTo(
      controlsRef.current,
      { y: 20, opacity: 0 },
      { y: 0, opacity: 1, duration: 0.8, delay: 0.4, ease: 'power2.out' }
    );
    
    gsap.fromTo(
      gridRef.current,
      { y: 30, opacity: 0 },
      { y: 0, opacity: 1, duration: 0.8, delay: 0.6, ease: 'power2.out' }
    );
  }, []);
  
  useEffect(() => {
    const fetchVideos = async () => {
      try {
        setLoading(true);
        
        // 模拟API请求延迟
        await new Promise(resolve => setTimeout(resolve, 800));
        
        // 使用模拟数据而不是发起实际API请求
        // 在实际API实现后，可以替换为真实请求
        // const endpoint = `/api/videos/popular?type=${popularType}`;
        // const res = await fetch(endpoint);
        // const data = await res.json();
        // setVideos(data.videos || []);
        
        // 根据排序方式排序模拟数据
        let sortedVideos = [...MOCK_VIDEOS];
        if (sortBy === 'views') {
          sortedVideos.sort((a, b) => b.views - a.views);
        } else if (sortBy === 'likes') {
          sortedVideos.sort((a, b) => b.likes - a.likes);
        } else if (sortBy === 'date') {
          sortedVideos.sort((a, b) => new Date(b.releaseDate).getTime() - new Date(a.releaseDate).getTime());
        }
        
        setVideos(sortedVideos);
      } catch (err: any) {
        console.error('Error fetching popular videos:', err);
        setError(err.message || 'Failed to load popular videos');
      } finally {
        setLoading(false);
      }
    };

    fetchVideos();
  }, [popularType, sortBy]);
    
  // 设置页面标题
  let pageTitle = t('nav.popular') || '热门视频';
  if (popularType !== 'default') {
    const titleMap: Record<string, string> = {
      'weekly': t('common.weekly') || '本周热门',
      'monthly': t('common.monthly') || '本月热门',
      'yearly': t('common.yearly') || '年度热门',
      'newcomers': t('common.newcomers') || '新人热门',
      'hd': t('popular.hdPopular') || 'HD热门',
      '4k': t('popular.4kPopular') || '4K热门',
    };
    pageTitle = titleMap[popularType] || pageTitle;
  }

  // 格式化数量显示
  const formatCount = (count: number): string => {
    if (count >= 1000000) {
      return `${(count / 1000000).toFixed(1)}M`;
    }
    if (count >= 1000) {
      return `${(count / 1000).toFixed(1)}K`;
    }
    return count.toString();
  };

  if (error) {
    return (
      <div className="min-h-screen bg-gray-900 text-white pt-20 pb-12">
        <div className="container mx-auto px-4">
          <div className="text-center py-20">
            <div className="text-6xl mb-4">😞</div>
            <h3 className="text-xl font-medium mb-2 text-gray-300">{t('common.loadingFailed')}</h3>
            <p className="text-gray-500">{error}</p>
          </div>
        </div>
      </div>
    );
  }
  
  return (
    <div className="min-h-screen bg-gray-900 text-white pt-20 pb-12">
      <div className="container mx-auto px-4">
        {/* 页面标题 - 参考女优页面风格 */}
        <div className="text-center mb-12">
          <h1 
            ref={titleRef}
            className="text-4xl md:text-5xl font-bold mb-4 bg-gradient-to-r from-white via-gray-200 to-gray-400 bg-clip-text text-transparent"
          >
            <FiTrendingUp className="inline-block mr-3 text-red-500" />
            {pageTitle}
          </h1>
          <p 
            ref={subtitleRef}
            className="text-gray-400 text-lg"
          >
            {videos.length > 0 && (
              t('common.discoveredVideos', { count: formatCount(videos.length) })
            )}
          </p>
        </div>
        
        {/* 控制栏 */}
        <div ref={controlsRef} className="max-w-md mx-auto mb-12">
          <div className="flex justify-center">
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value)}
              className="bg-gray-800/30 border border-gray-700/30 rounded-full px-6 py-3 text-sm text-gray-300 focus:outline-none focus:border-red-500/50 transition-all duration-300 backdrop-blur-sm"
            >
              <option value="views">{t('common.sortByViews')}</option>
              <option value="likes">{t('common.sortByLikes')}</option>
              <option value="date">{t('common.sortByDate')}</option>
            </select>
          </div>
        </div>
        
        {/* 视频网格 */}
        {loading ? (
          <div className="flex justify-center items-center min-h-[400px]">
            <div className="text-center">
              <div className="relative">
                <div className="animate-spin rounded-full h-12 w-12 border-2 border-gray-700 border-t-red-500 mx-auto mb-4"></div>
              </div>
              <p className="text-gray-400">{t('common.loading') || '正在加载...'}</p>
            </div>
          </div>
        ) : (
          <div ref={gridRef} className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-6">
            {videos.map((video) => (
              <VideoCard 
                key={video.id} 
                video={video} 
                locale={locale as string}
                size="medium"
              />
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default PopularClient; 