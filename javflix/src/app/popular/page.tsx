import type { Metadata } from 'next';
import { generateMetadata as createMetadata } from '@/lib/seo';
import PopularClient from './PopularClient';

// 导出静态元数据
export const metadata: Metadata = createMetadata({
  title: '热门人气视频',
  description: 'JAVFLIX最受欢迎的日本AV视频合集，高清画质，稳定观看体验，精选热门番号作品，无码有码应有尽有',
  path: '/popular',
  keywords: [
    '热门AV', '人气作品', '日本AV', '高清视频', '无码有码', 
    '中文字幕', 'JAV', '票选最高', '最多观看'
  ],
});

// 主页面是服务器组件，只用于包装客户端组件
export default function PopularPage() {
  return <PopularClient />;
} 