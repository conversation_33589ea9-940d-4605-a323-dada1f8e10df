'use client';

import React, { useEffect, useRef } from 'react';
import Link from 'next/link';
import gsap from 'gsap';
import { FiArrowLeft, FiHome, FiSearch } from 'react-icons/fi';

export default function NotFoundPage() {
  const containerRef = useRef<HTMLDivElement>(null);
  const titleRef = useRef<HTMLHeadingElement>(null);
  const messageRef = useRef<HTMLParagraphElement>(null);
  const buttonsRef = useRef<HTMLDivElement>(null);
  const suggestionRef = useRef<HTMLDivElement>(null);
  
  useEffect(() => {
    const tl = gsap.timeline({ defaults: { ease: 'power3.out' } });
    
    // 渐入动画
    if (titleRef.current) {
      tl.fromTo(
        titleRef.current,
        { y: -50, opacity: 0 },
        { y: 0, opacity: 1, duration: 0.8 }
      );
    }
    
    if (messageRef.current) {
      tl.fromTo(
        messageRef.current,
        { y: -20, opacity: 0 },
        { y: 0, opacity: 1, duration: 0.6 },
        "-=0.4"
      );
    }
    
    if (buttonsRef.current) {
      tl.fromTo(
        buttonsRef.current,
        { y: 20, opacity: 0 },
        { y: 0, opacity: 1, duration: 0.6 },
        "-=0.3"
      );
    }
    
    if (suggestionRef.current) {
      tl.fromTo(
        suggestionRef.current,
        { y: 20, opacity: 0 },
        { y: 0, opacity: 1, duration: 0.6 },
        "-=0.3"
      );
    }
    
    // 背景动画
    if (containerRef.current) {
      const moveX = gsap.utils.random(-20, 20);
      const moveY = gsap.utils.random(-20, 20);
      
      gsap.to(containerRef.current, {
        backgroundPosition: `${moveX}px ${moveY}px`,
        duration: 8,
        repeat: -1,
        yoyo: true,
        ease: 'sine.inOut'
      });
    }
  }, []);
  
  return (
    <div 
      ref={containerRef}
      className="min-h-screen flex items-center justify-center px-4 py-16 bg-gradient-to-br from-gray-900 via-black to-gray-800 relative"
      style={{ backgroundSize: '120% 120%', backgroundPosition: 'center' }}
    >
      {/* 装饰元素 */}
      <div className="absolute inset-0 overflow-hidden opacity-20 pointer-events-none">
        <div className="absolute top-1/4 left-1/4 w-64 h-64 bg-red-500 rounded-full blur-[100px]"></div>
        <div className="absolute bottom-1/3 right-1/3 w-80 h-80 bg-purple-600 rounded-full blur-[120px]"></div>
      </div>
      
      <div className="max-w-xl w-full p-8 bg-gray-900 bg-opacity-80 backdrop-blur-sm rounded-xl shadow-2xl border border-gray-800/50 z-10">
        <div className="text-center">
          <h1 
            ref={titleRef} 
            className="text-7xl md:text-9xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-red-500 to-red-700"
          >
            404
          </h1>
          
          <p 
            ref={messageRef} 
            className="mt-6 text-xl md:text-2xl text-white"
          >
            哎呀！您访问的页面好像走丢了
          </p>
          
          <div ref={buttonsRef} className="mt-8 flex flex-col sm:flex-row gap-4 justify-center">
            <Link 
              href="/"
              className="inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-lg text-white bg-red-600 hover:bg-red-700 transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
            >
              <FiHome className="mr-2" />
              返回首页
            </Link>
            
            <Link 
              href="javascript:history.back()"
              className="inline-flex items-center justify-center px-6 py-3 border border-gray-700 text-base font-medium rounded-lg text-white bg-gray-800 hover:bg-gray-700 transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
            >
              <FiArrowLeft className="mr-2" />
              返回上一页
            </Link>
          </div>
        </div>
        
        <div ref={suggestionRef} className="mt-10 pt-8 border-t border-gray-800">
          <h2 className="text-lg font-medium text-white mb-4">您可能想要尝试:</h2>
          
          <div className="space-y-4">
            <div className="flex items-start">
              <FiSearch className="mt-1 mr-3 text-red-500" />
              <div>
                <h3 className="font-medium text-white">搜索您想要的内容</h3>
                <p className="text-gray-400 text-sm mt-1">使用顶部的搜索框查找您感兴趣的视频或女优。</p>
              </div>
            </div>
            
            <div className="flex items-start">
              <FiHome className="mt-1 mr-3 text-red-500" />
              <div>
                <h3 className="font-medium text-white">浏览热门内容</h3>
                <p className="text-gray-400 text-sm mt-1">查看<Link href="/popular" className="text-red-500 hover:text-red-400">热门视频</Link>或<Link href="/new" className="text-red-500 hover:text-red-400">最新视频</Link>，发现精彩内容。</p>
              </div>
            </div>
            
            <div className="text-center mt-8 text-sm text-gray-500">
              如有任何问题，请<Link href="/contact" className="text-red-500 hover:text-red-400">联系我们</Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
} 