'use client';

import { useState, useEffect, useRef, useCallback } from 'react';
import { useRouter, useParams } from 'next/navigation';
import Image from 'next/image';
import Link from 'next/link';
import { 
  FiUser, FiHeart, FiClock, FiSettings, FiLogOut, 
  FiEdit2, FiPlus, FiCalendar, FiMail, FiShield, 
  FiStar, FiThumbsUp, FiThumbsDown, FiEye
} from 'react-icons/fi';
import gsap from 'gsap';
import { useClientTranslations } from '@/components/TranslationsProvider';
// 导入API客户端函数
import { getUserFavorites, removeFromFavorites } from '@/lib/user-client';
import { getUserHistory, addToHistory } from '@/lib/user-client';
import { getCurrentUser, logoutUser } from '@/lib/auth-client';
import { 
  getUserPreferences, updateUserPreferences, 
  getFollowedStars, followStar, unfollowStar,
  getRecommendations, submitRecommendationFeedback,
  getRealUserFavorites, getUserLikes
} from '@/lib/user-client';

// 数据类型
interface UserData {
  id: string;
  username: string;
  email: string;
  avatar?: string;
  avatarUrl?: string;
  createdAt?: string;
  created_at?: string;
  lastLoginAt?: string;
  watchedCount?: number;
  favoriteCount?: number;
}

interface VideoData {
  id: number;
  title: string;
  slug: string;
  duration: string;
  imageUrl: string;
  views: number;
  releaseDate?: string;
  createdAt?: string;
  progress?: number;
  isCompleted?: boolean;
  watchedAt?: string;
  date?: string; // 仅用于前端展示
}

interface ActressData {
  id: number;
  name: string;
  slug: string;
  imageUrl: string;
  videos: number;
  followedAt?: string;
}

// 标签类型
type TabType = 'favorites' | 'history' | 'settings' | 'following' | 'recommendations' | 'likes';

const ProfileClient = () => {
  const { t } = useClientTranslations();
  const params = useParams();
  const locale = params?.locale?.toString() || 'zh-CN';
  
  const [activeTab, setActiveTab] = useState<TabType>('favorites');
  const [isLoading, setIsLoading] = useState(true);
  const [logoutLoading, setLogoutLoading] = useState(false);
  const [userData, setUserData] = useState<UserData | null>(null);
  const [favoriteVideos, setFavoriteVideos] = useState<VideoData[]>([]);
  const [historyVideos, setHistoryVideos] = useState<VideoData[]>([]);
  const [likedVideos, setLikedVideos] = useState<VideoData[]>([]);
  const [followedActresses, setFollowedActresses] = useState<ActressData[]>([]);
  const [recommendedVideos, setRecommendedVideos] = useState<VideoData[]>([]);
  const [userPreferences, setUserPreferences] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);
  
  const profileRef = useRef<HTMLDivElement>(null);
  const contentRef = useRef<HTMLDivElement>(null);
  const router = useRouter();
  
  // 格式化日期
  const formatDate = useCallback((dateString?: string) => {
    if (!dateString) return t('common.unknown') || '未知';
    
    const date = new Date(dateString);
    return date.toISOString().split('T')[0]; // 简单格式化为 YYYY-MM-DD
  }, [t]);
  
  // 格式化相对时间
  const formatRelativeTime = useCallback((dateString?: string) => {
    if (!dateString) return t('common.unknown') || '未知';
    
    const date = new Date(dateString);
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);
    
    if (diffInSeconds < 60) {
      return t('profile.time.justNow') || '刚刚';
    } else if (diffInSeconds < 3600) {
      return t('profile.time.minutesAgo', { minutes: Math.floor(diffInSeconds / 60) }) || `${Math.floor(diffInSeconds / 60)}分钟前`;
    } else if (diffInSeconds < 86400) {
      return t('profile.time.hoursAgo', { hours: Math.floor(diffInSeconds / 3600) }) || `${Math.floor(diffInSeconds / 3600)}小时前`;
    } else if (diffInSeconds < 604800) {
      return t('profile.time.daysAgo', { days: Math.floor(diffInSeconds / 86400) }) || `${Math.floor(diffInSeconds / 86400)}天前`;
    } else {
      return formatDate(dateString);
    }
  }, [formatDate, t]);
  
  // 格式化浏览量
  const formatViews = (views: number) => {
    if (views >= 1000000) {
      return `${(views / 1000000).toFixed(1)}M`;
    } else if (views >= 1000) {
      return `${(views / 1000).toFixed(1)}K`;
    } else {
      return views.toString();
    }
  };
  
  // 获取用户数据
  useEffect(() => {
    async function fetchUserData() {
      try {
        setIsLoading(true);
        
        // 获取真实用户数据
        const user = await getCurrentUser();
        if (user) {
          setUserData(user);
        } else {
          // 如果获取失败，使用模拟数据
          const mockUserData: UserData = {
            id: '12345',
            username: '爱看片的小王',
            email: '<EMAIL>',
            avatar: '/images/avatar.jpg',
            createdAt: '2022-05-10T08:15:30Z',
            lastLoginAt: '2023-06-15T14:22:45Z',
            watchedCount: 128,
            favoriteCount: 45
          };
          
          setUserData(mockUserData);
        }
        
        // 获取用户偏好设置
        const preferences = await getUserPreferences();
        if (preferences) {
          setUserPreferences(preferences);
        }
      } catch (error) {
        console.error('获取用户数据出错:', error);
      } finally {
        setIsLoading(false);
      }
    }
    
    fetchUserData();
  }, []);
  
  // 获取收藏视频
  useEffect(() => {
    async function fetchFavorites() {
      if (!userData) return;
      
      try {
        // 使用客户端API获取收藏列表
        const favoritesData = await getRealUserFavorites(1, 8); // 只展示前8个
        
        if (favoritesData.data && favoritesData.data.length > 0) {
          // 转换数据结构以匹配组件所需
          const formattedFavorites = favoritesData.data.map(video => ({
            id: Number(video.id) || 0,
            title: video.title,
            slug: video.slug || video.movie_id || '',
            duration: video.duration || '00:00',
            imageUrl: video.image_url || video.thumbnail_url || video.thumbnail || '/images/placeholder.jpg',
            views: video.view_count || video.views || 0,
            date: video.timeAgo || formatRelativeTime(video.created_at?.toString())
          }));
          
          setFavoriteVideos(formattedFavorites);
          
          // 更新收藏计数
          if (userData && favoritesData.pagination) {
            setUserData({
              ...userData,
              favoriteCount: favoritesData.pagination.total
            });
          }
        }
      } catch (error) {
        console.error('获取收藏视频出错:', error);
      }
    }
    
    fetchFavorites();
  }, [userData, formatRelativeTime]);
  
  // 获取观看历史
  useEffect(() => {
    async function fetchHistory() {
      if (!userData) return;
      
      try {
        // 使用客户端API获取观看历史
        const historyData = await getUserHistory(1, 8); // 只展示前8个
        
        if (historyData.data && historyData.data.length > 0) {
          // 转换数据结构以匹配组件所需
          const formattedHistory = historyData.data.map((video: any) => ({
            id: Number(video.id) || 0,
            title: video.title,
            slug: video.slug || video.movie_id || '',
            duration: video.duration || '00:00',
            imageUrl: video.image_url || video.thumbnail_url || video.thumbnail || '/images/placeholder.jpg',
            views: video.view_count || video.views || 0,
            progress: video.progress || 0,
            isCompleted: video.progress >= 95, // 观看进度超过95%视为已完成
            date: video.timeAgo || formatRelativeTime(video.watched_at?.toString() || video.created_at?.toString()),
            watchedAt: video.watched_at?.toString() || video.created_at?.toString()
          }));
          
          setHistoryVideos(formattedHistory);
          
          // 更新观看计数
          if (userData && historyData.pagination) {
            setUserData({
              ...userData,
              watchedCount: historyData.pagination.total
            });
          }
        }
      } catch (error) {
        console.error('获取观看历史出错:', error);
      }
    }
    
    fetchHistory();
  }, [userData, formatRelativeTime]);
  
  // 获取关注的女优
  useEffect(() => {
    async function fetchFollowedActresses() {
      if (!userData) return;
      
      try {
        const followedStarsData = await getFollowedStars(1, 8);
        
        if (followedStarsData && followedStarsData.data.length > 0) {
          // 转换数据结构以匹配组件所需格式
          const formattedActresses = followedStarsData.data.map(star => ({
            id: star.id,
            name: star.name,
            slug: star.star_id,
            imageUrl: star.image_url || star.cached_image_url || '/images/placeholder.jpg',
            videos: 0, // 这个信息API中没有提供，可能需要额外请求
            followedAt: star.followed_at
          }));
          
          setFollowedActresses(formattedActresses);
        }
      } catch (error) {
        console.error('获取关注演员失败:', error);
      }
    }
    
    fetchFollowedActresses();
  }, [userData]);
  
  // 获取个性化推荐
  useEffect(() => {
    async function fetchRecommendations() {
      if (!userData) return;
      
      try {
        const recommendationsData = await getRecommendations(8);
        
        if (recommendationsData && recommendationsData.length > 0) {
          // 转换数据结构以匹配组件所需
          const formattedRecommendations = recommendationsData.map(video => ({
            id: Number(video.id),
            title: video.title,
            slug: video.movie_id || '',
            duration: video.duration || '00:00',
            imageUrl: video.image_url || video.cover_image || '/images/placeholder.jpg',
            views: 0, // 这个信息API中没有提供
            releaseDate: video.release_date
          }));
          
          setRecommendedVideos(formattedRecommendations);
        }
      } catch (error) {
        console.error('获取个性化推荐失败:', error);
      }
    }
    
    fetchRecommendations();
  }, [userData]);
  
  // 获取点赞视频
  useEffect(() => {
    async function fetchLikedVideos() {
      if (!userData) return;
      
      try {
        console.log('开始获取点赞视频数据...');
        
        // 使用客户端API获取点赞列表
        const likesData = await getUserLikes(1, 8); // 只展示前8个
        
        console.log('点赞API返回数据:', likesData);
        console.log('点赞数据类型:', typeof likesData);
        console.log('点赞数据data属性:', likesData.data);
        console.log('data是否为数组:', Array.isArray(likesData.data));
        
        // 获取视频数组
        let videosArray: any[] = [];
        
        if (likesData && likesData.data && Array.isArray(likesData.data)) {
          videosArray = likesData.data;
          console.log('成功获取视频数组，长度:', videosArray.length);
        } else {
          console.log('未获取到有效的视频数组数据');
          console.log('likesData结构:', JSON.stringify(likesData, null, 2));
        }
        
        console.log('解析出的视频数组:', videosArray);
        
        if (videosArray.length > 0) {
          console.log('第一个视频数据结构:', videosArray[0]);
          
          // 转换数据结构以匹配组件所需
          const formattedLikes = videosArray.map((video: any) => {
            const formattedVideo = {
              id: Number(video.id) || 0,
              title: video.title || '未知标题',
              slug: video.slug || video.movie_id || video.id?.toString() || '',
              duration: video.duration || '00:00',
              imageUrl: video.image_url || video.thumbnail_url || video.thumbnail || video.cached_image_url || '/images/placeholder.jpg',
              views: video.view_count || video.views || 0,
              releaseDate: video.release_date ? formatDate(video.release_date) : undefined,
              date: video.timeAgo || formatRelativeTime(video.liked_at?.toString() || video.created_at?.toString())
            };
            
            console.log('格式化视频:', formattedVideo);
            return formattedVideo;
          });
          
          console.log('格式化后的点赞数据:', formattedLikes);
          setLikedVideos(formattedLikes);
        } else {
          console.log('没有找到点赞数据，设置为空数组');
          setLikedVideos([]);
        }
      } catch (error) {
        console.error('获取点赞视频出错:', error);
        console.error('错误详情:', error instanceof Error ? error.message : error);
        setLikedVideos([]);
      }
    }
    
    fetchLikedVideos();
  }, [userData, formatDate, formatRelativeTime]);
  
  useEffect(() => {
    // 页面加载动画
    if (!isLoading && userData && profileRef.current && contentRef.current) {
      const tl = gsap.timeline({ defaults: { ease: 'power3.out' } });
      
      tl.fromTo(
        profileRef.current,
        { y: -20, opacity: 0 },
        { y: 0, opacity: 1, duration: 0.8 }
      );
      
      tl.fromTo(
        contentRef.current,
        { y: 20, opacity: 0 },
        { y: 0, opacity: 1, duration: 0.6 },
        '-=0.5'
      );
    }
  }, [isLoading, userData]);
  
  useEffect(() => {
    // 标签切换动画
    if (contentRef.current) {
      const tl = gsap.timeline({ defaults: { ease: 'power3.out' } });
      
      tl.fromTo(
        contentRef.current,
        { y: 20, opacity: 0 },
        { y: 0, opacity: 1, duration: 0.6 }
      );
    }
  }, [activeTab]);
  
  // 处理取消收藏
  const handleRemoveFavorite = async (videoId: string, e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    
    try {
      await removeFromFavorites(videoId);
      setFavoriteVideos(prevVideos => prevVideos.filter(video => video.id !== parseInt(videoId)));
    } catch (error) {
      console.error('取消收藏失败:', error);
    }
  };
  
  // 取消点赞视频
  const handleUnlikeVideo = async (videoId: number, e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    
    try {
      const { unlikeVideo } = await import('@/lib/user-client');
      await unlikeVideo(videoId);
      setLikedVideos(prevVideos => prevVideos.filter(video => video.id !== videoId));
    } catch (error) {
      console.error('取消点赞失败:', error);
    }
  };
  
  const handleLogout = async () => {
    try {
      setLogoutLoading(true);
      const response = await fetch('/api/auth/logout', {
        method: 'POST',
      });
      
      if (response.ok) {
        // 触发自定义事件通知导航栏用户状态已更改
        window.dispatchEvent(new Event('auth-state-changed'));
        
        // 跳转到首页（包含语言路径）
        router.push(`/${locale}`);
      } else {
        console.error('登出失败');
      }
    } catch (error) {
      console.error('登出时出错:', error);
    } finally {
      setLogoutLoading(false);
    }
  };
  
  // 取消关注演员
  const handleUnfollowStar = async (starId: number, e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    
    try {
      const success = await unfollowStar(starId);
      
      if (success) {
        // 更新UI
        setFollowedActresses(prevStars => 
          prevStars.filter(star => star.id !== starId)
        );
      }
    } catch (error) {
      console.error('取消关注演员失败:', error);
    }
  };
  
  // 处理推荐反馈
  const handleRecommendationFeedback = async (videoId: number, action: 'like' | 'dislike', e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    
    try {
      const success = await submitRecommendationFeedback(videoId, action);
      
      if (success && action === 'dislike') {
        // 如果是不喜欢，从推荐列表中移除
        setRecommendedVideos(prevVideos => 
          prevVideos.filter(video => video.id !== videoId)
        );
      }
    } catch (error) {
      console.error('提交推荐反馈失败:', error);
    }
  };
  
  // 更新用户偏好
  const handleUpdatePreferences = async (preferences: any) => {
    try {
      const success = await updateUserPreferences(preferences);
      
      if (success) {
        // 更新UI
        setUserPreferences({...userPreferences, ...preferences});
      }
    } catch (error) {
      console.error('更新用户偏好失败:', error);
    }
  };
  
  const renderTabContent = () => {
    if (isLoading) {
      return (
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-red-500"></div>
        </div>
      );
    }
    
    switch (activeTab) {
      case 'favorites':
        return (
          <div className="space-y-6">
            <div className="flex justify-between items-center">
              <h2 className="text-xl font-bold">{t('profile.myFavorites') || '我的收藏'}</h2>
              <Link href={`/${locale}/profile/favorites`} className="text-sm text-red-500 hover:text-red-400">{t('common.viewMore') || '查看全部'}</Link>
            </div>
            
            {favoriteVideos.length === 0 ? (
              <div className="py-8 text-center">
                <p className="text-gray-400">{t('profile.noFavorites') || '您还没有收藏任何视频'}</p>
                <Link href={`/${locale}`} className="text-red-500 hover:text-red-400 mt-2 inline-block">
                  {t('profile.browseVideos') || '去浏览视频'}
                </Link>
              </div>
            ) : (
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-5">
                {favoriteVideos.map(video => (
                  <div key={video.id} className="flex flex-col space-y-2">
                    {/* 视频图片区块 */}
                    <Link href={`/${locale}/video/${video.slug}`} className="block">
                      <div className="relative overflow-hidden rounded-xl">
                        <div className="relative h-40 md:h-36 overflow-hidden">
                          <Image
                            src={video.imageUrl}
                            alt={video.title}
                            fill
                            className="object-cover transition-transform duration-500 group-hover:scale-110"
                            sizes="(max-width: 768px) 100vw, 400px"
                          />
                          <div className="absolute inset-0 bg-gradient-to-t from-black to-transparent opacity-70 group-hover:opacity-60 transition-opacity duration-300"></div>
                          
                          {/* 时长标签 */}
                          <div className="absolute top-2 right-2 bg-red-600 text-white text-xs px-2 py-1 rounded font-medium">
                            {video.duration}
                          </div>
                          
                          {/* 取消收藏按钮 */}
                          <div className="absolute top-2 left-2">
                            <button 
                              className="bg-gray-800 bg-opacity-70 hover:bg-opacity-90 p-1.5 rounded-full transition-colors"
                              onClick={(e) => handleRemoveFavorite(video.id.toString(), e)}
                            >
                              <FiHeart className="text-red-500" size={16} />
                            </button>
                          </div>
                        </div>
                      </div>
                    </Link>
                    
                    {/* 视频信息区块 */}
                    <div className="flex flex-col">
                      <Link href={`/${locale}/video/${video.slug}`} className="block">
                        <h3 className="text-white font-semibold text-base truncate hover:text-red-500 transition-colors duration-300">
                          {video.title}
                        </h3>
                      </Link>
                      <div className="flex justify-between items-center mt-1">
                        <p className="text-gray-400 text-xs">{video.views} {t('video.views') || '次观看'}</p>
                        <span className="text-xs text-gray-400">{video.date}</span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
            
            <div className="pt-6">
              <h2 className="text-xl font-bold mb-4">{t('profile.followedActresses') || '关注的女优'}</h2>
              {followedActresses.length === 0 ? (
                <div className="py-4 text-center">
                  <p className="text-gray-400">{t('profile.noFollowedActresses') || '您还没有关注任何女优'}</p>
                  <Link href={`/${locale}/actresses`} className="text-red-500 hover:text-red-400 mt-2 inline-block">
                    {t('profile.browseActresses') || '去浏览女优'}
                  </Link>
                </div>
              ) : (
                <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4">
                  {followedActresses.map(actress => (
                    <Link key={actress.id} href={`/${locale}/actress/${actress.slug}`} className="block">
                      <div className="flex flex-col items-center group">
                        <div className="relative w-20 h-20 rounded-full overflow-hidden border-2 border-red-500/50 group-hover:border-red-500 transition-all">
                          <Image
                            src={actress.imageUrl}
                            alt={actress.name}
                            fill
                            className="object-cover"
                          />
                        </div>
                        <h3 className="mt-2 text-sm font-medium text-white group-hover:text-red-500 transition-colors">
                          {actress.name}
                        </h3>
                        <p className="text-xs text-gray-400">{actress.videos}{t('actress.videos') || '部作品'}</p>
                      </div>
                    </Link>
                  ))}
                  
                  <Link href={`/${locale}/actresses`} className="block">
                    <div className="flex flex-col items-center group">
                      <div className="relative w-20 h-20 rounded-full overflow-hidden border-2 border-dashed border-gray-600 flex items-center justify-center group-hover:border-red-500 transition-all bg-gray-800">
                        <FiPlus size={24} className="text-gray-400 group-hover:text-red-500 transition-colors" />
                      </div>
                      <h3 className="mt-2 text-sm font-medium text-gray-400 group-hover:text-red-500 transition-colors">
                        {t('profile.followMore') || '关注更多'}
                      </h3>
                    </div>
                  </Link>
                </div>
              )}
            </div>
          </div>
        );
        
      case 'history':
        return (
          <div className="space-y-6">
            <div className="flex justify-between items-center">
              <h2 className="text-xl font-bold">{t('profile.watchHistory') || '观看历史'}</h2>
              {historyVideos.length > 0 && (
                <button className="text-sm text-red-500 hover:text-red-400">{t('profile.clearHistory') || '清除历史记录'}</button>
              )}
            </div>
            
            {historyVideos.length === 0 ? (
              <div className="py-8 text-center">
                <p className="text-gray-400">{t('profile.noWatchHistory') || '您还没有观看任何视频'}</p>
                <Link href={`/${locale}`} className="text-red-500 hover:text-red-400 mt-2 inline-block">
                  {t('profile.browseVideos') || '去浏览视频'}
                </Link>
              </div>
            ) : (
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-5">
                {historyVideos.map(video => (
                  <div key={video.id} className="flex flex-col space-y-2">
                    {/* 视频图片区块 */}
                    <Link href={`/${locale}/video/${video.slug}`} className="block">
                      <div className="relative overflow-hidden rounded-xl">
                        <div className="relative h-40 md:h-36 overflow-hidden">
                          <Image
                            src={video.imageUrl}
                            alt={video.title}
                            fill
                            className="object-cover transition-transform duration-500 group-hover:scale-110"
                            sizes="(max-width: 768px) 100vw, 400px"
                          />
                          <div className="absolute inset-0 bg-gradient-to-t from-black to-transparent opacity-70 group-hover:opacity-60 transition-opacity duration-300"></div>
                          
                          {/* 时长标签 */}
                          <div className="absolute top-2 right-2 bg-red-600 text-white text-xs px-2 py-1 rounded font-medium">
                            {video.duration}
                          </div>
                          
                          {/* 播放日期标签 */}
                          <div className="absolute top-2 left-2">
                            <div className="bg-gray-800 bg-opacity-70 text-xs px-2 py-1 rounded text-white">
                              {video.date}
                            </div>
                          </div>
                          
                          {/* 播放进度条 */}
                          <div className="absolute bottom-0 left-0 right-0 h-1 bg-gray-700">
                            <div 
                              className="h-full bg-red-600" 
                              style={{ width: `${video.progress}%` }}
                            ></div>
                          </div>
                        </div>
                      </div>
                    </Link>
                    
                    {/* 视频信息区块 */}
                    <div className="flex flex-col">
                      <Link href={`/${locale}/video/${video.slug}`} className="block">
                        <h3 className="text-white font-semibold text-base truncate hover:text-red-500 transition-colors duration-300">
                          {video.title}
                        </h3>
                      </Link>
                      <div className="flex justify-between items-center mt-1">
                        <p className="text-gray-400 text-xs">{t('profile.watched') || '已观看'} {video.progress}%</p>
                        {video.isCompleted ? (
                          <span className="text-xs text-green-500">{t('profile.completed') || '已完成'}</span>
                        ) : (
                          <Link href={`/${locale}/video/${video.slug}`} className="text-xs text-red-500 hover:text-red-400">
                            {t('profile.continueWatching') || '继续观看'}
                          </Link>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        );
        
      case 'following':
        return (
          <div className="space-y-6">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-xl font-semibold">{t('profile.following.title') || '关注的演员'}</h2>
              <Link 
                href={`/${locale}/actresses`}
                className="text-sm text-red-500 hover:text-red-600 transition flex items-center gap-1"
              >
                <FiPlus className="text-lg" />
                {t('profile.following.browse') || '浏览更多演员'}
              </Link>
            </div>
            
            {followedActresses.length === 0 ? (
              <div className="text-center py-12 border border-dashed rounded-lg">
                <FiStar className="mx-auto text-4xl mb-3 text-gray-400" />
                <p className="text-gray-500">{t('profile.following.empty') || '您还没有关注任何演员'}</p>
                <Link 
                  href={`/${locale}/actresses`}
                  className="inline-block mt-4 px-4 py-2 bg-red-500 text-white rounded-md hover:bg-red-600 transition"
                >
                  {t('profile.following.discover') || '发现演员'}
                </Link>
              </div>
            ) : (
              <div className="grid grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                {followedActresses.map(actress => (
                  <Link 
                    key={actress.id} 
                    href={`/${locale}/actress/${actress.slug}`}
                    className="group relative rounded-lg overflow-hidden hover:ring-2 hover:ring-red-500 transition"
                  >
                    <div className="aspect-[3/4] relative">
                      <Image 
                        src={actress.imageUrl} 
                        alt={actress.name}
                        fill
                        className="object-cover group-hover:scale-105 transition duration-300"
                      />
                      <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/20 to-transparent"></div>
                      <button
                        onClick={(e) => handleUnfollowStar(actress.id, e)}
                        className="absolute top-2 right-2 p-1.5 bg-black/50 hover:bg-red-500 rounded-full text-white transition"
                        title={t('profile.following.unfollow') || '取消关注'}
                      >
                        <FiHeart className="text-lg" fill="white" />
                      </button>
                    </div>
                    <div className="absolute bottom-0 left-0 right-0 p-3 text-white">
                      <h3 className="text-sm font-medium truncate">{actress.name}</h3>
                      <div className="flex items-center justify-between mt-1 text-xs text-gray-300">
                        <span>{formatRelativeTime(actress.followedAt)}</span>
                      </div>
                    </div>
                  </Link>
                ))}
              </div>
            )}
          </div>
        );
        
      case 'recommendations':
        return (
          <div className="space-y-6">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-xl font-semibold">{t('profile.recommendations.title') || '为您推荐'}</h2>
              <button
                onClick={() => {
                  // 重新获取推荐
                  setRecommendedVideos([]);
                  getRecommendations(8).then(data => {
                    if (data && data.length > 0) {
                      const formattedVideos = data.map(video => ({
                        id: Number(video.id),
                        title: video.title,
                        slug: video.movie_id || '',
                        duration: video.duration || '00:00',
                        imageUrl: video.image_url || video.cover_image || '/images/placeholder.jpg',
                        views: 0,
                        releaseDate: video.release_date
                      }));
                      setRecommendedVideos(formattedVideos);
                    }
                  });
                }}
                className="text-sm text-red-500 hover:text-red-600 transition flex items-center gap-1"
              >
                <FiPlus className="text-lg" />
                {t('profile.recommendations.refresh') || '刷新推荐'}
              </button>
            </div>
            
            {recommendedVideos.length === 0 ? (
              <div className="text-center py-12 border border-dashed rounded-lg">
                <FiEye className="mx-auto text-4xl mb-3 text-gray-400" />
                <p className="text-gray-500">{t('profile.recommendations.empty') || '正在为您生成推荐...'}</p>
              </div>
            ) : (
              <div className="grid grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                {recommendedVideos.map(video => (
                  <Link 
                    key={video.id} 
                    href={`/${locale}/video/${video.slug}`}
                    className="group relative rounded-lg overflow-hidden hover:ring-2 hover:ring-red-500 transition"
                  >
                    <div className="aspect-video relative">
                      <Image 
                        src={video.imageUrl} 
                        alt={video.title}
                        fill
                        className="object-cover group-hover:scale-105 transition duration-300"
                      />
                      <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/20 to-transparent"></div>
                      <div className="absolute top-2 right-2 flex gap-1">
                        <button
                          onClick={(e) => handleRecommendationFeedback(video.id, 'like', e)}
                          className="p-1.5 bg-black/50 hover:bg-green-500 rounded-full text-white transition"
                          title={t('profile.recommendations.like') || '喜欢'}
                        >
                          <FiThumbsUp className="text-lg" />
                        </button>
                        <button
                          onClick={(e) => handleRecommendationFeedback(video.id, 'dislike', e)}
                          className="p-1.5 bg-black/50 hover:bg-red-500 rounded-full text-white transition"
                          title={t('profile.recommendations.dislike') || '不喜欢'}
                        >
                          <FiThumbsDown className="text-lg" />
                        </button>
                      </div>
                    </div>
                    <div className="absolute bottom-0 left-0 right-0 p-3 text-white">
                      <h3 className="text-sm font-medium line-clamp-2">{video.title}</h3>
                      <div className="flex items-center justify-between mt-1 text-xs text-gray-300">
                        <span>{video.duration}</span>
                        <span>{video.releaseDate}</span>
                      </div>
                    </div>
                  </Link>
                ))}
              </div>
            )}
          </div>
        );
        
      case 'likes':
        return (
          <div className="space-y-6">
            <div className="flex justify-between items-center">
              <h2 className="text-xl font-bold">{t('profile.myLikes') || '我的点赞'}</h2>
              <Link href={`/${locale}/profile/likes`} className="text-sm text-red-500 hover:text-red-400">{t('common.viewMore') || '查看全部'}</Link>
            </div>
            
            {likedVideos.length === 0 ? (
              <div className="py-8 text-center">
                <p className="text-gray-400">{t('profile.noLikes') || '您还没有点赞任何视频'}</p>
                <Link href={`/${locale}`} className="text-red-500 hover:text-red-400 mt-2 inline-block">
                  {t('profile.browseVideos') || '去浏览视频'}
                </Link>
              </div>
            ) : (
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-5">
                {likedVideos.map(video => (
                  <div key={video.id} className="flex flex-col space-y-2">
                    {/* 视频图片区块 */}
                    <Link href={`/${locale}/video/${video.slug}`} className="block">
                      <div className="relative overflow-hidden rounded-xl">
                        <div className="relative h-40 md:h-36 overflow-hidden">
                          <Image
                            src={video.imageUrl}
                            alt={video.title}
                            fill
                            className="object-cover transition-transform duration-500 group-hover:scale-110"
                            sizes="(max-width: 768px) 100vw, 400px"
                          />
                          <div className="absolute inset-0 bg-gradient-to-t from-black to-transparent opacity-70 group-hover:opacity-60 transition-opacity duration-300"></div>
                          
                          {/* 时长标签 */}
                          <div className="absolute top-2 right-2 bg-red-600 text-white text-xs px-2 py-1 rounded font-medium">
                            {video.duration}
                          </div>
                          
                          {/* 点赞标识 */}
                          <div className="absolute top-2 left-2">
                            <div className="bg-red-600 bg-opacity-80 p-1.5 rounded-full">
                              <FiHeart className="text-white fill-current" size={16} />
                            </div>
                          </div>
                        </div>
                      </div>
                    </Link>
                    
                    {/* 视频信息区块 */}
                    <div className="flex flex-col">
                      <Link href={`/${locale}/video/${video.slug}`} className="block">
                        <h3 className="text-white font-semibold text-base truncate hover:text-red-500 transition-colors duration-300">
                          {video.title}
                        </h3>
                      </Link>
                      <div className="flex justify-between items-center mt-1">
                        <p className="text-gray-400 text-xs">{video.views} {t('video.views') || '次观看'}</p>
                        <span className="text-xs text-gray-400">{video.date}</span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        );
        
      case 'settings':
        return (
          <div className="space-y-6">
            <div className="mb-4">
              <h2 className="text-xl font-semibold">{t('profile.settings.title') || '设置偏好'}</h2>
              <p className="text-sm text-gray-500 mt-1">{t('profile.settings.subtitle') || '自定义您的使用体验'}</p>
            </div>
            
            <div className="space-y-6 bg-gray-50 dark:bg-gray-800 rounded-lg p-6">
              <div>
                <h3 className="text-lg font-medium mb-4">{t('profile.settings.appearance') || '外观设置'}</h3>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium mb-2">
                      {t('profile.settings.theme') || '主题'}
                    </label>
                    <div className="flex gap-3">
                      <button
                        onClick={() => handleUpdatePreferences({ theme: 'light' })}
                        className={`px-4 py-2 rounded-md border transition ${
                          !userPreferences || userPreferences.theme === 'light' 
                            ? 'bg-red-500 text-white border-red-500' 
                            : 'border-gray-300 dark:border-gray-700'
                        }`}
                      >
                        {t('profile.settings.light') || '浅色'}
                      </button>
                      <button
                        onClick={() => handleUpdatePreferences({ theme: 'dark' })}
                        className={`px-4 py-2 rounded-md border transition ${
                          userPreferences?.theme === 'dark' 
                            ? 'bg-red-500 text-white border-red-500' 
                            : 'border-gray-300 dark:border-gray-700'
                        }`}
                      >
                        {t('profile.settings.dark') || '深色'}
                      </button>
                    </div>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium mb-2">
                      {t('profile.settings.language') || '语言'}
                    </label>
                    <select
                      value={userPreferences?.language || 'zh'}
                      onChange={(e) => handleUpdatePreferences({ language: e.target.value })}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-md bg-white dark:bg-gray-700"
                    >
                      <option value="zh">简体中文</option>
                      <option value="en">English</option>
                      <option value="ja">日本語</option>
                    </select>
                  </div>
                </div>
              </div>
              
              <div>
                <h3 className="text-lg font-medium mb-4">{t('profile.settings.playback') || '播放设置'}</h3>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <label className="text-sm font-medium">
                      {t('profile.settings.autoplay') || '自动播放'}
                    </label>
                    <div className="relative inline-block w-10 mr-2 align-middle">
                      <input 
                        type="checkbox" 
                        id="autoplay"
                        checked={userPreferences?.autoplay !== false}
                        onChange={(e) => handleUpdatePreferences({ autoplay: e.target.checked })}
                        className="sr-only" 
                      />
                      <label
                        htmlFor="autoplay"
                        className={`block h-6 w-10 rounded-full ${
                          userPreferences?.autoplay !== false ? 'bg-red-500' : 'bg-gray-300 dark:bg-gray-700'
                        } cursor-pointer transition-colors duration-300`}
                      >
                        <span 
                          className={`block h-4 w-4 mt-1 ml-1 rounded-full bg-white shadow-md transform transition-transform duration-300 ${
                            userPreferences?.autoplay !== false ? 'translate-x-4' : ''
                          }`} 
                        />
                      </label>
                    </div>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <label className="text-sm font-medium">
                      {t('profile.settings.subtitles') || '默认显示字幕'}
                    </label>
                    <div className="relative inline-block w-10 mr-2 align-middle">
                      <input 
                        type="checkbox" 
                        id="subtitles"
                        checked={userPreferences?.subtitle_enabled !== false}
                        onChange={(e) => handleUpdatePreferences({ subtitle_enabled: e.target.checked })}
                        className="sr-only" 
                      />
                      <label
                        htmlFor="subtitles"
                        className={`block h-6 w-10 rounded-full ${
                          userPreferences?.subtitle_enabled !== false ? 'bg-red-500' : 'bg-gray-300 dark:bg-gray-700'
                        } cursor-pointer transition-colors duration-300`}
                      >
                        <span 
                          className={`block h-4 w-4 mt-1 ml-1 rounded-full bg-white shadow-md transform transition-transform duration-300 ${
                            userPreferences?.subtitle_enabled !== false ? 'translate-x-4' : ''
                          }`} 
                        />
                      </label>
                    </div>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium mb-2">
                      {t('profile.settings.quality') || '默认视频质量'}
                    </label>
                    <select
                      value={userPreferences?.video_quality || 'auto'}
                      onChange={(e) => handleUpdatePreferences({ video_quality: e.target.value })}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-md bg-white dark:bg-gray-700"
                    >
                      <option value="auto">自动</option>
                      <option value="1080p">1080p</option>
                      <option value="720p">720p</option>
                      <option value="480p">480p</option>
                      <option value="360p">360p</option>
                    </select>
                  </div>
                </div>
              </div>
              
              <div>
                <h3 className="text-lg font-medium mb-4">{t('profile.settings.notifications') || '通知设置'}</h3>
                <div className="flex items-center justify-between">
                  <label className="text-sm font-medium">
                    {t('profile.settings.enable_notifications') || '启用通知'}
                  </label>
                  <div className="relative inline-block w-10 mr-2 align-middle">
                    <input 
                      type="checkbox" 
                      id="notifications"
                      checked={userPreferences?.notifications_enabled !== false}
                      onChange={(e) => handleUpdatePreferences({ notifications_enabled: e.target.checked })}
                      className="sr-only" 
                    />
                    <label
                      htmlFor="notifications"
                      className={`block h-6 w-10 rounded-full ${
                        userPreferences?.notifications_enabled !== false ? 'bg-red-500' : 'bg-gray-300 dark:bg-gray-700'
                      } cursor-pointer transition-colors duration-300`}
                    >
                      <span 
                        className={`block h-4 w-4 mt-1 ml-1 rounded-full bg-white shadow-md transform transition-transform duration-300 ${
                          userPreferences?.notifications_enabled !== false ? 'translate-x-4' : ''
                        }`} 
                      />
                    </label>
                  </div>
                </div>
              </div>
            </div>
          </div>
        );
        
      default:
        return null;
    }
  };
  
  // 加载中显示
  if (isLoading) {
    return (
      <div className="min-h-screen pt-20 pb-16 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-red-500"></div>
      </div>
    );
  }
  
  // 错误显示
  if (error) {
    return (
      <div className="min-h-screen pt-20 pb-16 flex flex-col items-center justify-center">
        <div className="text-red-500 mb-4">{error}</div>
        <button 
          onClick={() => router.push(`/${locale}/auth/login`)}
          className="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded"
        >
          {t('profile.goToLogin') || '去登录'}
        </button>
      </div>
    );
  }
  
  // 如果没有用户数据，也重定向到登录页
  if (!userData) {
    router.push(`/${locale}/auth/login`);
    return null;
  }
  
  // 计算收藏和观看数
  const watchedCount = historyVideos.length;
  const favoriteCount = favoriteVideos.length;
  
  return (
    <div className="min-h-screen pt-20 pb-16">
      <div className="container mx-auto px-4">
        {/* 用户资料卡片 */}
        <div 
          ref={profileRef}
          className="bg-gradient-to-r from-gray-900 to-black rounded-xl overflow-hidden mb-8"
        >
          <div className="p-6 md:p-8">
            <div className="flex flex-col md:flex-row items-center gap-6">
              <div className="relative">
                <div className="w-28 h-28 md:w-32 md:h-32 rounded-full overflow-hidden border-4 border-red-500">
                  {userData.avatarUrl ? (
                    <Image
                      src={userData.avatarUrl}
                      alt={userData.username}
                      width={128}
                      height={128}
                      className="object-cover"
                    />
                  ) : (
                    <div className="w-full h-full bg-red-600 flex items-center justify-center text-white text-4xl font-bold">
                      {userData.username.charAt(0).toUpperCase()}
                    </div>
                  )}
                </div>
                <button className="absolute bottom-0 right-0 bg-gray-800 hover:bg-gray-700 p-2 rounded-full border border-gray-700 transition-colors">
                  <FiEdit2 size={16} />
                </button>
              </div>
              
              <div className="text-center md:text-left">
                <h1 className="text-2xl md:text-3xl font-bold">{userData.username}</h1>
                <p className="text-gray-400 mt-1">{userData.email}</p>
                
                <div className="flex flex-wrap justify-center md:justify-start items-center mt-3 gap-4">
                  <div className="flex items-center">
                    <FiCalendar className="mr-1.5 text-red-500" />
                    <span className="text-sm">{t('profile.joinedOn') || '加入于'} {formatDate(userData.createdAt || userData.created_at)}</span>
                  </div>
                  <div className="flex items-center">
                    <FiClock className="mr-1.5 text-red-500" />
                    <span className="text-sm">{t('profile.watchedCount', {count: watchedCount}) || `观看 ${watchedCount} 部`}</span>
                  </div>
                  <div className="flex items-center">
                    <FiHeart className="mr-1.5 text-red-500" />
                    <span className="text-sm">{t('profile.favoriteCount', {count: favoriteCount}) || `收藏 ${favoriteCount} 部`}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        {/* 标签页 */}
        <div className="mb-6 flex overflow-x-auto pb-2 scrollbar-hide">
          <button
            onClick={() => setActiveTab('favorites')}
            className={`flex items-center px-4 py-2 mr-2 rounded-md whitespace-nowrap transition ${
              activeTab === 'favorites' 
                ? 'bg-red-500 text-white' 
                : 'hover:bg-gray-100 dark:hover:bg-gray-800'
            }`}
          >
            <FiHeart className="mr-2" />
            {t('profile.tabs.favorites') || '我的收藏'}
          </button>
          
          <button
            onClick={() => setActiveTab('history')}
            className={`flex items-center px-4 py-2 mr-2 rounded-md whitespace-nowrap transition ${
              activeTab === 'history' 
                ? 'bg-red-500 text-white' 
                : 'hover:bg-gray-100 dark:hover:bg-gray-800'
            }`}
          >
            <FiClock className="mr-2" />
            {t('profile.tabs.history') || '观看历史'}
          </button>
          
          <button
            onClick={() => setActiveTab('following')}
            className={`flex items-center px-4 py-2 mr-2 rounded-md whitespace-nowrap transition ${
              activeTab === 'following' 
                ? 'bg-red-500 text-white' 
                : 'hover:bg-gray-100 dark:hover:bg-gray-800'
            }`}
          >
            <FiStar className="mr-2" />
            {t('profile.tabs.following') || '关注演员'}
          </button>
          
          <button
            onClick={() => setActiveTab('recommendations')}
            className={`flex items-center px-4 py-2 mr-2 rounded-md whitespace-nowrap transition ${
              activeTab === 'recommendations' 
                ? 'bg-red-500 text-white' 
                : 'hover:bg-gray-100 dark:hover:bg-gray-800'
            }`}
          >
            <FiEye className="mr-2" />
            {t('profile.tabs.recommendations') || '个性化推荐'}
          </button>
          
          <button
            onClick={() => setActiveTab('likes')}
            className={`flex items-center px-4 py-2 mr-2 rounded-md whitespace-nowrap transition ${
              activeTab === 'likes' 
                ? 'bg-red-500 text-white' 
                : 'hover:bg-gray-100 dark:hover:bg-gray-800'
            }`}
          >
            <FiThumbsUp className="mr-2" />
            {t('profile.tabs.likes') || '我的点赞'}
          </button>
          
          <button
            onClick={() => setActiveTab('settings')}
            className={`flex items-center px-4 py-2 mr-2 rounded-md whitespace-nowrap transition ${
              activeTab === 'settings' 
                ? 'bg-red-500 text-white' 
                : 'hover:bg-gray-100 dark:hover:bg-gray-800'
            }`}
          >
            <FiSettings className="mr-2" />
            {t('profile.tabs.settings') || '设置偏好'}
          </button>
        </div>
        
        {/* 标签页内容 */}
        <div ref={contentRef}>
          {renderTabContent()}
        </div>
      </div>
    </div>
  );
};

export default ProfileClient;
