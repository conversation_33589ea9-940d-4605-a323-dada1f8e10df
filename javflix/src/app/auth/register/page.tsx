'use client';

import { useState, useEffect, useRef } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { FiUser, FiMail, <PERSON>Lock, <PERSON>Eye, FiEyeOff, FiGithub, FiTwitter, FiCheckCircle } from 'react-icons/fi';
import { FcGoogle } from 'react-icons/fc';
import gsap from 'gsap';

export default function RegisterPage() {
  const [username, setUsername] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [acceptTerms, setAcceptTerms] = useState(false);
  const [apiError, setApiError] = useState('');
  const [errors, setErrors] = useState({
    username: '',
    email: '',
    password: '',
    confirmPassword: '',
    terms: ''
  });
  const [registerSuccess, setRegisterSuccess] = useState(false);
  
  const router = useRouter();
  const formRef = useRef<HTMLFormElement>(null);
  const titleRef = useRef<HTMLHeadingElement>(null);
  const subtitleRef = useRef<HTMLParagraphElement>(null);
  const successOverlayRef = useRef<HTMLDivElement>(null);
  
  useEffect(() => {
    // 页面加载动画
    const tl = gsap.timeline({ defaults: { ease: 'power3.out' } });
    
    tl.fromTo(
      titleRef.current,
      { y: -20, opacity: 0 },
      { y: 0, opacity: 1, duration: 0.6 }
    );
    
    tl.fromTo(
      subtitleRef.current,
      { y: -10, opacity: 0 },
      { y: 0, opacity: 1, duration: 0.6 },
      '-=0.3'
    );
    
    tl.fromTo(
      formRef.current,
      { y: 20, opacity: 0 },
      { y: 0, opacity: 1, duration: 0.8 },
      '-=0.3'
    );
  }, []);
  
  const validateForm = () => {
    let valid = true;
    const newErrors = {
      username: '',
      email: '',
      password: '',
      confirmPassword: '',
      terms: ''
    };
    
    // 验证用户名
    if (username.trim().length < 3) {
      newErrors.username = '用户名至少需要3个字符';
      valid = false;
    }
    
    // 验证邮箱
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      newErrors.email = '请输入有效的邮箱地址';
      valid = false;
    }
    
    // 验证密码
    if (password.length < 8) {
      newErrors.password = '密码至少需要8个字符';
      valid = false;
    }
    
    // 验证确认密码
    if (password !== confirmPassword) {
      newErrors.confirmPassword = '两次输入的密码不一致';
      valid = false;
    }
    
    // 验证服务条款
    if (!acceptTerms) {
      newErrors.terms = '您必须同意服务条款和隐私政策';
      valid = false;
    }
    
    setErrors(newErrors);
    return valid;
  };
  
  // 注册成功动画
  const playSuccessAnimation = () => {
    if (!successOverlayRef.current) return;
    
    // 创建动画时间线
    const tl = gsap.timeline({
      onComplete: () => {
        // 动画完成后直接导航到首页
        router.push('/');
      }
    });
    
    // 显示成功覆盖层
    tl.set(successOverlayRef.current, { display: 'flex' });
    
    // 背景动画
    tl.fromTo(
      successOverlayRef.current,
      { opacity: 0 },
      { opacity: 1, duration: 0.3 }
    );
    
    // 成功图标动画
    tl.fromTo(
      '.success-icon',
      {
        scale: 0.5,
        opacity: 0,
        rotation: -30
      },
      {
        scale: 1,
        opacity: 1,
        rotation: 0,
        duration: 0.6,
        ease: 'elastic.out(1, 0.5)'
      }
    );
    
    // 成功消息动画
    tl.fromTo(
      '.success-message',
      { y: 20, opacity: 0 },
      { y: 0, opacity: 1, duration: 0.4 },
      '-=0.3'
    );
    
    // 停留短暂时间后淡出
    tl.to(
      successOverlayRef.current,
      { opacity: 0, duration: 0.5, delay: 1 }
    );
  };
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }
    
    setIsLoading(true);
    setApiError('');
    
    try {
      const response = await fetch('/api/auth/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          username,
          email,
          password,
        }),
      });
      
      const data = await response.json();
      
      if (!response.ok || !data.success) {
        setApiError(data.error || '注册失败，请重试');
        setIsLoading(false);
        return;
      }
      
      // 触发自定义事件通知导航栏用户状态已更改
      window.dispatchEvent(new Event('auth-state-changed'));
      
      // 设置注册成功状态
      setRegisterSuccess(true);
      
      // 播放成功动画，而不是立即跳转
      playSuccessAnimation();
    } catch (error) {
      console.error('注册错误:', error);
      setApiError('注册时发生错误，请稍后重试');
      setIsLoading(false);
    }
  };
  
  return (
    <div className="min-h-screen bg-gradient-to-b from-gray-900 to-black py-16 px-4 sm:px-6 flex items-center justify-center">
      {/* 注册成功覆盖层 */}
      <div 
        ref={successOverlayRef}
        className="fixed inset-0 bg-black/80 z-50 flex-col items-center justify-center"
        style={{ display: 'none' }}
      >
        <div className="success-icon text-green-500 mb-6">
          <FiCheckCircle size={80} />
        </div>
        <h2 className="success-message text-2xl font-bold text-white mb-2">
          注册成功
        </h2>
        <p className="success-message text-gray-300">
          欢迎加入，{username}！
        </p>
      </div>
      
      <div className="max-w-md w-full space-y-8 bg-gray-800 p-8 rounded-xl shadow-2xl">
        <div className="text-center">
          <h2 ref={titleRef} className="text-3xl font-bold text-white">
            创建新账户
          </h2>
          <p ref={subtitleRef} className="mt-2 text-sm text-gray-400">
            加入我们，获取更多精彩内容
          </p>
        </div>
        
        {apiError && (
          <div className="bg-red-500/20 border border-red-500 text-red-100 px-4 py-3 rounded relative" role="alert">
            <span className="block sm:inline">{apiError}</span>
          </div>
        )}
        
        <form ref={formRef} className="mt-8 space-y-6" onSubmit={handleSubmit}>
          <div className="space-y-4">
            {/* 用户名输入 */}
            <div>
              <label htmlFor="username" className="block text-sm font-medium text-gray-300 mb-1">
                用户名
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <FiUser className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  id="username"
                  name="username"
                  type="text"
                  required
                  value={username}
                  onChange={(e) => setUsername(e.target.value)}
                  className={`block w-full pl-10 pr-3 py-2 border ${errors.username ? 'border-red-500' : 'border-gray-600'} rounded-md bg-gray-700 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent`}
                  placeholder="设置您的用户名"
                />
              </div>
              {errors.username && (
                <p className="mt-1 text-sm text-red-500">{errors.username}</p>
              )}
            </div>
            
            {/* 邮箱输入 */}
            <div>
              <label htmlFor="email" className="block text-sm font-medium text-gray-300 mb-1">
                邮箱地址
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <FiMail className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  id="email"
                  name="email"
                  type="email"
                  autoComplete="email"
                  required
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className={`block w-full pl-10 pr-3 py-2 border ${errors.email ? 'border-red-500' : 'border-gray-600'} rounded-md bg-gray-700 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent`}
                  placeholder="您的邮箱地址"
                />
              </div>
              {errors.email && (
                <p className="mt-1 text-sm text-red-500">{errors.email}</p>
              )}
            </div>
            
            {/* 密码输入 */}
            <div>
              <label htmlFor="password" className="block text-sm font-medium text-gray-300 mb-1">
                密码
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <FiLock className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  id="password"
                  name="password"
                  type={showPassword ? "text" : "password"}
                  required
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className={`block w-full pl-10 pr-10 py-2 border ${errors.password ? 'border-red-500' : 'border-gray-600'} rounded-md bg-gray-700 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent`}
                  placeholder="设置您的密码"
                />
                <div 
                  className="absolute inset-y-0 right-0 pr-3 flex items-center cursor-pointer"
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? (
                    <FiEyeOff className="h-5 w-5 text-gray-400 hover:text-gray-300" />
                  ) : (
                    <FiEye className="h-5 w-5 text-gray-400 hover:text-gray-300" />
                  )}
                </div>
              </div>
              {errors.password && (
                <p className="mt-1 text-sm text-red-500">{errors.password}</p>
              )}
            </div>
            
            {/* 确认密码输入 */}
            <div>
              <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-300 mb-1">
                确认密码
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <FiLock className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  id="confirmPassword"
                  name="confirmPassword"
                  type={showConfirmPassword ? "text" : "password"}
                  required
                  value={confirmPassword}
                  onChange={(e) => setConfirmPassword(e.target.value)}
                  className={`block w-full pl-10 pr-10 py-2 border ${errors.confirmPassword ? 'border-red-500' : 'border-gray-600'} rounded-md bg-gray-700 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent`}
                  placeholder="确认您的密码"
                />
                <div 
                  className="absolute inset-y-0 right-0 pr-3 flex items-center cursor-pointer"
                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                >
                  {showConfirmPassword ? (
                    <FiEyeOff className="h-5 w-5 text-gray-400 hover:text-gray-300" />
                  ) : (
                    <FiEye className="h-5 w-5 text-gray-400 hover:text-gray-300" />
                  )}
                </div>
              </div>
              {errors.confirmPassword && (
                <p className="mt-1 text-sm text-red-500">{errors.confirmPassword}</p>
              )}
            </div>
            
            {/* 服务条款 */}
            <div className="flex items-start">
              <div className="flex items-center h-5">
                <input
                  id="acceptTerms"
                  name="acceptTerms"
                  type="checkbox"
                  checked={acceptTerms}
                  onChange={(e) => setAcceptTerms(e.target.checked)}
                  className={`h-4 w-4 text-red-600 focus:ring-red-500 border-gray-500 rounded bg-gray-700 ${errors.terms ? 'border-red-500' : ''}`}
                />
              </div>
              <div className="ml-3 text-sm">
                <label htmlFor="acceptTerms" className="font-medium text-gray-300">
                  我同意
                  <a href="#" className="text-red-500 hover:text-red-400 mx-1">服务条款</a>
                  和
                  <a href="#" className="text-red-500 hover:text-red-400 mx-1">隐私政策</a>
                </label>
                {errors.terms && (
                  <p className="mt-1 text-sm text-red-500">{errors.terms}</p>
                )}
              </div>
            </div>
          </div>
          
          <div>
            <button
              type="submit"
              disabled={isLoading}
              className="group relative w-full flex justify-center py-2 px-4 border border-transparent rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors"
            >
              {isLoading ? (
                <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
              ) : "注册"}
            </button>
          </div>
          
          <div className="mt-4 text-center">
            <span className="text-sm text-gray-400">
              已有账户? {' '}
              <Link href="/auth/login" className="text-red-500 hover:text-red-400">
                立即登录
              </Link>
            </span>
          </div>
        </form>
        
        <div className="mt-6">
          <div className="relative">
            <div className="absolute inset-0 flex items-center">
              <div className="w-full border-t border-gray-600"></div>
            </div>
            <div className="relative flex justify-center text-sm">
              <span className="px-4 bg-gray-800 text-gray-400">或通过以下方式注册</span>
            </div>
          </div>
          
          <div className="mt-6 grid grid-cols-3 gap-3">
            <div>
              <a
                href="#"
                className="w-full inline-flex justify-center py-2 px-4 border border-gray-600 rounded-md shadow-sm bg-gray-700 hover:bg-gray-600 transition-colors"
              >
                <FcGoogle className="h-5 w-5" />
              </a>
            </div>
            <div>
              <a
                href="#"
                className="w-full inline-flex justify-center py-2 px-4 border border-gray-600 rounded-md shadow-sm bg-gray-700 hover:bg-gray-600 transition-colors"
              >
                <FiTwitter className="h-5 w-5 text-[#1DA1F2]" />
              </a>
            </div>
            <div>
              <a
                href="#"
                className="w-full inline-flex justify-center py-2 px-4 border border-gray-600 rounded-md shadow-sm bg-gray-700 hover:bg-gray-600 transition-colors"
              >
                <FiGithub className="h-5 w-5 text-white" />
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
} 