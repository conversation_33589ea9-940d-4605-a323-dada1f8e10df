'use client';

import { useState, useEffect, useRef } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { FiMail, FiLock, FiEye, FiEyeOff, FiGithub, FiTwitter, FiCheckCircle } from 'react-icons/fi';
import { FcGoogle } from 'react-icons/fc';
import gsap from 'gsap';

export default function LoginPage() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [rememberMe, setRememberMe] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [loginSuccess, setLoginSuccess] = useState(false);
  
  const router = useRouter();
  const formRef = useRef<HTMLFormElement>(null);
  const titleRef = useRef<HTMLHeadingElement>(null);
  const subtitleRef = useRef<HTMLParagraphElement>(null);
  const successOverlayRef = useRef<HTMLDivElement>(null);
  
  useEffect(() => {
    // 页面加载动画
    const tl = gsap.timeline({ defaults: { ease: 'power3.out' } });
    
    tl.fromTo(
      titleRef.current,
      { y: -20, opacity: 0 },
      { y: 0, opacity: 1, duration: 0.6 }
    );
    
    tl.fromTo(
      subtitleRef.current,
      { y: -10, opacity: 0 },
      { y: 0, opacity: 1, duration: 0.6 },
      '-=0.3'
    );
    
    tl.fromTo(
      formRef.current,
      { y: 20, opacity: 0 },
      { y: 0, opacity: 1, duration: 0.8 },
      '-=0.3'
    );
  }, []);
  
  // 登录成功动画
  const playSuccessAnimation = () => {
    if (!successOverlayRef.current) return;
    
    // 创建动画时间线
    const tl = gsap.timeline({
      onComplete: () => {
        // 动画完成后导航到首页
        router.push('/');
      }
    });
    
    // 显示成功覆盖层
    tl.set(successOverlayRef.current, { display: 'flex' });
    
    // 背景动画
    tl.fromTo(
      successOverlayRef.current,
      { opacity: 0 },
      { opacity: 1, duration: 0.3 }
    );
    
    // 成功图标动画
    tl.fromTo(
      '.success-icon',
      {
        scale: 0.5,
        opacity: 0,
      },
      {
        scale: 1,
        opacity: 1,
        duration: 0.6,
        ease: 'elastic.out(1, 0.5)'
      }
    );
    
    // 成功消息动画
    tl.fromTo(
      '.success-message',
      { y: 20, opacity: 0 },
      { y: 0, opacity: 1, duration: 0.4 },
      '-=0.3'
    );
    
    // 停留短暂时间后淡出
    tl.to(
      successOverlayRef.current,
      { opacity: 0, duration: 0.5, delay: 0.8 }
    );
  };
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');
    
    try {
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email,
          password,
          rememberMe,
        }),
      });
      
      const data = await response.json();
      
      if (!response.ok || !data.success) {
        setError(data.error || '登录失败，请重试');
        setIsLoading(false);
        return;
      }
      
      // 触发自定义事件通知导航栏用户状态已更改
      window.dispatchEvent(new Event('auth-state-changed'));
      
      // 设置登录成功状态
      setLoginSuccess(true);
      
      // 播放成功动画，而不是立即跳转
      playSuccessAnimation();
      
    } catch (error) {
      console.error('登录错误:', error);
      setError('登录时发生错误，请稍后重试');
      setIsLoading(false);
    }
  };
  
  return (
    <div className="min-h-screen bg-gradient-to-b from-gray-900 to-black py-16 px-4 sm:px-6 flex items-center justify-center">
      {/* 登录成功覆盖层 */}
      <div 
        ref={successOverlayRef}
        className="fixed inset-0 bg-black/80 z-50 flex-col items-center justify-center"
        style={{ display: 'none' }}
      >
        <div className="success-icon text-green-500 mb-6">
          <FiCheckCircle size={80} />
        </div>
        <h2 className="success-message text-2xl font-bold text-white mb-2">
          登录成功
        </h2>
        <p className="success-message text-gray-300">
          欢迎回来，{email.split('@')[0]}
        </p>
      </div>
      
      <div className="max-w-md w-full space-y-8 bg-gray-800 p-8 rounded-xl shadow-2xl">
        <div className="text-center">
          <h2 ref={titleRef} className="text-3xl font-bold text-white">
            登录账户
          </h2>
          <p ref={subtitleRef} className="mt-2 text-sm text-gray-400">
            欢迎回来！请登录您的账户以继续
          </p>
        </div>
        
        {error && (
          <div className="bg-red-500/20 border border-red-500 text-red-100 px-4 py-3 rounded relative" role="alert">
            <span className="block sm:inline">{error}</span>
          </div>
        )}
        
        <form ref={formRef} className="mt-8 space-y-6" onSubmit={handleSubmit}>
          <div className="space-y-4">
            <div>
              <label htmlFor="email" className="block text-sm font-medium text-gray-300 mb-1">
                邮箱地址
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <FiMail className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  id="email"
                  name="email"
                  type="email"
                  autoComplete="email"
                  required
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className="block w-full pl-10 pr-3 py-2 border border-gray-600 rounded-md bg-gray-700 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent"
                  placeholder="您的邮箱地址"
                />
              </div>
            </div>
            
            <div>
              <label htmlFor="password" className="block text-sm font-medium text-gray-300 mb-1">
                密码
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <FiLock className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  id="password"
                  name="password"
                  type={showPassword ? "text" : "password"}
                  autoComplete="current-password"
                  required
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className="block w-full pl-10 pr-10 py-2 border border-gray-600 rounded-md bg-gray-700 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent"
                  placeholder="您的密码"
                />
                <div 
                  className="absolute inset-y-0 right-0 pr-3 flex items-center cursor-pointer"
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? (
                    <FiEyeOff className="h-5 w-5 text-gray-400 hover:text-gray-300" />
                  ) : (
                    <FiEye className="h-5 w-5 text-gray-400 hover:text-gray-300" />
                  )}
                </div>
              </div>
            </div>
          </div>
          
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <input
                id="remember-me"
                name="remember-me"
                type="checkbox"
                checked={rememberMe}
                onChange={(e) => setRememberMe(e.target.checked)}
                className="h-4 w-4 text-red-600 focus:ring-red-500 border-gray-500 rounded bg-gray-700"
              />
              <label htmlFor="remember-me" className="ml-2 block text-sm text-gray-300">
                记住我
              </label>
            </div>
            
            <div className="text-sm">
              <Link href="/auth/forgot-password" className="text-red-500 hover:text-red-400">
                忘记密码?
              </Link>
            </div>
          </div>
          
          <div>
            <button
              type="submit"
              disabled={isLoading}
              className="group relative w-full flex justify-center py-2 px-4 border border-transparent rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors"
            >
              {isLoading ? (
                <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
              ) : "登录"}
            </button>
          </div>
          
          <div className="mt-4 text-center">
            <span className="text-sm text-gray-400">
              还没有账户? {' '}
              <Link href="/auth/register" className="text-red-500 hover:text-red-400">
                立即注册
              </Link>
            </span>
          </div>
        </form>
        
        <div className="mt-6">
          <div className="relative">
            <div className="absolute inset-0 flex items-center">
              <div className="w-full border-t border-gray-600"></div>
            </div>
            <div className="relative flex justify-center text-sm">
              <span className="px-4 bg-gray-800 text-gray-400">或通过以下方式登录</span>
            </div>
          </div>
          
          <div className="mt-6 grid grid-cols-3 gap-3">
            <div>
              <a
                href="#"
                className="w-full inline-flex justify-center py-2 px-4 border border-gray-600 rounded-md shadow-sm bg-gray-700 hover:bg-gray-600 transition-colors"
              >
                <FcGoogle className="h-5 w-5" />
              </a>
            </div>
            <div>
              <a
                href="#"
                className="w-full inline-flex justify-center py-2 px-4 border border-gray-600 rounded-md shadow-sm bg-gray-700 hover:bg-gray-600 transition-colors"
              >
                <FiTwitter className="h-5 w-5 text-[#1DA1F2]" />
              </a>
            </div>
            <div>
              <a
                href="#"
                className="w-full inline-flex justify-center py-2 px-4 border border-gray-600 rounded-md shadow-sm bg-gray-700 hover:bg-gray-600 transition-colors"
              >
                <FiGithub className="h-5 w-5 text-white" />
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
} 