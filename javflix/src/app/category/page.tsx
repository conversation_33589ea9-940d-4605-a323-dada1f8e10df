import type { Metadata } from 'next';
import { generateMetadata as createMetadata } from '@/lib/seo';
import { Suspense } from 'react';
import CategoryClient from './CategoryClient';

// 导出静态元数据
export const metadata: Metadata = createMetadata({
  title: '视频分类导航',
  description: 'JAVFLIX视频分类导航，包含无码有码、步兵骑兵、中文字幕、女优作品、情节分类等多种分类方式，一站式满足您的观影需求',
  path: '/category',
  keywords: [
    '日本AV分类', '视频分类', '无码分类', '有码分类', '中文字幕', 
    '情节分类', '职业系列', '特殊爱好', 'JAV分类导航', '女优分类'
  ],
});

// 加载占位符组件
function CategoryLoading() {
  return (
    <div className="min-h-screen bg-gray-900 text-white pt-20 pb-12">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <div className="h-12 bg-gray-800 rounded-lg animate-pulse mb-4 mx-auto max-w-md"></div>
          <div className="h-6 bg-gray-700 rounded-lg animate-pulse mx-auto max-w-sm"></div>
        </div>
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
          {[...Array(12)].map((_, i) => (
            <div key={i} className="bg-gray-800 rounded-lg p-4 animate-pulse">
              <div className="h-32 bg-gray-700 rounded-lg mb-4"></div>
              <div className="h-4 bg-gray-700 rounded mb-2"></div>
              <div className="h-3 bg-gray-600 rounded w-3/4"></div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}

// 主页面是服务器组件，只用于包装客户端组件
export default function CategoryPage() {
  return (
    <Suspense fallback={<CategoryLoading />}>
      <CategoryClient />
    </Suspense>
  );
}