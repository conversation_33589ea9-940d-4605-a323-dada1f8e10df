import { Metadata } from 'next';
import { generateMetadata as createMetadata } from '@/lib/seo';
import CategoryClient from './CategoryClient';

// 模拟分类数据
const categories = {
  'zhufu': {
    name: '制服',
    description: '制服类视频包含各种制服诱惑，校服、OL、空姐等多种职业制服，满足您的制服情结。',
  },
  'teacher': {
    name: '女教师',
    description: '女教师类视频展现知性与魅力的结合，特色场景与角色扮演让这一分类深受欢迎。',
  },
  'couple': {
    name: '情侣',
    description: '情侣类视频展现甜蜜的爱情故事，记录情侣间的亲密互动和浪漫时刻。',
  },
  'juru': {
    name: '巨乳',
    description: '巨乳类视频专注于展现丰满身材的魅力，这一分类拥有大量的粉丝和高质量作品。',
  },
};

// 动态生成分类详情页元数据
export async function generateMetadata({ params }: { params: Promise<{ slug: string }> }): Promise<Metadata> {
  const resolvedParams = await params;
  const { slug } = resolvedParams;
  const category = categories[slug as keyof typeof categories] || {
    name: '未知分类',
    description: '该分类不存在或已被删除。',
  };
  
  return createMetadata({
    title: `${category.name}系列视频`,
    description: `${category.description} 浏览JAVFLIX上所有${category.name}分类的高清日本AV影片，无码有码应有尽有。`,
    path: `/category/${slug}`,
    keywords: [
      `${category.name}`, '日本AV', '在线观看', '高清视频', 
      '分类合集', '无码有码', '中文字幕', '专题视频'
    ],
  });
}

export default async function CategoryPage({ params }: { params: Promise<{ slug: string }> }) {
  const resolvedParams = await params;
  const { slug } = resolvedParams;
  
  return <CategoryClient slug={slug} />;
} 