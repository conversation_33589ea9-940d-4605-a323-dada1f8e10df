'use client';

import React, { useEffect, useRef, useState, useCallback } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { FiFilter, FiChevronRight, FiChevronLeft, FiStar, FiPlayCircle } from 'react-icons/fi';
import { useParams } from 'next/navigation';
import gsap from 'gsap';

interface CategoryData {
  id: number;
  name: string;
  description: string;
  movieCount: number;
  imageUrl: string;
  sampleMovies: any[];
}

interface Movie {
  id: number;
  title: string;
  imageUrl: string;
  duration: string;
  views: string;
  date: string;
  slug: string;
  stars: any[];
  description: string;
}

interface VideoData {
  movies: Movie[];
  pagination: {
    currentPage: number;
    totalPages: number;
    totalItems: number;
    hasNextPage: boolean;
  } | null;
}

// 排序选项
const sortOptions = [
  { label: '最新上传', value: 'latest' },
  { label: '观看最多', value: 'views' },
  { label: '评分最高', value: 'rating' },
  { label: '时长最长', value: 'duration' },
];

interface CategoryClientProps {
  slug: string;
}

// 获取分类数据
async function fetchCategoryData(slug: string): Promise<CategoryData | null> {
  try {
    // 在客户端使用API路由
    const response = await fetch(`/api/db/genres/${slug}`);
    
    if (!response.ok) {
      if (response.status === 404) {
        console.error(`Category not found: ${slug}`);
        return null;
      } else if (response.status === 400) {
        console.error(`Invalid category identifier: ${slug}`);
        return null;
      } else {
        console.error(`Failed to fetch category data: ${response.status}`);
        return null;
      }
    }
    
    const result = await response.json();
    
    if (result.success && result.data) {
      return {
        id: result.data.id,
        name: result.data.name,
        description: result.data.description || `${result.data.name}分类下的精彩视频内容`,
        movieCount: result.data.movie_count || 0,
        imageUrl: result.data.image_url || '/images/defaults/category_default.jpg',
        sampleMovies: result.data.sample_movies || []
      };
    }
    
    return null;
  } catch (error) {
    console.error('Error fetching category data:', error);
    return null;
  }
}

// 获取分类视频数据
async function fetchCategoryMovies(categoryId: number, page: number = 1, limit: number = 12): Promise<VideoData> {
  try {
    // 在客户端使用API路由
    const response = await fetch(`/api/db/movies/genre/${categoryId}?page=${page}&limit=${limit}`);
    
    if (!response.ok) {
      console.error(`Failed to fetch movies: ${response.status}`);
      return { movies: [], pagination: null };
    }
    
    const result = await response.json();
    
    if (result.success && result.data && result.data.items) {
      return {
        movies: result.data.items.map((movie: any) => ({
          id: movie.id,
          title: movie.title,
          imageUrl: movie.cached_image_url || movie.image_url || '/images/defaults/video_default.jpg',
          duration: movie.duration || '未知',
          views: movie.view_count ? formatViewCount(movie.view_count) : '0',
          date: movie.release_date ? formatDate(movie.release_date) : '未知',
          slug: movie.movie_id || `movie-${movie.id}`,
          stars: movie.stars || [],
          description: movie.description || ''
        })),
        pagination: result.data.pagination
      };
    }
    
    return { movies: [], pagination: null };
  } catch (error) {
    console.error('Error fetching movies:', error);
    return { movies: [], pagination: null };
  }
}

// 辅助函数：格式化观看次数
function formatViewCount(count: number): string {
  if (count >= 1000000) {
    return `${(count / 1000000).toFixed(1)}M`;
  } else if (count >= 1000) {
    return `${(count / 1000).toFixed(1)}K`;
  }
  return count.toString();
}

// 辅助函数：格式化日期
function formatDate(dateString: string): string {
  try {
    const date = new Date(dateString);
    return date.toISOString().split('T')[0];
  } catch {
    return dateString;
  }
}

export default function CategoryClient({ slug }: CategoryClientProps) {
  // 获取当前的 locale 参数
  const params = useParams();
  const locale = params.locale || 'zh-CN';
  
  const [categoryData, setCategoryData] = useState<CategoryData | null>(null);
  const [videoData, setVideoData] = useState<VideoData>({ movies: [], pagination: null });
  const [currentPage, setCurrentPage] = useState(1);
  const [sortBy, setSortBy] = useState('latest');
  const [isLoading, setIsLoading] = useState(true);
  const [isLoadingMovies, setIsLoadingMovies] = useState(false);
  
  const headerRef = useRef<HTMLDivElement>(null);
  const contentRef = useRef<HTMLDivElement>(null);
  const videoRefs = useRef<Array<HTMLDivElement | null>>([]);
  
  // 初始加载分类数据
  useEffect(() => {
    const loadCategoryData = async () => {
      setIsLoading(true);
      const data = await fetchCategoryData(slug);
      setCategoryData(data);
      
      if (data) {
        // 加载第一页视频
        const moviesData = await fetchCategoryMovies(data.id, 1);
        setVideoData(moviesData);
      }
      
      setIsLoading(false);
    };
    
    loadCategoryData();
  }, [slug]);
  
  // 处理页面变化
  const handlePageChange = async (newPage: number) => {
    if (!categoryData || !videoData.pagination) return;
    if (newPage < 1 || newPage > videoData.pagination.totalPages) return;
    
    setIsLoadingMovies(true);
    setCurrentPage(newPage);
    
    // 滚动到顶部
    window.scrollTo({ top: 0, behavior: 'smooth' });
    
    const newMoviesData = await fetchCategoryMovies(categoryData.id, newPage);
    setVideoData(newMoviesData);
    setIsLoadingMovies(false);
    
    // 触发视频卡片动画
    setTimeout(() => animateVideoCards(), 100);
  };
  
  // 处理排序变化
  const handleSortChange = async (e: React.ChangeEvent<HTMLSelectElement>) => {
    if (!categoryData) return;
    
    setSortBy(e.target.value);
    setIsLoadingMovies(true);
    
    // 重新加载第一页数据（这里可以根据需要添加排序参数）
    const newMoviesData = await fetchCategoryMovies(categoryData.id, 1);
    setVideoData(newMoviesData);
    setCurrentPage(1);
    setIsLoadingMovies(false);
    
    // 触发视频卡片动画
    setTimeout(() => animateVideoCards(), 100);
  };
  
  // 生成页码数组
  const getPageNumbers = () => {
    if (!videoData.pagination) return [];
    
    const totalPages = videoData.pagination.totalPages;
    const pages: (number | string)[] = [];
    
    // 显示当前页附近的页码
    const range = 2; // 当前页左右各显示2个页码
    
    // 起始页码
    let startPage = Math.max(1, currentPage - range);
    // 结束页码
    let endPage = Math.min(totalPages, currentPage + range);
    
    // 调整使显示的页码数一致
    if (startPage <= 3) {
      endPage = Math.min(totalPages, 5);
    }
    
    if (endPage >= totalPages - 2) {
      startPage = Math.max(1, totalPages - 4);
    }
    
    // 第一页
    if (startPage > 1) {
      pages.push(1);
      if (startPage > 2) {
        pages.push('...');
      }
    }
    
    // 中间页
    for (let i = startPage; i <= endPage; i++) {
      pages.push(i);
    }
    
    // 最后一页
    if (endPage < totalPages) {
      if (endPage < totalPages - 1) {
        pages.push('...');
      }
      pages.push(totalPages);
    }
    
    return pages;
  };
  
  // 设置视频卡片引用
  const setVideoRef = (el: HTMLDivElement | null, index: number) => {
    videoRefs.current[index] = el;
  };
  
  // 视频卡片动画
  const animateVideoCards = useCallback(() => {
    videoRefs.current.forEach((ref, index) => {
      if (ref) {
        gsap.fromTo(
          ref,
          { opacity: 0, y: 30 },
          { 
            opacity: 1, 
            y: 0, 
            duration: 0.5, 
            delay: 0.1 + index * 0.05,
            ease: 'power3.out'
          }
        );
      }
    });
  }, []);
  
  useEffect(() => {
    if (isLoading || isLoadingMovies || videoData.movies.length === 0) return;
    
    videoRefs.current = videoRefs.current.slice(0, videoData.movies.length);
    
    // 动画效果
    if (headerRef.current) {
      gsap.fromTo(
        headerRef.current,
        { opacity: 0, y: -20 },
        { opacity: 1, y: 0, duration: 0.8, ease: 'power2.out' }
      );
    }
    
    if (contentRef.current) {
      gsap.fromTo(
        contentRef.current,
        { opacity: 0 },
        { opacity: 1, duration: 0.5, delay: 0.3, ease: 'power2.out' }
      );
    }
    
    // 调用视频卡片动画
    animateVideoCards();
  }, [isLoading, isLoadingMovies, videoData.movies.length, animateVideoCards]);

  // 加载状态
  if (isLoading) {
    return (
      <div className="min-h-screen pt-20 pb-16 flex justify-center items-center">
        <div className="w-12 h-12 border-4 border-gray-300 border-t-red-500 rounded-full animate-spin"></div>
        <p className="ml-3 text-lg font-medium">加载分类信息中...</p>
      </div>
    );
  }
  
  // 分类不存在
  if (!categoryData) {
    return (
      <div className="min-h-screen pt-20 pb-16 flex justify-center items-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-white mb-4">分类不存在</h1>
          <p className="text-gray-400">该分类不存在或已被删除。</p>
          <Link href={`/${locale}`} className="mt-4 inline-block bg-red-600 text-white px-6 py-2 rounded-lg hover:bg-red-700 transition-colors">
            返回首页
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen pt-20 pb-16">
      <div className="container mx-auto px-4">
        {/* 分类头部信息 */}
        <div 
          ref={headerRef}
          className="relative bg-gradient-to-r from-gray-900 to-black rounded-xl overflow-hidden mb-8"
        >
          <div className="absolute inset-0 opacity-25">
            <Image
              src={categoryData.imageUrl}
              alt={categoryData.name}
              fill
              className="object-cover"
              priority
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black to-transparent"></div>
          </div>
          
          <div className="relative z-10 p-6 md:p-8">
            <div className="flex flex-col max-w-3xl">
              <div className="flex items-center">
                <span className="text-xs text-red-500 font-semibold uppercase tracking-wider mr-2 bg-red-500/10 px-2 py-1 rounded">分类</span>
                <h1 className="text-2xl md:text-3xl font-bold">{categoryData.name}</h1>
              </div>
              
              <p className="text-gray-300 mt-3 md:text-lg leading-relaxed">
                {categoryData.description}
              </p>
              
              <div className="flex items-center mt-4 text-sm text-gray-400">
                <div className="flex items-center">
                  <FiPlayCircle className="mr-1.5 text-red-500" />
                  <span>{categoryData.movieCount} 部视频</span>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        {/* 视频列表 */}
        <div ref={contentRef}>
          {/* 排序选项 */}
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-xl font-bold flex items-center">
              <FiFilter className="mr-2 text-red-500" />
              所有视频
            </h2>
            
            <div className="flex items-center">
              <label htmlFor="sortBy" className="text-sm text-gray-400 mr-2">
                排序方式:
              </label>
              <div className="relative">
                <select
                  id="sortBy"
                  value={sortBy}
                  onChange={handleSortChange}
                  disabled={isLoadingMovies}
                  className="block text-sm pl-3 pr-10 py-2 border border-gray-600 rounded-md bg-gray-700 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent appearance-none"
                >
                  {sortOptions.map(option => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>
            </div>
          </div>
          
          {/* 视频网格 */}
          {isLoadingMovies ? (
            <div className="flex justify-center items-center py-12">
              <div className="w-12 h-12 border-4 border-gray-300 border-t-red-500 rounded-full animate-spin"></div>
              <p className="ml-3 text-lg font-medium">加载中...</p>
            </div>
          ) : (
            <>
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-5 mb-8">
                {videoData.movies.map((movie, index) => (
                  <div
                    key={movie.id}
                    ref={(el) => setVideoRef(el, index)}
                    className="flex flex-col space-y-2"
                  >
                    {/* 视频图片区块 */}
                    <Link href={`/${locale}/video/${movie.slug}`} className="block">
                      <div className="relative overflow-hidden rounded-xl group">
                        <div className="relative h-44 md:h-48 overflow-hidden">
                          <Image
                            src={movie.imageUrl}
                            alt={movie.title}
                            fill
                            className="object-cover transition-transform duration-500 group-hover:scale-110"
                            sizes="(max-width: 768px) 100vw, 400px"
                          />
                          <div className="absolute inset-0 bg-gradient-to-t from-black to-transparent opacity-70 group-hover:opacity-60 transition-opacity duration-300"></div>
                          
                          {/* 时长标签 */}
                          <div className="absolute top-2 right-2 bg-red-600 text-white text-xs px-2 py-1 rounded font-medium">
                            {movie.duration}
                          </div>
                          
                          {/* 播放图标 - 悬停时显示 */}
                          <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                            <div className="bg-red-600 bg-opacity-80 rounded-full p-3 transform group-hover:scale-110 transition-transform duration-300">
                              <FiPlayCircle className="text-white" size={30} />
                            </div>
                          </div>
                        </div>
                      </div>
                    </Link>
                    
                    {/* 视频信息区块 */}
                    <div className="flex flex-col">
                      <Link href={`/${locale}/video/${movie.slug}`} className="block">
                        <h3 className="text-white font-semibold text-base truncate hover:text-red-500 transition-colors duration-300">
                          {movie.title}
                        </h3>
                      </Link>
                      <div className="flex justify-between items-center mt-1">
                        <p className="text-gray-400 text-xs">{movie.views} 次观看</p>
                        <span className="text-xs text-gray-400">{movie.date}</span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
              
              {/* 分页 */}
              {videoData.pagination && videoData.pagination.totalPages > 1 && (
                <div className="flex justify-center items-center mt-8">
                  <div className="flex items-center space-x-2">
                    {/* 上一页按钮 */}
                    <button
                      onClick={() => handlePageChange(currentPage - 1)}
                      disabled={currentPage === 1 || isLoadingMovies}
                      className="flex items-center px-4 py-2 text-sm font-medium text-gray-300 bg-gray-700 border border-gray-600 rounded-l-lg hover:bg-gray-600 hover:text-white disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      <FiChevronLeft className="mr-1" />
                      上一页
                    </button>
                    
                    {/* 页码 */}
                    {getPageNumbers().map((page, index) => (
                      <React.Fragment key={index}>
                        {page === '...' ? (
                          <span className="px-4 py-2 text-sm font-medium text-gray-400">...</span>
                        ) : (
                          <button
                            onClick={() => handlePageChange(page as number)}
                            disabled={isLoadingMovies}
                            className={`px-4 py-2 text-sm font-medium border border-gray-600 ${
                              currentPage === page
                                ? 'text-white bg-red-600 border-red-600'
                                : 'text-gray-300 bg-gray-700 hover:bg-gray-600 hover:text-white'
                            } disabled:cursor-not-allowed`}
                          >
                            {page}
                          </button>
                        )}
                      </React.Fragment>
                    ))}
                    
                    {/* 下一页按钮 */}
                    <button
                      onClick={() => handlePageChange(currentPage + 1)}
                      disabled={currentPage === videoData.pagination.totalPages || isLoadingMovies}
                      className="flex items-center px-4 py-2 text-sm font-medium text-gray-300 bg-gray-700 border border-gray-600 rounded-r-lg hover:bg-gray-600 hover:text-white disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      下一页
                      <FiChevronRight className="ml-1" />
                    </button>
                  </div>
                </div>
              )}
            </>
          )}
        </div>
      </div>
    </div>
  );
} 