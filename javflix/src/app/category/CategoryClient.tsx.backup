'use client';

import { useEffect, useState, useRef, useCallback } from 'react';
import { usePathname, useParams, useSearchParams } from 'next/navigation';
import Link from 'next/link';
import { FiChevronRight, FiChevronLeft, FiSearch, FiFilter, FiGrid, FiList, FiHash, FiTrendingUp, FiUsers } from 'react-icons/fi';
import gsap from 'gsap';

// 分类数据结构类型
interface Category {
  id: number;
  name: string;
  movie_count: number;
  slug?: string;
  icon?: string;
}

// API响应类型
interface CategoriesResponse {
  success: boolean;
  data: {
    items: Category[];
    pagination?: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
    };
  };
  message: string;
  code: number;
}

const CategoryClient = () => {
  const params = useParams();
  const searchParams = useSearchParams();
  const locale = params?.locale || 'zh-Hans';
  
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState('name'); // name, movie_count, id
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [isSearching, setIsSearching] = useState(false);
  
  const containerRef = useRef<HTMLDivElement>(null);
  const categoryRefs = useRef<Array<HTMLDivElement | null>>([]);
  
  const perPage = 36; // 增加每页显示数量

  // 从URL参数中读取初始状态
  useEffect(() => {
    const page = searchParams.get('page');
    const search = searchParams.get('search');
    const sort = searchParams.get('sort');
    
    if (page) setCurrentPage(parseInt(page) || 1);
    if (search) setSearchTerm(search);
    if (sort) setSortBy(sort);
  }, [searchParams]);

  // 获取分类数据
  const fetchCategories = useCallback(async (page: number = 1, search: string = '', sort: string = 'name') => {
    setLoading(true);
    setError(null);
    
    try {
      console.log('正在获取分类数据...', { page, search, sort });
      
      // 构建API参数 - 不传递sort给后端，在客户端排序
      const params = new URLSearchParams({
        page: '1', // 获取所有数据用于客户端排序和搜索
        limit: '100' // 获取更多数据
      });
      
      const response = await fetch(`/api/db/genres?${params.toString()}`);
      
      if (!response.ok) {
        throw new Error(`API请求失败: ${response.status} ${response.statusText}`);
      }
      
      const result: CategoriesResponse = await response.json();
      console.log('分类API响应:', result);
      
      if (result.success && result.data?.items) {
        let allCategories = result.data.items;
        
        // 客户端搜索过滤
        if (search.trim()) {
          allCategories = allCategories.filter(category =>
            category.name.toLowerCase().includes(search.toLowerCase())
          );
        }
        
        // 客户端排序
        allCategories.sort((a, b) => {
          switch (sort) {
            case 'movie_count':
              return (b.movie_count || 0) - (a.movie_count || 0);
            case 'id':
              return a.id - b.id;
            case 'name':
            default:
              return a.name.localeCompare(b.name);
          }
        });
        
        // 客户端分页
        const startIndex = (page - 1) * perPage;
        const endIndex = startIndex + perPage;
        const paginatedCategories = allCategories.slice(startIndex, endIndex);
        
        // 处理数据并添加额外属性
        const processedCategories = paginatedCategories.map((category) => ({
          ...category,
          slug: generateSlug(category.name),
          icon: getIconForCategory(category.name)
        }));
        
        setCategories(processedCategories);
        setTotalPages(Math.ceil(allCategories.length / perPage));
        
        console.log('成功设置分类数据:', processedCategories);
        console.log(`总计 ${allCategories.length} 个分类，共 ${Math.ceil(allCategories.length / perPage)} 页`);
      } else {
        throw new Error('数据格式不正确');
      }
    } catch (error) {
      console.error('获取分类数据失败:', error);
      setError(error instanceof Error ? error.message : '未知错误');
      
      // 错误时使用备用数据
      let fallbackCategories = getFallbackCategories();
      
      // 对备用数据也应用搜索和排序
      if (search.trim()) {
        fallbackCategories = fallbackCategories.filter(category =>
          category.name.toLowerCase().includes(search.toLowerCase())
        );
      }
      
      fallbackCategories.sort((a, b) => {
        switch (sort) {
          case 'movie_count':
            return (b.movie_count || 0) - (a.movie_count || 0);
          case 'id':
            return (a.id as number) - (b.id as number);
          case 'name':
          default:
            return a.name.localeCompare(b.name);
        }
      });
      
      const startIndex = (page - 1) * perPage;
      const endIndex = startIndex + perPage;
      const paginatedFallback = fallbackCategories.slice(startIndex, endIndex);
      
      setCategories(paginatedFallback);
      setTotalPages(Math.ceil(fallbackCategories.length / perPage));
    } finally {
      setLoading(false);
    }
  }, [perPage]);

  // 初始加载
  useEffect(() => {
    fetchCategories(currentPage, searchTerm, sortBy);
  }, [currentPage, searchTerm, sortBy, fetchCategories]);

  // 添加动画效果
  useEffect(() => {
    if (loading || categories.length === 0) return;
    
    // 确保refs数组长度与categories匹配
    categoryRefs.current = categoryRefs.current.slice(0, categories.length);
    
    // 分类卡片动画
    categoryRefs.current.forEach((ref, index) => {
      if (ref) {
        gsap.fromTo(
          ref,
          { opacity: 0, y: 20 },
          { 
            opacity: 1, 
            y: 0,
            duration: 0.4, 
            delay: index * 0.02,
            ease: "power2.out"
          }
        );
      }
    });
  }, [loading, categories]);

  // 处理搜索
  const handleSearch = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSearching(true);
    setCurrentPage(1);
    
    try {
      await fetchCategories(1, searchTerm, sortBy);
    } finally {
      setIsSearching(false);
    }
  };

  // 处理排序变化
  const handleSortChange = async (newSort: string) => {
    setSortBy(newSort);
    setCurrentPage(1);
    setIsSearching(true);
    
    try {
      await fetchCategories(1, searchTerm, newSort);
    } finally {
      setIsSearching(false);
    }
  };

  // 处理页面变化
  const handlePageChange = async (newPage: number) => {
    if (newPage === currentPage || newPage < 1 || newPage > totalPages) return;
    
    setCurrentPage(newPage);
    setIsSearching(true);
    
    try {
      await fetchCategories(newPage, searchTerm, sortBy);
      // 滚动到顶部
      window.scrollTo({ top: 0, behavior: 'smooth' });
    } finally {
      setIsSearching(false);
    }
  };

  // 设置ref
  const setRef = (el: HTMLDivElement | null, index: number) => {
    categoryRefs.current[index] = el;
  };

  // 生成slug
  const generateSlug = (name: string): string => {
    return name
      .toLowerCase()
      .replace(/\s+/g, '-')
      .replace(/[^\w\-\u4e00-\u9fa5]/g, '')
      .replace(/\-+/g, '-')
      .trim();
  };

  // 获取分类图标
  const getIconForCategory = (name: string): string => {
    const iconMap: Record<string, string> = {
      '高清': '🔥',
      '无码': '💎',
      '有码': '🎬',
      '中文字幕': '📝',
      '巨乳': '🌸',
      '美女': '💫',
      '制服': '👔',
      '学生': '🎓',
      '人妻': '💍',
      '熟女': '🌹',
      '少女': '🌺',
      '萝莉': '🎀',
      '素人': '🌟',
      '偷拍': '📸',
      '按摩': '💆',
      '温泉': '♨️',
      '野外': '🌲',
      '群交': '👥',
      '肛交': '🔥',
      '口交': '💋',
      '手交': '✋',
      '足交': '👣',
      '乳交': '🤍',
      '69': '♻️',
      '多人': '👫',
      '3P': '3️⃣',
      'VR': '🥽',
      '4K': '📺',
      'cosplay': '🎭',
      '角色扮演': '🎭',
      '动漫': '🎨',
      '游戏': '🎮'
    };
    
    // 查找匹配的关键词
    for (const [key, icon] of Object.entries(iconMap)) {
      if (name.includes(key)) {
        return icon;
      }
    }
    
    return '📂';
  };

  // 格式化数量显示
  const formatCount = (count: number): string => {
    if (count >= 1000) {
      return `${(count / 1000).toFixed(1)}k`;
    }
    return count.toString();
  };

  // 生成分页数字
  const generatePageNumbers = () => {
    const delta = 2;
    const range: number[] = [];
    const rangeWithDots: (number | string)[] = [];
    
    for (let i = Math.max(2, currentPage - delta); i <= Math.min(totalPages - 1, currentPage + delta); i++) {
      range.push(i);
    }
    
    if (currentPage - delta > 2) {
      rangeWithDots.push(1, '...');
    } else {
      rangeWithDots.push(1);
    }
    
    rangeWithDots.push(...range);
    
    if (currentPage + delta < totalPages - 1) {
      rangeWithDots.push('...', totalPages);
    } else {
      if (totalPages > 1) {
        rangeWithDots.push(totalPages);
      }
    }
    
    return rangeWithDots;
  };

  return (
    <div className="min-h-screen bg-gray-900 text-white pt-20 pb-12">
      <div className="container mx-auto px-4">
        {/* 页面标题 - 简洁版 */}
        <div className="text-center mb-12">
          <h1 className="text-4xl md:text-5xl font-bold mb-4 bg-gradient-to-r from-white via-gray-200 to-gray-400 bg-clip-text text-transparent">
            分类浏览
          </h1>
          <p className="text-gray-400 text-lg">探索内容分类</p>
        </div>

        {/* 搜索和控制栏 */}
        <div className="max-w-2xl mx-auto mb-12">
          <form onSubmit={handleSearch} className="relative mb-6">
            <FiSearch className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
            <input
              type="text"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              placeholder="搜索分类..."
              className="w-full pl-12 pr-4 py-4 bg-gray-800/30 border border-gray-700/30 rounded-full text-white placeholder-gray-500 focus:outline-none focus:border-red-500/50 focus:ring-1 focus:ring-red-500/20 transition-all duration-300 backdrop-blur-sm"
            />
            {isSearching && (
              <div className="absolute right-4 top-1/2 transform -translate-y-1/2">
                <div className="animate-spin rounded-full h-5 w-5 border-2 border-gray-700 border-t-red-500"></div>
              </div>
            )}
          </form>
          
          {/* 排序选择 */}
          <div className="flex justify-center">
            <select
              value={sortBy}
              onChange={(e) => handleSortChange(e.target.value)}
              className="bg-gray-800/30 border border-gray-700/30 rounded-full px-6 py-2 text-sm text-gray-300 focus:outline-none focus:border-red-500/50 transition-all duration-300 backdrop-blur-sm"
            >
              <option value="name">按名称排序</option>
              <option value="movie_count">按内容数量排序</option>
              <option value="id">按ID排序</option>
            </select>
          </div>
        </div>

        {/* 分类网格 */}
        {loading ? (
          <div className="relative">
            {/* 背景装饰 */}
            <div className="absolute inset-0 pointer-events-none">
              <div className="absolute top-1/4 left-1/4 w-32 h-32 bg-red-500/10 rounded-full filter blur-2xl"></div>
              <div className="absolute bottom-1/3 right-1/3 w-48 h-48 bg-blue-500/8 rounded-full filter blur-2xl"></div>
            </div>

            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 xl:grid-cols-8 gap-4">
              {[...Array(24)].map((_, index) => (
                <div key={index} className="animate-pulse">
                  <div className="bg-gray-800/30 rounded-lg p-6 text-center">
                    <div className="w-8 h-8 bg-gray-700/30 rounded mx-auto mb-3"></div>
                    <div className="h-4 bg-gray-700/30 w-3/4 rounded mx-auto mb-2"></div>
                    <div className="h-3 bg-gray-700/30 w-1/2 rounded mx-auto"></div>
                  </div>
                </div>
              ))}
          </div>
        ) : viewMode === 'grid' ? (
          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 xl:grid-cols-8 2xl:grid-cols-10 gap-4">
            {categories.map((category, index) => (
              <div 
                key={category.id}
                ref={(el) => setRef(el, index)}
              >
                <Link href={`/${locale}/category/${category.slug}`}>
                  <div className="group p-6 text-center hover:bg-gray-800/20 rounded-lg transition-all duration-300 hover:scale-105">
                    {/* 图标 */}
                    <div className="text-3xl mb-3 group-hover:scale-110 transition-transform duration-300">
                      {category.icon}
                    </div>
                    
                    {/* 名称 */}
                    <h3 className="text-sm font-medium text-gray-300 group-hover:text-white transition-colors duration-300 mb-2 truncate">
                      {category.name}
                    </h3>
                    
                    {/* 数量 */}
                    <p className="text-xs text-gray-500 group-hover:text-gray-400 transition-colors duration-300">
                      <span className="text-orange-400 font-semibold">{formatCount(category.movie_count || 0)}</span>
                    </p>
                  </div>
                </Link>
              </div>
            ))}
          </div>
        ) : (
          <div className="max-w-2xl mx-auto space-y-2">
            {categories.map((category, index) => (
              <div 
                key={category.id}
                ref={(el) => setRef(el, index)}
              >
                <Link href={`/${locale}/category/${category.slug}`}>
                  <div className="group flex items-center justify-between p-4 hover:bg-gray-800/20 rounded-lg transition-all duration-300">
                    <div className="flex items-center space-x-4">
                      <span className="text-2xl group-hover:scale-110 transition-transform duration-300">
                        {category.icon}
                      </span>
                      <div>
                        <h3 className="text-gray-300 group-hover:text-white transition-colors duration-300 font-medium">
                          {category.name}
                        </h3>
                        <p className="text-sm text-gray-500 group-hover:text-gray-400 transition-colors duration-300">
                          <span className="text-orange-400 font-semibold">{formatCount(category.movie_count || 0)}</span> 个内容
                        </p>
                      </div>
                    </div>
                    <FiChevronRight className="text-gray-500 group-hover:text-gray-300 transition-colors duration-300" />
                  </div>
                </Link>
              </div>
            ))}
          </div>
        )}

        {/* 空状态 */}
        {!loading && categories.length === 0 && (
          <div className="text-center py-20">
            <div className="text-6xl mb-4">🔍</div>
            <h3 className="text-xl font-medium mb-2 text-gray-300">没有找到分类</h3>
            <p className="text-gray-500 mb-6">尝试调整搜索条件</p>
            {searchTerm && (
              <button 
                onClick={() => {
                  setSearchTerm('');
                  fetchCategories(1, '', sortBy);
                }}
                className="px-6 py-2 bg-red-500/20 text-red-400 rounded-full hover:bg-red-500/30 transition-colors duration-300"
              >
                清除搜索
              </button>
            )}
          </div>
        )}

        {/* 分页 */}
        {!loading && categories.length > 0 && totalPages > 1 && (
          <div className="flex justify-center space-x-2 mt-12">
            {/* 上一页 */}
            <button
              onClick={() => handlePageChange(currentPage - 1)}
              disabled={currentPage === 1}
              className="w-10 h-10 flex items-center justify-center rounded-full bg-gray-800/50 hover:bg-gray-700/50 transition-all duration-200 text-gray-300 hover:text-white disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <FiChevronLeft size={16} />
            </button>

            {/* 页码 */}
            {generatePageNumbers().map((pageNum, index) => (
              <div key={index}>
                {pageNum === '...' ? (
                  <span className="w-10 h-10 flex items-center justify-center text-gray-500">...</span>
                ) : (
                  <button
                    onClick={() => handlePageChange(pageNum as number)}
                    className={`w-10 h-10 flex items-center justify-center rounded-full transition-all duration-200 text-sm font-medium ${
                      currentPage === pageNum
                        ? 'bg-red-500 text-white shadow-lg shadow-red-500/25'
                        : 'bg-gray-800/50 hover:bg-gray-700/50 text-gray-300 hover:text-white'
                    }`}
                  >
                    {pageNum}
                  </button>
                )}
              </div>
            ))}

            {/* 下一页 */}
            <button
              onClick={() => handlePageChange(currentPage + 1)}
              disabled={currentPage === totalPages}
              className="w-10 h-10 flex items-center justify-center rounded-full bg-gray-800/50 hover:bg-gray-700/50 transition-all duration-200 text-gray-300 hover:text-white disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <FiChevronRight size={16} />
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

// 备用分类数据
function getFallbackCategories(): Category[] {
  return [
    { id: 1, name: '高清视频', movie_count: 1256, icon: '🔥' },
    { id: 2, name: '无码作品', movie_count: 892, icon: '💎' },
    { id: 3, name: '中文字幕', movie_count: 734, icon: '📝' },
    { id: 4, name: '巨乳美女', movie_count: 621, icon: '🌸' },
    { id: 5, name: '制服诱惑', movie_count: 543, icon: '👔' },
    { id: 6, name: '人妻熟女', movie_count: 456, icon: '💍' },
    { id: 7, name: '学生妹', movie_count: 398, icon: '🎓' },
    { id: 8, name: '素人出演', movie_count: 321, icon: '🌟' },
    { id: 9, name: 'VR体验', movie_count: 287, icon: '🥽' },
    { id: 10, name: '4K超清', movie_count: 234, icon: '📺' },
    { id: 11, name: 'Cosplay', movie_count: 198, icon: '🎭' },
    { id: 12, name: '温泉系列', movie_count: 167, icon: '♨️' }
  ].map(category => ({
    ...category,
    slug: category.name.toLowerCase().replace(/\s+/g, '-')
  }));
}

export default CategoryClient; 