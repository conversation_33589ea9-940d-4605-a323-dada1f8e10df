// 优化后的首页 - 使用ISR策略
import { Metadata } from 'next';
import { Suspense, useState, useEffect } from 'react';
import Hero from '@/components/Hero';
import RecentVideos from '@/components/RecentVideos';
import RecommendedVideos from '@/components/RecommendedVideos';
import Categories from '@/components/Categories';
import ActressShowcase from '@/components/ActressShowcase';

// 类型定义
interface Video {
  id: string;
  title: string;
  image: string;
  timeAgo: string;
}

interface Star {
  id: string;
  name: string;
  image: string;
}

interface Category {
  id: string;
  name: string;
  movieCount: number;
}

// 静态数据获取 - 构建时生成，定期重新生成
async function getStaticData() {
  try {
    const [popularVideos, recentVideos, topStars, categories] = await Promise.all([
      fetch(`${process.env.INTERNAL_API_URL}/api/db/movies/popular?limit=20`).then(r => r.json()),
      fetch(`${process.env.INTERNAL_API_URL}/api/db/movies/recent?limit=15`).then(r => r.json()),
      fetch(`${process.env.INTERNAL_API_URL}/api/db/stars/popular?limit=12`).then(r => r.json()),
      fetch(`${process.env.INTERNAL_API_URL}/api/db/genres/popular?limit=10`).then(r => r.json())
    ]);

    return {
      popularVideos: popularVideos.data?.items || [],
      recentVideos: recentVideos.data?.items || [],
      topStars: topStars.data?.items || [],
      categories: categories.data?.items || [],
      generatedAt: new Date().toISOString()
    };
  } catch (error) {
    console.error('获取静态数据失败:', error);
    return {
      popularVideos: [],
      recentVideos: [],
      topStars: [],
      categories: [],
      generatedAt: new Date().toISOString()
    };
  }
}

// 生成静态props，支持ISR
export async function generateStaticParams() {
  return []; // 首页不需要动态参数
}

// 使用React Server Components获取数据
export default async function HomePage() {
  const staticData = await getStaticData();

  return (
    <main className="min-h-screen">
      {/* 静态内容 - 在构建时生成 */}
      <Suspense fallback={<div className="animate-pulse h-96 bg-gray-800"></div>}>
        <Hero />
      </Suspense>

      <div className="container mx-auto px-4 space-y-12">
        {/* 热门视频 - 静态数据 */}
        <section>
          <h2 className="text-2xl font-bold mb-6">热门视频</h2>
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-5 gap-6">
            {staticData.popularVideos.map((video: Video) => (
              <VideoCard key={video.id} video={video} size="medium" />
            ))}
          </div>
        </section>

        {/* 最新视频 - 静态数据 */}
        <section>
          <h2 className="text-2xl font-bold mb-6">最新发布</h2>
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-5 gap-6">
            {staticData.recentVideos.map((video: Video) => (
              <VideoCard key={video.id} video={video} size="medium" />
            ))}
          </div>
        </section>

        {/* 热门演员 - 静态数据 */}
        <section>
          <h2 className="text-2xl font-bold mb-6">热门演员</h2>
          <div className="grid grid-cols-3 md:grid-cols-6 lg:grid-cols-8 gap-4">
            {staticData.topStars.map((star: Star) => (
              <StarCard key={star.id} star={star} />
            ))}
          </div>
        </section>

        {/* 分类导航 - 静态数据 */}
        <section>
          <h2 className="text-2xl font-bold mb-6">热门分类</h2>
          <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
            {staticData.categories.map((category: Category) => (
              <CategoryCard key={category.id} category={category} />
            ))}
          </div>
        </section>

        {/* 实时推荐 - 客户端加载 */}
        <Suspense fallback={<div className="animate-pulse h-64 bg-gray-800 rounded"></div>}>
          <RealtimeRecommendations />
        </Suspense>
      </div>

      {/* 页面生成信息 - 开发时显示 */}
      {process.env.NODE_ENV === 'development' && (
        <div className="fixed bottom-4 right-4 bg-gray-800 text-white p-2 rounded text-xs">
          生成时间: {new Date(staticData.generatedAt).toLocaleString()}
        </div>
      )}
    </main>
  );
}

// 实时推荐组件 - 客户端渲染
function RealtimeRecommendations() {
  const [recommendations, setRecommendations] = useState<Video[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // 客户端获取个性化推荐
    async function loadRecommendations() {
      try {
        const response = await fetch('/api/recommendations?limit=10');
        const data = await response.json();
        setRecommendations(data.data || []);
      } catch (error) {
        console.error('加载推荐失败:', error);
      } finally {
        setLoading(false);
      }
    }

    loadRecommendations();
  }, []);

  if (loading) {
    return <div className="animate-pulse h-64 bg-gray-800 rounded"></div>;
  }

  return (
    <section>
      <h2 className="text-2xl font-bold mb-6">为您推荐</h2>
      <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-5 gap-6">
        {recommendations.map((video) => (
          <VideoCard key={video.id} video={video} size="medium" />
        ))}
      </div>
    </section>
  );
}

// 组件定义
function VideoCard({ video, size = "medium" }: { video: Video; size?: string }) {
  return (
    <div className="bg-gray-800 rounded-lg overflow-hidden hover:scale-105 transition-transform">
      <img src={video.image} alt={video.title} className="w-full h-48 object-cover" />
      <div className="p-3">
        <h3 className="font-semibold truncate">{video.title}</h3>
        <p className="text-sm text-gray-400">{video.timeAgo}</p>
      </div>
    </div>
  );
}

function StarCard({ star }: { star: Star }) {
  return (
    <div className="text-center">
      <img src={star.image} alt={star.name} className="w-16 h-16 rounded-full mx-auto mb-2" />
      <p className="text-sm truncate">{star.name}</p>
    </div>
  );
}

function CategoryCard({ category }: { category: Category }) {
  return (
    <div className="bg-gray-800 rounded-lg p-4 hover:bg-gray-700 transition-colors">
      <h3 className="font-semibold">{category.name}</h3>
      <p className="text-sm text-gray-400">{category.movieCount} 部</p>
    </div>
  );
}

export const metadata: Metadata = {
  title: 'JAVFLIX.TV - 首页',
  description: '最新最热门的视频内容',
};