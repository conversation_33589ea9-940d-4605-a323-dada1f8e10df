import { Suspense } from 'react';
import { Metadata } from 'next';
import VideoInfo from '@/components/VideoInfo';
import RelatedVideos from '@/components/RelatedVideos';
import DownloadOptions from '@/components/DownloadOptions';
import { generateVideoJsonLd } from '@/lib/seo';
import { generateMetadata as generateSeoMetadata } from '@/lib/seo';
import dynamic from 'next/dynamic';
import { getTranslations } from '@/i18n';

// 视频加载占位符组件
const VideoLoadingPlaceholder = () => (
  <div className="aspect-video rounded-xl overflow-hidden bg-black shadow-2xl border border-gray-800 flex items-center justify-center">
    <div className="flex flex-col items-center">
      <div className="w-12 h-12 border-4 border-gray-300 border-t-red-500 rounded-full animate-spin"></div>
      <p className="text-white mt-4 font-medium">Loading video...</p>
    </div>
  </div>
);

// 动态导入VideoPlayer组件，确保它只在客户端渲染
const VideoPlayer = dynamic(() => import('@/components/VideoPlayer'), {
  ssr: false,
  loading: () => <VideoLoadingPlaceholder />
});

// 从API获取视频数据的函数（实际应用中会连接到真实数据库）
async function getVideoData(slug: string) {
  // 这里应该是实际的API调用，例如：
  // const response = await fetch(`https://api.javflix.tv/videos/${slug}`);
  // const data = await response.json();
  
  // 模拟的数据
  return {
    id: '1',
    title: `${slug} - 高清精彩视频`,
    description: '这是一部高清精彩视频的描述文本。这里可以放置更多的视频详细信息，包括演员、导演、制作公司等。',
    videoUrl: 'https://cdn.plyr.io/static/demo/View_From_A_Blue_Moon_Trailer-576p.mp4',
    posterUrl: '/images/p380b.jpg',
    duration: '2:15:10',
    views: '128K',
    likes: '23K',
    uploadDate: '2023-04-15',
    categories: ['高清', '热门', '推荐'],
    tags: ['标签1', '标签2', '标签3', '标签4'],
    actorNames: ['演员1', '演员2'],
    downloadOptions: [
      { label: '1080p 高清', quality: '1080p', size: '2.1 GB', url: '#download-1080p' },
      { label: '720p 标清', quality: '720p', size: '1.5 GB', url: '#download-720p' },
      { label: '480p 流畅', quality: '480p', size: '850 MB', url: '#download-480p' },
      { label: '360p 省流', quality: '360p', size: '550 MB', url: '#download-360p' }
    ],
    magnetUri: 'magnet:?xt=urn:btih:example_hash&dn=example_file_name&tr=http://tracker.example.com'
  };
}

// 生成元数据
export async function generateMetadata({ params }: { params: { slug: string } }): Promise<Metadata> {
  const videoData = await getVideoData(params.slug);
  
  return generateSeoMetadata({
    title: videoData.title,
    description: videoData.description,
    path: `/video/${params.slug}`,
    imageUrl: `https://javflix.tv${videoData.posterUrl}`,
    type: 'video.movie',
    keywords: [...videoData.tags, ...videoData.categories],
  });
}

export default async function VideoPage({ params }: { params: { slug: string } }) {
  const videoData = await getVideoData(params.slug);
  
  // 生成视频的JSON-LD结构化数据
  const videoJsonLd = generateVideoJsonLd({
    id: videoData.id,
    title: videoData.title,
    description: videoData.description,
    thumbnailUrl: `https://javflix.tv${videoData.posterUrl}`,
    uploadDate: videoData.uploadDate,
    duration: `PT${videoData.duration.replace(/:/g, 'H')}M`,
    tags: videoData.tags,
    actorNames: videoData.actorNames,
  });
  
  return (
    <div className="container mx-auto px-4 pt-24 pb-16">
      {/* 结构化数据通过next/script插入 */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(videoJsonLd) }}
      />
      
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <div className="lg:col-span-2">
          {/* 视频播放器 */}
          <div className="mb-6">
            <Suspense fallback={<VideoLoadingPlaceholder />}>
              <VideoPlayer videoUrl={videoData.videoUrl} posterUrl={videoData.posterUrl} />
            </Suspense>
          </div>
          
          {/* 视频信息 */}
          <div className="mb-6">
            <VideoInfo 
              title={videoData.title}
              description={videoData.description}
              views={videoData.views}
              likes={videoData.likes}
              uploadDate={videoData.uploadDate}
              duration={videoData.duration}
              categories={videoData.categories}
              tags={videoData.tags}
              videoId={videoData.id}
              enableRealTimeStats={true}
            />
          </div>
          
          {/* 下载选项 */}
          <div>
            <DownloadOptions 
              options={videoData.downloadOptions}
              magnetUri={videoData.magnetUri}
              onClose={() => {}}
            />
          </div>
        </div>
        
        {/* 相关视频 */}
        <div className="lg:col-span-1">
          <RelatedVideos videoId={parseInt(videoData.id)} />
        </div>
      </div>
    </div>
  );
} 