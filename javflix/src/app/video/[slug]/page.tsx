'use client';

import React, { useState, useEffect, useCallback, useRef } from 'react';
import { useParams, useSearchParams, useRouter } from 'next/navigation';
import Link from 'next/link';
import Image from 'next/image';
import VideoStatsDisplay from '@/components/VideoStatsDisplay';
import VideoSection from '@/components/VideoSection';
import RelatedVideos from '@/components/RelatedVideos';
import MagneticDownloadButton from '@/components/MagneticDownloadButton';
import CommentsButton from '@/components/comments/CommentsButton';
import CommentsDrawer from '@/components/comments/CommentsDrawer';

import { RealtimeStatsProvider } from '@/contexts/RealtimeStatsContext';
import { useClientTranslations } from '@/components/TranslationsProvider';
import { useVideoStats } from '@/hooks/useVideoStats';
import { startSyncManager, cacheVideoInfo } from '@/lib/sync-manager';
import { getCurrentUserId } from '@/lib/user-utils';
import { defaultLocale } from '@/i18n';
import { 
  FiShare2, 
  FiDownload, 
  FiUser, 
  FiCalendar, 
  FiClock, 
  FiTag,
  FiVideo,
  FiAlertCircle,
  FiExternalLink,
  FiGlobe,
  FiEye,
  FiLoader,

  FiRefreshCw,
  FiPlay,
  FiHeart,
  FiBookmark,
  FiStar,
  FiImage,
  FiChevronDown,
  FiThumbsUp,
  FiPlus,
  FiInfo
} from 'react-icons/fi';

// ==================== 类型定义 ====================

interface VideoData {
  id: string;
  code: string;
  title: string;
  description?: string;
  duration?: string;
  releaseDate?: string;
  uploadDate: string;
  created_at?: string;
  coverImage?: string;
  posterUrl?: string;
  videoUrl?: string;
  studio?: string;
  director?: string;
  publisher?: string;
  series?: string;
  actors: Array<{ id?: string; name: string; avatar?: string }>;
  categories: string[];
  tags: string[];
  magnetUri?: string;
  downloadOptions?: any[];
  sampleImages?: Array<{ id: string; alt: string; src: string; thumbnail: string }>;
  similarMovies?: Array<{ id: string; title: string; img: string; href: string }>;
}

// ==================== API 获取函数 ====================

// 获取视频数据
async function fetchVideoData(videoId: string): Promise<VideoData | null> {
  try {
    const port = typeof window !== 'undefined' ? window.location.port || '3000' : '3001';
    const baseUrl = typeof window !== 'undefined' 
      ? `${window.location.protocol}//${window.location.hostname}:${port}`
      : `http://localhost:${port}`;
    
    const response = await fetch(`${baseUrl}/api/video-proxy?video_id=${videoId}`, {
      cache: 'no-store'
    });
    
    if (!response.ok) {
      throw new Error(`API错误: ${response.status}`);
    }
    
    return await response.json();
  } catch (error) {
    console.error('获取视频数据失败:', error);
    return null;
  }
}

// ==================== 🚀 Netflix风格播放页面组件 ====================

export default function VideoPage() {
  const params = useParams();
  const searchParams = useSearchParams();
  const router = useRouter();
  const slug = params.slug as string;
  const locale = defaultLocale; // 使用默认语言
  const code = searchParams.get('code');
  const { t } = useClientTranslations();
  
  // 状态管理
  const [videoData, setVideoData] = useState<VideoData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [syncManagerInitialized, setSyncManagerInitialized] = useState(false);
  const [showFullDescription, setShowFullDescription] = useState(false);
  const [selectedImageIndex, setSelectedImageIndex] = useState<number | null>(null);
  
  // 评论抽屉状态
  const [isCommentsOpen, setIsCommentsOpen] = useState(false);
  const [currentUserId, setCurrentUserId] = useState<string | null>(null);

  // 🔥 集成实时视频统计系统（README架构核心）
  // 使用slug作为videoId，API会自动处理ID转换
  const videoIdForStats = slug || '0';
  const actualVideoId = videoData?.id ? parseInt(videoData.id) || 0 : 0;
  
  const { 
    stats, 
    loading: statsLoading, 
    error: statsError, 
    actions, 
    isConnected,
    isFused,
    userStatus
  } = useVideoStats({
    videoId: videoIdForStats,           // 使用slug，让API处理转换
    enableRealtime: true,               // 始终启用实时更新
    useFusedApi: true                   // 始终使用融合API
  });

  // ==================== 🛡️ 智能数据管理（README架构）====================

  // 初始化同步管理器（仅一次）
  useEffect(() => {
    if (!syncManagerInitialized) {

      startSyncManager();
      setSyncManagerInitialized(true);
    }
  }, [syncManagerInitialized]);

  // 辅助函数，根据当前语言生成URL
  const getLocalizedUrl = (path: string): string => {
    // 使用默认语言，不添加语言前缀
    return path;
  };

  // 加载视频数据并缓存
  const loadVideoData = useCallback(async () => {
    if (!slug) return;

    try {
      setLoading(true);
      setError(null);
      

      const data = await fetchVideoData(slug);
      
      if (data) {
        setVideoData(data);
        
        // 🔄 智能数据缓存（README架构特性）
        const videoInfo = {
          id: parseInt(data.id) || 0,
          title: data.title,
          slug: slug,
          imageUrl: data.coverImage || data.posterUrl || '',
          duration: data.duration || '',
          movie_id: data.code,
          code: data.code,
          coverImage: data.coverImage || data.posterUrl || '',
          posterUrl: data.posterUrl || data.coverImage || ''
        };
        
        // 缓存视频信息到同步管理器
        cacheVideoInfo(videoInfo);

        
        // 🔥 同时设置到window对象，供useVideoStats使用
        if (typeof window !== 'undefined') {
          (window as any).currentVideoData = videoInfo;

        }
        
        // 📊 添加到观看历史（本地缓存优先策略）🔒 用户隔离版本
        try {
          const { userLocalStorage, getCurrentUserId } = await import('@/lib/user-utils');
          const userId = getCurrentUserId();
          
          const history = userLocalStorage.getJSON('watchHistory', []);
          const newItem = {
            id: data.id || data.code,
            title: data.title,
            thumbnail: data.coverImage || data.posterUrl,
            viewedAt: new Date().toISOString(),
            slug: slug,
            code: data.code,
            duration: data.duration
          };
          
          const filteredHistory = history.filter((item: any) => item.id !== newItem.id);
          filteredHistory.unshift(newItem);
          const updatedHistory = filteredHistory.slice(0, 50);
          userLocalStorage.setJSON('watchHistory', updatedHistory);
          
          // 🔥 触发跨页面同步事件，通知个人页面更新观看历史
          window.dispatchEvent(new CustomEvent('watchHistoryChanged', { 
            detail: { 
              newVideo: newItem,
              totalCount: updatedHistory.length,
              userId: userId
            } 
          }));
          

        } catch (historyError) {
          console.warn('⚠️ 更新观看历史失败:', historyError);
        }
      } else {
        setError('无法加载视频数据');
      }
    } catch (err) {
      console.error('❌ 加载视频失败:', err);
      setError(err instanceof Error ? err.message : '加载视频时出错');
    } finally {
      setLoading(false);
    }
  }, [slug]);

  // 初始化数据加载
  useEffect(() => {
    loadVideoData();
  }, [loadVideoData]);

  // 初始化当前用户ID
  useEffect(() => {
    const userId = getCurrentUserId();
    setCurrentUserId(userId);
  }, []);

  // 🔍 智能观看次数增加：仅在页面真正停留时增加
  const hasIncrementedView = useRef(false);
  useEffect(() => {
    if (!videoData || hasIncrementedView.current) return;

    const incrementViewOnStay = () => {
      if (hasIncrementedView.current) return;
      
      // 检查防重复机制
      const lastViewKey = `lastView_${slug}`;
      const lastViewTime = localStorage.getItem(lastViewKey);
      const now = Date.now();
      const tenMinutes = 10 * 60 * 1000; // 增加到10分钟

      if (!lastViewTime || (now - parseInt(lastViewTime)) > tenMinutes) {
        hasIncrementedView.current = true;
        
        // 只有在页面停留3秒后才增加观看次数
        setTimeout(() => {
          actions.incrementViews().then(() => {
            localStorage.setItem(lastViewKey, now.toString());
          }).catch((err) => {
            hasIncrementedView.current = false; // 失败时重置，允许重试
          });
        }, 3000);
      }
    };

    // 页面可见性检测：只有在页面可见时才增加观看次数
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible' && !hasIncrementedView.current) {
        incrementViewOnStay();
      }
    };

    if (document.visibilityState === 'visible') {
      incrementViewOnStay();
    }

    document.addEventListener('visibilitychange', handleVisibilityChange);
    
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [videoData, actions, slug]);

  // ==================== 🎯 用户交互处理 ====================

  // 时间格式化函数 - 完全复制首页calculateTimeAgo的逻辑
  const formatRelativeTime = (dateString: string) => {
    if (!dateString) return '未知时间';
    
    const date = new Date(dateString);
    const now = new Date();
    const seconds = Math.floor((now.getTime() - date.getTime()) / 1000);
    
    let interval = Math.floor(seconds / 31536000);
    if (interval > 1) {
      return `${interval}年前`;
    }
    
    interval = Math.floor(seconds / 2592000);
    if (interval > 1) {
      return `${interval}个月前`;
    }
    
    interval = Math.floor(seconds / 86400);
    if (interval > 1) {
      return `${interval}天前`;
    }
    
    interval = Math.floor(seconds / 3600);
    if (interval > 1) {
      return `${interval}小时前`;
    }
    
    interval = Math.floor(seconds / 60);
    if (interval > 1) {
      return `${interval}分钟前`;
    }
    
    return '刚刚';
  };





  // ==================== 🎨 Netflix风格UI组件 ====================

  // 响应式HTML5视频播放器 - 避免DOM操作冲突
  const renderResponsivePlayer = () => {
    if (!videoData) return null;

    return (
      <div className="relative w-full aspect-video bg-black rounded-lg overflow-hidden">
        <video
          controls
          controlsList="nodownload"
          crossOrigin="anonymous"
          poster={videoData.coverImage || videoData.posterUrl || ''}
          className="w-full h-full object-contain"
          playsInline
          preload="metadata"
          style={{
            width: '100%',
            height: '100%',
            backgroundColor: '#000'
          }}
        >
          <source 
            src={videoData.videoUrl || 'https://cdn.plyr.io/static/demo.mp4'} 
            type="video/mp4" 
          />
          <p className="text-white text-center p-4">
            {t('common.browserNotSupported')}
          </p>
        </video>
        
        {/* 视频加载时的封面覆盖层 */}
        {(videoData.coverImage || videoData.posterUrl) && (
          <div className="absolute inset-0 bg-black flex items-center justify-center pointer-events-none">
            <Image
              src={videoData.coverImage || videoData.posterUrl || ''}
              alt={videoData.title}
              fill
              className="object-cover opacity-50"
              sizes="100vw"
              priority={false}
            />
            <div className="absolute inset-0 bg-black/30" />
          </div>
        )}
      </div>
    );
  };

  // Netflix风格标题和操作区域
  const renderNetflixTitle = () => {
    if (!videoData) return null;

    return (
      <div className="mt-6 mb-8">
        {/* 标题 */}
        <h1 className="text-lg md:text-xl font-bold text-white mb-4 leading-tight">
          {videoData.title}
        </h1>
        
        {/* 统计信息和基本信息组合 */}
        <div className="mb-6 flex flex-wrap items-center gap-6">
          {/* 时间信息 - 移动到最左边 */}
          {(videoData.created_at || videoData.uploadDate) && (
            <span className="flex items-center gap-2 text-gray-300 text-sm">
              <FiClock className="text-red-400" />
              <span title={`采集时间: ${new Date(videoData.created_at || videoData.uploadDate || '').toLocaleDateString()}`}>
                {formatRelativeTime(videoData.created_at || videoData.uploadDate || '')}
              </span>
            </span>
          )}
          
          {/* 统计信息 - 点赞收藏观看次数 */}
          <div className="flex-1">
            <VideoStatsDisplay
              videoId={videoIdForStats}
              enableRealtime={true}
              showActions={true}
              autoIncrementViews={false}
              size="large"
              className="text-white"
            />
          </div>
          
          {/* 操作按钮区域 */}
          <div className="flex items-center gap-3 flex-shrink-0">
            {/* 评论按钮 */}
            <CommentsButton
              videoId={videoData.code}
              onClick={() => setIsCommentsOpen(true)}
            />
            
            {/* 磁力下载按钮 */}
            <MagneticDownloadButton
              movieCode={videoData.code}
              variant="secondary"
            />
          </div>
        </div>

      </div>
    );
  };

  // Netflix风格相关视频侧边栏
  const renderNetflixSidebar = () => {
    if (!videoData?.similarMovies || videoData.similarMovies.length === 0) {
      return (
        <div className="w-80 pl-8">
          <h3 className="text-white text-xl font-bold mb-4">相关视频</h3>
          <div className="text-center py-8">
            <FiVideo className="mx-auto text-gray-500 mb-4" size={48} />
            <p className="text-gray-400">暂无相关内容</p>
          </div>
        </div>
      );
    }

    return (
      <div className="w-80 pl-8">
        <h3 className="text-white text-xl font-bold mb-4">相关视频</h3>
        <div className="space-y-3 max-h-[600px] overflow-y-auto">
          {videoData.similarMovies.slice(0, 8).map((movie, index) => (
            <Link
              key={movie.id}
              href={movie.href}
              className="group flex gap-3 p-3 rounded-lg hover:bg-gray-800/50 transition-all duration-300"
            >
              <div className="relative flex-shrink-0 w-28 h-16 bg-gray-800 rounded overflow-hidden">
                <Image
                  src={movie.img}
                  alt={movie.title}
                  fill
                  className="object-cover group-hover:scale-105 transition-transform duration-300"
                  sizes="112px"
                  loading="lazy"
                />
                <div className="absolute inset-0 bg-black/20 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
                  <FiPlay className="text-white text-lg" />
                </div>
              </div>
              
              <div className="flex-1 min-w-0">
                <h4 className="text-white text-sm font-medium group-hover:text-red-400 transition-colors line-clamp-2 mb-1">
                  {movie.title}
                </h4>
                <div className="text-xs text-gray-400 font-mono">
                  {movie.id}
                </div>
              </div>
            </Link>
          ))}
        </div>
      </div>
    );
  };

  // Netflix风格底部信息区域
  const renderNetflixDetails = () => {
    if (!videoData) return null;

    return (
      <div className="mt-12 space-y-8">
        {/* 标签区域 - 移动到演员阵容上面 */}
        {videoData.tags && videoData.tags.length > 0 && (
          <div>
            <h3 className="text-white text-2xl font-bold mb-6">标签</h3>
            <div className="flex flex-wrap gap-2">
              {videoData.tags.map((tag, index) => (
                <Link
                  key={index}
                  href={`/category/${tag.toLowerCase()}`}
                  className="bg-gray-700/50 hover:bg-gray-600 text-gray-300 hover:text-white px-3 py-1 rounded-full text-sm transition-all duration-300"
                >
                  #{tag}
                </Link>
              ))}
            </div>
          </div>
        )}

        {/* 演员阵容 - 限制显示数量防止溢出 */}
        {videoData.actors && videoData.actors.length > 0 && (
          <div>
            <h3 className="text-white text-2xl font-bold mb-6">演员阵容</h3>
            <div className="flex flex-wrap gap-4">
              {videoData.actors.slice(0, 12).map((actor, index) => {
                const actorIdentifier = actor.id || encodeURIComponent(actor.name);
                return (
                  <Link
                    key={index}
                    href={`/actress/${actorIdentifier}`}
                    className="group flex items-center gap-3 bg-gray-800/30 hover:bg-gray-700/50 rounded-full px-4 py-2 transition-all duration-300"
                  >
                    <div className="relative w-8 h-8 bg-gray-800 rounded-full overflow-hidden flex-shrink-0">
                      {actor.avatar ? (
                        <Image
                          src={actor.avatar}
                          alt={actor.name}
                          fill
                          className="object-cover group-hover:scale-110 transition-transform duration-300"
                          sizes="32px"
                        />
                      ) : (
                        <div className="flex items-center justify-center h-full">
                          <FiUser className="text-gray-500 text-sm" />
                        </div>
                      )}
                    </div>
                    <span className="text-white text-sm group-hover:text-red-400 transition-colors whitespace-nowrap">
                      {actor.name}
                    </span>
                  </Link>
                );
              })}
              {videoData.actors.length > 12 && (
                <div className="flex items-center gap-3 bg-gray-800/20 rounded-full px-4 py-2">
                  <span className="text-gray-400 text-sm">
                    +{videoData.actors.length - 12} 更多演员
                  </span>
                </div>
              )}
            </div>
          </div>
        )}

        {/* 制作信息 */}
        <div>
          <h3 className="text-white text-2xl font-bold mb-6">详细信息</h3>
          <div className="flex flex-wrap gap-3">
            {videoData.director && (
              <div className="bg-gray-800/30 rounded px-3 py-2 inline-block">
                <div className="text-gray-400 text-xs mb-1">导演</div>
                <Link
                  href={`/director/${encodeURIComponent(videoData.director)}`}
                  className="text-white text-sm font-medium hover:text-red-400 transition-colors whitespace-nowrap"
                >
                  {videoData.director}
                </Link>
              </div>
            )}
            
            {videoData.studio && (
              <div className="bg-gray-800/30 rounded px-3 py-2 inline-block">
                <div className="text-gray-400 text-xs mb-1">制作商</div>
                <Link
                  href={`/studio/${encodeURIComponent(videoData.studio)}`}
                  className="text-white text-sm font-medium hover:text-red-400 transition-colors whitespace-nowrap"
                >
                  {videoData.studio}
                </Link>
              </div>
            )}
            
            {videoData.publisher && (
              <div className="bg-gray-800/30 rounded px-3 py-2 inline-block">
                <div className="text-gray-400 text-xs mb-1">发行商</div>
                <Link
                  href={`/publisher/${encodeURIComponent(videoData.publisher)}`}
                  className="text-white text-sm font-medium hover:text-red-400 transition-colors whitespace-nowrap"
                >
                  {videoData.publisher}
                </Link>
              </div>
            )}
            
            {videoData.series && (
              <div className="bg-gray-800/30 rounded px-3 py-2 inline-block">
                <div className="text-gray-400 text-xs mb-1">系列</div>
                <Link
                  href={getLocalizedUrl(`/series/${encodeURIComponent(videoData.series)}`)}
                  className="text-white text-sm font-medium hover:text-red-400 transition-colors whitespace-nowrap"
                >
                  {videoData.series}
                </Link>
              </div>
            )}
            
            {videoData.releaseDate && (
              <div className="bg-gray-800/30 rounded px-3 py-2 inline-block">
                <div className="text-gray-400 text-xs mb-1">发行时间</div>
                <span className="text-white text-sm font-medium whitespace-nowrap">
                  {(() => {
                    const releaseDate = new Date(videoData.releaseDate);
                    const now = new Date();
                    // 验证发行时间不能是未来的日期
                    if (releaseDate > now) {
                      return '未知';
                    }
                    return releaseDate.toLocaleDateString('zh-CN', {
                      year: 'numeric',
                      month: '2-digit',
                      day: '2-digit'
                    });
                  })()}
                </span>
              </div>
            )}
          </div>
        </div>



        {/* 预览截图 */}
        {videoData.sampleImages && videoData.sampleImages.length > 0 && (
          <div>
            <h3 className="text-white text-2xl font-bold mb-6">预览截图</h3>
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
              {videoData.sampleImages.map((sample, index) => (
                <div
                  key={sample.id || index}
                  className="relative aspect-video bg-gray-800 rounded-lg overflow-hidden group cursor-pointer"
                  onClick={() => setSelectedImageIndex(index)}
                >
                  <Image
                    src={sample.thumbnail || sample.src}
                    alt={sample.alt || `预览图 ${index + 1}`}
                    fill
                    className="object-cover group-hover:scale-105 transition-transform duration-300"
                    sizes="(max-width: 768px) 50vw, (max-width: 1200px) 33vw, 25vw"
                    loading="lazy"
                  />
                  <div className="absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
                    <FiEye className="text-white text-xl" />
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    );
  };

  // ==================== 🎯 主渲染 ====================

  // 加载中状态
  if (loading) {
    return (
      <RealtimeStatsProvider>
        <div className="min-h-screen bg-black text-white">
          <div className="container mx-auto px-4 py-8">
            <div className="flex items-center justify-center min-h-[70vh]">
              <div className="text-center">
                <div className="w-20 h-20 mx-auto bg-red-600/20 rounded-full flex items-center justify-center mb-8">
                  <FiLoader className="text-red-500 text-3xl animate-spin" />
                </div>
                <h2 className="text-2xl font-bold mb-3">正在加载视频</h2>
                <p className="text-gray-400">正在获取视频详情...</p>
              </div>
            </div>
          </div>
        </div>
      </RealtimeStatsProvider>
    );
  }

  // 错误状态
  if (error || !videoData) {
    return (
      <RealtimeStatsProvider>
        <div className="min-h-screen bg-black text-white">
          <div className="container mx-auto px-4 py-8">
            <div className="flex items-center justify-center min-h-[70vh]">
              <div className="text-center">
                <div className="w-20 h-20 mx-auto bg-red-600/20 rounded-full flex items-center justify-center mb-8">
                  <FiAlertCircle className="text-red-500 text-3xl" />
                </div>
                <h2 className="text-3xl font-bold mb-4">无法加载视频</h2>
                <p className="text-gray-400 mb-8 max-w-md mx-auto text-lg">
                  {error || '视频不存在或已被删除'}
                </p>
                <div className="flex gap-4 justify-center">
                  <button
                    onClick={() => window.location.reload()}
                    className="bg-red-600 hover:bg-red-700 text-white px-8 py-4 rounded transition-all duration-300 flex items-center gap-3"
                  >
                    <FiRefreshCw />
                    重试
                  </button>
                  <Link 
                    href={getLocalizedUrl('/')} 
                    className="bg-gray-700 hover:bg-gray-600 text-white px-8 py-4 rounded transition-all duration-300"
                  >
                    返回首页
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </RealtimeStatsProvider>
    );
  }

  // Netflix风格主页面内容
  return (
    <RealtimeStatsProvider>
      <div className="min-h-screen bg-black text-white">


        {/* 错误提示 */}
        {statsError && (
          <div className="container mx-auto px-4 mb-4">
            <div className="bg-orange-900/30 border border-orange-600/50 rounded p-4">
              <div className="flex items-center gap-3 text-orange-200">
                <FiAlertCircle />
                <span>数据加载失败: {statsError}</span>
              </div>
            </div>
          </div>
        )}

        <div className="container mx-auto px-4 pb-16">
          {/* Netflix风格主要布局 */}
          <div className="flex gap-8">
            {/* 左侧主要内容区域 */}
            <div className="flex-1">
              {/* 播放器 */}
              {renderResponsivePlayer()}
              
              {/* 标题和操作区域 */}
              {renderNetflixTitle()}
            </div>

            {/* 右侧相关视频 */}
            {renderNetflixSidebar()}
          </div>

          {/* 底部详细信息 */}
          {renderNetflixDetails()}
        </div>

        {/* 评论抽屉 */}
        <CommentsDrawer
          isOpen={isCommentsOpen}
          onClose={() => setIsCommentsOpen(false)}
          videoId={videoData.code}
          currentUserId={currentUserId || undefined}
        />
      </div>
    </RealtimeStatsProvider>
  );
} 