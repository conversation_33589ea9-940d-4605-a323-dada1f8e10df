'use client';

import React, { useState } from 'react';
import LoginPromptModal from '@/components/LoginPromptModal';

export default function TestLoginPromptPage() {
  const [showModal, setShowModal] = useState(false);
  const [actionType, setActionType] = useState<'like' | 'favorite' | 'comment' | 'general'>('general');

  const handleShowModal = (type: 'like' | 'favorite' | 'comment' | 'general') => {
    setActionType(type);
    setShowModal(true);
  };

  return (
    <div className="min-h-screen bg-gray-900 text-white p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-8">登录提示模态框测试</h1>
        
        <div className="grid grid-cols-2 gap-4 mb-8">
          <button
            onClick={() => handleShowModal('like')}
            className="p-4 bg-red-600 hover:bg-red-700 rounded-lg transition-colors"
          >
            测试点赞提示
          </button>
          
          <button
            onClick={() => handleShowModal('favorite')}
            className="p-4 bg-yellow-600 hover:bg-yellow-700 rounded-lg transition-colors"
          >
            测试收藏提示
          </button>
          
          <button
            onClick={() => handleShowModal('comment')}
            className="p-4 bg-blue-600 hover:bg-blue-700 rounded-lg transition-colors"
          >
            测试评论提示
          </button>
          
          <button
            onClick={() => handleShowModal('general')}
            className="p-4 bg-green-600 hover:bg-green-700 rounded-lg transition-colors"
          >
            测试通用提示
          </button>
        </div>

        <div className="bg-gray-800 p-6 rounded-lg">
          <h2 className="text-xl font-semibold mb-4">功能说明</h2>
          <ul className="space-y-2 text-gray-300">
            <li>• 点击上方按钮可以测试不同类型的登录提示</li>
            <li>• 每种类型都有对应的图标、颜色和消息</li>
            <li>• 模态框包含温柔的提示文本和快速登录入口</li>
            <li>• 支持键盘 ESC 键关闭</li>
            <li>• 包含登录后可享受功能的列表</li>
          </ul>
        </div>

        <LoginPromptModal
          isOpen={showModal}
          onClose={() => setShowModal(false)}
          actionType={actionType}
        />
      </div>
    </div>
  );
}
