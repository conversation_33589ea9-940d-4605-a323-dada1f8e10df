'use client';

import { useEffect, useState, useCallback, useRef } from 'react';
import { usePathname, useParams, useSearchParams } from 'next/navigation';
import Link from 'next/link';
import { FiVideo, FiSearch, FiFilter, FiGrid, FiList, FiChevronLeft, FiChevronRight, FiStar, FiHeart, FiLoader } from 'react-icons/fi';
import Image from 'next/image';
import { useClientTranslations } from '@/components/TranslationsProvider';
import gsap from 'gsap';

// 女优数据类型
interface Actress {
  id: number;
  name: string;
  star_id?: string;
  image_url?: string;
  cached_image_url?: string;
  movie_count?: number;
  birth_date?: string;
  height?: number;
  cup_size?: string;
  created_at?: string;
  updated_at?: string;
}

// API响应类型
interface ActressAPIResponse {
  success: boolean;
  data: {
    items: Actress[];
    total: number;
    page: number;
    limit: number;
    total_pages: number;
  };
  message?: string;
}

// 统计API响应类型
interface StatsAPIResponse {
  success: boolean;
  data: {
    totalStars: number;
    topStars?: Actress[];
  };
  message?: string;
}

// 排序选项 - 将在组件内部动态生成以支持国际化

// 视图模式
type ViewMode = 'grid' | 'list';

// 备用数据（API失败时使用）
const FALLBACK_ACTRESSES: Actress[] = [
  { id: 1, name: '三上悠亚', movie_count: 86, image_url: '/images/1Ayvrd.jpg' },
  { id: 2, name: '深田咏美', movie_count: 94, image_url: '/images/2mKN5y.jpg' },
  { id: 3, name: '桥本有菜', movie_count: 78, image_url: '/images/3nydbz.jpg' },
  { id: 4, name: '水卜樱', movie_count: 65, image_url: '/images/4DKBXJ.jpg' },
  { id: 5, name: '明日花绮罗', movie_count: 120, image_url: '/images/5nK11a.jpg' },
  { id: 6, name: '葵司', movie_count: 83, image_url: '/images/6deyV7.jpg' },
  { id: 7, name: '天使萌', movie_count: 92, image_url: '/images/7yKgJ1.jpg' },
  { id: 8, name: '松下纱荣子', movie_count: 76, image_url: '/images/82Ez9x.jpg' },
  { id: 9, name: '河北彩花', movie_count: 68, image_url: '/images/96e0Bg.jpg' },
  { id: 10, name: '凉森玲梦', movie_count: 71, image_url: '/images/0ey9pq.jpg' },
  { id: 11, name: '桃乃木香奈', movie_count: 88, image_url: '/images/AqEZQm.jpg' },
  { id: 12, name: '上原亚衣', movie_count: 131, image_url: '/images/e840eM.jpg' }
];

const ActressClient = () => {
  const { t } = useClientTranslations();
  const params = useParams();
  const searchParams = useSearchParams();
  const locale = (params?.locale as string) || 'zh-CN';
  
  // 状态管理
  const [actresses, setActresses] = useState<Actress[]>([]);
  const [loading, setLoading] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalCount, setTotalCount] = useState(0);
  const [sortBy, setSortBy] = useState('movie_count');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [viewMode, setViewMode] = useState<ViewMode>('grid');
  const [isUsingFallback, setIsUsingFallback] = useState(false);
  const [hasMoreData, setHasMoreData] = useState(true);
  
  // 动画refs
  const itemRefs = useRef<Array<HTMLDivElement | null>>([]);
  const containerRef = useRef<HTMLDivElement>(null);
  const loadMoreRef = useRef<HTMLDivElement>(null);
  
  const itemsPerPage = 48; // 增加每页数量以提高加载效率

  // 获取女优统计数据
  const fetchActressStats = useCallback(async () => {
    try {
      const response = await fetch('/api/db/stats/stars');
      
      if (response.ok) {
        const result: StatsAPIResponse = await response.json();
        
        if (result.success && result.data && typeof result.data.totalStars === 'number') {
          setTotalCount(result.data.totalStars);
          return result.data.totalStars;
        }
      }
      
      // 如果统计API失败，尝试通过stars API获取总数
      const starsResponse = await fetch('/api/db/stars?page=1&limit=1');
      if (starsResponse.ok) {
        const starsResult: ActressAPIResponse = await starsResponse.json();
        if (starsResult.success && starsResult.data) {
          const totalItems = starsResult.data.pagination?.totalItems || starsResult.data.total || 0;
          setTotalCount(totalItems);
          return totalItems;
        }
      }
      
      throw new Error('无法获取女优统计数据');
    } catch (error) {
      // 设置为备用数据长度，避免显示0
      setTotalCount(FALLBACK_ACTRESSES.length);
      return FALLBACK_ACTRESSES.length;
    }
  }, []);

  // 获取女优图片URL
  const getActressImageUrl = (actress: Actress): string => {
    if (actress.cached_image_url) {
      return actress.cached_image_url;
    }
    if (actress.image_url) {
      return actress.image_url;
    }
    return '/images/defaults/actress_default.jpg';
  };

  // 获取女优详情页链接
  const getActressLink = (actress: Actress): string => {
    // 优先使用star_id，如果没有则使用演员名称
    const identifier = actress.star_id || encodeURIComponent(actress.name);
    return `/${locale}/actress/${identifier}`;
  };

  // 格式化数量显示
  const formatCount = (count: number): string => {
    if (count >= 1000) {
      return `${(count / 1000).toFixed(1)}k`;
    }
    return count.toString();
  };

  // 点击动画效果
  const handleActressClick = (e: React.MouseEvent, index: number) => {
    const element = itemRefs.current[index];
    if (element) {
      // 创建点击波纹效果
      gsap.to(element, {
        scale: 0.95,
        duration: 0.1,
        ease: "power2.out",
        onComplete: () => {
          gsap.to(element, {
            scale: 1,
            duration: 0.2,
            ease: "elastic.out(1, 0.3)"
          });
        }
      });
      
      // 创建头像特殊动画
      const avatar = element.querySelector('.actress-avatar');
      if (avatar) {
        gsap.to(avatar, {
          rotation: 360,
          scale: 1.1,
          duration: 0.5,
          ease: "back.out(1.7)"
        });
      }
    }
  };

  // 获取女优数据 - 支持累积加载
  const fetchActresses = useCallback(async (loadMore = false) => {
    try {
      if (loadMore) {
        setLoadingMore(true);
      } else {
        setLoading(true);
        setError(null);
      }

      const targetPage = loadMore ? currentPage : 1;

      // 使用数据库API代理，参照首页的成功做法
      let response;
      
      if (searchTerm.trim()) {
        // 如果有搜索词，使用普通stars端点进行搜索
        const params = new URLSearchParams({
          page: targetPage.toString(),
          limit: itemsPerPage.toString(),
          sort: sortBy,
          order: sortOrder,
          search: searchTerm.trim()
        });
        response = await fetch(`/api/db/stars?${params.toString()}`);
      } else {
        // 无搜索词时，优先尝试popular端点
        if (targetPage === 1 && sortBy === 'movie_count' && sortOrder === 'desc') {
          // 首页默认排序，使用popular端点
          response = await fetch(`/api/db/stars/popular?limit=${itemsPerPage}`);
        } else {
          // 其他情况使用普通端点
          const params = new URLSearchParams({
            page: targetPage.toString(),
            limit: itemsPerPage.toString(),
            sort: sortBy,
            order: sortOrder
          });
          response = await fetch(`/api/db/stars?${params.toString()}`);
        }
      }

      if (response.ok) {
        const result: ActressAPIResponse = await response.json();
        
        if (result.success && result.data && Array.isArray(result.data.items)) {
          if (loadMore) {
            // 加载更多：追加到现有数据
            setActresses(prev => [...prev, ...result.data.items]);
          } else {
            // 初始加载或重新搜索：替换数据
            setActresses(result.data.items);
          }
          
          // 处理分页信息，支持多种API格式
          const pagination = result.data.pagination;
          let totalPages, totalItems;
          
          if (pagination) {
            // 普通的stars端点有完整分页信息
            totalPages = pagination.totalPages;
            totalItems = pagination.totalItems;
          } else {
            // popular端点或旧格式，从统计数据或返回的items推算
            if (totalCount > 0) {
              totalItems = totalCount;
              totalPages = Math.ceil(totalItems / itemsPerPage);
            } else {
              // 如果没有统计数据，假设还有更多数据（除非返回的项目少于请求的数量）
              const returnedCount = result.data.items?.length || 0;
              totalItems = returnedCount;
              // 如果返回的数量等于请求数量，可能还有更多数据
              totalPages = returnedCount === itemsPerPage ? targetPage + 1 : targetPage;
            }
          }
          
          setTotalPages(totalPages);
          setHasMoreData(targetPage < totalPages);
          if (totalItems > 0) {
            setTotalCount(totalItems);
          }
          
          setIsUsingFallback(false);
        } else {
          throw new Error('API响应格式错误');
        }
      } else {
        throw new Error(`API请求失败: ${response.status}`);
      }
    } catch (error) {
      setError('获取女优数据失败，正在使用备用数据');
      
      if (!loadMore) {
        // 只在初始加载失败时使用备用数据
        const start = (currentPage - 1) * itemsPerPage;
        const end = start + itemsPerPage;
        let filteredData = FALLBACK_ACTRESSES;
        
        if (searchTerm.trim()) {
          filteredData = FALLBACK_ACTRESSES.filter(actress => 
            actress.name.toLowerCase().includes(searchTerm.toLowerCase())
          );
        }
        
        setActresses(filteredData.slice(start, end));
        setTotalPages(Math.ceil(filteredData.length / itemsPerPage));
        setTotalCount(filteredData.length);
        setIsUsingFallback(true);
        setHasMoreData(false);
      }
    } finally {
      if (loadMore) {
        setLoadingMore(false);
      } else {
        setLoading(false);
      }
    }
  }, [currentPage, itemsPerPage, sortBy, sortOrder, searchTerm]);

  // 加载更多数据
  const loadMoreData = useCallback(() => {
    if (!loadingMore && hasMoreData && currentPage < totalPages) {
      setCurrentPage(prev => prev + 1);
    }
  }, [loadingMore, hasMoreData, currentPage, totalPages]);

  // 初始化数据获取 - 先获取统计数据，再获取女优数据
  useEffect(() => {
    const initializeData = async () => {
      try {
        await fetchActressStats();
        fetchActresses();
      } catch (error) {
        console.error('初始化数据失败:', error);
        fetchActresses(); // 即使统计失败也要尝试获取女优数据
      }
    };
    
    initializeData();
  }, []); // 仅在组件挂载时执行

  // 当currentPage变化时，加载更多数据
  useEffect(() => {
    if (currentPage > 1) {
      fetchActresses(true);
    }
  }, [currentPage]);

  // 无限滚动监听器
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting && hasMoreData && !loadingMore && !loading) {
          loadMoreData();
        }
      },
      {
        threshold: 0.1,
        rootMargin: '100px'
      }
    );

    if (loadMoreRef.current) {
      observer.observe(loadMoreRef.current);
    }

    return () => {
      if (loadMoreRef.current) {
        observer.unobserve(loadMoreRef.current);
      }
    };
  }, [hasMoreData, loadingMore, loading, loadMoreData]);

  // 入场动画
  useEffect(() => {
    if (!loading && actresses.length > 0) {
      // 重置refs数组
      itemRefs.current = itemRefs.current.slice(0, actresses.length);
      
      // 对所有女优卡片应用入场动画
      const validRefs = itemRefs.current.filter(Boolean);
      if (validRefs.length > 0) {
        gsap.fromTo(
          validRefs,
          {
            opacity: 0,
            y: 30,
            scale: 0.9
          },
          {
            opacity: 1,
            y: 0,
            scale: 1,
            duration: 0.5,
            stagger: 0.05,
            ease: "power2.out"
          }
        );
      }
    }
  }, [loading, actresses]);

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setCurrentPage(1);
    setActresses([]); // 清空现有数据
    setHasMoreData(true);
    fetchActresses();
  };

  const handleSortChange = (newSortBy: string) => {
    setSortBy(newSortBy);
    setSortOrder(newSortBy === sortBy && sortOrder === 'desc' ? 'asc' : 'desc');
    setCurrentPage(1);
    setActresses([]); // 清空现有数据
    setHasMoreData(true);
  };

  // 重新获取数据当排序改变时
  useEffect(() => {
    if (actresses.length === 0) return; // 避免初始化时重复调用
    setCurrentPage(1);
    setActresses([]);
    setHasMoreData(true);
    fetchActresses();
  }, [sortBy, sortOrder]);



  return (
    <div className="min-h-screen relative overflow-hidden bg-gradient-to-br from-gray-900 via-black to-gray-800 text-white pt-20 pb-12">
      {/* 现代化背景装饰 - 参考2025年设计趋势 */}
      <div className="absolute inset-0 pointer-events-none">
        <div className="absolute top-1/4 left-1/4 w-80 h-80 bg-gradient-to-br from-pink-500/12 to-purple-500/8 rounded-full filter blur-3xl"></div>
        <div className="absolute bottom-1/3 right-1/3 w-96 h-96 bg-gradient-to-tr from-red-500/10 to-orange-500/8 rounded-full filter blur-3xl"></div>
        <div className="absolute top-1/2 left-1/2 w-64 h-64 bg-gradient-to-bl from-blue-500/8 to-cyan-500/6 rounded-full filter blur-2xl"></div>
      </div>

      <div className="relative container mx-auto px-4">
        {/* 简洁页面标题 */}
        <div className="text-center mb-16">
          <h1 className="text-5xl md:text-7xl font-bold mb-6 text-white leading-tight">
            {t('actress.popular') || '女优名鉴'}
          </h1>
          
          <p className="text-xl text-gray-300 font-light">
            {totalCount > 0 && (
              <>
                发现 <span className="text-pink-400 font-semibold">{formatCount(totalCount)}</span> 位精彩女优
              </>
            )}
            {searchTerm && (
              <>
                <br />
                <span className="text-gray-400">搜索结果: </span>
                <span className="text-pink-400">&quot;{searchTerm}&quot;</span>
              </>
            )}
          </p>
        </div>

        {/* 简洁搜索和控制栏 */}
        <div className="max-w-2xl mx-auto mb-16">
          <form onSubmit={handleSearch} className="relative mb-8">
            <div className="relative">
              <FiSearch className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
              <input
                type="text"
                placeholder={t('actress.searchPlaceholder') || '搜索女优姓名...'}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-12 pr-4 py-4 bg-gray-800/40 backdrop-blur-sm border border-gray-700/30 rounded-2xl text-white placeholder-gray-400 focus:outline-none focus:border-gray-600/50"
              />
            </div>
          </form>
          
          {/* 简洁排序选择 */}
          <div className="flex justify-center">
            <div className="relative">
              <select
                value={`${sortBy}-${sortOrder}`}
                onChange={(e) => {
                  const [field, order] = e.target.value.split('-');
                  handleSortChange(field);
                  setSortOrder(order as 'asc' | 'desc');
                }}
                className="bg-gray-800/60 backdrop-blur-sm border border-gray-700/30 rounded-2xl px-8 py-3 text-sm text-gray-300 focus:outline-none focus:border-gray-600/50 appearance-none cursor-pointer"
              >
                <option value="movie_count-desc">{t('actress.sortByVideosDesc') || '作品数量 ↓'}</option>
                <option value="popularity-desc">作品人气 ↓</option>
                <option value="name-asc">{t('actress.sortByNameAsc') || '姓名 A-Z'}</option>
                <option value="name-desc">{t('actress.sortByNameDesc') || '姓名 Z-A'}</option>
                <option value="created_at-desc">{t('actress.sortByNewest') || '最新添加'}</option>
                <option value="created_at-asc">{t('actress.sortByOldest') || '最早添加'}</option>
              </select>
              <FiFilter className="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400 pointer-events-none" />
            </div>
          </div>
        </div>

        {/* 女优网格 - 现代化设计 */}
        {loading ? (
          <div className="flex justify-center items-center min-h-[500px]">
            <div className="text-center">
              <div className="w-16 h-16 border-4 border-gray-700 border-t-white rounded-full animate-spin mx-auto mb-6"></div>
              <p className="text-gray-300 text-lg font-light">{t('common.loading') || '正在加载...'}</p>
            </div>
          </div>
        ) : actresses.length > 0 ? (
          <div ref={containerRef} className="grid grid-cols-3 sm:grid-cols-4 md:grid-cols-6 lg:grid-cols-8 xl:grid-cols-10 2xl:grid-cols-12 gap-6">
            {actresses.map((actress, index) => (
              <Link
                key={actress.id}
                href={getActressLink(actress)}
                onClick={(e) => handleActressClick(e, index)}
                className="group block"
              >
                <div
                  ref={(el) => {
                    itemRefs.current[index] = el;
                  }}
                  className="text-center transition-all duration-300 hover:scale-105"
                >
                  {/* 圆形头像 - 无背景框 */}
                  <div className="relative mx-auto mb-3">
                    <div className="w-16 h-16 md:w-20 md:h-20 rounded-full overflow-hidden mx-auto actress-avatar transition-all duration-300 group-hover:shadow-lg group-hover:shadow-red-500/25">
                      <Image
                        src={getActressImageUrl(actress)}
                        alt={actress.name}
                        className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110"
                        width={80}
                        height={80}
                        onError={(e) => {
                          e.currentTarget.src = '/images/defaults/actress_default.jpg';
                        }}
                      />
                    </div>
                    
                    {/* 悬浮爱心图标 */}
                    <div className="absolute -top-1 -right-1 w-6 h-6 bg-red-500 rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-300 transform scale-0 group-hover:scale-100">
                      <FiHeart className="text-white" size={12} />
                    </div>
                  </div>
                  
                  {/* 姓名 - 简洁版 */}
                  <h3 className="text-xs md:text-sm font-medium text-gray-300 group-hover:text-white transition-colors duration-300 truncate leading-tight">
                    {actress.name}
                  </h3>
                  
                  {/* 作品数量 - 极简版 */}
                  <p className="text-xs text-gray-500 group-hover:text-gray-400 transition-colors duration-300 mt-1">
                    {formatCount(actress.movie_count || 0)}
                  </p>
                </div>
              </Link>
            ))}
          </div>
        ) : (
          <div className="text-center py-24">
            <div className="relative inline-block mb-8">
              <div className="text-8xl filter drop-shadow-lg">👸</div>
              <div className="absolute -bottom-2 left-1/2 transform -translate-x-1/2 w-12 h-1 bg-gradient-to-r from-transparent via-pink-500 to-transparent"></div>
            </div>
            <h3 className="text-2xl font-medium mb-3 text-gray-200">{t('actress.noActressesFound') || '没有找到女优'}</h3>
            <p className="text-gray-400 text-lg">{t('actress.tryAdjustSearch') || '尝试调整搜索条件或浏览所有女优'}</p>
          </div>
        )}

        {/* 无限滚动加载指示器 */}
        {actresses.length > 0 && (
          <div className="mt-16">
            {/* 加载更多指示器 */}
            {hasMoreData && (
              <div 
                ref={loadMoreRef} 
                className="flex justify-center items-center py-12"
              >
                {loadingMore ? (
                  <div className="text-center">
                    <div className="w-8 h-8 border-2 border-gray-600 border-t-pink-400 rounded-full animate-spin mx-auto mb-4"></div>
                    <p className="text-gray-400 text-sm">正在加载更多女优...</p>
                  </div>
                ) : (
                  <button
                    onClick={loadMoreData}
                    className="group bg-gradient-to-r from-gray-800/60 via-gray-700/40 to-gray-800/60 backdrop-blur-sm px-8 py-4 rounded-2xl border border-pink-500/20 hover:border-pink-500/40 transition-all duration-300 hover:shadow-lg hover:shadow-pink-500/10 transform hover:scale-105"
                  >
                    <div className="flex items-center gap-3">
                      <FiLoader className="text-pink-400 group-hover:animate-spin transition-transform duration-300" />
                      <span className="text-gray-300 font-medium">加载更多女优</span>
                      <span className="text-pink-400 text-sm">({actresses.length}/{totalCount})</span>
                    </div>
                  </button>
                )}
              </div>
            )}
            
            {/* 已加载完所有数据的提示 */}
            {!hasMoreData && !loading && actresses.length > 0 && (
              <div className="text-center py-12">
                <div className="inline-flex items-center gap-2 bg-gradient-to-r from-gray-800/40 via-gray-700/30 to-gray-800/40 backdrop-blur-sm px-6 py-3 rounded-2xl border border-gray-700/20">
                  <FiStar className="text-yellow-400" />
                  <span className="text-gray-300 text-sm">
                    已显示全部 {totalCount} 位女优
                  </span>
                  <FiStar className="text-yellow-400" />
                </div>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default ActressClient; 