import React from 'react';
import { Metadata } from 'next';
import { generateMetadata as generateSeoMetadata, generatePersonJsonLd } from '@/lib/seo';
import ActressDetailClient from './ActressDetailClient';

// 模拟女优详情数据获取函数
function getActressData(id: string) {
  const actresses = {
    '1': {
      id: 1,
      name: '三上悠亚',
      nameJp: '三上悠亜',
      birthdate: '1993-08-16',
      height: '159cm',
      bust: '90cm',
      waist: '54cm',
      hip: '86cm',
      blood: 'A型',
      hobby: '唱歌、瑜伽',
      debut: '2015-06-01',
      description: '三上悠亚，日本AV女优，原名鬼头桃菜，曾是日本女子偶像团体SKE48的成员，2015年转型成为AV女优，现为HONEY POPCORN的队长。',
      socialMedia: {
        instagram: 'https://instagram.com/yua_mikami',
        twitter: 'https://twitter.com/yua_mikami',
      },
      fans: '523K',
      videos: 86,
      imageUrl: '/images/1Ayvrd.jpg',
      coverImage: '/images/mOpM4Z.jpg'
    },
    '2': {
      id: 2,
      name: '深田咏美',
      nameJp: '深田えいみ',
      birthdate: '1998-03-18',
      height: '158cm',
      bust: '93cm',
      waist: '57cm',
      hip: '85cm',
      blood: 'O型',
      hobby: '游泳、烹饪',
      debut: '2017-10-01',
      description: '深田咏美，日本AV女优，2017年出道，因其甜美的长相和丰满的身材受到广大粉丝喜爱。',
      socialMedia: {
        instagram: 'https://instagram.com/eimy_fukada',
        twitter: 'https://twitter.com/eimy_fukada',
      },
      fans: '428K',
      videos: 94,
      imageUrl: '/images/2mKN5y.jpg',
      coverImage: '/images/7yKgJ1.jpg'
    },
  };
  
  return actresses[id as keyof typeof actresses] || {
    id: parseInt(id),
    name: '未知女优',
    nameJp: '不明',
    birthdate: '未知',
    height: '未知',
    bust: '未知',
    waist: '未知',
    hip: '未知',
    blood: '未知',
    hobby: '未知',
    debut: '未知',
    description: '没有找到该女优的详细信息。',
    socialMedia: {},
    fans: '0',
    videos: 0,
    imageUrl: '/images/placeholder.jpg',
    coverImage: '/images/placeholder-cover.jpg'
  };
}

// 模拟女优相关视频
function getActressVideos(id: string) {
  return [
    {
      id: 1,
      title: `${id}-女优视频1`,
      imageUrl: '/images/a8BGN3.jpg',
      duration: '2:25:10',
      views: '215K',
      date: '2023-05-15',
      slug: `actress-${id}-video-1`
    },
    {
      id: 2,
      title: `${id}-女优视频2`,
      imageUrl: '/images/Ww3pkp.jpg',
      duration: '1:58:30',
      views: '186K',
      date: '2023-04-20',
      slug: `actress-${id}-video-2`
    },
    {
      id: 3,
      title: `${id}-女优视频3`,
      imageUrl: '/images/V48ryA.jpg',
      duration: '2:15:45',
      views: '162K',
      date: '2023-03-18',
      slug: `actress-${id}-video-3`
    },
    {
      id: 4,
      title: `${id}-女优视频4`,
      imageUrl: '/images/XeqJre.jpg',
      duration: '1:45:20',
      views: '143K',
      date: '2023-02-25',
      slug: `actress-${id}-video-4`
    },
    {
      id: 5,
      title: `${id}-女优视频5`,
      imageUrl: '/images/QN0dG9.jpg',
      duration: '2:05:40',
      views: '138K',
      date: '2023-01-30',
      slug: `actress-${id}-video-5`
    },
    {
      id: 6,
      title: `${id}-女优视频6`,
      imageUrl: '/images/YwqAGK.jpg',
      duration: '1:52:15',
      views: '125K',
      date: '2022-12-28',
      slug: `actress-${id}-video-6`
    },
    {
      id: 7,
      title: `${id}-女优视频7`,
      imageUrl: '/images/p380b.jpg',
      duration: '2:10:05',
      views: '118K',
      date: '2022-11-15',
      slug: `actress-${id}-video-7`
    },
    {
      id: 8,
      title: `${id}-女优视频8`,
      imageUrl: '/images/nKMNpX.jpg',
      duration: '1:48:55',
      views: '105K',
      date: '2022-10-22',
      slug: `actress-${id}-video-8`
    }
  ];
}

// 生成女优详情页的元数据
export async function generateMetadata({ params }: { params: Promise<{ id: string }> }): Promise<Metadata> {
  const resolvedParams = await params;
  const actressData = getActressData(resolvedParams.id);
  
  return generateSeoMetadata({
    title: `${actressData.name} (${actressData.nameJp}) - 女优详情`,
    description: actressData.description.substring(0, 160),
    path: `/actress/${resolvedParams.id}`,
    imageUrl: `https://javflix.tv${actressData.imageUrl}`,
    type: 'profile',
    keywords: ['女优', 'AV女优', '日本女优', actressData.name],
  });
}

// 生成JSON-LD结构化数据
function generateActressJsonLd(actressData: any) {
  return generatePersonJsonLd({
    name: actressData.name,
    description: actressData.description,
    imageUrl: `https://javflix.tv${actressData.imageUrl}`,
    birthDate: actressData.birthdate,
    height: actressData.height,
    weight: '未知',
    measurements: `${actressData.bust}-${actressData.waist}-${actressData.hip}`,
  });
}

// 服务器端页面组件
export default async function ActressPage({ params }: { params: Promise<{ id: string }> }) {
  const resolvedParams = await params;
  const actressData = getActressData(resolvedParams.id);
  const actressVideos = getActressVideos(resolvedParams.id);
  const personJsonLd = generateActressJsonLd(actressData);
  
  return (
    <>
      {/* 注入结构化数据 */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(personJsonLd) }}
      />
      
      {/* 使用客户端组件渲染UI */}
      <ActressDetailClient 
        actressData={actressData} 
        actressVideos={actressVideos} 
      />
    </>
  );
} 