'use client';

import React, { useEffect, useRef, useState } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { FiHeart, FiVideo, FiCalendar, FiUser, FiInstagram, FiTwitter } from 'react-icons/fi';
import gsap from 'gsap';

// Helper function for time formatting (copied from homepage logic)
function formatRelativeTime(dateString?: string): string {
  if (!dateString) return '未知时间';
  
  const date = new Date(dateString);
  const now = new Date();
  const seconds = Math.floor((now.getTime() - date.getTime()) / 1000);
  
  let interval = Math.floor(seconds / 31536000);
  if (interval >= 1) { // Use >= 1 for years
    return `${interval}年前`;
  }
  
  interval = Math.floor(seconds / 2592000);
  if (interval >= 1) { // Use >= 1 for months
    return `${interval}个月前`;
  }
  
  interval = Math.floor(seconds / 86400);
  if (interval >= 1) { // Use >= 1 for days
    return `${interval}天前`;
  }
  
  interval = Math.floor(seconds / 3600);
  if (interval >= 1) { // Use >= 1 for hours
    return `${interval}小时前`;
  }
  
  interval = Math.floor(seconds / 60);
  if (interval > 1) { // Keep > 1 for minutes as per original
    return `${interval}分钟前`;
  }
  
  return '刚刚';
}

// 接口定义
interface ActressData {
  id: number;
  name: string;
  nameJp: string;
  birthdate: string;
  height: string;
  bust: string;
  waist: string;
  hip: string;
  blood: string;
  hobby: string;
  debut: string;
  description: string;
  socialMedia: {
    instagram?: string;
    twitter?: string;
  };
  fans: string;
  videos: number;
  imageUrl: string;
  coverImage: string;
}

interface ActressVideo {
  id: number;
  title: string;
  imageUrl: string;
  duration: string;
  views: string;
  date: string;
  slug: string;
}

interface VideoStats {
  views: number;
  // Potentially add likes, favorites if needed later
}

interface ActressDetailClientProps {
  actressData: ActressData;
  actressVideos: ActressVideo[];
}

export default function ActressDetailClient({ actressData, actressVideos }: ActressDetailClientProps) {
  const [isFollowing, setIsFollowing] = useState(false);
  const [visibleVideos, setVisibleVideos] = useState(6);
  const [videoStats, setVideoStats] = useState<Record<string, VideoStats>>({});
  
  const headerRef = useRef<HTMLDivElement>(null);
  const infoRef = useRef<HTMLDivElement>(null);
  const videosRef = useRef<HTMLDivElement>(null);
  const videoRefs = useRef<Array<HTMLDivElement | null>>([]);
  
  useEffect(() => {
    videoRefs.current = videoRefs.current.slice(0, actressVideos.length);
    
    const fetchVideoStats = async (videoId: string) => {
      try {
        // TODO: Get API_BASE_URL from a shared config or env variable
        const response = await fetch(`/api/video-stats/fused/${videoId}`);
        if (response.ok) {
          const result = await response.json();
          if (result.success && result.data) {
            setVideoStats(prevStats => ({
              ...prevStats,
              [videoId]: { views: result.data.views || 0 },
            }));
          }
        }
      } catch (error) {
        console.error(`Failed to fetch stats for video ${videoId}:`, error);
        // Optionally set a default/error state for this video's stats
        setVideoStats(prevStats => ({
          ...prevStats,
          [videoId]: { views: 0 }, // Fallback to 0 or keep previous if preferred
        }));
      }
    };

    actressVideos.slice(0, visibleVideos).forEach(video => {
      if (video.slug && !videoStats[video.slug]) { // Fetch only if slug exists and not already fetched
        fetchVideoStats(video.slug);
      }
    });
    
    // 页面元素动画
    const tl = gsap.timeline({ defaults: { ease: 'power3.out' } });
    
    tl.fromTo(headerRef.current, 
      { opacity: 0, y: -20 }, 
      { opacity: 1, y: 0, duration: 0.8 }
    );
    
    tl.fromTo(infoRef.current,
      { opacity: 0, x: -20 },
      { opacity: 1, x: 0, duration: 0.6 },
      "-=0.4"
    );
    
    tl.fromTo(videosRef.current,
      { opacity: 0, y: 30 },
      { opacity: 1, y: 0, duration: 0.6 },
      "-=0.3"
    );
    
    // 视频卡片动画
    videoRefs.current.slice(0, visibleVideos).forEach((ref, index) => {
      if (ref) {
        gsap.fromTo(
          ref,
          { opacity: 0, y: 20 },
          { 
            opacity: 1, 
            y: 0, 
            duration: 0.5, 
            delay: 0.8 + index * 0.08 
          }
        );
      }
    });
  }, [actressVideos, visibleVideos, videoStats]);
  
  const loadMoreVideos = () => {
    setVisibleVideos(prev => Math.min(prev + 6, actressVideos.length));
  };
  
  const setVideoRef = (el: HTMLDivElement | null, index: number) => {
    videoRefs.current[index] = el;
  };
  
  return (
    <div className="min-h-screen relative overflow-hidden bg-gradient-to-br from-gray-900 via-black to-gray-800 text-white pt-20 pb-16">
      {/* 现代化背景装饰 - 参考2025年设计趋势 */}
      <div className="absolute inset-0 pointer-events-none">
        <div className="absolute top-1/4 left-1/4 w-80 h-80 bg-gradient-to-br from-pink-500/12 to-purple-500/8 rounded-full filter blur-3xl"></div>
        <div className="absolute bottom-1/3 right-1/3 w-96 h-96 bg-gradient-to-tr from-red-500/10 to-orange-500/8 rounded-full filter blur-3xl"></div>
        <div className="absolute top-1/2 left-1/2 w-64 h-64 bg-gradient-to-bl from-blue-500/8 to-cyan-500/6 rounded-full filter blur-2xl"></div>
      </div>

      <div className="relative container mx-auto px-4">
        {/* 现代化女优头部英雄区域 - 匹配最新视频和热门视频风格 */}
        <div 
          ref={headerRef}
          className="relative bg-gradient-to-r from-gray-900/80 via-black/60 to-gray-900/80 backdrop-blur-xl rounded-3xl overflow-hidden mb-16 border border-gray-700/30 shadow-2xl shadow-red-500/5"
        >
          {/* 增强背景效果 */}
          <div className="absolute inset-0 opacity-15">
            <Image
              src={actressData.coverImage}
              alt={actressData.name}
              fill
              className="object-cover"
              priority
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black via-black/80 to-black/40"></div>
          </div>
          
          {/* 添加装饰性渐变覆盖 */}
          <div className="absolute inset-0 bg-gradient-to-br from-red-500/5 via-transparent to-purple-500/5"></div>
          
          <div className="relative z-10 flex flex-col md:flex-row items-center p-8 md:p-12">
            {/* 超现代化头像设计 - 匹配热门视频页面风格 */}
            <div className="relative group">
              {/* 外层光环效果 */}
              <div className="absolute -inset-6 rounded-full bg-gradient-to-r from-red-500/20 via-pink-500/10 to-purple-500/20 blur-lg opacity-75 group-hover:opacity-100 transition-opacity duration-500"></div>
              
              <div className="relative w-36 h-36 md:w-52 md:h-52 rounded-full overflow-hidden border-4 border-gradient-to-r from-red-400 via-pink-500 to-red-600 shadow-2xl">
                <Image
                  src={actressData.imageUrl}
                  alt={actressData.name}
                  width={208}
                  height={208}
                  className="object-cover transition-all duration-700 group-hover:scale-110 group-hover:brightness-110"
                  priority
                />
                {/* 增强光晕效果 */}
                <div className="absolute inset-0 bg-gradient-to-t from-red-500/30 via-transparent to-pink-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
              </div>
              
              {/* 多层装饰圆圈 */}
              <div className="absolute -inset-3 rounded-full border border-red-500/40 animate-pulse"></div>
              <div className="absolute -inset-4 rounded-full border border-pink-500/20 animate-pulse" style={{animationDelay: '0.5s'}}></div>
            </div>
            
            <div className="md:ml-12 mt-8 md:mt-0 text-center md:text-left">
              {/* 超大号现代化标题设计 - 匹配热门和最新视频页面风格 */}
              <div className="relative">
                <h1 className="text-4xl md:text-7xl lg:text-8xl font-bold bg-gradient-to-r from-white via-red-300 to-red-500 bg-clip-text text-transparent leading-tight mb-2">
                  {actressData.name}
                </h1>
                {/* 标题装饰效果 */}
                <div className="absolute -bottom-1 left-0 md:left-0 w-16 h-1 bg-gradient-to-r from-red-500 to-pink-500 rounded-full"></div>
              </div>
              <p className="text-gray-300 mt-4 text-xl font-light">{actressData.nameJp}</p>
              
              {/* 添加副标题装饰 */}
              <div className="mt-2 flex items-center justify-center md:justify-start">
                <div className="w-6 h-0.5 bg-gradient-to-r from-transparent to-red-500 mr-2"></div>
                <span className="text-gray-400 text-sm">女优详情</span>
                <div className="w-6 h-0.5 bg-gradient-to-r from-red-500 to-transparent ml-2"></div>
              </div>
              
              {/* 超现代化统计信息卡片 */}
              <div className="flex flex-wrap justify-center md:justify-start items-center mt-8 gap-4">
                <div className="group relative bg-gradient-to-r from-gray-800/60 via-gray-700/40 to-gray-800/60 backdrop-blur-sm px-6 py-3 rounded-2xl border border-red-500/20 hover:border-red-500/40 transition-all duration-300 hover:shadow-lg hover:shadow-red-500/10">
                  <div className="flex items-center">
                    <FiVideo className="mr-3 text-red-400 text-lg" />
                    <span className="font-bold text-xl text-white">{actressData.videos}</span>
                    <span className="ml-2 text-gray-300 text-sm">部作品</span>
                  </div>
                </div>
                
                <div className="group relative bg-gradient-to-r from-gray-800/60 via-gray-700/40 to-gray-800/60 backdrop-blur-sm px-6 py-3 rounded-2xl border border-pink-500/20 hover:border-pink-500/40 transition-all duration-300 hover:shadow-lg hover:shadow-pink-500/10">
                  <div className="flex items-center">
                    <FiHeart className="mr-3 text-pink-400 text-lg" />
                    <span className="font-bold text-xl text-white">{actressData.fans}</span>
                    <span className="ml-2 text-gray-300 text-sm">粉丝</span>
                  </div>
                </div>
                
                <div className="group relative bg-gradient-to-r from-gray-800/60 via-gray-700/40 to-gray-800/60 backdrop-blur-sm px-6 py-3 rounded-2xl border border-blue-500/20 hover:border-blue-500/40 transition-all duration-300 hover:shadow-lg hover:shadow-blue-500/10">
                  <div className="flex items-center">
                    <FiCalendar className="mr-3 text-blue-400 text-lg" />
                    <span className="font-bold text-xl text-white">{actressData.debut}</span>
                    <span className="ml-2 text-gray-300 text-sm">出道</span>
                  </div>
                </div>
              </div>
              
              {/* 超现代化按钮设计 - 匹配其他页面风格 */}
              <div className="mt-10 flex justify-center md:justify-start gap-4">
                <button 
                  onClick={() => setIsFollowing(!isFollowing)}
                  className={`relative overflow-hidden px-10 py-4 rounded-2xl font-bold text-lg transition-all duration-500 transform hover:scale-105 hover:shadow-2xl ${
                    isFollowing 
                      ? 'bg-gradient-to-r from-gray-700 via-gray-600 to-gray-700 text-white shadow-xl border border-gray-500/30' 
                      : 'bg-gradient-to-r from-red-600 via-red-500 to-red-600 hover:from-red-700 hover:via-red-600 hover:to-red-700 text-white shadow-xl border border-red-400/30'
                  }`}
                >
                  <span className="relative z-10">{isFollowing ? '✓ 已关注' : '♡ 关注'}</span>
                  {!isFollowing && (
                    <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-700"></div>
                  )}
                </button>
                
                <div className="flex gap-3">
                  {actressData.socialMedia.twitter && (
                    <a 
                      href={actressData.socialMedia.twitter} 
                      target="_blank" 
                      rel="noopener noreferrer"
                      className="bg-gradient-to-r from-gray-800/80 to-gray-700/60 hover:from-blue-600 hover:to-blue-700 p-4 rounded-2xl transition-all duration-500 transform hover:scale-110 backdrop-blur-sm border border-gray-600/30 hover:border-blue-400/50 hover:shadow-lg hover:shadow-blue-500/20"
                    >
                      <FiTwitter size={24} />
                    </a>
                  )}
                  
                  {actressData.socialMedia.instagram && (
                    <a 
                      href={actressData.socialMedia.instagram} 
                      target="_blank" 
                      rel="noopener noreferrer"
                      className="bg-gradient-to-r from-gray-800/80 to-gray-700/60 hover:from-pink-500 hover:to-purple-600 p-4 rounded-2xl transition-all duration-500 transform hover:scale-110 backdrop-blur-sm border border-gray-600/30 hover:border-pink-400/50 hover:shadow-lg hover:shadow-pink-500/20"
                    >
                      <FiInstagram size={24} />
                    </a>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* 超现代化女优详细信息卡片 */}
          <div 
            ref={infoRef} 
            className="lg:col-span-1 bg-gradient-to-br from-gray-900/90 via-black/70 to-gray-900/90 backdrop-blur-xl rounded-3xl p-8 h-fit border border-gray-700/30 shadow-2xl shadow-purple-500/5"
          >
            <div className="text-center mb-8">
              <h2 className="text-2xl md:text-3xl font-bold mb-4 bg-gradient-to-r from-white via-red-300 to-red-500 bg-clip-text text-transparent">
                个人资料
              </h2>
              <div className="flex items-center justify-center">
                <div className="w-8 h-0.5 bg-gradient-to-r from-transparent via-red-500 to-red-400 rounded-full"></div>
                <FiUser className="mx-3 text-red-500" />
                <div className="w-8 h-0.5 bg-gradient-to-r from-red-400 via-red-500 to-transparent rounded-full"></div>
              </div>
            </div>
            
            <div className="space-y-4">
              <div className="group flex justify-between items-center p-4 bg-gradient-to-r from-gray-800/60 via-gray-700/40 to-gray-800/60 rounded-2xl backdrop-blur-sm border border-gray-600/20 hover:border-red-500/30 transition-all duration-300">
                <span className="text-gray-300 font-medium">生日</span>
                <span className="text-white font-bold">{actressData.birthdate}</span>
              </div>
              <div className="group flex justify-between items-center p-4 bg-gradient-to-r from-gray-800/60 via-gray-700/40 to-gray-800/60 rounded-2xl backdrop-blur-sm border border-gray-600/20 hover:border-red-500/30 transition-all duration-300">
                <span className="text-gray-300 font-medium">身高</span>
                <span className="text-white font-bold">{actressData.height}</span>
              </div>
              <div className="group flex justify-between items-center p-4 bg-gradient-to-r from-gray-800/60 via-gray-700/40 to-gray-800/60 rounded-2xl backdrop-blur-sm border border-gray-600/20 hover:border-red-500/30 transition-all duration-300">
                <span className="text-gray-300 font-medium">三围</span>
                <span className="text-white font-bold">B{actressData.bust} W{actressData.waist} H{actressData.hip}</span>
              </div>
              <div className="group flex justify-between items-center p-4 bg-gradient-to-r from-gray-800/60 via-gray-700/40 to-gray-800/60 rounded-2xl backdrop-blur-sm border border-gray-600/20 hover:border-red-500/30 transition-all duration-300">
                <span className="text-gray-300 font-medium">血型</span>
                <span className="text-white font-bold">{actressData.blood}</span>
              </div>
              <div className="group flex justify-between items-center p-4 bg-gradient-to-r from-gray-800/60 via-gray-700/40 to-gray-800/60 rounded-2xl backdrop-blur-sm border border-gray-600/20 hover:border-red-500/30 transition-all duration-300">
                <span className="text-gray-300 font-medium">爱好</span>
                <span className="text-white font-bold">{actressData.hobby}</span>
              </div>
            </div>
            
            {/* 现代化个人简介 */}
            <div className="mt-10">
              <h3 className="text-xl font-bold mb-6 bg-gradient-to-r from-white to-red-400 bg-clip-text text-transparent text-center">
                个人简介
              </h3>
              <div className="p-6 bg-gradient-to-br from-gray-800/40 via-gray-700/30 to-gray-800/40 rounded-2xl backdrop-blur-sm border border-gray-700/20 hover:border-red-500/20 transition-all duration-300">
                <p className="text-gray-300 leading-relaxed text-center">{actressData.description}</p>
              </div>
            </div>
          </div>
          
          {/* 现代化女优相关视频 */}
          <div ref={videosRef} className="lg:col-span-2">
            <div className="text-center mb-8">
              <h2 className="text-3xl md:text-4xl font-bold mb-4 bg-gradient-to-r from-white via-red-300 to-red-500 bg-clip-text text-transparent">
                相关视频
              </h2>
              <div className="flex items-center justify-center">
                <div className="w-12 h-1 bg-gradient-to-r from-transparent via-red-500 to-red-400 rounded-full"></div>
                <FiVideo className="mx-4 text-red-500 animate-pulse" />
                <div className="w-12 h-1 bg-gradient-to-r from-red-400 via-red-500 to-transparent rounded-full"></div>
              </div>
            </div>
            
            {/* Adjusted grid columns as per user feedback, removing max-width constraint */}
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6 md:gap-8">
              {actressVideos.slice(0, visibleVideos).map((video, index) => (
                <div
                  key={video.id}
                  ref={(el) => setVideoRef(el, index)}
                  className="group relative bg-gradient-to-br from-gray-900/80 to-black/60 backdrop-blur-xl rounded-2xl overflow-hidden border border-gray-700/30 hover:border-red-500/50 transition-all duration-500 hover:scale-105 hover:shadow-2xl hover:shadow-red-500/10"
                >
                  {/* 现代化视频缩略图 */}
                  <Link href={`/video/${video.slug}`} className="block">
                    <div className="relative overflow-hidden">
                      {/* Adjusted aspect ratio to be closer to homepage cards, e.g., 16:9 or similar */}
                      <div className="relative w-full aspect-video bg-gray-800/30 rounded-xl overflow-hidden group-hover:shadow-xl group-hover:shadow-red-500/20 transition-all duration-300 transform group-hover:scale-100">
                        <Image 
                          src={video.imageUrl} 
                          alt={video.title} 
                          fill
                          className="object-cover transition-all duration-500 group-hover:brightness-110"
                          sizes="(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 33vw" // Adjusted sizes
                        />
                        <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-transparent to-black/10"></div>
                        <div className="absolute top-2 right-2 bg-black/70 backdrop-blur-sm text-white px-2 py-0.5 rounded text-xs">{video.duration}</div>
                        {/* Modern Play Button */}
                        <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                          <div className="bg-red-600/80 backdrop-blur-sm rounded-full p-4 shadow-xl">
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-8 h-8 text-white">
                              <path fillRule="evenodd" d="M4.5 5.653c0-1.426 1.529-2.33 2.779-1.643l11.54 6.348c1.295.712 1.295 2.573 0 3.285L7.279 19.99c-1.25.687-2.779-.217-2.779-1.643V5.653z" clipRule="evenodd" />
                            </svg>
                          </div>
                        </div>
                      </div>
                    </div>
                  </Link>
                  
                  {/* 视频信息 */}
                  <div className="mt-4">
                    <h3 className="text-lg font-semibold text-gray-100 group-hover:text-red-400 transition-colors duration-300 truncate">{video.title}</h3>
                    <div className="flex items-center text-sm text-gray-400 mt-2 space-x-3">
                      {/* 观看次数 */}
                      <div className="flex items-center">
                        <FiVideo className="mr-1.5" />
                        <span>{videoStats[video.slug] ? `${(videoStats[video.slug]?.views || 0).toLocaleString()} 次观看` : `${video.views} 观看 (mock)`}</span>
                      </div>
                      {/* 日期 - Changed to relative time */}
                      <div className="flex items-center">
                        <FiCalendar className="mr-1.5" />
                        <span>{formatRelativeTime(video.date)}</span>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
            
            {visibleVideos < actressVideos.length && (
              <div className="text-center mt-10">
                <button 
                  onClick={loadMoreVideos}
                  className="px-8 py-3 bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 rounded-full transition-all duration-300 transform hover:scale-105 font-medium text-white shadow-lg"
                >
                  加载更多视频
                </button>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
} 