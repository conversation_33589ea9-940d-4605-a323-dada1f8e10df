import type { Metadata } from 'next';
import { generateMetadata as createMetadata } from '@/lib/seo';
import { Suspense } from 'react';
import ActressClient from './ActressClient';

// 导出静态元数据
export const metadata: Metadata = createMetadata({
  title: '日本AV女优库',
  description: 'JAVFLIX女优库收录上千位日本AV女优资料，包含高清写真、个人简介、全部作品合集，提供按人气、新人、身材、年龄等多维度筛选',
  path: '/actress',
  keywords: [
    '日本AV女优', '女优库', '女优排行', '女优写真', '女优作品集', 
    '新人女优', '人气女优', '女优分类', '女优百科', '女优信息'
  ],
});

// 加载占位符组件
function ActressLoading() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-800 text-white pt-20 pb-12">
      {/* 背景装饰 */}
      <div className="absolute inset-0 pointer-events-none">
        <div className="absolute top-1/4 left-1/4 w-64 h-64 bg-red-500/10 rounded-full filter blur-3xl"></div>
        <div className="absolute bottom-1/3 right-1/3 w-96 h-96 bg-blue-500/8 rounded-full filter blur-3xl"></div>
        <div className="absolute top-1/2 left-1/2 w-48 h-48 bg-purple-500/12 rounded-full filter blur-2xl"></div>
      </div>

      <div className="relative container mx-auto px-4">
        <div className="text-center mb-12">
          <div className="h-12 bg-gray-800 rounded-lg animate-pulse mb-4 mx-auto max-w-md"></div>
          <div className="h-6 bg-gray-700 rounded-lg animate-pulse mx-auto max-w-sm"></div>
        </div>
        <div className="grid grid-cols-3 sm:grid-cols-4 md:grid-cols-6 lg:grid-cols-8 xl:grid-cols-10 2xl:grid-cols-12 gap-6">
          {[...Array(24)].map((_, i) => (
            <div key={i} className="text-center">
              <div className="w-16 h-16 md:w-20 md:h-20 rounded-full bg-gray-800 animate-pulse mx-auto mb-3"></div>
              <div className="h-4 bg-gray-700 rounded animate-pulse mb-2"></div>
              <div className="h-3 bg-gray-600 rounded animate-pulse w-8 mx-auto"></div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}

// 主页面是服务器组件，只用于包装客户端组件
export default function ActressPage() {
  return (
    <Suspense fallback={<ActressLoading />}>
      <ActressClient />
    </Suspense>
  );
} 