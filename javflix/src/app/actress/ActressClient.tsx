'use client';

import { useEffect, useState, useCallback, useRef, useTransition } from 'react';
import { usePathname, useParams, useSearchParams, useRouter } from 'next/navigation';
import Link from 'next/link';
import { <PERSON>Video, FiSearch, FiLoader } from 'react-icons/fi';
import Image from 'next/image';
import { useClientTranslations } from '@/components/TranslationsProvider';

interface Actress {
  id: number;
  star_id: string;
  name: string;
  image_url?: string;
  cached_image_url?: string;
  movie_count: number;
}

interface APIResponse {
  success: boolean;
  data: {
    items: Actress[];
    pagination?: {
      currentPage: number;
      totalPages: number;
      totalItems: number;
      limit: number;
    };
  };
  message?: string;
}

export default function ActressClient() {
  const pathname = usePathname();
  const params = useParams();
  const locale = params?.locale as string || 'zh-CN';
  const searchParams = useSearchParams();
  const router = useRouter();
  const { t } = useClientTranslations();
  
  // 状态管理
  const [actresses, setActresses] = useState<Actress[]>([]);
  const [loading, setLoading] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState('movie_count');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [hasMoreData, setHasMoreData] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalCount, setTotalCount] = useState(0);
  
  const loadMoreRef = useRef<HTMLDivElement>(null);
  const searchTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const itemsPerPage = 48;

  // 获取女优数据
  const fetchActresses = useCallback(async (page: number = 1, isLoadMore: boolean = false, searchQuery?: string) => {
    try {
      if (isLoadMore) {
        setLoadingMore(true);
      } else {
        setLoading(true);
        setError(null);
      }

      const params = new URLSearchParams({
        page: page.toString(),
        limit: itemsPerPage.toString(),
        sort: sortBy,
        order: sortOrder
      });

      // 使用传入的搜索词或当前状态的搜索词
      const finalSearchTerm = searchQuery !== undefined ? searchQuery : searchTerm;
      if (finalSearchTerm && finalSearchTerm.trim()) {
        params.append('search', finalSearchTerm.trim());
      }

      console.log('Fetching actresses:', {
        page,
        isLoadMore,
        searchQuery: finalSearchTerm,
        url: `/api/db/stars?${params.toString()}`
      });

      const response = await fetch(`/api/db/stars?${params.toString()}`);
      
      if (!response.ok) {
        if (response.status === 400) {
          throw new Error(`请求参数错误: ${response.status}`);
        } else if (response.status === 404) {
          throw new Error(`未找到数据: ${response.status}`);
        } else if (response.status >= 500) {
          throw new Error(`服务器错误: ${response.status}`);
        } else {
          throw new Error(`API请求失败: ${response.status}`);
        }
      }

      const result: APIResponse = await response.json();
      
      if (!result.success || !Array.isArray(result.data.items)) {
        throw new Error('API响应格式错误');
      }

      const newActresses = result.data.items;
      
      if (isLoadMore) {
        setActresses(prev => [...prev, ...newActresses]);
      } else {
        setActresses(newActresses);
      }

      // 处理分页信息
      if (result.data.pagination) {
        setTotalCount(result.data.pagination.totalItems);
        setHasMoreData(page < result.data.pagination.totalPages);
      } else {
        setHasMoreData(newActresses.length === itemsPerPage);
      }

    } catch (error) {
      console.error('获取女优数据失败:', error);
      setError(error instanceof Error ? error.message : '获取数据失败');
      if (!isLoadMore) {
        setActresses([]);
      }
    } finally {
      setLoading(false);
      setLoadingMore(false);
    }
  }, [sortBy, sortOrder, itemsPerPage]);

  // 获取统计数据
  const fetchStats = useCallback(async () => {
    try {
      const response = await fetch('/api/db/stats/stars');
      if (response.ok) {
        const result = await response.json();
        if (result.success && result.data?.totalStars) {
          setTotalCount(result.data.totalStars);
        }
      }
    } catch (error) {
      console.error('获取统计数据失败:', error);
    }
  }, []);

  // 搜索处理 - 防抖搜索
  const handleSearchInput = (value: string) => {
    setSearchTerm(value);
    
    // 清除之前的搜索定时器
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }
    
    // 设置新的搜索定时器（300ms防抖）
    searchTimeoutRef.current = setTimeout(() => {
      console.log('Debounced search triggered:', value);
      performSearch(value);
    }, 300);
  };

  // 执行搜索
  const performSearch = (searchQuery: string) => {
    console.log('Performing search:', searchQuery);
    
    // 重置页面状态
    setCurrentPage(1);
    setActresses([]);
    setHasMoreData(true);
    
    // 获取数据
    fetchActresses(1, false, searchQuery);
  };

  // 搜索表单提交
  const handleSearchSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    // 清除防抖定时器
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }
    
    // 立即搜索
    performSearch(searchTerm);
  };

  // 排序处理
  const handleSortChange = (newSortBy: string, newSortOrder: 'asc' | 'desc') => {
    setSortBy(newSortBy);
    setSortOrder(newSortOrder);
    setCurrentPage(1);
    setActresses([]);
    setHasMoreData(true);
  };

  // 加载更多数据
  const loadMoreData = useCallback(() => {
    if (!loadingMore && hasMoreData) {
      const nextPage = currentPage + 1;
      setCurrentPage(nextPage);
      fetchActresses(nextPage, true, searchTerm);
    }
  }, [currentPage, loadingMore, hasMoreData, fetchActresses, searchTerm]);

  // 获取女优图片URL
  const getActressImageUrl = (actress: Actress): string => {
    if (actress.cached_image_url) {
      return actress.cached_image_url;
    }
    if (actress.image_url) {
      return actress.image_url;
    }
    return '/images/defaults/actress_default.jpg';
  };

  // 获取女优详情页链接
  const getActressLink = (actress: Actress): string => {
    const identifier = actress.star_id || encodeURIComponent(actress.name);
    return `/${locale}/actress/${identifier}`;
  };

  // 初始化数据
  useEffect(() => {
    fetchStats();
    fetchActresses(1, false, '');
  }, []);

  // 当排序改变时重新获取数据
  useEffect(() => {
    if (sortBy && sortOrder) {
      fetchActresses(1, false, '');
    }
  }, [sortBy, sortOrder, fetchActresses]);

  // 无限滚动监听
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting && hasMoreData && !loadingMore && !loading) {
          loadMoreData();
        }
      },
      {
        threshold: 0.1,
        rootMargin: '100px'
      }
    );

    if (loadMoreRef.current) {
      observer.observe(loadMoreRef.current);
    }

    return () => {
      if (loadMoreRef.current) {
        observer.unobserve(loadMoreRef.current);
      }
    };
  }, [loadMoreData, hasMoreData, loadingMore, loading]);

  // 清理搜索定时器
  useEffect(() => {
    return () => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }
    };
  }, []);

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-800 text-white pt-20 pb-12">
      {/* 背景装饰 */}
      <div className="absolute inset-0 pointer-events-none">
        <div className="absolute top-1/4 left-1/4 w-64 h-64 bg-red-500/10 rounded-full filter blur-3xl"></div>
        <div className="absolute bottom-1/3 right-1/3 w-96 h-96 bg-blue-500/8 rounded-full filter blur-3xl"></div>
        <div className="absolute top-1/2 left-1/2 w-48 h-48 bg-purple-500/12 rounded-full filter blur-2xl"></div>
      </div>

      <div className="relative container mx-auto px-4">
        {/* 页面标题 */}
        <div className="text-center mb-16">
          <h1 className="text-5xl md:text-7xl font-bold mb-6 text-white leading-tight">
            {t('actress.popular')}
          </h1>
          <p className="text-xl text-gray-300 font-light">
            {totalCount > 0 ? (
              t('common.discoveredActresses', { count: totalCount })
            ) : (
              t('common.loading')
            )}
          </p>
        </div>

        {/* 搜索和筛选 */}
        <div className="mb-12 flex flex-col lg:flex-row gap-6 items-center justify-between">
          {/* 搜索框 */}
          <form onSubmit={handleSearchSubmit} className="flex gap-3 flex-1 max-w-lg">
            <div className="relative flex-1">
              <FiSearch className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400" size={18} />
              <input
                type="text"
                value={searchTerm}
                onChange={(e) => handleSearchInput(e.target.value)}
                placeholder={t('common.searchActress')}
                className="w-full bg-gray-800/60 backdrop-blur-sm border border-gray-700/50 rounded-2xl pl-12 pr-4 py-4 text-white placeholder-gray-400 focus:outline-none focus:border-red-500/50 focus:ring-2 focus:ring-red-500/20 transition-all duration-300"
              />
            </div>
            <button
              type="submit"
              className="bg-gradient-to-r from-red-600 to-red-700 hover:from-red-500 hover:to-red-600 text-white px-8 py-4 rounded-2xl transition-all duration-300 text-sm font-medium shadow-lg shadow-red-500/25 transform hover:scale-105"
            >
              {t('common.searchButton')}
            </button>
          </form>

          {/* 排序选择 */}
          <div className="flex items-center gap-4">
            <span className="text-gray-300 text-sm font-medium">{t('common.sortBy')}</span>
            <select
              value={`${sortBy}-${sortOrder}`}
              onChange={(e) => {
                const [field, order] = e.target.value.split('-');
                handleSortChange(field, order as 'asc' | 'desc');
              }}
              className="bg-gray-800/60 backdrop-blur-sm border border-gray-700/50 rounded-2xl px-6 py-4 text-white focus:outline-none focus:border-red-500/50 focus:ring-2 focus:ring-red-500/20 cursor-pointer transition-all duration-300"
            >
              <option value="movie_count-desc">{t('actress.sortByVideosDesc')}</option>
              <option value="popularity-desc">{t('actress.sortByPopularity')}</option>
            </select>
          </div>
        </div>

        {/* 错误提示 */}
        {error && (
          <div className="mb-8 p-6 bg-red-500/10 border border-red-500/30 rounded-2xl text-red-300 text-center backdrop-blur-sm">
            <div className="text-2xl mb-2">⚠️</div>
            {error}
          </div>
        )}

        {/* 女优列表 */}
        {loading && actresses.length === 0 ? (
          <div className="flex justify-center items-center min-h-[500px] relative">
            {/* 背景装饰 */}
            <div className="absolute inset-0 pointer-events-none">
              <div className="absolute top-1/4 left-1/4 w-32 h-32 bg-red-500/10 rounded-full filter blur-2xl"></div>
              <div className="absolute bottom-1/3 right-1/3 w-48 h-48 bg-blue-500/8 rounded-full filter blur-2xl"></div>
            </div>

            <div className="relative text-center">
              <div className="relative">
                <div className="w-16 h-16 border-4 border-gray-700 border-t-white rounded-full animate-spin mx-auto mb-6"></div>
                <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-8 h-8 bg-white/20 rounded-full animate-pulse"></div>
              </div>
              <p className="text-gray-300 text-lg font-light">{t('common.loading')}</p>
            </div>
          </div>
        ) : actresses.length === 0 ? (
          <div className="text-center py-24">
            <div className="relative inline-block mb-8">
              <div className="text-8xl filter drop-shadow-lg">👤</div>
              <div className="absolute -bottom-2 left-1/2 transform -translate-x-1/2 w-12 h-1 bg-gradient-to-r from-transparent via-red-500 to-transparent"></div>
            </div>
            <h3 className="text-2xl font-medium mb-3 text-gray-200">{t('actress.noActressesFound')}</h3>
            <p className="text-gray-400 text-lg">{t('actress.tryAdjustSearch')}</p>
          </div>
        ) : (
          <>
            {/* 女优网格 - 圆形头像设计 */}
            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 2xl:grid-cols-8 gap-6 mb-12">
              {actresses.map((actress, index) => (
                <Link
                  key={`${actress.id}-${index}`}
                  href={getActressLink(actress)}
                  className="group block transform transition-all duration-500 hover:scale-105"
                  style={{
                    animationDelay: `${index * 50}ms`
                  }}
                >
                  <div className="text-center">
                    {/* 圆形头像 */}
                    <div className="flex justify-center mb-4">
                      <div className="relative w-20 h-20 rounded-full overflow-hidden border-2 border-transparent group-hover:border-red-500/50 transition-all duration-300">
                    <Image
                      src={getActressImageUrl(actress)}
                      alt={actress.name}
                      fill
                          className="object-cover group-hover:scale-110 transition-transform duration-500"
                      onError={(e) => {
                        const target = e.target as HTMLImageElement;
                        target.src = '/images/defaults/actress_default.jpg';
                      }}
                    />
                        {/* 悬停光晕效果 */}
                        <div className="absolute inset-0 bg-gradient-to-t from-red-500/20 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-full" />
                      </div>
                  </div>
                    
                    {/* 信息区域 */}
                    <div className="text-center">
                      <h3 className="text-white text-sm font-medium truncate mb-2 group-hover:text-red-300 transition-colors duration-300">
                      {actress.name}
                    </h3>
                      <div className="flex items-center justify-center gap-2 text-xs text-gray-400">
                      <FiVideo size={12} />
                        <span className="group-hover:text-red-400 transition-colors duration-300">
                          {actress.movie_count} {t('actress.videos')}
                        </span>
                      </div>
                    </div>
                  </div>
                </Link>
              ))}
            </div>

            {/* 加载更多指示器 */}
            {hasMoreData && (
              <div ref={loadMoreRef} className="flex justify-center items-center py-16">
                {loadingMore ? (
                  <div className="text-center">
                    <div className="relative">
                      <div className="w-12 h-12 border-3 border-gray-700 border-t-red-500 rounded-full animate-spin mx-auto mb-4"></div>
                      <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-6 h-6 bg-red-500/20 rounded-full animate-pulse"></div>
                    </div>
                    <p className="text-gray-300 text-sm font-light">{t('common.loading')}</p>
                  </div>
                ) : (
                  <button
                    onClick={loadMoreData}
                    className="bg-gray-800/60 hover:bg-gray-700/60 backdrop-blur-sm text-gray-300 hover:text-white px-8 py-4 rounded-2xl transition-all duration-300 text-sm font-medium border border-gray-700/30 hover:border-red-500/50 transform hover:scale-105 shadow-lg"
                  >
                    {t('actress.loadMore')}
                  </button>
                )}
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
}