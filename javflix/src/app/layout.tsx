import '@/app/globals.css';
import type { Metadata, Viewport } from "next";
import { Inter } from "next/font/google";
import { defaultLocale } from '@/i18n';
import { AuthProvider } from '../context/AuthContext';

const inter = Inter({ subsets: ["latin"] });

// 根布局元数据
export const metadata: Metadata = {
  title: 'JAVFLIX.TV - 高清在线AV影片',
  description: '最新、最全、最优质的日本成人AV影片在线观看与下载平台，提供无码有码高清视频，中文字幕，每日更新最新影片',
  keywords: '日本AV,在线观看,高清无码,有码,中文字幕,免费下载,女优库,番号搜索',
  manifest: '/manifest.json',
  icons: {
    icon: '/favicon.ico',
    apple: '/apple-touch-icon.png',
  },
  metadataBase: new URL('https://javflix.tv'),
};

// 视口配置
export const viewport: Viewport = {
  width: 'device-width',
  initialScale: 1,
  themeColor: '#000000',
  colorScheme: 'dark'
};

// 根布局需要包含HTML和body标签
export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="zh-Hans">
      <head>
        <meta charSet="utf-8" />
        <meta name="applicable-device" content="pc,mobile" />
        <meta httpEquiv="X-UA-Compatible" content="IE=edge,chrome=1" />
        <meta name="format-detection" content="telephone=no,email=no,address=no" />
        <meta name="renderer" content="webkit" />
        
        {/* Performance optimizations - DNS prefetch and preconnect */}
        <link rel="dns-prefetch" href="//localhost:4000" />
        <link rel="preconnect" href="http://localhost:4000" crossOrigin="" />
        <link rel="dns-prefetch" href="//javbus.com" />
        <link rel="preconnect" href="https://javbus.com" crossOrigin="" />
        
        {/* Router Cache prefetching for main pages */}
        <link rel="prefetch" href="/zh/new" />
        <link rel="prefetch" href="/zh/popular" />
        <link rel="prefetch" href="/zh/actress" />
        <link rel="prefetch" href="/api/videos" />
        <link rel="prefetch" href="/api/popular-videos" />
        
        {/* 站点地图链接 */}
        <link rel="sitemap" type="application/xml" href="/sitemap-index.xml" />
        <link rel="sitemap" type="application/xml" href="/sitemap.xml" />
        <link rel="sitemap" type="application/xml" href="/sitemap-video.xml" />
        <link rel="sitemap" type="application/xml" href="/sitemap-video-index.xml" />
      </head>
      <body className={`${inter.className} bg-gray-900 text-white min-h-screen flex flex-col`}>
        <AuthProvider>
          {children}
        </AuthProvider>
      </body>
    </html>
  );
}
