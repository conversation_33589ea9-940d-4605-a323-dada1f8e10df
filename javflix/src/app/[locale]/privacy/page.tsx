import { Metadata } from 'next';
import { getTranslations, locales } from '@/i18n';
import { Locale } from '@/i18n/types';
import { notFound } from 'next/navigation';

interface PrivacyPageProps {
  params: Promise<{ locale: string }>;
}

export async function generateMetadata({
  params
}: PrivacyPageProps): Promise<Metadata> {
  const resolvedParams = await params;
  const locale = resolvedParams.locale as Locale;
  
  if (!locale || !locales.includes(locale)) {
    return {
      title: 'Privacy Policy',
      description: 'JAVFLIX.TV Privacy Policy',
    };
  }
  
  const t = await getTranslations(locale);
  
  return {
    title: t.pages.privacy.title,
    description: t.pages.privacy.description,
  };
}

export default async function PrivacyPage({ params }: PrivacyPageProps) {
  const resolvedParams = await params;
  const locale = resolvedParams.locale as Locale;
  
  if (!locale || !locales.includes(locale)) {
    notFound();
  }
  
  const t = await getTranslations(locale);

  return (
    <div className="min-h-screen bg-gray-900 text-white">
      <div className="container mx-auto px-4 py-8 max-w-4xl">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl md:text-5xl font-bold mb-4 bg-gradient-to-r from-red-500 to-red-700 bg-clip-text text-transparent">
            {t.privacy.title}
          </h1>
          <p className="text-gray-400 text-sm">
            {t.privacy.lastUpdated}
          </p>
        </div>

        <div className="space-y-8">
          {/* Introduction */}
          <section className="bg-gray-800 rounded-lg p-6">
            <h2 className="text-2xl font-bold mb-4 text-red-500">
              {t.privacy.intro.title}
            </h2>
            <p className="text-gray-300 leading-relaxed">
              {t.privacy.intro.content}
            </p>
          </section>

          {/* Information Collection */}
          <section className="bg-gray-800 rounded-lg p-6">
            <h2 className="text-2xl font-bold mb-4 text-red-500">
              {t.privacy.collection.title}
            </h2>
            <p className="text-gray-300 mb-4">
              {t.privacy.collection.subtitle}
            </p>
            <div className="space-y-4">
              <div>
                <h3 className="text-lg font-semibold mb-2 text-red-400">
                  {t.privacy.collection.personal.title}
                </h3>
                <p className="text-gray-300">
                  {t.privacy.collection.personal.content}
                </p>
              </div>
              <div>
                <h3 className="text-lg font-semibold mb-2 text-red-400">
                  {t.privacy.collection.usage.title}
                </h3>
                <p className="text-gray-300">
                  {t.privacy.collection.usage.content}
                </p>
              </div>
              <div>
                <h3 className="text-lg font-semibold mb-2 text-red-400">
                  {t.privacy.collection.technical.title}
                </h3>
                <p className="text-gray-300">
                  {t.privacy.collection.technical.content}
                </p>
              </div>
            </div>
          </section>

          {/* Information Usage */}
          <section className="bg-gray-800 rounded-lg p-6">
            <h2 className="text-2xl font-bold mb-4 text-red-500">
              {t.privacy.usage.title}
            </h2>
            <p className="text-gray-300 mb-4">
              {t.privacy.usage.subtitle}
            </p>
            <ul className="space-y-2 text-gray-300">
              <li className="flex items-start">
                <span className="text-red-400 mr-2">•</span>
                {t.privacy.usage.item1}
              </li>
              <li className="flex items-start">
                <span className="text-red-400 mr-2">•</span>
                {t.privacy.usage.item2}
              </li>
              <li className="flex items-start">
                <span className="text-red-400 mr-2">•</span>
                {t.privacy.usage.item3}
              </li>
              <li className="flex items-start">
                <span className="text-red-400 mr-2">•</span>
                {t.privacy.usage.item4}
              </li>
              <li className="flex items-start">
                <span className="text-red-400 mr-2">•</span>
                {t.privacy.usage.item5}
              </li>
            </ul>
          </section>

          {/* Information Sharing */}
          <section className="bg-gray-800 rounded-lg p-6">
            <h2 className="text-2xl font-bold mb-4 text-red-500">
              {t.privacy.sharing.title}
            </h2>
            <p className="text-gray-300 mb-4">
              {t.privacy.sharing.content}
            </p>
            <ul className="space-y-2 text-gray-300">
              <li className="flex items-start">
                <span className="text-red-400 mr-2">•</span>
                {t.privacy.sharing.item1}
              </li>
              <li className="flex items-start">
                <span className="text-red-400 mr-2">•</span>
                {t.privacy.sharing.item2}
              </li>
              <li className="flex items-start">
                <span className="text-red-400 mr-2">•</span>
                {t.privacy.sharing.item3}
              </li>
            </ul>
          </section>

          {/* Information Security */}
          <section className="bg-gray-800 rounded-lg p-6">
            <h2 className="text-2xl font-bold mb-4 text-red-500">
              {t.privacy.security.title}
            </h2>
            <p className="text-gray-300 leading-relaxed">
              {t.privacy.security.content}
            </p>
          </section>

          {/* Cookie Policy */}
          <section className="bg-gray-800 rounded-lg p-6">
            <h2 className="text-2xl font-bold mb-4 text-red-500">
              {t.privacy.cookies.title}
            </h2>
            <p className="text-gray-300 leading-relaxed">
              {t.privacy.cookies.content}
            </p>
          </section>

          {/* Your Rights */}
          <section className="bg-gray-800 rounded-lg p-6">
            <h2 className="text-2xl font-bold mb-4 text-red-500">
              {t.privacy.rights.title}
            </h2>
            <p className="text-gray-300 mb-4">
              {t.privacy.rights.content}
            </p>
            <ul className="space-y-2 text-gray-300">
              <li className="flex items-start">
                <span className="text-red-400 mr-2">•</span>
                {t.privacy.rights.item1}
              </li>
              <li className="flex items-start">
                <span className="text-red-400 mr-2">•</span>
                {t.privacy.rights.item2}
              </li>
              <li className="flex items-start">
                <span className="text-red-400 mr-2">•</span>
                {t.privacy.rights.item3}
              </li>
              <li className="flex items-start">
                <span className="text-red-400 mr-2">•</span>
                {t.privacy.rights.item4}
              </li>
            </ul>
          </section>

          {/* Contact */}
          <section className="bg-gray-800 rounded-lg p-6 text-center">
            <h2 className="text-2xl font-bold mb-4 text-red-500">
              {t.privacy.contact.title}
            </h2>
            <p className="text-gray-300">
              {t.privacy.contact.content}
            </p>
          </section>
        </div>
      </div>
    </div>
  );
} 