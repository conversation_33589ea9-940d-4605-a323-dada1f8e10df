'use client';

import { useEffect, useState, useRef } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import Link from 'next/link';
import { FiSearch, FiFilter, FiX, FiChevronDown, FiChevronUp } from 'react-icons/fi';
import gsap from 'gsap';
import { buildApiUrl } from '@/lib/api-config';
import { getSocketClient, VideoStats } from '@/lib/socket-client';
import { formatDuration } from '@/lib/duration-format';
import VideoCard from '@/components/VideoCard';
import SectionHeader from '@/components/SectionHeader';
import { LoadingSpinner, PageLoadingOverlay, VideoGridSkeleton, ErrorState } from '@/components/loading';
import { useGSAPPageAnimation, useVideoCardAnimations } from '@/hooks/animations';
import { useClientTranslations } from '@/components/TranslationsProvider';

interface Video {
  id: string | number;
  title: string;
  thumbnail: string;
  image: string;
  duration: string;
  views?: number;
  viewCount: number;
  timeAgo: string;
  slug: string;
  isHD?: boolean;
  rating?: number;
  studio?: string;
  category?: string;
  tags?: string[];
  code?: string;
  movie_id?: string;
  cover_image?: string;
  image_url?: string;
  cached_image_url?: string;
  thumbnail_url?: string;
  release_date?: string;
  description?: string;
  hasSubtitle?: boolean;
  stars?: Array<{
    id: string;
    name: string;
  }>;
}

interface SearchSuggestion {
  id: string;
  text: string;
  type: 'movie' | 'star';
  image?: string;
  meta?: string;
}

interface SearchClientProps {
  locale: string;
  initialQuery: string;
  initialCategory: string;
  initialDuration: string;
  initialTime: string;
  initialQuality: string;
  initialSort: string;
  initialPage: number;
}

interface FusedVideoStats {
  videoId: string;
  numericVideoId: number;
  views: number;
  likes: number;
  favorites: number;
  dbBase: {
    views: number;
    likes: number;
    favorites: number;
  };
  redisIncrement: {
    views: number;
    likes: number;
    favorites: number;
  };
  lastUpdated: number;
  fusionTimestamp: number;
}

// 时长格式化函数现在从 lib/duration-format.ts 导入

// 格式化时间函数
function getTimeAgo(dateString: string, locale: string = 'zh-CN') {
  const date = new Date(dateString);
  const now = new Date();
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);
  
  if (locale === 'en') {
    if (diffInSeconds < 60) return `${diffInSeconds} seconds ago`;
    const diffInMinutes = Math.floor(diffInSeconds / 60);
    if (diffInMinutes < 60) return `${diffInMinutes} minutes ago`;
    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) return `${diffInHours} hours ago`;
    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays < 30) return `${diffInDays} days ago`;
    const diffInMonths = Math.floor(diffInDays / 30);
    if (diffInMonths < 12) return `${diffInMonths} months ago`;
    const diffInYears = Math.floor(diffInMonths / 12);
    return `${diffInYears} years ago`;
  } else if (locale === 'ja') {
    if (diffInSeconds < 60) return `${diffInSeconds}秒前`;
    const diffInMinutes = Math.floor(diffInSeconds / 60);
    if (diffInMinutes < 60) return `${diffInMinutes}分前`;
    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) return `${diffInHours}時間前`;
    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays < 30) return `${diffInDays}日前`;
    const diffInMonths = Math.floor(diffInDays / 30);
    if (diffInMonths < 12) return `${diffInMonths}ヶ月前`;
    const diffInYears = Math.floor(diffInMonths / 12);
    return `${diffInYears}年前`;
  } else {
    // 默认中文
    if (diffInSeconds < 60) return `${diffInSeconds}秒前`;
    const diffInMinutes = Math.floor(diffInSeconds / 60);
    if (diffInMinutes < 60) return `${diffInMinutes}分钟前`;
    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) return `${diffInHours}小时前`;
    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays < 30) return `${diffInDays}天前`;
    const diffInMonths = Math.floor(diffInDays / 30);
    if (diffInMonths < 12) return `${diffInMonths}个月前`;
    const diffInYears = Math.floor(diffInMonths / 12);
    return `${diffInYears}年前`;
  }
}

export function SearchClient({
  locale,
  initialQuery,
  initialCategory,
  initialDuration,
  initialTime,
  initialQuality,
  initialSort,
  initialPage
}: SearchClientProps) {
  const router = useRouter();
  const pathname = usePathname();
  const { t } = useClientTranslations();
  
  const [query, setQuery] = useState(initialQuery);
  const [category, setCategory] = useState(initialCategory);
  const [duration, setDuration] = useState(initialDuration);
  const [time, setTime] = useState(initialTime);
  const [quality, setQuality] = useState(initialQuality);
  const [sort, setSort] = useState(initialSort);
  const [page, setPage] = useState(initialPage);

  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [results, setResults] = useState<Video[]>([]);
  const [totalResults, setTotalResults] = useState(0);
  const [totalPages, setTotalPages] = useState(0);
  const [showFilters, setShowFilters] = useState(false);
  const [expandedSections, setExpandedSections] = useState({
    category: true,
    duration: true, 
    time: true,
    quality: true
  });
  
  // 添加搜索词推荐相关状态
  const [searchTerm, setSearchTerm] = useState(initialQuery);
  const [suggestions, setSuggestions] = useState<SearchSuggestion[]>([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [isInputFocused, setIsInputFocused] = useState(false);
  const suggestionsRef = useRef<HTMLDivElement>(null);
  const searchInputRef = useRef<HTMLInputElement>(null);
  const suggestionDebounceRef = useRef<NodeJS.Timeout | null>(null);
  
  // 融合统计相关状态
  const [fusedStats, setFusedStats] = useState<Record<string, FusedVideoStats>>({});
  const socketClient = useRef(getSocketClient());
  
  // GSAP animations
  const { titleRef, subtitleRef, contentRef, animatePageEntrance } = useGSAPPageAnimation();
  const { animateVideoCards, setVideoRef, resetVideoRefs } = useVideoCardAnimations();
  
  const videoRefs = useRef<Array<HTMLDivElement | null>>([]);
  const filtersRef = useRef<HTMLDivElement>(null);

  // 分类选项
  const categories = [
    { value: 'all', label: t('search.allCategories') },
    { value: 'drama', label: t('search.drama') },
    { value: 'uniform', label: t('search.uniform') },
    { value: 'schoolgirl', label: t('search.schoolgirl') },
    { value: 'mature', label: t('search.mature') },
    { value: 'idol', label: t('search.idol') },
    { value: 'cosplay', label: t('search.cosplay') },
    { value: 'outdoor', label: t('search.outdoor') },
  ];

  // 视频时长选项
  const durations = [
    { value: 'all', label: t('search.allDurations') },
    { value: 'short', label: t('search.short') },
    { value: 'medium', label: t('search.medium') },
    { value: 'long', label: t('search.long') },
    { value: 'movie', label: t('search.movie') },
  ];

  // 上传时间选项
  const uploadTimes = [
    { value: 'all', label: t('search.allTimes') },
    { value: 'today', label: t('search.today') },
    { value: 'week', label: t('search.week') },
    { value: 'month', label: t('search.month') },
    { value: 'year', label: t('search.year') },
  ];

  // 清晰度选项
  const qualities = [
    { value: 'all', label: t('search.allQualities') },
    { value: 'hd', label: t('search.hd') },
    { value: 'fullhd', label: t('search.fullhd') },
    { value: '4k', label: t('search.4k') },
  ];

  // 侧边栏收起/展开逻辑
  const toggleSection = (section: 'category' | 'duration' | 'time' | 'quality') => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  // 批量获取融合统计数据
  const fetchFusedStats = async (videoList: Video[]) => {
    try {
      if (videoList.length === 0) return;

      const videoIds = videoList.map(video => video.id.toString());
      
      const response = await fetch(buildApiUrl('/api/video-stats/fused/batch'), {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ videoIds }),
      });

      if (!response.ok) {
        return;
      }

      const result = await response.json();
      
      if (result.success && result.data) {
        setFusedStats(result.data);
      }
    } catch (error) {
      // Silently handle fusion stats errors
    }
  };

  // 构建查询参数
  const buildQueryParams = (params: Record<string, string>) => {
    const newParams = new URLSearchParams();
    
    // 始终包含查询词
    if (query) {
      newParams.set('q', query);
    }
    
    // 添加当前参数
    if (category && category !== 'all') newParams.set('category', category);
    if (duration && duration !== 'all') newParams.set('duration', duration);
    if (time && time !== 'all') newParams.set('time', time);
    if (quality && quality !== 'all') newParams.set('quality', quality);
    if (sort && sort !== 'relevance') newParams.set('sort', sort);
    if (page > 1) newParams.set('page', page.toString());
    
    // 添加新参数
    Object.entries(params).forEach(([key, value]) => {
      if (value && value !== 'all') {
        newParams.set(key, value);
      } else {
        newParams.delete(key);
      }
    });
    
    return newParams.toString();
  };

  // 应用筛选器
  const applyFilter = (filterType: string, value: string) => {
    const params: Record<string, string> = {};
    
    switch (filterType) {
      case 'category':
        params.category = value;
        setCategory(value);
        break;
      case 'duration':
        params.duration = value;
        setDuration(value);
        break;
      case 'time':
        params.time = value;
        setTime(value);
        break;
      case 'quality':
        params.quality = value;
        setQuality(value);
        break;
      case 'sort':
        params.sort = value;
        setSort(value);
        break;
    }
    
    // 重置页码
    params.page = '1';
    setPage(1);
    
    const queryString = buildQueryParams(params);
    router.push(`/${locale}/search?${queryString}`);
  };

  // 清除所有筛选器
  const clearAllFilters = () => {
    setCategory('all');
    setDuration('all');
    setTime('all');
    setQuality('all');
    setSort('relevance');
    setPage(1);
    
    router.push(`/${locale}/search?q=${encodeURIComponent(query)}`);
  };

  // 获取搜索建议
  const fetchSuggestions = async (term: string) => {
    if (term.trim().length === 0) {
      setSuggestions([]);
      setShowSuggestions(false);
      return;
    }

    try {
      const response = await fetch(`/api/search/suggestions?q=${encodeURIComponent(term)}&limit=8`);
      if (response.ok) {
        const data = await response.json();
        if (data.suggestions) {
          setSuggestions(data.suggestions);
          setShowSuggestions(data.suggestions.length > 0);
        }
      }
    } catch (error) {
      console.error('获取搜索建议失败:', error);
    }
  };

  // 记录搜索词
  const recordSearch = async (term: string) => {
    if (!term.trim()) return;
    
    try {
      const userId = localStorage.getItem('user_id');
      if (userId) {
        await fetch('/api/search/record', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            userId,
            searchTerm: term,
            locale
          }),
        });
      }
    } catch (error) {
      console.error('记录搜索词失败:', error);
    }
  };

  // 处理搜索输入变化
  const handleSearchInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchTerm(value);
    
    // 防抖获取建议
    if (suggestionDebounceRef.current) {
      clearTimeout(suggestionDebounceRef.current);
    }
    
    suggestionDebounceRef.current = setTimeout(() => {
      if (value.trim() && isInputFocused) {
        fetchSuggestions(value);
      }
    }, 300);
  };

  // 处理建议点击
  const handleSuggestionClick = (suggestion: SearchSuggestion) => {
    setSearchTerm(suggestion.text);
    setQuery(suggestion.text);
    setShowSuggestions(false);

    // 记录搜索
    recordSearch(suggestion.text);

    // 重置筛选器和页码
    setCategory('all');
    setDuration('all');
    setTime('all');
    setQuality('all');
    setSort('relevance');
    setPage(1);

    // 跳转到搜索结果页面
    router.push(`/${locale}/search?q=${encodeURIComponent(suggestion.text)}`);
  };

  // 处理搜索提交
  const handleSearchSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!searchTerm.trim()) return;
    
    setQuery(searchTerm);
    setShowSuggestions(false);
    
    // 记录搜索
    recordSearch(searchTerm);
    
    // 重置筛选器和页码
    setCategory('all');
    setDuration('all');
    setTime('all');
    setQuality('all');
    setSort('relevance');
    setPage(1);
    
    // 跳转到搜索结果页面
    router.push(`/${locale}/search?q=${encodeURIComponent(searchTerm)}`);
  };

  // 点击外部关闭建议
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        suggestionsRef.current && 
        !suggestionsRef.current.contains(event.target as Node) &&
        searchInputRef.current &&
        !searchInputRef.current.contains(event.target as Node)
      ) {
        setShowSuggestions(false);
        setIsInputFocused(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // 获取搜索结果
  useEffect(() => {
    const fetchResults = async () => {
      if (!query) {
        setResults([]);
        setLoading(false);
        return;
      }
      
      setLoading(true);
      setError(null);
      
      try {
        // 构建API URL
        const apiUrl = new URL('/api/search', window.location.origin);
        if (query) apiUrl.searchParams.append('q', query);
        if (category !== 'all') apiUrl.searchParams.append('category', category);
        if (duration !== 'all') apiUrl.searchParams.append('duration', duration);
        if (time !== 'all') apiUrl.searchParams.append('time', time);
        if (quality !== 'all') apiUrl.searchParams.append('quality', quality);
        if (sort !== 'relevance') apiUrl.searchParams.append('sort', sort);
        apiUrl.searchParams.append('page', page.toString());
        apiUrl.searchParams.append('locale', locale);
        
        // 添加用户ID用于记录搜索
        const userId = localStorage.getItem('user_id');
        if (userId) {
          apiUrl.searchParams.append('userId', userId);
        }
        
        const response = await fetch(apiUrl.toString());
        
        if (response.ok) {
          const data = await response.json();
          
          // 适配新的API响应格式
          if (data.results) {
            // 转换数据格式以匹配Video接口
            const transformedResults = data.results.map((item: any) => ({
              id: item.id,
              title: item.title,
              thumbnail: item.image || item.thumbnail,
              image: item.image || item.thumbnail,
              duration: formatDuration(item.duration),
              views: item.viewCount || item.views || 0,
              viewCount: item.viewCount || item.views || 0,
              timeAgo: item.releaseDate ? getTimeAgo(item.releaseDate, locale) : '未知时间',
              slug: item.id,
              isHD: item.isHD,
              rating: item.rating,
              studio: item.studio,
              category: item.category,
              tags: item.tags,
              code: item.id,
              movie_id: item.id,
              cover_image: item.image || item.thumbnail,
              image_url: item.image || item.thumbnail,
              cached_image_url: item.image || item.thumbnail,
              thumbnail_url: item.image || item.thumbnail,
              release_date: item.releaseDate,
              description: item.description,
              hasSubtitle: item.hasSubtitle,
              stars: item.stars
            }));
            
            setResults(transformedResults);
            setTotalResults(data.total || transformedResults.length);
            setTotalPages(Math.ceil((data.total || transformedResults.length) / 20));
            
            // 获取融合统计数据
            await fetchFusedStats(transformedResults);
          } else {
            setResults([]);
          }
        } else {
          setError(`搜索请求失败: ${response.statusText}`);
          setResults([]);
        }
      } catch (error) {
        setError('网络连接错误，请检查网络后重试');
        setResults([]);
      } finally {
        setLoading(false);
      }
    };
    
    fetchResults();
  }, [query, category, duration, time, quality, sort, page, locale]);
  
  // 设置Socket.IO实时统计监听
  useEffect(() => {
    if (results.length === 0) return;

    // 为每个视频设置统计更新监听
    const unsubscribeFunctions: (() => void)[] = [];

    results.forEach(video => {
      const videoId = video.id.toString();
      
      const handleStatsUpdate = (updatedStats: VideoStats) => {
        // 更新融合统计数据
        setFusedStats(prevStats => {
          const existingFused = prevStats[videoId];
          if (existingFused) {
            return {
              ...prevStats,
              [videoId]: {
                ...existingFused,
                views: updatedStats.views,
                likes: updatedStats.likes,
                favorites: updatedStats.favorites,
                fusionTimestamp: Date.now()
              }
            };
          }
          // 如果没有融合数据，创建新的
          return {
            ...prevStats,
            [videoId]: {
              videoId,
              numericVideoId: parseInt(videoId),
              views: updatedStats.views,
              likes: updatedStats.likes,
              favorites: updatedStats.favorites,
              dbBase: { views: 0, likes: 0, favorites: 0 },
              redisIncrement: updatedStats,
              lastUpdated: Date.now(),
              fusionTimestamp: Date.now()
            }
          };
        });
      };

      // 订阅统计更新
      socketClient.current.subscribeToStats(videoId, handleStatsUpdate);
      
      // 记录取消订阅函数
      unsubscribeFunctions.push(() => {
        socketClient.current.unsubscribeFromStats(videoId);
      });
    });

    // 清理函数：取消所有订阅
    return () => {
      unsubscribeFunctions.forEach(unsubscribe => unsubscribe());
    };
  }, [results]);
  
  // 动画效果
  useEffect(() => {
    if (!loading && results.length > 0) {
      resetVideoRefs(results.length);
      
      // 延迟执行动画，确保DOM已渲染
      setTimeout(() => {
        const elements = videoRefs.current.filter(Boolean) as HTMLElement[];
        if (elements.length > 0) {
          animateVideoCards(elements);
        }
      }, 50);
    }
  }, [loading, results, animateVideoCards, resetVideoRefs]);

  // 页面入场动画
  useEffect(() => {
    if (!loading) {
      animatePageEntrance();
    }
  }, [loading, animatePageEntrance]);
  
  // 设置引用
  const setRef = (el: HTMLDivElement | null, index: number) => {
    videoRefs.current[index] = el;
  };
  
  // 如果正在搜索中且没有结果，显示加载中
  if (loading) {
    return (
      <PageLoadingOverlay 
        text={t('search.loading')}
        showBackgroundDecorations={true}
      />
    );
  }

  // 如果有错误，显示错误状态
  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-800 text-white pt-20 pb-12">
        <div className="container mx-auto px-4">
          <ErrorState 
            error={error}
            onRetry={() => {
              setError(null);
              // 重新触发搜索
              const fetchResults = async () => {
                if (!query) return;
                setLoading(true);
                // 这里可以重复搜索逻辑，或者简单地刷新页面
                window.location.reload();
              };
              fetchResults();
            }}
            title="搜索失败"
            emoji="🔍"
          />
        </div>
      </div>
    );
  }

  // 如果没有搜索结果
  if (query && !loading && results.length === 0) {
    return (
      <div className="min-h-screen bg-gray-900 text-white">
        <div className="container mx-auto px-4 py-10">
          <SectionHeader 
            title={`${t('search.searchResults')}: ${query}`}
            className="mb-8"
          />
          <div className="text-center py-20">
            <FiSearch size={50} className="mx-auto text-gray-600 mb-4" />
            <h3 className="text-xl font-medium mb-2">{t('search.noResults')}</h3>
            <p className="text-gray-400 mb-6">
              {t('search.noResultsDesc')}
            </p>
            <Link 
              href={`/${locale}/category/all`}
              className="px-5 py-2.5 bg-red-600 hover:bg-red-700 rounded-md text-white font-medium transition-all"
            >
              {t('search.browseCategories')}
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-800 text-white pb-20">
      {/* Background decorations */}
      <div className="absolute inset-0 pointer-events-none">
        <div className="absolute top-1/4 left-1/4 w-64 h-64 bg-red-500/10 rounded-full filter blur-3xl"></div>
        <div className="absolute bottom-1/3 right-1/3 w-96 h-96 bg-blue-500/8 rounded-full filter blur-3xl"></div>
        <div className="absolute top-1/2 left-1/2 w-48 h-48 bg-purple-500/12 rounded-full filter blur-2xl"></div>
      </div>

      <div className="relative container mx-auto px-4 py-8">
        {/* 搜索表单 */}
        <div className="relative max-w-3xl mx-auto mb-8">
          <form onSubmit={handleSearchSubmit} className="relative">
            <div className="relative">
              <input
                ref={searchInputRef}
                type="text"
                value={searchTerm}
                onChange={handleSearchInputChange}
                onFocus={() => {
                  setIsInputFocused(true);
                  if (searchTerm.trim()) {
                    fetchSuggestions(searchTerm);
                  }
                }}
                placeholder={t('search.placeholder')}
                className="w-full bg-gray-800 border border-gray-700 rounded-full pl-12 pr-4 py-3 outline-none focus:border-pink-500 transition-all"
              />
              <FiSearch className="absolute left-4 top-1/2 -translate-y-1/2 text-gray-400 text-xl" />
              {searchTerm && (
                <button
                  type="button"
                  onClick={() => setSearchTerm('')}
                  className="absolute right-16 top-1/2 -translate-y-1/2 text-gray-400 hover:text-white"
                >
                  <FiX />
                </button>
              )}
              <button
                type="submit"
                className="absolute right-3 top-1/2 -translate-y-1/2 bg-pink-600 hover:bg-pink-700 text-white rounded-full p-2 transition-colors"
              >
                <FiSearch className="text-lg" />
              </button>
            </div>
            
            {/* 搜索建议 */}
            {showSuggestions && suggestions.length > 0 && (
              <div
                ref={suggestionsRef}
                className="absolute z-50 top-full left-0 right-0 mt-1 rounded-xl overflow-hidden bg-gray-800 border border-gray-700 shadow-xl backdrop-blur-sm"
              >
                <ul className="py-2 max-h-96 overflow-y-auto">
                  {suggestions.map((suggestion, index) => (
                    <li key={suggestion.id || index}>
                      <button
                        type="button"
                        onClick={() => handleSuggestionClick(suggestion)}
                        className="w-full text-left px-4 py-3 hover:bg-gray-700 flex items-center space-x-3 transition-colors duration-200"
                      >
                        {/* 图片 */}
                        <div className="flex-shrink-0 w-12 h-12 rounded-lg overflow-hidden bg-gray-700">
                          {suggestion.image ? (
                            <img
                              src={suggestion.image}
                              alt={suggestion.text}
                              className="w-full h-full object-cover"
                              onError={(e) => {
                                const target = e.target as HTMLImageElement;
                                target.style.display = 'none';
                                target.nextElementSibling?.classList.remove('hidden');
                              }}
                            />
                          ) : null}
                          <div className={`w-full h-full flex items-center justify-center ${suggestion.image ? 'hidden' : ''}`}>
                            {suggestion.type === 'movie' ? (
                              <FiSearch className="text-gray-400 text-lg" />
                            ) : (
                              <div className="w-8 h-8 bg-gray-600 rounded-full flex items-center justify-center">
                                <span className="text-xs text-gray-300 font-medium">
                                  {suggestion.text.charAt(0).toUpperCase()}
                                </span>
                              </div>
                            )}
                          </div>
                        </div>

                        {/* 文本内容 */}
                        <div className="flex-1 min-w-0">
                          <div className="text-white font-medium truncate">
                            {suggestion.text}
                          </div>
                          {suggestion.meta && (
                            <div className="text-xs text-gray-400 truncate mt-1">
                              {suggestion.meta}
                            </div>
                          )}
                        </div>

                        {/* 类型标识 */}
                        <div className="flex-shrink-0">
                          <span className={`px-2 py-1 text-xs rounded-full ${
                            suggestion.type === 'movie'
                              ? 'bg-red-600/20 text-red-400'
                              : 'bg-blue-600/20 text-blue-400'
                          }`}>
                            {suggestion.type === 'movie' ? t('search.movie') : t('search.actress')}
                          </span>
                        </div>
                      </button>
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </form>
        </div>
        
        {/* 搜索结果 */}
        <div className="relative">
          {/* 筛选条件和排序 - 桌面版 */}
          <div className="md:flex mb-6 items-center justify-between">
            <div className="flex items-center space-x-4 mb-4 md:mb-0">
              <div className="flex-shrink-0">
                <h1
                  ref={titleRef}
                  className="text-3xl md:text-4xl font-bold mb-2 bg-gradient-to-r from-red-400 to-pink-500 bg-clip-text text-transparent"
                >
                  {query ? t('search.searchResults', { query }) : t('search.searchResults')}
                </h1>
                {query && (
                  <p
                    ref={subtitleRef}
                    className="text-sm text-gray-400"
                  >
                    {t('search.foundResults', { count: totalResults })}
                  </p>
                )}
              </div>
            </div>
            
            <div className="flex items-center justify-between md:justify-end w-full md:w-auto space-x-4">
              <button
                onClick={() => setShowFilters(!showFilters)}
                className="md:hidden flex items-center space-x-2 px-4 py-2 bg-gray-800 rounded-lg hover:bg-gray-700 transition-colors"
              >
                <FiFilter />
                <span>{t('search.filters')}</span>
              </button>
              
              {/* 排序选择 */}
              <div className="flex items-center space-x-2">
                <span className="text-sm text-gray-400 hidden md:inline">{t('search.sortBy')}:</span>
                <select
                  value={sort}
                  onChange={(e) => applyFilter('sort', e.target.value)}
                  className="bg-gray-800 border border-gray-700 rounded-lg px-3 py-2 text-sm outline-none focus:border-pink-500"
                >
                  <option value="relevance">{t('search.relevance')}</option>
                  <option value="views">{t('search.views')}</option>
                  <option value="recent">{t('search.recent')}</option>
                  <option value="oldest">{t('search.oldest')}</option>
                </select>
              </div>
            </div>
          </div>
          
          <div className="flex gap-8">
            {/* 侧边栏筛选器 - 桌面版 */}
            <div className={`hidden md:block w-64 flex-shrink-0 space-y-6 ${showFilters ? 'block' : 'hidden'} md:block`}>
              <div className="bg-gray-800 rounded-xl p-6 sticky top-4">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold">{t('search.filters')}</h3>
                  <button
                    onClick={clearAllFilters}
                    className="text-sm text-pink-500 hover:text-pink-400"
                  >
                    {t('search.clearFilters')}
                  </button>
                </div>
                
                {/* 分类筛选 */}
                <div className="mb-6">
                  <button
                    onClick={() => toggleSection('category')}
                    className="flex items-center justify-between w-full mb-3 text-left font-medium"
                  >
                    {t('search.category')}
                    {expandedSections.category ? <FiChevronUp /> : <FiChevronDown />}
                  </button>
                  {expandedSections.category && (
                    <div className="space-y-2">
                      {categories.map(cat => (
                        <label key={cat.value} className="flex items-center">
                          <input
                            type="radio"
                            name="category"
                            value={cat.value}
                            checked={category === cat.value}
                            onChange={(e) => applyFilter('category', e.target.value)}
                            className="mr-2"
                          />
                          <span className="text-sm">{cat.label}</span>
                        </label>
                      ))}
                    </div>
                  )}
                </div>
                
                {/* 时长筛选 */}
                <div className="mb-6">
                  <button
                    onClick={() => toggleSection('duration')}
                    className="flex items-center justify-between w-full mb-3 text-left font-medium"
                  >
                    {t('search.duration')}
                    {expandedSections.duration ? <FiChevronUp /> : <FiChevronDown />}
                  </button>
                  {expandedSections.duration && (
                    <div className="space-y-2">
                      {durations.map(dur => (
                        <label key={dur.value} className="flex items-center">
                          <input
                            type="radio"
                            name="duration"
                            value={dur.value}
                            checked={duration === dur.value}
                            onChange={(e) => applyFilter('duration', e.target.value)}
                            className="mr-2"
                          />
                          <span className="text-sm">{dur.label}</span>
                        </label>
                      ))}
                    </div>
                  )}
                </div>
                
                {/* 时间筛选 */}
                <div className="mb-6">
                  <button
                    onClick={() => toggleSection('time')}
                    className="flex items-center justify-between w-full mb-3 text-left font-medium"
                  >
                    {t('search.time')}
                    {expandedSections.time ? <FiChevronUp /> : <FiChevronDown />}
                  </button>
                  {expandedSections.time && (
                    <div className="space-y-2">
                      {uploadTimes.map(timeOption => (
                        <label key={timeOption.value} className="flex items-center">
                          <input
                            type="radio"
                            name="time"
                            value={timeOption.value}
                            checked={time === timeOption.value}
                            onChange={(e) => applyFilter('time', e.target.value)}
                            className="mr-2"
                          />
                          <span className="text-sm">{timeOption.label}</span>
                        </label>
                      ))}
                    </div>
                  )}
                </div>
                
                {/* 清晰度筛选 */}
                <div>
                  <button
                    onClick={() => toggleSection('quality')}
                    className="flex items-center justify-between w-full mb-3 text-left font-medium"
                  >
                    {t('search.quality')}
                    {expandedSections.quality ? <FiChevronUp /> : <FiChevronDown />}
                  </button>
                  {expandedSections.quality && (
                    <div className="space-y-2">
                      {qualities.map(qual => (
                        <label key={qual.value} className="flex items-center">
                          <input
                            type="radio"
                            name="quality"
                            value={qual.value}
                            checked={quality === qual.value}
                            onChange={(e) => applyFilter('quality', e.target.value)}
                            className="mr-2"
                          />
                          <span className="text-sm">{qual.label}</span>
                        </label>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            </div>
            
            {/* 主要内容区域 */}
            <div className="flex-1" ref={contentRef}>
              {/* 移动版筛选器 */}
              {showFilters && (
                <div className="md:hidden mb-6 bg-gray-800 rounded-xl p-4">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-semibold">{t('search.filters')}</h3>
                    <button
                      onClick={() => setShowFilters(false)}
                      className="text-gray-400 hover:text-white"
                    >
                      <FiX />
                    </button>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4">
                    {/* 分类 */}
                    <div>
                      <label className="block text-sm font-medium mb-2">{t('search.category')}</label>
                      <select
                        value={category}
                        onChange={(e) => applyFilter('category', e.target.value)}
                        className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-sm"
                      >
                        {categories.map(cat => (
                          <option key={cat.value} value={cat.value}>{cat.label}</option>
                        ))}
                      </select>
                    </div>
                    
                    {/* 时长 */}
                    <div>
                      <label className="block text-sm font-medium mb-2">{t('search.duration')}</label>
                      <select
                        value={duration}
                        onChange={(e) => applyFilter('duration', e.target.value)}
                        className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-sm"
                      >
                        {durations.map(dur => (
                          <option key={dur.value} value={dur.value}>{dur.label}</option>
                        ))}
                      </select>
                    </div>
                    
                    {/* 时间 */}
                    <div>
                      <label className="block text-sm font-medium mb-2">{t('search.time')}</label>
                      <select
                        value={time}
                        onChange={(e) => applyFilter('time', e.target.value)}
                        className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-sm"
                      >
                        {uploadTimes.map(timeOption => (
                          <option key={timeOption.value} value={timeOption.value}>{timeOption.label}</option>
                        ))}
                      </select>
                    </div>
                    
                    {/* 清晰度 */}
                    <div>
                      <label className="block text-sm font-medium mb-2">{t('search.quality')}</label>
                      <select
                        value={quality}
                        onChange={(e) => applyFilter('quality', e.target.value)}
                        className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-sm"
                      >
                        {qualities.map(qual => (
                          <option key={qual.value} value={qual.value}>{qual.label}</option>
                        ))}
                      </select>
                    </div>
                  </div>
                  
                  <button
                    onClick={clearAllFilters}
                    className="w-full mt-4 px-4 py-2 bg-pink-600 hover:bg-pink-700 rounded-lg text-white transition-colors"
                  >
                    {t('search.clearFilters')}
                  </button>
                </div>
              )}
              
              {/* 视频网格 - 使用VideoCard组件 */}
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-6 mb-8">
                {results.map((video, index) => (
                  <div
                    key={video.id}
                    ref={(el) => {
                      videoRefs.current[index] = el;
                      setVideoRef(el, index);
                    }}
                    className="transform-gpu"
                  >
                    <VideoCard
                      video={video}
                      locale={locale}
                      size="medium"
                      fusedStats={fusedStats[video.id.toString()]}
                    />
                  </div>
                ))}
              </div>
              
              {/* 分页 */}
              {totalPages > 1 && (
                <div className="flex justify-center space-x-2">
                  {/* 上一页 */}
                  {page > 1 && (
                    <button
                      onClick={() => {
                        setPage(page - 1);
                        const queryString = buildQueryParams({ page: (page - 1).toString() });
                        router.push(`/${locale}/search?${queryString}`);
                      }}
                      className="px-4 py-2 bg-gray-800 hover:bg-gray-700 rounded-lg transition-colors"
                    >
                      上一页
                    </button>
                  )}
                  
                  {/* 页码 */}
                  {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                    let pageNum;
                    if (totalPages <= 5) {
                      pageNum = i + 1;
                    } else if (page <= 3) {
                      pageNum = i + 1;
                    } else if (page >= totalPages - 2) {
                      pageNum = totalPages - 4 + i;
                    } else {
                      pageNum = page - 2 + i;
                    }
                    
                    return (
                      <button
                        key={pageNum}
                        onClick={() => {
                          setPage(pageNum);
                          const queryString = buildQueryParams({ page: pageNum.toString() });
                          router.push(`/${locale}/search?${queryString}`);
                        }}
                        className={`px-4 py-2 rounded-lg transition-colors ${
                          page === pageNum
                            ? 'bg-pink-600 text-white'
                            : 'bg-gray-800 hover:bg-gray-700'
                        }`}
                      >
                        {pageNum}
                      </button>
                    );
                  })}
                  
                  {/* 下一页 */}
                  {page < totalPages && (
                    <button
                      onClick={() => {
                        setPage(page + 1);
                        const queryString = buildQueryParams({ page: (page + 1).toString() });
                        router.push(`/${locale}/search?${queryString}`);
                      }}
                      className="px-4 py-2 bg-gray-800 hover:bg-gray-700 rounded-lg transition-colors"
                    >
                      下一页
                    </button>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
} 