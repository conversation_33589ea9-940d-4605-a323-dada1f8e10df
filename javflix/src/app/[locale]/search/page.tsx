import { Suspense } from 'react';
import { Metadata } from 'next';
import { SearchClient } from './client';
import { notFound } from 'next/navigation';
import { PageLoadingOverlay } from '@/components/loading';
import { isValidLocale } from '@/i18n';

// Route Segment Config for Server-Side Rendering (SSR)
export const dynamic = 'force-dynamic'; // Enable SSR - render on each request
 // Ensure fresh data on each request
export const runtime = 'nodejs'; // Use Node.js runtime for better performance
export const preferredRegion = 'auto'; // Auto-select optimal region
export const revalidate = false; // Disable revalidation for true SSR

interface SearchPageProps {
  params: Promise<{ locale: string }>;
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>;
}

export async function generateMetadata({ params }: SearchPageProps): Promise<Metadata> {
  const { locale } = await params;

  const titles = {
    'zh-Hans': '搜索 - JAVFLIX',
    'zh': '搜索 - JAVFLIX',
    'en': 'Search - JAVFLIX',
    'ja': '検索 - JAVFLIX'
  };

  const descriptions = {
    'zh-Hans': '搜索成千上万的高质量影片',
    'zh': '搜索成千上万的高质量影片',
    'en': 'Search thousands of high-quality videos',
    'ja': '数千の高品質動画を検索'
  };

  return {
    title: titles[locale as keyof typeof titles] || titles['zh'],
    description: descriptions[locale as keyof typeof descriptions] || descriptions['zh'],
  };
}

// 服务器端搜索页面
export default async function SearchPage({ params, searchParams }: SearchPageProps) {
  const { locale } = await params;

  // 验证locale - 使用统一的locale验证函数
  if (!isValidLocale(locale)) {
    notFound();
  }
  
  // 在Next.js 15中，正确处理searchParams
  const urlParams = await searchParams;
  const query = typeof urlParams.q === 'string' ? urlParams.q : '';
  const category = typeof urlParams.category === 'string' ? urlParams.category : 'all';
  const duration = typeof urlParams.duration === 'string' ? urlParams.duration : 'all';
  const time = typeof urlParams.time === 'string' ? urlParams.time : 'all';
  const quality = typeof urlParams.quality === 'string' ? urlParams.quality : 'all';
  const sort = typeof urlParams.sort === 'string' ? urlParams.sort : 'relevance';
  const page = typeof urlParams.page === 'string' ? parseInt(urlParams.page) : 1;

  return (
    <Suspense fallback={
      <PageLoadingOverlay 
        text="正在加载搜索页面..."
        showBackgroundDecorations={true}
      />
    }>
      <SearchClient 
        locale={locale}
        initialQuery={query}
        initialCategory={category}
        initialDuration={duration}
        initialTime={time}
        initialQuality={quality}
        initialSort={sort}
        initialPage={page}
      />
    </Suspense>
  );
} 