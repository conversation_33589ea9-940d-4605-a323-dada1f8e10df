'use client';

import { useRouter, useParams } from 'next/navigation';
import { useEffect } from 'react';

export default function SignupRedirect() {
  const router = useRouter();
  const params = useParams();
  const locale = params?.locale?.toString() || 'zh-CN';
  
  useEffect(() => {
    // 重定向到同一语言的register页面
    router.push(`/${locale}/auth/register`);
  }, [router, locale]);
  
  // 返回一个加载状态
  return (
    <div className="min-h-screen pt-20 pb-16 flex items-center justify-center">
      <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-red-500"></div>
    </div>
  );
} 