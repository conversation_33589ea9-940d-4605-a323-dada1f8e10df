'use client';

import { useState, useEffect, useRef } from 'react';
import Link from 'next/link';
import { useRouter, useParams } from 'next/navigation';
import { FiUser, FiMail, FiLock, FiEye, FiEyeOff, FiGithub, FiTwitter, FiCheckCircle } from 'react-icons/fi';
import { FcGoogle } from 'react-icons/fc';
import gsap from 'gsap';
import { useClientTranslations } from '@/components/TranslationsProvider';
import { useAuth } from '@/context/AuthContext';

export default function RegisterPage() {
  const params = useParams();
  const locale = params?.locale?.toString() || 'zh-CN';
  const { t } = useClientTranslations();
  const { register, isAuthenticated, user } = useAuth();
  
  const [username, setUsername] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [acceptTerms, setAcceptTerms] = useState(false);
  const [apiError, setApiError] = useState('');
  const [errors, setErrors] = useState({
    username: '',
    email: '',
    password: '',
    confirmPassword: '',
    terms: ''
  });
  const [registerSuccess, setRegisterSuccess] = useState(false);
  
  const router = useRouter();
  const formRef = useRef<HTMLFormElement>(null);
  const titleRef = useRef<HTMLHeadingElement>(null);
  const subtitleRef = useRef<HTMLParagraphElement>(null);
  const successOverlayRef = useRef<HTMLDivElement>(null);
  
  useEffect(() => {
    // 页面加载动画
    const tl = gsap.timeline({ defaults: { ease: 'power3.out' } });
    
    tl.fromTo(
      titleRef.current,
      { y: -20, opacity: 0 },
      { y: 0, opacity: 1, duration: 0.6 }
    );
    
    tl.fromTo(
      subtitleRef.current,
      { y: -10, opacity: 0 },
      { y: 0, opacity: 1, duration: 0.6 },
      '-=0.3'
    );
    
    tl.fromTo(
      formRef.current,
      { y: 20, opacity: 0 },
      { y: 0, opacity: 1, duration: 0.8 },
      '-=0.3'
    );
  }, []);

  // 监听认证状态变化，如果用户已登录则自动跳转
  useEffect(() => {
    if (isAuthenticated && user && registerSuccess) {
      console.log('注册成功且用户已认证，准备跳转到首页');
      // 延迟跳转，让用户看到成功动画
      setTimeout(() => {
        router.push(`/${locale}`);
      }, 2000);
    }
  }, [isAuthenticated, user, registerSuccess, router, locale]);
  
  const validateForm = () => {
    let valid = true;
    const newErrors = {
      username: '',
      email: '',
      password: '',
      confirmPassword: '',
      terms: ''
    };
    
    // 验证用户名
    if (username.trim().length < 3) {
      newErrors.username = t('auth.register.usernameError');
      valid = false;
    }
    
    // 验证邮箱
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      newErrors.email = t('auth.register.emailError');
      valid = false;
    }
    
    // 验证密码
    if (password.length < 8) {
      newErrors.password = t('auth.register.passwordError');
      valid = false;
    }
    
    // 验证确认密码
    if (password !== confirmPassword) {
      newErrors.confirmPassword = t('auth.register.confirmPasswordError');
      valid = false;
    }
    
    // 验证服务条款
    if (!acceptTerms) {
      newErrors.terms = t('auth.register.termsError');
      valid = false;
    }
    
    setErrors(newErrors);
    return valid;
  };
  
  // 注册成功动画
  const playSuccessAnimation = () => {
    if (!successOverlayRef.current) return;
    
    // 创建动画时间线
    const tl = gsap.timeline({
      onComplete: () => {
        // 动画完成后导航到首页,使用当前语言
        router.push(`/${locale}`);
      }
    });
    
    // 显示成功覆盖层
    tl.set(successOverlayRef.current, { display: 'flex' });
    
    // 背景动画
    tl.fromTo(
      successOverlayRef.current,
      { opacity: 0 },
      { opacity: 1, duration: 0.3 }
    );
    
    // 成功图标动画
    tl.fromTo(
      '.success-icon',
      {
        scale: 0.5,
        opacity: 0,
        rotation: -30
      },
      {
        scale: 1,
        opacity: 1,
        rotation: 0,
        duration: 0.6,
        ease: 'elastic.out(1, 0.5)'
      }
    );
    
    // 成功消息动画
    tl.fromTo(
      '.success-message',
      { y: 20, opacity: 0 },
      { y: 0, opacity: 1, duration: 0.4 },
      '-=0.3'
    );
    
    // 停留短暂时间后淡出
    tl.to(
      successOverlayRef.current,
      { opacity: 0, duration: 0.5, delay: 1 }
    );
  };
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }
    
    setIsLoading(true);
    setApiError('');
    
    try {
      // 使用AuthContext中的register方法代替直接API调用
      const result = await register(username, email, password);
      
      if (!result.success) {
        setApiError(result.error || t('auth.register.registerError'));
        setIsLoading(false);
        return;
      }
      
      // 设置注册成功状态
      setRegisterSuccess(true);
      
      console.log('注册成功，用户数据已通过AuthContext设置');
      
      // 播放成功动画，动画完成后会自动跳转
      playSuccessAnimation();
    } catch (error) {
      console.error('注册错误:', error);
      setApiError(t('auth.register.registerError'));
      setIsLoading(false);
    }
  };
  
  return (
    <div className="min-h-screen bg-gradient-to-b from-gray-900 to-black py-16 px-4 sm:px-6 flex items-center justify-center">
      {/* 注册成功覆盖层 */}
      <div 
        ref={successOverlayRef}
        className="fixed inset-0 bg-black/80 z-50 flex-col items-center justify-center"
        style={{ display: 'none' }}
      >
        <div className="success-icon text-green-500 mb-6">
          <FiCheckCircle size={80} />
        </div>
        <h2 className="success-message text-2xl font-bold text-white mb-2">
          {t('auth.register.registerSuccess')}
        </h2>
        <p className="success-message text-gray-300">
          {t('auth.register.welcome')}{username}！
        </p>
      </div>
      
      <div className="max-w-md w-full space-y-8 bg-gray-800 p-8 rounded-xl shadow-2xl">
        <div className="text-center">
          <h2 ref={titleRef} className="text-3xl font-bold text-white">
            {t('auth.register.title')}
          </h2>
          <p ref={subtitleRef} className="mt-2 text-sm text-gray-400">
            {t('auth.register.subtitle')}
          </p>
        </div>
        
        {apiError && (
          <div className="bg-red-500/20 border border-red-500 text-red-100 px-4 py-3 rounded relative" role="alert">
            <span className="block sm:inline">{apiError}</span>
          </div>
        )}
        
        <form ref={formRef} className="mt-8 space-y-6" onSubmit={handleSubmit}>
          <div className="space-y-4">
            {/* 用户名输入 */}
            <div>
              <label htmlFor="username" className="block text-sm font-medium text-gray-300 mb-1">
                {t('auth.register.username')}
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <FiUser className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  id="username"
                  name="username"
                  type="text"
                  autoComplete="username"
                  required
                  className={`block w-full pl-10 pr-3 py-2 border ${
                    errors.username ? 'border-red-500' : 'border-gray-600'
                  } rounded-md bg-gray-700 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-pink-500 focus:border-transparent`}
                  placeholder={t('auth.register.username')}
                  value={username}
                  onChange={(e) => setUsername(e.target.value)}
                />
              </div>
              {errors.username && (
                <p className="mt-1 text-sm text-red-500">{errors.username}</p>
              )}
            </div>
            
            {/* 邮箱输入 */}
            <div>
              <label htmlFor="email" className="block text-sm font-medium text-gray-300 mb-1">
                {t('auth.register.email')}
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <FiMail className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  id="email"
                  name="email"
                  type="email"
                  autoComplete="email"
                  required
                  className={`block w-full pl-10 pr-3 py-2 border ${
                    errors.email ? 'border-red-500' : 'border-gray-600'
                  } rounded-md bg-gray-700 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-pink-500 focus:border-transparent`}
                  placeholder={t('auth.register.email')}
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                />
              </div>
              {errors.email && (
                <p className="mt-1 text-sm text-red-500">{errors.email}</p>
              )}
            </div>
            
            {/* 密码输入 */}
            <div>
              <label htmlFor="password" className="block text-sm font-medium text-gray-300 mb-1">
                {t('auth.register.password')}
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <FiLock className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  id="password"
                  name="password"
                  type={showPassword ? "text" : "password"}
                  autoComplete="new-password"
                  required
                  className={`block w-full pl-10 pr-10 py-2 border ${
                    errors.password ? 'border-red-500' : 'border-gray-600'
                  } rounded-md bg-gray-700 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-pink-500 focus:border-transparent`}
                  placeholder={t('auth.register.password')}
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                />
                <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="text-gray-400 hover:text-white focus:outline-none"
                  >
                    {showPassword ? <FiEyeOff className="h-5 w-5" /> : <FiEye className="h-5 w-5" />}
                  </button>
                </div>
              </div>
              {errors.password && (
                <p className="mt-1 text-sm text-red-500">{errors.password}</p>
              )}
            </div>
            
            {/* 确认密码输入 */}
            <div>
              <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-300 mb-1">
                {t('auth.register.confirmPassword')}
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <FiLock className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  id="confirmPassword"
                  name="confirmPassword"
                  type={showConfirmPassword ? "text" : "password"}
                  autoComplete="new-password"
                  required
                  className={`block w-full pl-10 pr-10 py-2 border ${
                    errors.confirmPassword ? 'border-red-500' : 'border-gray-600'
                  } rounded-md bg-gray-700 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-pink-500 focus:border-transparent`}
                  placeholder={t('auth.register.confirmPassword')}
                  value={confirmPassword}
                  onChange={(e) => setConfirmPassword(e.target.value)}
                />
                <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
                  <button
                    type="button"
                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                    className="text-gray-400 hover:text-white focus:outline-none"
                  >
                    {showConfirmPassword ? <FiEyeOff className="h-5 w-5" /> : <FiEye className="h-5 w-5" />}
                  </button>
                </div>
              </div>
              {errors.confirmPassword && (
                <p className="mt-1 text-sm text-red-500">{errors.confirmPassword}</p>
              )}
            </div>
          </div>
          
          {/* 服务条款 */}
          <div className="flex items-start">
            <div className="flex items-center h-5">
              <input
                id="terms"
                name="terms"
                type="checkbox"
                className="h-4 w-4 text-pink-600 focus:ring-pink-500 border-gray-300 rounded"
                checked={acceptTerms}
                onChange={(e) => setAcceptTerms(e.target.checked)}
              />
            </div>
            <div className="ml-3 text-sm">
              <label htmlFor="terms" className="font-medium text-gray-300">
                {t('auth.register.agreeToTerms')} 
                <Link href={`/${locale}/terms`} className="text-pink-500 hover:text-pink-400 mx-1">
                  {t('auth.register.termsOfService')}
                </Link>
                {t('auth.register.and')}
                <Link href={`/${locale}/privacy`} className="text-pink-500 hover:text-pink-400 ml-1">
                  {t('auth.register.privacyPolicy')}
                </Link>
              </label>
              {errors.terms && (
                <p className="mt-1 text-sm text-red-500">{errors.terms}</p>
              )}
            </div>
          </div>
          
          <div>
            <button
              type="submit"
              disabled={isLoading}
              className="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-gradient-to-r from-pink-600 to-red-600 hover:from-pink-500 hover:to-red-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-pink-500 transition-colors duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading ? (
                <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
              ) : (
                t('auth.register.registerButton')
              )}
            </button>
          </div>
        </form>
        
        {/* 分隔线 */}
        <div className="mt-6 relative">
          <div className="absolute inset-0 flex items-center">
            <div className="w-full border-t border-gray-600"></div>
          </div>
          <div className="relative flex justify-center text-sm">
            <span className="px-2 bg-gray-800 text-gray-400">
              {t('auth.oauth.or')}
            </span>
          </div>
        </div>
        
        {/* 社交登录按钮 */}
        <div className="mt-6 grid grid-cols-3 gap-3">
          <div>
            <button
              type="button"
              className="w-full inline-flex justify-center py-2 px-4 border border-gray-600 rounded-md shadow-sm bg-gray-700 text-sm font-medium text-white hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-pink-500 transition-colors duration-300"
            >
              <span className="sr-only">{t('auth.oauth.continueWith')} Google</span>
              <FcGoogle className="h-5 w-5" />
            </button>
          </div>
          
          <div>
            <button
              type="button"
              className="w-full inline-flex justify-center py-2 px-4 border border-gray-600 rounded-md shadow-sm bg-gray-700 text-sm font-medium text-white hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-pink-500 transition-colors duration-300"
            >
              <span className="sr-only">{t('auth.oauth.continueWith')} GitHub</span>
              <FiGithub className="h-5 w-5" />
            </button>
          </div>
          
          <div>
            <button
              type="button"
              className="w-full inline-flex justify-center py-2 px-4 border border-gray-600 rounded-md shadow-sm bg-gray-700 text-sm font-medium text-white hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-pink-500 transition-colors duration-300"
            >
              <span className="sr-only">{t('auth.oauth.continueWith')} Twitter</span>
              <FiTwitter className="h-5 w-5" />
            </button>
          </div>
        </div>
        
        <div className="text-center mt-6">
          <p className="text-sm text-gray-400">
            {t('auth.register.haveAccount')}
            <Link href={`/${locale}/auth/login`} className="ml-1 font-medium text-pink-500 hover:text-pink-400 transition-colors duration-300">
              {t('auth.register.loginHere')}
            </Link>
          </p>
        </div>
      </div>
    </div>
  );
}
