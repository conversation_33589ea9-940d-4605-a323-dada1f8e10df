import { Suspense } from 'react';
import { Locale } from '@/i18n/types';
import { getTranslations, locales } from '@/i18n';
import { notFound } from 'next/navigation';
import { generateMetadata as baseGenerateMetadata } from '@/lib/seo';
import type { Metadata } from 'next';
import Link from 'next/link';

// 为语言路由生成静态路径参数
export async function generateStaticParams() {
  return locales.map(locale => ({ locale }));
}

// 生成该语言的元数据
export async function generateMetadata(
  { params }: { params: Promise<{ locale: string }> }
): Promise<Metadata> {
  // 获取locale参数
  const resolvedParams = await params;
  const locale = resolvedParams.locale;
  
  // 验证locale是否是有效的支持语言
  if (!locale || !locales.includes(locale as Locale)) {
    return baseGenerateMetadata({});
  }
  
  // 获取该语言的翻译
  const translations = await getTranslations(locale as Locale);
  
  // 使用翻译的站点标题和描述
  return baseGenerateMetadata({
    title: `测试页面 - ${translations.site.title}`,
    description: '这是一个测试页面',
  });
}

// 简单的客户端组件
function ActressTestClient({ locale }: { locale: Locale }) {
  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-2xl font-bold mb-6">测试页面 - 语言: {locale}</h1>
      <p className="mb-4">这是一个用于测试的页面</p>
      <Link href={`/${locale}`} className="text-blue-600 hover:underline">
        返回首页
      </Link>
    </div>
  );
}

// 主页面组件
export default async function ActressTestPage({ 
  params 
}: { 
  params: Promise<{ locale: string }> 
}) {
  // 获取locale参数
  const resolvedParams = await params;
  const locale = resolvedParams.locale as Locale;
  
  // 检验locale是否支持，否则显示404
  if (!locale || !locales.includes(locale)) {
    notFound();
  }

  return (
    <Suspense fallback={<div className="h-screen flex items-center justify-center">Loading...</div>}>
      <ActressTestClient locale={locale} />
    </Suspense>
  );
} 