import '@/app/globals.css';
import type { Metadata, Viewport } from "next";
import { Inter } from "next/font/google";
import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";
import { generateMetadata as baseGenerateMetadata } from "@/lib/seo";
import Script from "next/script";
import { TranslationsProvider } from '@/components/TranslationsProvider';
import { getTranslations, locales, defaultLocale } from '@/i18n';
import type { Locale } from '@/i18n/types';
import { notFound } from 'next/navigation';

const inter = Inter({ subsets: ["latin"] });

// 为语言路由生成静态路径参数
export async function generateStaticParams() {
  return locales.map(locale => ({ locale }));
}

// 视口配置
export const viewport: Viewport = {
  width: 'device-width',
  initialScale: 1,
  themeColor: '#000000',
  colorScheme: 'dark'
};

// 设置默认的元数据，这将被各个页面的元数据覆盖
export async function generateMetadata(
  { params }: { params: Promise<{ locale: string }> }
): Promise<Metadata> {
  // 安全地访问params.locale，避免使用直接访问
  const paramsData = await params;
  const locale = paramsData.locale;
  
  // 验证locale是否是有效的支持语言
  if (!locale || !locales.includes(locale as Locale)) {
    return baseGenerateMetadata({});
  }
  
  // 获取该语言的翻译
  const translations = await getTranslations(locale as Locale);
  
  // 使用翻译的站点标题和描述
  return baseGenerateMetadata({
    title: translations.site.title,
    description: translations.site.description
  });
}

export default async function LocaleLayout({
  children,
  params,
}: {
  children: React.ReactNode;
  params: Promise<{ locale: string }>;
}) {
  // 安全地访问params.locale，避免使用直接访问
  const paramsData = await params;
  const locale = paramsData.locale as Locale;
  
  // 验证locale是否支持，否则显示404
  if (!locale || !locales.includes(locale)) {
    notFound();
  }
  
  // 异步获取该语言的翻译
  const translations = await getTranslations(locale);
  
  return (
    <>
      {/* Google Analytics */}
      <Script
        strategy="afterInteractive"
        src={`https://www.googletagmanager.com/gtag/js?id=G-XXXXXXXXXX`}
      />
      <Script id="google-analytics" strategy="afterInteractive">
        {`
          window.dataLayer = window.dataLayer || [];
          function gtag(){dataLayer.push(arguments);}
          gtag('js', new Date());
          gtag('config', 'G-XXXXXXXXXX');
        `}
      </Script>
      
      <TranslationsProvider translations={translations}>
        <Navbar />
        <main className="flex-grow pt-16">
          {children}
        </main>
        <Footer />
      </TranslationsProvider>
    </>
  );
} 