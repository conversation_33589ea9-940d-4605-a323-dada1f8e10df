import type { Metadata } from 'next';
import { generateMetadata as createMetadata } from '@/lib/seo';
import { getTranslations, locales } from '@/i18n';
import type { Locale } from '@/i18n/types';
import { notFound } from 'next/navigation';
import FavoritesClient from './FavoritesClient';

// 为语言路由生成静态路径参数
export async function generateStaticParams() {
  return locales.map(locale => ({ locale }));
}

// 生成该语言的元数据
export async function generateMetadata(
  { params }: { params: Promise<{ locale: string }> }
): Promise<Metadata> {
  // 获取locale参数
  const resolvedParams = await params;
  const locale = resolvedParams.locale;
  
  // 验证locale是否是有效的支持语言
  if (!locale || !locales.includes(locale as Locale)) {
    return createMetadata({});
  }
  
  // 获取该语言的翻译
  const translations = await getTranslations(locale as Locale);
  
  // 使用翻译的站点标题和描述
  return createMetadata({
    title: `${translations.profile?.myFavorites || '我的收藏'} - ${translations.site.title}`,
    description: translations.pages.profile?.description || '管理您收藏的视频内容',
  });
}

// 主页面是服务器组件，只用于包装客户端组件
export default async function FavoritesPage({ 
  params 
}: { 
  params: Promise<{ locale: string }> 
}) {
  // 获取locale参数
  const resolvedParams = await params;
  const locale = resolvedParams.locale as Locale;
  
  // 验证locale是否支持，否则显示404
  if (!locale || !locales.includes(locale)) {
    notFound();
  }
  
  try {
    // 异步获取该语言的翻译
    await getTranslations(locale);
  } catch (error) {
    console.error('Failed to load translations in FavoritesPage:', error);
  }
  
  return <FavoritesClient />;
} 