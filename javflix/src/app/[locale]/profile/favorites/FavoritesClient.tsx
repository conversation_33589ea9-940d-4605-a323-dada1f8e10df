'use client';

import { useState, useEffect, useCallback } from 'react';
import { useParams } from 'next/navigation';
import Image from 'next/image';
import Link from 'next/link';
import { FiBookmark, FiArrowLeft } from 'react-icons/fi';
import { useClientTranslations } from '@/components/TranslationsProvider';
import { getUserFavorites, removeFromFavorites } from '@/lib/user-client';

// 视频数据接口
interface VideoData {
  id: number;
  title: string;
  slug: string;
  duration: string;
  imageUrl: string;
  views: number;
  releaseDate?: string;
  createdAt?: string;
  date?: string; // 仅用于前端展示
}

const FavoritesClient = () => {
  const { t } = useClientTranslations();
  const params = useParams();
  const locale = params?.locale?.toString() || 'zh-CN';
  
  const [favoriteVideos, setFavoriteVideos] = useState<VideoData[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalItems, setTotalItems] = useState(0);
  const itemsPerPage = 12;
  
  // 格式化相对时间
  const formatRelativeTime = useCallback((dateString?: string) => {
    if (!dateString) return t('common.unknown') || '未知';
    
    const date = new Date(dateString);
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);
    
    if (diffInSeconds < 60) {
      return t('profile.time.justNow') || '刚刚';
    } else if (diffInSeconds < 3600) {
      return t('profile.time.minutesAgo', { minutes: Math.floor(diffInSeconds / 60) }) || `${Math.floor(diffInSeconds / 60)}分钟前`;
    } else if (diffInSeconds < 86400) {
      return t('profile.time.hoursAgo', { hours: Math.floor(diffInSeconds / 3600) }) || `${Math.floor(diffInSeconds / 3600)}小时前`;
    } else if (diffInSeconds < 604800) {
      return t('profile.time.daysAgo', { days: Math.floor(diffInSeconds / 86400) }) || `${Math.floor(diffInSeconds / 86400)}天前`;
    } else {
      return new Date(dateString).toISOString().split('T')[0]; // 简单格式化为 YYYY-MM-DD
    }
  }, [t]);
  
  // 获取收藏视频
  useEffect(() => {
    async function fetchFavorites() {
      setIsLoading(true);
      
      try {
        // 使用客户端API获取收藏列表
        const favoritesData = await getUserFavorites(currentPage, itemsPerPage);
        
        if (favoritesData.data && favoritesData.data.length > 0) {
          // 转换数据结构以匹配组件所需
          const formattedFavorites = favoritesData.data.map(video => ({
            id: Number(video.id),
            title: video.title,
            slug: video.slug || '',
            duration: video.duration || '00:00',
            imageUrl: video.thumbnail_url || video.thumbnail || '/images/placeholder.jpg',
            views: video.view_count || video.views || 0,
            date: video.timeAgo || formatRelativeTime(video.created_at?.toString())
          }));
          
          setFavoriteVideos(formattedFavorites);
          
          // 更新分页信息
          if (favoritesData.pagination) {
            setTotalPages(favoritesData.pagination.totalPages);
            setTotalItems(favoritesData.pagination.total);
          }
        } else {
          setFavoriteVideos([]);
        }
      } catch (error) {
        console.error('获取收藏视频出错:', error);
        setFavoriteVideos([]);
      } finally {
        setIsLoading(false);
      }
    }
    
    fetchFavorites();
  }, [currentPage, formatRelativeTime]);
  
  // 处理取消收藏
  const handleRemoveFavorite = async (videoId: string, e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    
    try {
      const success = await removeFromFavorites(videoId);
      
      if (success) {
        // 从当前列表中移除
        setFavoriteVideos(prev => prev.filter(video => video.id.toString() !== videoId));
        setTotalItems(prev => prev - 1);
        
        // 如果当前页面已经没有数据了，且不是第一页，则返回上一页
        if (favoriteVideos.length === 1 && currentPage > 1) {
          setCurrentPage(prev => prev - 1);
        }
      }
    } catch (error) {
      console.error('取消收藏失败:', error);
    }
  };
  
  // 渲染分页控制
  const renderPagination = () => {
    if (totalPages <= 1) return null;
    
    return (
      <div className="flex justify-center mt-8 space-x-2">
        <button
          onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
          disabled={currentPage === 1}
          className={`px-4 py-2 rounded ${currentPage === 1 ? 'bg-gray-700 text-gray-400 cursor-not-allowed' : 'bg-gray-800 text-white hover:bg-gray-700'}`}
        >
          {t('pagination.previous') || '上一页'}
        </button>
        
        <span className="px-4 py-2 bg-gray-800 text-white rounded">
          {currentPage} / {totalPages}
        </span>
        
        <button
          onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
          disabled={currentPage === totalPages}
          className={`px-4 py-2 rounded ${currentPage === totalPages ? 'bg-gray-700 text-gray-400 cursor-not-allowed' : 'bg-gray-800 text-white hover:bg-gray-700'}`}
        >
          {t('pagination.next') || '下一页'}
        </button>
      </div>
    );
  };
  
  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-6">
        <Link href={`/${locale}/profile`} className="inline-flex items-center text-gray-300 hover:text-white">
          <FiArrowLeft className="mr-2" />
          {t('common.backToProfile') || '返回个人资料'}
        </Link>
      </div>
      
      <h1 className="text-2xl font-bold mb-8">{t('profile.myFavorites') || '我的收藏'}</h1>
      
      {isLoading ? (
        <div className="flex justify-center py-20">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-red-500"></div>
        </div>
      ) : favoriteVideos.length === 0 ? (
        <div className="py-20 text-center">
          <p className="text-gray-400 text-lg">{t('profile.noFavorites') || '您还没有收藏任何视频'}</p>
          <Link href={`/${locale}`} className="text-red-500 hover:text-red-400 mt-4 inline-block">
            {t('profile.browseVideos') || '去浏览视频'}
          </Link>
        </div>
      ) : (
        <>
          <p className="text-gray-400 mb-6">
            {t('profile.totalFavorites', { count: totalItems }) || (
              <>共收藏了 <span className="text-pink-400 font-semibold">{totalItems}</span> 个视频</>
            )}
          </p>
          
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-5">
            {favoriteVideos.map(video => (
              <div key={video.id} className="flex flex-col space-y-2">
                {/* 视频图片区块 */}
                <Link href={`/${locale}/video/${video.slug}`} className="block">
                  <div className="relative overflow-hidden rounded-xl">
                    <div className="relative h-40 md:h-48 overflow-hidden">
                      <Image
                        src={video.imageUrl}
                        alt={video.title}
                        fill
                        className="object-cover transition-transform duration-500 group-hover:scale-110"
                        sizes="(max-width: 768px) 100vw, 400px"
                      />
                      <div className="absolute inset-0 bg-gradient-to-t from-black to-transparent opacity-70 group-hover:opacity-60 transition-opacity duration-300"></div>
                      
                      {/* 时长标签 */}
                      <div className="absolute top-2 right-2 bg-red-600 text-white text-xs px-2 py-1 rounded font-medium">
                        {video.duration}
                      </div>
                      
                      {/* 取消收藏按钮 */}
                      <div className="absolute top-2 left-2">
                        <button 
                          className="bg-gray-800 bg-opacity-70 hover:bg-opacity-90 p-1.5 rounded-full transition-colors"
                          onClick={(e) => handleRemoveFavorite(video.id.toString(), e)}
                        >
                          <FiBookmark className="text-yellow-500 fill-current" size={16} />
                        </button>
                      </div>
                    </div>
                  </div>
                </Link>
                
                {/* 视频信息区块 */}
                <div className="flex flex-col">
                  <Link href={`/${locale}/video/${video.slug}`} className="block">
                    <h3 className="text-white font-semibold text-base truncate hover:text-red-500 transition-colors duration-300">
                      {video.title}
                    </h3>
                  </Link>
                  <div className="flex justify-between items-center mt-1">
                    <p className="text-gray-400 text-xs">{video.views} {t('video.views') || '次观看'}</p>
                    <span className="text-xs text-gray-400">{video.date}</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
          
          {renderPagination()}
        </>
      )}
    </div>
  );
};

export default FavoritesClient; 