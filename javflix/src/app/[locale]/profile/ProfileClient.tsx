'use client';

import { useState, useEffect, useCallback, useRef } from 'react';
import { useRouter, useParams } from 'next/navigation';
import Image from 'next/image';
import Link from 'next/link';
import { 
  FiUser, FiHeart, FiClock, FiSettings, FiLogOut, 
  FiEdit2, FiStar, FiThumbsUp, FiEye, FiCalendar,
  FiPlay, FiTrendingUp, FiAward, FiActivity,
  FiUsers, FiLoader, FiRefreshCw, FiWifi, FiWifiOff,
  FiBookmark
} from 'react-icons/fi';
import { useClientTranslations } from '@/components/TranslationsProvider';
import { getCurrentUser } from '@/lib/auth-client';
import { 
  getUserLikes, 
  getUserFavorites, 
  startSyncManager,
  useWatchLikeStatus,
  useWatchFavoriteStatus,
  cacheVideoInfo
} from '@/lib/sync-manager';
import { getCurrentUserId, userLocalStorage } from '@/lib/user-utils';

// ==================== 类型定义 ====================

interface User {
  id: string;
  username: string;
  email: string;
  avatarUrl?: string;
  createdAt?: string;
}

interface Video {
  id: number;
  title: string;
  slug: string;
  duration: string;
  imageUrl: string;
  views: number;
  date?: string;
  progress?: number;
  movie_id?: string;
  code?: string;
  liked_at?: string;
  favorited_at?: string;
}

interface Star {
  id: number;
  name: string;
  slug: string;
  imageUrl: string;
}

interface UserStats {
  totalLikes: number;
  totalFavorites: number;
  totalWatchTime: string;
  totalFollowing: number;
  joinDays: number;
  level: number;
}

interface Achievement {
  id: string;
  title: string;
  description: string;
  icon: string;
  earned: boolean;
  progress?: number;
  maxProgress?: number;
}

type TabType = 'overview' | 'likes' | 'favorites' | 'history' | 'following' | 'achievements' | 'settings';

// ==================== 智能同步管理器个人页面组件 ====================

const ProfileClient = () => {
  const { t } = useClientTranslations();
  const params = useParams();
  const locale = params?.locale?.toString() || 'zh-CN';
  const router = useRouter();
  
  // 状态管理 - 支持URL参数中的tab
  const [activeTab, setActiveTab] = useState<TabType>(() => {
    if (typeof window !== 'undefined') {
      const urlParams = new URLSearchParams(window.location.search);
      const tabParam = urlParams.get('tab') as TabType;
      return ['overview', 'likes', 'favorites', 'history', 'following', 'settings'].includes(tabParam) 
        ? tabParam 
        : 'overview';
    }
    return 'overview';
  });
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [user, setUser] = useState<User | null>(null);
  const [likedVideos, setLikedVideos] = useState<Video[]>([]);
  const [favoriteVideos, setFavoriteVideos] = useState<Video[]>([]);
  const [historyVideos, setHistoryVideos] = useState<Video[]>([]);
  const [followedStars, setFollowedStars] = useState<Star[]>([]);
  const [userStats, setUserStats] = useState<UserStats>({
    totalLikes: 0,
    totalFavorites: 0,
    totalWatchTime: '0小时',
    totalFollowing: 0,
    joinDays: 0,
    level: 1
  });
  const [achievements, setAchievements] = useState<Achievement[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [syncStatus, setSyncStatus] = useState<'online' | 'offline' | 'syncing'>('online');

  // 引用管理
  const syncManagerInitialized = useRef(false);
  const refreshTimeout = useRef<NodeJS.Timeout | null>(null);

  // ==================== 🔥 跨页面实时同步机制 ====================

  // 🔥 跨页面同步事件监听器（移到函数定义后）

  // ==================== 🛡️ 智能数据管理 ====================

  // 获取用户信息
  const fetchUser = useCallback(async () => {
    try {
      setSyncStatus('syncing');
      const userData = await getCurrentUser();
      if (userData) {
        setUser(userData);
        setSyncStatus('online');
        return userData;
      } else {
        router.push(`/${locale}/auth/login`);
        return null;
      }
    } catch (error) {
      console.error('❌ 获取用户信息失败:', error);
      setSyncStatus('offline');
      setError('获取用户信息失败，使用离线模式');
      return null;
    }
  }, [locale, router]);

  // 智能刷新点赞数据（仿照观看历史，直接从localStorage读取）
  const refreshLikesData = useCallback(async (showLoading = false) => {
    try {
      if (showLoading) setIsRefreshing(true);
      
      const userId = getCurrentUserId();
      
      // 🔥 直接从新的数组存储读取（仿照观看历史）
      const likedData = userLocalStorage.getJSON('likedVideos', []);
      
      // 转换数据格式以匹配Video接口
      const formattedLikes: Video[] = likedData.map((item: any) => ({
        id: parseInt(item.id) || 0,
        title: item.title || '',
        slug: item.slug || '',
        duration: item.duration || '',
        imageUrl: item.thumbnail || '/images/placeholder.jpg',
        views: 0,
        date: item.likedAt || new Date().toISOString(),
        movie_id: item.code || item.id,
        code: item.code || item.id,
        liked_at: item.likedAt
      }));
      
      setLikedVideos(formattedLikes);
      
      // 更新统计
      setUserStats(prev => ({
        ...prev,
        totalLikes: formattedLikes.length
      }));
      
      setSyncStatus('online');
      
      // 🔄 可选：尝试同步到后端（不阻塞UI，不影响数据显示）
      try {
        const token = localStorage.getItem('auth_token') || localStorage.getItem('auth-token');
        if (token && formattedLikes.length > 0) {
          // 异步获取服务器数据进行对比，但不替换当前显示
          getUserLikes(1, 50).then(serverResult => {
            // Server data comparison completed
          }).catch(error => {
            // Server sync failed (doesn't affect functionality)
          });
        }
      } catch (syncError) {
        // Backend sync failed (doesn't affect functionality)
      }
      
    } catch (error) {
      console.error('❌ 刷新点赞数据失败:', error);
      setSyncStatus('offline');
      setError('刷新点赞数据失败');
      setLikedVideos([]);
    } finally {
      if (showLoading) setIsRefreshing(false);
    }
  }, []);

  // 智能刷新收藏数据（仿照观看历史，直接从localStorage读取）
  const refreshFavoritesData = useCallback(async (showLoading = false) => {
    try {
      if (showLoading) setIsRefreshing(true);
      
      const userId = getCurrentUserId();
      
      // 🔥 直接从新的数组存储读取（仿照观看历史）
      const favoritedData = userLocalStorage.getJSON('favoritedVideos', []);
      
      // 转换数据格式以匹配Video接口
      const formattedFavorites: Video[] = favoritedData.map((item: any) => ({
        id: parseInt(item.id) || 0,
        title: item.title || '',
        slug: item.slug || '',
        duration: item.duration || '',
        imageUrl: item.thumbnail || '/images/placeholder.jpg',
        views: 0,
        date: item.favoritedAt || new Date().toISOString(),
        movie_id: item.code || item.id,
        code: item.code || item.id,
        favorited_at: item.favoritedAt
      }));
      
      setFavoriteVideos(formattedFavorites);
      
      // 更新统计
      setUserStats(prev => ({
        ...prev,
        totalFavorites: formattedFavorites.length
      }));
      
      setSyncStatus('online');
      
      // 🔄 可选：尝试同步到后端（不阻塞UI，不影响数据显示）
      try {
        const token = localStorage.getItem('auth_token') || localStorage.getItem('auth-token');
        if (token && formattedFavorites.length > 0) {
          // 异步获取服务器数据进行对比，但不替换当前显示
          getUserFavorites(1, 50).then(serverResult => {
            // Server data comparison completed
          }).catch(error => {
            // Server sync failed (doesn't affect functionality)
          });
        }
      } catch (syncError) {
        // Backend sync failed (doesn't affect functionality)
      }
      
    } catch (error) {
      console.error('❌ 刷新收藏数据失败:', error);
      setSyncStatus('offline');
      setError('刷新收藏数据失败');
      setFavoriteVideos([]);
    } finally {
      if (showLoading) setIsRefreshing(false);
    }
  }, []);

  // 获取观看历史（从localStorage读取，与播放页面保持一致）🔒 用户隔离版本
  const fetchWatchHistory = useCallback(async () => {
    try {
      const userId = getCurrentUserId();
      
      // 🔥 从用户专属localStorage读取观看历史（与播放页面保持一致）🔒 用户隔离版本
      const historyData = userLocalStorage.getJSON('watchHistory', []);
      
      // 转换数据格式以匹配Video接口
      const formattedHistory: Video[] = historyData.map((item: any) => ({
        id: parseInt(item.id) || 0,
        title: item.title || '',
        slug: item.slug || '',
        duration: item.duration || '',
        imageUrl: item.thumbnail || '',
        views: 0,
        date: item.viewedAt || new Date().toISOString(),
        movie_id: item.code || item.id,
        code: item.code || item.id
      }));
      
      setHistoryVideos(formattedHistory);
      
      // 更新统计信息
      setUserStats(prev => ({
        ...prev,
        totalWatchTime: calculateTotalWatchTime(formattedHistory)
      }));
      
      // 🔄 可选：尝试同步到后端（不阻塞UI）
      try {
        const token = localStorage.getItem('auth_token') || localStorage.getItem('auth-token');
        if (token && formattedHistory.length > 0) {
          // 异步同步最新的观看记录到后端
          const latestVideo = formattedHistory[0];
          fetch('/api/proxy/users/watch-history', {
            method: 'POST',
            headers: {
              'Authorization': `Bearer ${token}`,
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              videoId: latestVideo.id || latestVideo.code,
              watchedAt: latestVideo.date
            })
          }).catch(error => {
            // Backend sync failed (doesn't affect functionality)
          });
        }
      } catch (syncError) {
        // Backend sync failed (doesn't affect functionality)
      }
      
    } catch (error) {
      console.error('❌ 获取观看历史失败:', error);
      setHistoryVideos([]);
    }
  }, []);

  // 计算总观看时长的辅助函数
  const calculateTotalWatchTime = useCallback((history: Video[]) => {
    if (!history || history.length === 0) return '0分钟';
    
    // 简单估算：每个视频按平均30分钟计算
    const totalMinutes = history.length * 30;
    const hours = Math.floor(totalMinutes / 60);
    const minutes = totalMinutes % 60;
    
    if (hours > 0) {
      return `${hours}小时${minutes}分钟`;
    } else {
      return `${minutes}分钟`;
    }
  }, []);

  // 获取关注的演员
  const fetchFollowedStars = useCallback(async () => {
    try {
      const token = localStorage.getItem('auth_token') || localStorage.getItem('auth-token');
      
      if (!token) {
        return;
      }

      const response = await fetch('/api/proxy/users/following?page=1&limit=20', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const result = await response.json();
        const followingData = result.data?.data || result.data || [];
        setFollowedStars(Array.isArray(followingData) ? followingData : []);
        
        // 更新统计
        setUserStats(prev => ({
          ...prev,
          totalFollowing: followingData.length
        }));
      } else {
        setFollowedStars([]);
      }
    } catch (error) {
      console.error('❌ 获取关注列表失败:', error);
      setFollowedStars([]);
    }
  }, []);

  // ==================== 📊 初始化和生命周期管理 ====================

  // 🔥 跨页面同步事件监听器
  useEffect(() => {
    const handleLikeStatusChanged = (event: Event) => {
      const customEvent = event as CustomEvent;
      const { videoId, isLiked } = customEvent.detail;
      
      // 🔥 始终刷新点赞数据，确保实时同步
      refreshLikesData(false);
      
      // 更新统计计数
      setUserStats(prev => ({
        ...prev,
        totalLikes: isLiked ? prev.totalLikes + 1 : Math.max(0, prev.totalLikes - 1)
      }));
    };

    const handleFavoriteStatusChanged = (event: Event) => {
      const customEvent = event as CustomEvent;
      const { videoId, isFavorited } = customEvent.detail;
      
      // 🔥 始终刷新收藏数据，确保实时同步
      refreshFavoritesData(false);
      
      // 更新统计计数
      setUserStats(prev => ({
        ...prev,
        totalFavorites: isFavorited ? prev.totalFavorites + 1 : Math.max(0, prev.totalFavorites - 1)
      }));
    };

    // 🔥 监听观看历史变化（localStorage变化）🔒 用户隔离版本
    const handleStorageChange = (event: StorageEvent) => {
      const userId = getCurrentUserId();
      const userWatchHistoryKey = `user_${userId}_watchHistory`;
      
      if (event.key === userWatchHistoryKey || event.key === 'watchHistory') {
        fetchWatchHistory();
      }
    };

    // 🔥 监听观看历史跨页面事件
    const handleWatchHistoryChanged = (event: Event) => {
      const customEvent = event as CustomEvent;
      const { newVideo, totalCount } = customEvent.detail;
      
      // 立即刷新观看历史
      fetchWatchHistory();
    };

    // 注册跨页面事件监听器
    window.addEventListener('likeStatusChanged', handleLikeStatusChanged);
    window.addEventListener('favoriteStatusChanged', handleFavoriteStatusChanged);
    window.addEventListener('storage', handleStorageChange);
    window.addEventListener('watchHistoryChanged', handleWatchHistoryChanged);

    return () => {
      window.removeEventListener('likeStatusChanged', handleLikeStatusChanged);
      window.removeEventListener('favoriteStatusChanged', handleFavoriteStatusChanged);
      window.removeEventListener('storage', handleStorageChange);
      window.removeEventListener('watchHistoryChanged', handleWatchHistoryChanged);
    };
  }, [refreshLikesData, refreshFavoritesData, fetchWatchHistory]);

  // 初始化同步管理器和页面数据
  const initializeProfile = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      // 1. 启动同步管理器（仅一次）
      if (!syncManagerInitialized.current) {
        startSyncManager();
        syncManagerInitialized.current = true;
      }

      // 2. 获取用户信息
      const userData = await fetchUser();
      if (!userData) return;

             // 3. 并行加载所有数据（提升性能）
       await Promise.allSettled([
         refreshLikesData(false),
         refreshFavoritesData(false), 
         fetchWatchHistory(),
         fetchFollowedStars()
       ]);
      
    } catch (error) {
      console.error('❌ 个人页面初始化失败:', error);
      setError('页面初始化失败，请刷新重试');
    } finally {
      setIsLoading(false);
    }
  }, [fetchUser, refreshLikesData, refreshFavoritesData, fetchWatchHistory, fetchFollowedStars]);

  // 组件挂载时初始化
  useEffect(() => {
    initializeProfile();
  }, [initializeProfile]);

  // 智能数据刷新（根据当前标签页）
  const handleRefresh = useCallback(async () => {
    setSyncStatus('syncing');
    
    switch (activeTab) {
      case 'likes':
        await refreshLikesData(true);
        break;
      case 'favorites':
        await refreshFavoritesData(true);
        break;
      case 'history':
        await fetchWatchHistory();
        break;
      case 'following':
        await fetchFollowedStars();
        break;
             case 'overview':
         await Promise.allSettled([
           refreshLikesData(false),
           refreshFavoritesData(false),
           fetchWatchHistory(),
           fetchFollowedStars()
         ]);
         break;
    }
    
    setSyncStatus('online');
  }, [activeTab, refreshLikesData, refreshFavoritesData, fetchWatchHistory, fetchFollowedStars]);

  // 退出登录
  const handleLogout = async () => {
    try {
      localStorage.removeItem('auth_token');
      localStorage.removeItem('auth-token');
      sessionStorage.clear();
      router.push(`/${locale}/auth/login`);
    } catch (error) {
      console.error('退出登录失败:', error);
    }
  };

  // ==================== 🎨 UI 渲染组件 ====================

  // 渲染同步状态指示器
  const renderSyncStatus = () => (
    <div className="flex items-center gap-2 text-sm">
      {syncStatus === 'online' && (
        <>
          <FiWifi className="text-green-400" />
          <span className="text-green-300">在线同步</span>
        </>
      )}
      {syncStatus === 'offline' && (
        <>
          <FiWifiOff className="text-orange-400" />
          <span className="text-orange-300">离线模式</span>
        </>
      )}
      {syncStatus === 'syncing' && (
        <>
          <FiLoader className="text-blue-400 animate-spin" />
          <span className="text-blue-300">同步中...</span>
        </>
      )}
    </div>
  );

  // 渲染统计卡片
  const renderStatsCards = () => (
    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
      <div className="bg-gray-800/40 backdrop-blur-sm border border-red-500/30 rounded-xl p-4 hover:bg-gray-800/60 hover:shadow-lg hover:shadow-red-500/25 transition-all duration-300">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-red-400 text-sm font-medium">点赞视频</p>
            <p className="text-red-400 text-2xl font-bold">{userStats.totalLikes}</p>
          </div>
          <FiThumbsUp className="text-red-500 text-2xl" />
        </div>
      </div>
      
      <div className="bg-gray-800/40 backdrop-blur-sm border border-yellow-500/30 rounded-xl p-4 hover:bg-gray-800/60 hover:shadow-lg hover:shadow-yellow-500/25 transition-all duration-300">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-yellow-400 text-sm font-medium">收藏视频</p>
            <p className="text-white text-2xl font-bold">{userStats.totalFavorites}</p>
          </div>
          <FiBookmark className="text-yellow-500 text-2xl" />
        </div>
      </div>
      
      <div className="bg-gray-800/40 backdrop-blur-sm border border-blue-500/30 rounded-xl p-4 hover:bg-gray-800/60 hover:shadow-lg hover:shadow-blue-500/25 transition-all duration-300">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-blue-400 text-sm font-medium">观看时长</p>
            <p className="text-white text-2xl font-bold">{userStats.totalWatchTime}</p>
          </div>
          <FiEye className="text-blue-500 text-2xl" />
        </div>
      </div>
      
      <div className="bg-gray-800/40 backdrop-blur-sm border border-purple-500/30 rounded-xl p-4 hover:bg-gray-800/60 hover:shadow-lg hover:shadow-purple-500/25 transition-all duration-300">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-purple-400 text-sm font-medium">关注演员</p>
            <p className="text-white text-2xl font-bold">{userStats.totalFollowing}</p>
          </div>
          <FiUsers className="text-purple-500 text-2xl" />
        </div>
      </div>
    </div>
  );

  // 渲染视频网格
  const renderVideoGrid = (videos: Video[], emptyMessage: string) => {
    if (videos.length === 0) {
      return (
        <div className="text-center py-12">
          <div className="mx-auto w-24 h-24 bg-gray-800/30 backdrop-blur-sm rounded-full flex items-center justify-center mb-4 border border-gray-700/30">
            <FiEye className="text-gray-400 text-3xl" />
          </div>
          <p className="text-gray-300 text-lg">{emptyMessage}</p>
          <p className="text-gray-500 text-sm mt-2">去播放页面点赞或收藏视频会自动同步到这里</p>
        </div>
      );
    }

    return (
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
        {videos.map((video, index) => (
          <Link 
            key={`${video.id}-${video.slug || video.movie_id}-${index}`}
            href={`/${locale}/video/${video.slug || video.movie_id}`}
            className="group block"
          >
            <div className="bg-gray-800/30 backdrop-blur-sm rounded-xl border border-gray-700/30 overflow-hidden hover:shadow-lg hover:shadow-red-500/25 transition-all duration-300 hover:-translate-y-1 group">
              <div className="relative aspect-video">
                <Image
                  src={video.imageUrl || '/placeholder-video.jpg'}
                  alt={video.title}
                  fill
                  className="object-cover group-hover:scale-105 transition-transform duration-300"
                  sizes="(max-width: 768px) 50vw, (max-width: 1200px) 33vw, 25vw"
                />
                <div className="absolute top-2 right-2 bg-black/70 text-white text-xs px-2 py-1 rounded">
                  {video.duration}
                </div>
                {video.progress && video.progress > 0 && (
                  <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/60 to-transparent">
                    <div className="bg-red-500 h-1" style={{ width: `${video.progress}%` }} />
                  </div>
                )}
              </div>
              <div className="p-4">
                <h3 className="font-semibold text-white text-sm mb-2 line-clamp-2 group-hover:text-red-400 transition-colors">
                  {video.title}
                </h3>
                <div className="flex items-center justify-between text-xs text-gray-400">
                  <span className="flex items-center gap-1">
                    <FiEye className="text-xs" />
                    {video.views?.toLocaleString() || '0'}
                  </span>
                  {(video.liked_at || video.favorited_at) && (
                    <span className="text-xs text-gray-500">
                      {new Date(video.liked_at || video.favorited_at!).toLocaleDateString()}
                    </span>
                  )}
                </div>
              </div>
            </div>
          </Link>
        ))}
      </div>
    );
  };

  // 渲染演员网格
  const renderStarGrid = () => {
    if (followedStars.length === 0) {
      return (
        <div className="text-center py-12">
          <div className="mx-auto w-24 h-24 bg-gray-800/30 backdrop-blur-sm rounded-full flex items-center justify-center mb-4 border border-gray-700/30">
            <FiStar className="text-gray-400 text-3xl" />
          </div>
          <p className="text-gray-300 text-lg">还没有关注任何演员</p>
          <p className="text-gray-500 text-sm mt-2">在演员页面点击关注按钮</p>
        </div>
      );
    }

    return (
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
        {followedStars.map((star) => (
          <Link 
            key={star.id}
            href={`/${locale}/actress/${star.slug}`}
            className="group block"
          >
            <div className="bg-gray-800/30 backdrop-blur-sm rounded-xl border border-gray-700/30 overflow-hidden hover:shadow-lg hover:shadow-red-500/25 transition-all duration-300 hover:-translate-y-1">
              <div className="relative aspect-square">
                <Image
                  src={star.imageUrl || '/placeholder-actress.jpg'}
                  alt={star.name}
                  fill
                  className="object-cover group-hover:scale-105 transition-transform duration-300"
                  sizes="(max-width: 768px) 50vw, (max-width: 1200px) 33vw, 25vw"
                />
              </div>
              <div className="p-4">
                <h3 className="font-semibold text-white text-center group-hover:text-red-400 transition-colors">
                  {star.name}
                </h3>
              </div>
            </div>
          </Link>
        ))}
      </div>
    );
  };

  // 渲染概览页面
  const renderOverview = () => (
    <div className="space-y-8">
      {renderStatsCards()}
      
      {/* 最近点赞 */}
      <div>
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-xl font-bold text-white flex items-center gap-2">
            <FiThumbsUp className="text-red-500" />
            最近点赞
          </h3>
          <Link 
            href={`/${locale}/profile?tab=likes`}
            className="text-red-400 hover:text-red-300 text-sm font-medium"
          >
            查看全部
          </Link>
        </div>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          {likedVideos.slice(0, 4).map((video, index) => (
            <Link 
              key={`${video.id}-recent-like-${index}`}
              href={`/${locale}/video/${video.slug || video.movie_id}`}
              className="group block"
            >
              <div className="bg-gray-800/30 backdrop-blur-sm rounded-lg border border-gray-700/30 overflow-hidden hover:shadow-md hover:shadow-red-500/20 transition-all">
                <div className="relative aspect-video">
                  <Image
                    src={video.imageUrl || '/placeholder-video.jpg'}
                    alt={video.title}
                    fill
                    className="object-cover group-hover:scale-105 transition-transform duration-300"
                    sizes="(max-width: 768px) 50vw, 25vw"
                  />
                  <div className="absolute top-2 right-2 bg-black/70 text-white text-xs px-2 py-1 rounded">
                    {video.duration}
                  </div>
                </div>
                <div className="p-3">
                  <h4 className="font-medium text-white text-sm line-clamp-2 group-hover:text-red-400">
                    {video.title}
                  </h4>
                </div>
              </div>
            </Link>
          ))}
        </div>
      </div>

      {/* 最近收藏 */}
      <div>
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-xl font-bold text-white flex items-center gap-2">
            <FiBookmark className="text-yellow-500" />
            最近收藏
          </h3>
          <Link 
            href={`/${locale}/profile?tab=favorites`}
            className="text-yellow-400 hover:text-yellow-300 text-sm font-medium"
          >
            查看全部
          </Link>
        </div>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          {favoriteVideos.slice(0, 4).map((video, index) => (
            <Link 
              key={`${video.id}-recent-favorite-${index}`}
              href={`/${locale}/video/${video.slug || video.movie_id}`}
              className="group block"
            >
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-all">
                <div className="relative aspect-video">
                  <Image
                    src={video.imageUrl || '/placeholder-video.jpg'}
                    alt={video.title}
                    fill
                    className="object-cover group-hover:scale-105 transition-transform duration-300"
                    sizes="(max-width: 768px) 50vw, 25vw"
                  />
                  <div className="absolute top-2 right-2 bg-black/70 text-white text-xs px-2 py-1 rounded">
                    {video.duration}
                  </div>
                </div>
                <div className="p-3">
                  <h4 className="font-medium text-white text-sm line-clamp-2 group-hover:text-yellow-400">
                    {video.title}
                  </h4>
                </div>
              </div>
            </Link>
          ))}
        </div>
      </div>
    </div>
  );

  // 渲染标签页内容
  const renderTabContent = () => {
    switch (activeTab) {
      case 'overview':
        return renderOverview();
      case 'likes':
        return renderVideoGrid(likedVideos, '还没有点赞任何视频');
      case 'favorites':
        return renderVideoGrid(favoriteVideos, '还没有收藏任何视频');
      case 'history':
        return renderVideoGrid(historyVideos, '还没有观看历史');
      case 'following':
        return renderStarGrid();
      case 'settings':
        return (
          <div className="bg-gray-800/30 backdrop-blur-sm rounded-xl border border-gray-700/30 p-6">
            <h3 className="text-lg font-semibold mb-4 text-white">个人设置</h3>
            <div className="space-y-4">
              <button
                onClick={handleLogout}
                className="flex items-center gap-2 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 hover:shadow-lg hover:shadow-red-500/25 transition-all duration-300"
              >
                <FiLogOut />
                退出登录
              </button>
            </div>
          </div>
        );
      default:
        return null;
    }
  };

  // ==================== 🎯 主渲染 ====================

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <FiLoader className="mx-auto text-4xl text-gray-400 animate-spin mb-4" />
          <p className="text-gray-300">加载个人资料中...</p>
          <p className="text-gray-500 text-sm mt-2">正在加载数据...</p>
        </div>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <FiUser className="mx-auto text-4xl text-gray-400 mb-4" />
          <p className="text-gray-300">未找到用户信息</p>
          <button
            onClick={() => router.push(`/${locale}/auth/login`)}
            className="mt-4 px-6 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 hover:shadow-lg hover:shadow-red-500/25 transition-all duration-300"
          >
            重新登录
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-900 text-white">
      <div className="max-w-7xl mx-auto px-4 py-8">
        
        {/* 个人信息头部 */}
        <div className="bg-gray-800/30 backdrop-blur-sm rounded-xl border border-gray-700/30 p-6 mb-8">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="w-16 h-16 bg-gradient-to-br from-red-500 to-pink-500 rounded-full flex items-center justify-center shadow-lg shadow-red-500/25">
                <FiUser className="text-white text-2xl" />
              </div>
              <div>
                <h1 className="text-3xl font-bold bg-gradient-to-r from-white via-gray-200 to-gray-400 bg-clip-text text-transparent">{user.username}</h1>
                <p className="text-gray-300">{user.email}</p>
                <div className="flex items-center gap-4 mt-2">
                  {renderSyncStatus()}
                  <span className="text-sm text-gray-400">
                    加入于 {user.createdAt ? new Date(user.createdAt).toLocaleDateString() : '未知'}
                  </span>
                </div>
              </div>
            </div>
            
            <div className="flex items-center gap-3">
              <button
                onClick={handleRefresh}
                disabled={isRefreshing}
                className="px-4 py-2 bg-gray-800/30 border border-gray-700/30 rounded-lg hover:bg-gray-700/30 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2 text-gray-300 hover:text-white backdrop-blur-sm"
              >
                <FiRefreshCw className={`${isRefreshing ? 'animate-spin' : ''}`} />
                刷新
              </button>
            </div>
          </div>
        </div>

        {error && (
          <div className="bg-orange-900/20 backdrop-blur-sm border border-orange-500/30 rounded-xl p-4 mb-6">
            <div className="flex items-center gap-2 text-orange-300">
              <FiWifiOff />
              <span className="font-medium">{error}</span>
            </div>
          </div>
        )}

        {/* 导航标签 */}
        <div className="bg-gray-800/30 backdrop-blur-sm rounded-xl border border-gray-700/30 p-1 mb-8">
          <div className="flex space-x-1 overflow-x-auto">
            {[
              { key: 'overview', label: '概览', icon: FiActivity },
              { key: 'likes', label: '点赞', icon: FiThumbsUp },
              { key: 'favorites', label: '收藏', icon: FiBookmark },
              { key: 'history', label: '历史', icon: FiClock },
              { key: 'following', label: '关注', icon: FiStar },
              { key: 'settings', label: '设置', icon: FiSettings },
            ].map(({ key, label, icon: Icon }) => (
              <button
                key={key}
                onClick={() => setActiveTab(key as TabType)}
                className={`flex items-center gap-2 px-4 py-3 rounded-lg transition-all duration-300 whitespace-nowrap ${
                  activeTab === key
                    ? 'bg-red-600 text-white shadow-lg shadow-red-500/25'
                    : 'text-gray-300 hover:bg-gray-700/30 hover:text-white'
                }`}
              >
                <Icon className="text-lg" />
                {label}
              </button>
            ))}
          </div>
        </div>

        {/* 标签页内容 */}
        <div className="animate-in fade-in duration-300">
          {renderTabContent()}
        </div>
      </div>
    </div>
  );
};

export default ProfileClient; 