import type { Metadata } from 'next';
import { generateMetadata as createMetadata } from '@/lib/seo';
import { Suspense } from 'react';
import CategoryClient from '@/app/category/CategoryClient';
import { getTranslations, locales } from '@/i18n';
import type { Locale } from '@/i18n/types';
import { notFound } from 'next/navigation';

// Route Segment Config for Server-Side Rendering (SSR)
export const dynamic = 'force-dynamic'; // Enable SSR - render on each request
 // Ensure fresh data on each request
export const runtime = 'nodejs'; // Use Node.js runtime for better performance
export const preferredRegion = 'auto'; // Auto-select optimal region
export const revalidate = false; // Disable revalidation for true SSR

// 为语言路由生成静态路径参数
export async function generateStaticParams() {
  return locales.map(locale => ({ locale }));
}

// 生成该语言的元数据
export async function generateMetadata(
  { params }: { params: Promise<{ locale: string }> }
): Promise<Metadata> {
  // 获取locale参数
  const resolvedParams = await params;
  const locale = resolvedParams.locale;
  
  // 默认元数据内容
  const defaultMetadata = {
    title: '视频分类',
    description: '浏览JAVFLIX的所有视频分类，包括无码有码、中文字幕等多种类型',
    path: locale ? `/${locale}/category` : '/category',
    keywords: [
      'JAV分类', '日本AV类型', '无码', '有码', '中文字幕', 
      '高清', '素人', '巨乳', '人妻', '女同性恋', '性爱分类'
    ],
  };
  
  // 验证locale是否是有效的支持语言
  if (!locale || !locales.includes(locale as Locale)) {
    return createMetadata(defaultMetadata);
  }
  
  try {
    // 获取该语言的翻译
    const translations = await getTranslations(locale as Locale);
    
    // 安全检查translations.pages.category是否存在
    if (translations?.pages?.category) {
      return createMetadata({
        title: translations.pages.category.title || defaultMetadata.title,
        description: translations.pages.category.description || defaultMetadata.description,
        path: `/${locale}/category`,
        keywords: defaultMetadata.keywords,
      });
    }
  } catch (error) {
    console.error('Failed to load translations:', error);
  }
  
  // 如果翻译不存在或出错，使用默认值
  return createMetadata(defaultMetadata);
}

// 加载占位符组件
function CategoryLoading() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-800 text-white pt-20 pb-12">
      {/* 背景装饰 */}
      <div className="absolute inset-0 pointer-events-none">
        <div className="absolute top-1/4 left-1/4 w-64 h-64 bg-red-500/10 rounded-full filter blur-3xl"></div>
        <div className="absolute bottom-1/3 right-1/3 w-96 h-96 bg-blue-500/8 rounded-full filter blur-3xl"></div>
        <div className="absolute top-1/2 left-1/2 w-48 h-48 bg-purple-500/12 rounded-full filter blur-2xl"></div>
      </div>

      <div className="relative container mx-auto px-4">
        <div className="text-center mb-12">
          <div className="h-12 bg-gray-800 rounded-lg animate-pulse mb-4 mx-auto max-w-md"></div>
          <div className="h-6 bg-gray-700 rounded-lg animate-pulse mx-auto max-w-sm"></div>
        </div>
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
          {[...Array(12)].map((_, i) => (
            <div key={i} className="bg-gray-800 rounded-lg p-4 animate-pulse">
              <div className="h-32 bg-gray-700 rounded-lg mb-4"></div>
              <div className="h-4 bg-gray-700 rounded mb-2"></div>
              <div className="h-3 bg-gray-600 rounded w-3/4"></div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}

// 主页面是服务器组件，只用于包装客户端组件
export default async function CategoryPage({ 
  params 
}: { 
  params: Promise<{ locale: string }> 
}) {
  // 获取locale参数
  const resolvedParams = await params;
  const locale = resolvedParams.locale as Locale;
  
  // 验证locale是否支持，否则显示404
  if (!locale || !locales.includes(locale)) {
    notFound();
  }
  
  try {
    // 异步获取该语言的翻译
    await getTranslations(locale);
  } catch (error) {
    console.error('Failed to load translations in CategoryPage:', error);
  }
  
  return (
    <Suspense fallback={<CategoryLoading />}>
      <CategoryClient />
    </Suspense>
  );
} 