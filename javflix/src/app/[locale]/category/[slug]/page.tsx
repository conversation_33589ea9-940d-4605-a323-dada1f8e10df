import React from 'react';
import { Metadata } from 'next';
import { generateMetadata as generateSeoMetadata } from '@/lib/seo';
import CategoryClient from '@/app/category/[slug]/CategoryClient';
import { getTranslations, locales } from '@/i18n';
import type { Locale } from '@/i18n/types';
import { notFound } from 'next/navigation';

// Route Segment Config for Server-Side Rendering (SSR)
export const dynamic = 'force-dynamic'; // Enable SSR - render on each request
 // Ensure fresh data on each request
export const runtime = 'nodejs'; // Use Node.js runtime for better performance
export const preferredRegion = 'auto'; // Auto-select optimal region
export const revalidate = false; // Disable revalidation for true SSR

// 为语言路由生成静态路径参数
export async function generateStaticParams() {
  return locales.map(locale => ({ locale }));
}

// 从数据库API获取分类详情数据
async function getCategoryData(slug: string) {
  try {
    // 解码URL编码的slug
    const decodedSlug = decodeURIComponent(slug);
    console.log(`Fetching category data for slug: ${slug} (decoded: ${decodedSlug})`);

    // 尝试通过ID或名称获取分类详情 - 使用前端API路由
    const response = await fetch(`http://localhost:3001/api/db/genres/${encodeURIComponent(slug)}`, {
      // 使用动态路由，不使用no-store缓存
      next: { revalidate: 0 }
    });
    
    if (!response.ok) {
      console.error(`Failed to fetch category data for slug ${slug} (decoded: ${decodedSlug}): ${response.status}`);
      // 输出响应内容以便调试
      try {
        const errorText = await response.text();
        console.error('API response:', errorText);
      } catch (e) {
        console.error('Could not read response text:', e);
      }
      return null;
    }
    
    const result = await response.json();
    
    if (result.success && result.data) {
      return {
        id: result.data.id,
        name: result.data.name,
        description: result.data.description || `${result.data.name}分类下的精彩视频内容`,
        movieCount: result.data.movie_count || 0,
        slug: slug,
        imageUrl: result.data.image_url || '/images/defaults/category_default.jpg',
        // 如果API返回了示例影片，可以在这里处理
        sampleMovies: result.data.sample_movies || []
      };
    }
    
    console.error(`API returned success: false for slug ${slug}:`, result);
    return null;
  } catch (error) {
    console.error(`Error fetching category data for slug ${slug}:`, error);
    return null;
  }
}

// 获取分类下的视频数据
async function getCategoryMovies(categoryId: string, page: number = 1, limit: number = 12) {
  try {
    const response = await fetch(`http://localhost:4000/api/db/movies/genre/${categoryId}?page=${page}&limit=${limit}`, {
      next: { revalidate: 0 }
    });
    
    if (!response.ok) {
      console.error(`Failed to fetch movies for category ${categoryId}: ${response.status}`);
      return { movies: [], pagination: null };
    }
    
    const result = await response.json();
    
    if (result.success && result.data) {
      return {
        movies: result.data.items || [],
        pagination: result.data.pagination || null
      };
    }
    
    return { movies: [], pagination: null };
  } catch (error) {
    console.error(`Error fetching movies for category ${categoryId}:`, error);
    return { movies: [], pagination: null };
  }
}

// 辅助函数：格式化时长
function formatDuration(minutes: number): string {
  const hours = Math.floor(minutes / 60);
  const mins = minutes % 60;
  return `${hours}:${mins.toString().padStart(2, '0')}:00`;
}

// 辅助函数：格式化观看次数
function formatViewCount(count: number): string {
  if (count >= 1000000) {
    return `${(count / 1000000).toFixed(1)}M`;
  } else if (count >= 1000) {
    return `${(count / 1000).toFixed(1)}K`;
  }
  return count.toString();
}

// 辅助函数：格式化日期
function formatDate(dateString: string): string {
  try {
    const date = new Date(dateString);
    return date.toISOString().split('T')[0];
  } catch {
    return dateString;
  }
}

// 生成分类详情页的元数据（国际化版本）
export async function generateMetadata({ 
  params 
}: { 
  params: Promise<{ locale: string; slug: string }> 
}): Promise<Metadata> {
  const resolvedParams = await params;
  const { locale, slug } = resolvedParams;
  
  // 获取分类数据
  const categoryData = await getCategoryData(slug);
  
  // 如果没有找到分类数据，返回默认元数据
  if (!categoryData) {
    return generateSeoMetadata({
      title: '分类详情 - 未找到',
      description: '未找到该分类的详细信息',
      path: `/${locale}/category/${slug}`,
      keywords: ['分类', '视频分类', '影片类型'],
    });
  }
  
  // 验证locale是否是有效的支持语言
  if (!locale || !locales.includes(locale as Locale)) {
    return generateSeoMetadata({
      title: `${categoryData.name} - 分类详情`,
      description: categoryData.description,
      path: `/category/${slug}`,
      imageUrl: categoryData.imageUrl ? `https://javflix.tv${categoryData.imageUrl}` : undefined,
      keywords: ['分类', '视频分类', '影片类型', categoryData.name],
    });
  }

  try {
    // 获取该语言的翻译
    const translations = await getTranslations(locale as Locale);
    
    return generateSeoMetadata({
      title: `${categoryData.name} - ${translations.pages?.category?.title || '分类详情'}`,
      description: categoryData.description,
      path: `/${locale}/category/${slug}`,
      imageUrl: categoryData.imageUrl ? `https://javflix.tv${categoryData.imageUrl}` : undefined,
      keywords: ['分类', '视频分类', '影片类型', categoryData.name],
    });
  } catch (error) {
    console.error('Failed to load translations:', error);
    return generateSeoMetadata({
      title: `${categoryData.name} - 分类详情`,
      description: categoryData.description,
      path: `/${locale}/category/${slug}`,
      imageUrl: categoryData.imageUrl ? `https://javflix.tv${categoryData.imageUrl}` : undefined,
      keywords: ['分类', '视频分类', '影片类型', categoryData.name],
    });
  }
}

// 服务器端页面组件（国际化版本）
export default async function CategoryPage({ 
  params,
  searchParams
}: { 
  params: Promise<{ locale: string; slug: string }>;
  searchParams: Promise<{ page?: string }>;
}) {
  const resolvedParams = await params;
  const resolvedSearchParams = await searchParams;
  const { locale, slug } = resolvedParams;
  const page = parseInt(resolvedSearchParams.page || '1', 10);
  
  // 验证locale是否支持，否则显示404
  if (!locale || !locales.includes(locale as Locale)) {
    notFound();
  }

  try {
    // 异步获取该语言的翻译
    await getTranslations(locale as Locale);
  } catch (error) {
    console.error('Failed to load translations in CategoryPage:', error);
  }

  // 获取真实数据
  const categoryData = await getCategoryData(slug);
  
  // 如果没有找到分类数据，显示404
  if (!categoryData) {
    notFound();
  }
  
  // 注意：这里暂时不获取电影数据，让客户端组件处理
  return (
    <CategoryClient slug={slug} />
  );
} 