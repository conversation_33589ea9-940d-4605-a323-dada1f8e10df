import { Metadata } from 'next';
import { getTranslations, locales } from '@/i18n';
import { Locale } from '@/i18n/types';
import { notFound } from 'next/navigation';

interface FAQPageProps {
  params: Promise<{ locale: string }>;
}

export async function generateMetadata({
  params
}: FAQPageProps): Promise<Metadata> {
  const resolvedParams = await params;
  const locale = resolvedParams.locale as Locale;
  
  return {
    title: 'FAQ - JAVFLIX.TV',
    description: 'Frequently asked questions about JAVFLIX.TV platform and services',
  };
}

export default async function FAQPage({ params }: FAQPageProps) {
  const resolvedParams = await params;
  const locale = resolvedParams.locale as Locale;
  
  if (!locale || !locales.includes(locale)) {
    notFound();
  }

  // 根据语言确定内容
  const getContent = () => {
    switch (locale) {
      case 'zh':
        return {
          title: '常見問題',
          subtitle: 'JAVFLIX.TV 相關問題解答',
          sections: [
            {
              title: '一般問題',
              questions: [
                {
                  q: 'JAVFLIX.TV 是免費的嗎？',
                  a: '是的，JAVFLIX.TV 是完全免費的平台。您無需註冊或登入即可觀看內容。'
                },
                {
                  q: '我需要註冊嗎？',
                  a: '註冊不是必需的，但註冊後您可以使用收藏功能、觀看歷史、個人化推薦等功能。'
                },
                {
                  q: '支援哪些影片品質？',
                  a: '我們支援從標清(SD)到4K超高清的各種解析度。您可以根據設備和網路狀況選擇最適合的品質。'
                }
              ]
            },
            {
              title: '技術問題',
              questions: [
                {
                  q: '影片無法播放怎麼辦？',
                  a: '請先確認網路連線穩定，然後嘗試清除瀏覽器快取或使用其他瀏覽器。如問題持續，請聯繫客服。'
                },
                {
                  q: '支援哪些瀏覽器？',
                  a: '我們支援 Chrome、Firefox、Safari、Edge 的最新版本。建議更新到最新版本以獲得最佳效能。'
                },
                {
                  q: '可以在行動裝置上使用嗎？',
                  a: '是的，JAVFLIX.TV 採用響應式設計，在智慧手機和平板電腦上都能完美運作。'
                }
              ]
            }
          ],
          contact: {
            title: '其他問題',
            content: '如果您在這裡找不到答案，請隨時透過 <EMAIL> 聯繫我們，我們會在24小時內回覆。'
          }
        };
      case 'en':
        return {
          title: 'Frequently Asked Questions',
          subtitle: 'Common questions about JAVFLIX.TV',
          sections: [
            {
              title: 'General Questions',
              questions: [
                {
                  q: 'Is JAVFLIX.TV free to use?',
                  a: 'Yes, JAVFLIX.TV is a completely free platform. You can watch content without registration or login.'
                },
                {
                  q: 'Do I need to register?',
                  a: 'Registration is not required, but we recommend it to access features like favorites, watch history, and personalized recommendations.'
                },
                {
                  q: 'What video qualities are supported?',
                  a: 'We support various resolutions from Standard Definition (SD) to 4K Ultra HD. Choose the best quality based on your device and network.'
                }
              ]
            },
            {
              title: 'Technical Issues',
              questions: [
                {
                  q: 'Videos won\'t play, what should I do?',
                  a: 'First, ensure your internet connection is stable. Then try clearing browser cache or using a different browser. Contact support if issues persist.'
                },
                {
                  q: 'Which browsers are supported?',
                  a: 'We support the latest versions of Chrome, Firefox, Safari, and Edge. We recommend updating to the latest version for best performance.'
                },
                {
                  q: 'Can I use it on mobile devices?',
                  a: 'Yes, JAVFLIX.TV is fully responsive and works perfectly on smartphones and tablets.'
                }
              ]
            }
          ],
          contact: {
            title: 'Other Questions',
            content: 'If you can\'t find your answer here, feel free to contact <NAME_EMAIL> and we\'ll respond within 24 hours.'
          }
        };
      case 'ja':
        return {
          title: 'よくある質問',
          subtitle: 'JAVFLIX.TVに関するよくある質問',
          sections: [
            {
              title: '一般的な質問',
              questions: [
                {
                  q: 'JAVFLIX.TVは無料で使用できますか？',
                  a: 'はい、JAVFLIX.TVは完全に無料のプラットフォームです。登録やログインは必要ありません。'
                },
                {
                  q: '登録する必要がありますか？',
                  a: '登録は必須ではありませんが、お気に入り機能、視聴履歴、パーソナライズ推薦などの機能を利用するには登録をお勧めします。'
                },
                {
                  q: 'どのような動画品質がサポートされていますか？',
                  a: '標準画質（SD）から4K超高画質まで、さまざまな解像度をサポートしています。お使いのデバイスとネットワークに応じて最適な品質を選択してください。'
                }
              ]
            },
            {
              title: '技術的な問題',
              questions: [
                {
                  q: '動画が再生されません。どうすればよいですか？',
                  a: 'まず、インターネット接続が安定していることを確認してください。次に、ブラウザキャッシュをクリアするか、別のブラウザを試してください。問題が続く場合は、サポートチームにお問い合わせください。'
                },
                {
                  q: 'サポートされているブラウザは何ですか？',
                  a: 'Chrome、Firefox、Safari、Edgeの最新バージョンをサポートしています。最高のパフォーマンスのために、ブラウザを最新バージョンに更新することをお勧めします。'
                },
                {
                  q: 'モバイルデバイスで使用できますか？',
                  a: 'はい、JAVFLIX.TVは完全にレスポンシブデザインで、スマートフォンやタブレットで完璧に動作します。'
                }
              ]
            }
          ],
          contact: {
            title: 'その他のご質問',
            content: 'ここで回答が見つからない場合は、******************までお気軽にお問い合わせください。24時間以内に返信いたします。'
          }
        };
      default:
        return {
          title: 'FAQ',
          subtitle: 'Frequently Asked Questions',
          sections: [],
          contact: { title: 'Contact', content: 'Contact us for more information.' }
        };
    }
  };

  const content = getContent();

  return (
    <div className="min-h-screen bg-gray-900 text-white">
      <div className="container mx-auto px-4 py-8 max-w-4xl">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl md:text-5xl font-bold mb-4 bg-gradient-to-r from-red-500 to-red-700 bg-clip-text text-transparent">
            {content.title}
          </h1>
          <p className="text-gray-400 text-sm">
            {content.subtitle}
          </p>
        </div>

        <div className="space-y-8">
          {content.sections.map((section, sectionIndex) => (
            <section key={sectionIndex} className="bg-gray-800 rounded-lg p-6">
              <h2 className="text-2xl font-bold mb-6 text-red-500">
                {section.title}
              </h2>
              <div className="space-y-4">
                {section.questions.map((item, itemIndex) => (
                  <div key={itemIndex} className="border-b border-gray-700 pb-4 last:border-b-0">
                    <h3 className="text-lg font-semibold mb-2 text-white">
                      Q: {item.q}
                    </h3>
                    <p className="text-gray-300 leading-relaxed">
                      A: {item.a}
                    </p>
                  </div>
                ))}
              </div>
            </section>
          ))}

          {/* Contact Section */}
          <section className="bg-gray-800 rounded-lg p-6 text-center">
            <h2 className="text-2xl font-bold mb-4 text-red-500">
              {content.contact.title}
            </h2>
            <p className="text-gray-300 leading-relaxed">
              {content.contact.content}
            </p>
          </section>
        </div>
      </div>
    </div>
  );
} 