'use client';

import React, { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import Link from 'next/link';
import Image from 'next/image';
import { FiUser, FiVideo, FiCalendar, FiArrowLeft } from 'react-icons/fi';

interface Movie {
  id: string;
  movie_id: string;
  title: string;
  image_url: string;
  release_date: string;
  duration: string;
}

interface DirectorData {
  name: string;
  movies: Movie[];
  totalMovies: number;
}

export default function DirectorPage() {
  const params = useParams();
  const locale = params.locale as string;
  const directorName = decodeURIComponent(params.name as string);
  
  const [directorData, setDirectorData] = useState<DirectorData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchDirectorData = async () => {
      try {
        setLoading(true);
        const response = await fetch(`/api/director/${encodeURIComponent(directorName)}`);
        
        if (!response.ok) {
          throw new Error('导演信息获取失败');
        }
        
        const data = await response.json();
        setDirectorData(data);
      } catch (err) {
        setError(err instanceof Error ? err.message : '获取导演信息时出错');
      } finally {
        setLoading(false);
      }
    };

    if (directorName) {
      fetchDirectorData();
    }
  }, [directorName]);

  if (loading) {
    return (
      <div className="container mx-auto p-6 text-white min-h-screen bg-gray-900">
        <div className="flex items-center justify-center min-h-[50vh]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-red-500 mx-auto mb-4"></div>
            <p className="text-gray-300">正在加载导演信息...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error || !directorData) {
    return (
      <div className="container mx-auto p-6 text-white min-h-screen bg-gray-900">
        <div className="flex items-center justify-center min-h-[50vh]">
          <div className="text-center">
            <FiUser size={48} className="text-red-500 mb-4 mx-auto" />
            <h2 className="text-xl font-bold mb-2">导演信息获取失败</h2>
            <p className="text-gray-400 mb-6">{error || '导演不存在或暂无作品'}</p>
            <Link 
              href={`/${locale}`} 
              className="bg-red-600 hover:bg-red-700 text-white px-6 py-2 rounded-lg transition-colors"
            >
              返回首页
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 text-white min-h-screen bg-gray-900">
      <div className="max-w-7xl mx-auto">
        
        {/* 返回按钮 */}
        <div className="mb-6">
          <Link 
            href={`/${locale}`}
            className="inline-flex items-center gap-2 text-gray-400 hover:text-white transition-colors"
          >
            <FiArrowLeft />
            返回首页
          </Link>
        </div>

        {/* 导演信息头部 */}
        <div className="bg-gray-800 rounded-lg p-6 mb-8">
          <div className="flex items-center gap-4 mb-4">
            <div className="bg-red-600 p-3 rounded-full">
              <FiUser size={32} className="text-white" />
            </div>
            <div>
              <h1 className="text-3xl font-bold text-white mb-2">{directorData.name}</h1>
              <div className="flex items-center gap-4 text-gray-300">
                <div className="flex items-center gap-2">
                  <FiVideo className="text-blue-400" />
                  <span>{directorData.totalMovies} 部作品</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* 作品列表 */}
        <div className="mb-8">
          <h2 className="text-2xl font-bold text-white mb-6 flex items-center gap-2">
            <FiVideo className="text-red-400" />
            导演作品
          </h2>
          
          {directorData.movies.length > 0 ? (
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-6">
              {directorData.movies.map((movie) => (
                <Link
                  key={movie.id}
                  href={`/${locale}/video/${movie.movie_id}?code=${movie.movie_id}`}
                  className="group bg-gray-800 rounded-lg overflow-hidden hover:bg-gray-700 transition-colors"
                >
                  <div className="relative aspect-[3/4]">
                    <Image
                      src={movie.image_url || '/images/placeholder.jpg'}
                      alt={movie.title}
                      fill
                      className="object-cover group-hover:scale-105 transition-transform duration-300"
                      loading="lazy"
                    />
                    <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-300" />
                  </div>
                  
                  <div className="p-4">
                    <h3 className="text-white font-medium text-sm mb-2 line-clamp-2 group-hover:text-red-400 transition-colors">
                      {movie.title}
                    </h3>
                    
                    <div className="space-y-1 text-xs text-gray-400">
                      <div className="flex items-center gap-1">
                        <span className="bg-red-600 text-white px-2 py-1 rounded text-xs font-mono">
                          {movie.movie_id}
                        </span>
                      </div>
                      
                      {movie.release_date && (
                        <div className="flex items-center gap-1">
                          <FiCalendar size={12} />
                          <span>{new Date(movie.release_date).toLocaleDateString('zh-CN')}</span>
                        </div>
                      )}
                      
                      {movie.duration && (
                        <div className="text-gray-500">
                          时长: {movie.duration}
                        </div>
                      )}
                    </div>
                  </div>
                </Link>
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <FiVideo className="mx-auto text-gray-500 mb-4" size={48} />
              <p className="text-gray-400">该导演暂无作品</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
} 