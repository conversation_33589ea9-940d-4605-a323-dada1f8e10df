import { Metadata } from 'next';
import { getTranslations, locales } from '@/i18n';
import { Locale } from '@/i18n/types';
import { notFound } from 'next/navigation';

interface TermsPageProps {
  params: Promise<{ locale: string }>;
}

export async function generateMetadata({
  params
}: TermsPageProps): Promise<Metadata> {
  const resolvedParams = await params;
  const locale = resolvedParams.locale as Locale;
  
  if (!locale || !locales.includes(locale)) {
    return {
      title: 'Terms of Service',
      description: 'JAVFLIX.TV Terms of Service',
    };
  }
  
  const t = await getTranslations(locale);
  
  return {
    title: t.pages.terms.title,
    description: t.pages.terms.description,
  };
}

export default async function TermsPage({ params }: TermsPageProps) {
  const resolvedParams = await params;
  const locale = resolvedParams.locale as Locale;
  
  if (!locale || !locales.includes(locale)) {
    notFound();
  }
  
  const t = await getTranslations(locale);

  return (
    <div className="min-h-screen bg-gray-900 text-white">
      <div className="container mx-auto px-4 py-8 max-w-4xl">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl md:text-5xl font-bold mb-4 bg-gradient-to-r from-red-500 to-red-700 bg-clip-text text-transparent">
            {t.terms.title}
          </h1>
          <p className="text-gray-400 text-sm">
            {t.terms.lastUpdated}
          </p>
        </div>

        <div className="space-y-8">
          {/* Introduction */}
          <section className="bg-gray-800 rounded-lg p-6">
            <h2 className="text-2xl font-bold mb-4 text-red-500">
              {t.terms.intro.title}
            </h2>
            <p className="text-gray-300 leading-relaxed">
              {t.terms.intro.content}
            </p>
          </section>

          {/* Acceptance */}
          <section className="bg-gray-800 rounded-lg p-6">
            <h2 className="text-2xl font-bold mb-4 text-red-500">
              {t.terms.acceptance.title}
            </h2>
            <p className="text-gray-300 leading-relaxed">
              {t.terms.acceptance.content}
            </p>
          </section>

          {/* Eligibility */}
          <section className="bg-gray-800 rounded-lg p-6">
            <h2 className="text-2xl font-bold mb-4 text-red-500">
              {t.terms.eligibility.title}
            </h2>
            <p className="text-gray-300 leading-relaxed">
              {t.terms.eligibility.content}
            </p>
          </section>

          {/* Usage Guidelines */}
          <section className="bg-gray-800 rounded-lg p-6">
            <h2 className="text-2xl font-bold mb-4 text-red-500">
              {t.terms.useGuidelines.title}
            </h2>
            <p className="text-gray-300 mb-4">
              {t.terms.useGuidelines.subtitle}
            </p>
            <ul className="space-y-2 text-gray-300">
              <li className="flex items-start">
                <span className="text-red-400 mr-2">•</span>
                {t.terms.useGuidelines.item1}
              </li>
              <li className="flex items-start">
                <span className="text-red-400 mr-2">•</span>
                {t.terms.useGuidelines.item2}
              </li>
              <li className="flex items-start">
                <span className="text-red-400 mr-2">•</span>
                {t.terms.useGuidelines.item3}
              </li>
              <li className="flex items-start">
                <span className="text-red-400 mr-2">•</span>
                {t.terms.useGuidelines.item4}
              </li>
              <li className="flex items-start">
                <span className="text-red-400 mr-2">•</span>
                {t.terms.useGuidelines.item5}
              </li>
            </ul>
          </section>

          {/* Content Policy */}
          <section className="bg-gray-800 rounded-lg p-6">
            <h2 className="text-2xl font-bold mb-4 text-red-500">
              {t.terms.content.title}
            </h2>
            <p className="text-gray-300 leading-relaxed">
              {t.terms.content.content}
            </p>
          </section>

          {/* Account Responsibility */}
          <section className="bg-gray-800 rounded-lg p-6">
            <h2 className="text-2xl font-bold mb-4 text-red-500">
              {t.terms.account.title}
            </h2>
            <p className="text-gray-300 leading-relaxed">
              {t.terms.account.content}
            </p>
          </section>

          {/* Service Termination */}
          <section className="bg-gray-800 rounded-lg p-6">
            <h2 className="text-2xl font-bold mb-4 text-red-500">
              {t.terms.termination.title}
            </h2>
            <p className="text-gray-300 leading-relaxed">
              {t.terms.termination.content}
            </p>
          </section>

          {/* Disclaimer */}
          <section className="bg-gray-800 rounded-lg p-6">
            <h2 className="text-2xl font-bold mb-4 text-red-500">
              {t.terms.disclaimer.title}
            </h2>
            <p className="text-gray-300 leading-relaxed">
              {t.terms.disclaimer.content}
            </p>
          </section>

          {/* Limitation of Liability */}
          <section className="bg-gray-800 rounded-lg p-6">
            <h2 className="text-2xl font-bold mb-4 text-red-500">
              {t.terms.limitation.title}
            </h2>
            <p className="text-gray-300 leading-relaxed">
              {t.terms.limitation.content}
            </p>
          </section>

          {/* Governing Law */}
          <section className="bg-gray-800 rounded-lg p-6">
            <h2 className="text-2xl font-bold mb-4 text-red-500">
              {t.terms.governing.title}
            </h2>
            <p className="text-gray-300 leading-relaxed">
              {t.terms.governing.content}
            </p>
          </section>

          {/* Changes to Terms */}
          <section className="bg-gray-800 rounded-lg p-6">
            <h2 className="text-2xl font-bold mb-4 text-red-500">
              {t.terms.changes.title}
            </h2>
            <p className="text-gray-300 leading-relaxed">
              {t.terms.changes.content}
            </p>
          </section>

          {/* Contact */}
          <section className="bg-gray-800 rounded-lg p-6 text-center">
            <h2 className="text-2xl font-bold mb-4 text-red-500">
              {t.terms.contact.title}
            </h2>
            <p className="text-gray-300">
              {t.terms.contact.content}
            </p>
          </section>
        </div>
      </div>
    </div>
  );
} 