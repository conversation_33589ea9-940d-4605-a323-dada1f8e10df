import type { Metadata } from 'next';
import { generateMetadata as createMetadata } from '@/lib/seo';
import { Suspense } from 'react';
import NewClient from '@/app/new/NewClient';
import { getTranslations, locales } from '@/i18n';
import type { Locale } from '@/i18n/types';
import { notFound } from 'next/navigation';
import { RealtimeStatsProvider } from '@/contexts/RealtimeStatsContext';

// Route Segment Config
export const dynamic = 'force-dynamic';

// 为语言路由生成静态路径参数
export async function generateStaticParams() {
  return locales.map(locale => ({ locale }));
}

// 生成该语言的元数据
export async function generateMetadata(
  { params }: { params: Promise<{ locale: string }> }
): Promise<Metadata> {
  // 获取locale参数
  const resolvedParams = await params;
  const locale = resolvedParams.locale;
  
  // 默认元数据内容
  const defaultMetadata = {
    title: '最新上架视频',
    description: '浏览JAVFLIX最新上架的高清日本AV视频，每日更新，提供中文字幕、无码有码高清资源',
    path: locale ? `/${locale}/new` : '/new',
    keywords: [
      '最新AV', '日本AV', '在线观看', '高清无码', '中文字幕', 
      '每日更新', '新片', 'JAV', '成人视频'
    ],
  };
  
  // 验证locale是否是有效的支持语言
  if (!locale || !locales.includes(locale as Locale)) {
    return createMetadata(defaultMetadata);
  }
  
  try {
    // 获取该语言的翻译
    const translations = await getTranslations(locale as Locale);
    
    // 安全检查translations.pages.new是否存在
    if (translations?.pages?.new) {
      return createMetadata({
        title: translations.pages.new.title || defaultMetadata.title,
        description: translations.pages.new.description || defaultMetadata.description,
        path: `/${locale}/new`,
        keywords: defaultMetadata.keywords,
      });
    }
  } catch (error) {
    console.error('Failed to load translations:', error);
  }
  
  // 如果翻译不存在或出错，使用默认值
  return createMetadata(defaultMetadata);
}

// 加载占位符组件
function NewLoading() {
  return (
    <div className="min-h-screen bg-gray-900 text-white pt-20 pb-12">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <div className="h-12 bg-gray-800 rounded-lg animate-pulse mb-4 mx-auto max-w-md"></div>
          <div className="h-6 bg-gray-700 rounded-lg animate-pulse mx-auto max-w-sm"></div>
        </div>
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-6">
          {[...Array(20)].map((_, i) => (
            <div key={i} className="bg-gray-800 rounded-lg p-4 animate-pulse">
              <div className="h-48 bg-gray-700 rounded-lg mb-4"></div>
              <div className="h-4 bg-gray-700 rounded mb-2"></div>
              <div className="h-3 bg-gray-600 rounded w-3/4"></div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}

// 主页面是服务器组件，只用于包装客户端组件
export default async function NewPage({ 
  params 
}: { 
  params: Promise<{ locale: string }> 
}) {
  // 获取locale参数
  const resolvedParams = await params;
  const locale = resolvedParams.locale as Locale;
  
  // 验证locale是否支持，否则显示404
  if (!locale || !locales.includes(locale)) {
    notFound();
  }
  
  try {
    // 异步获取该语言的翻译
    await getTranslations(locale);
  } catch (error) {
    console.error('Failed to load translations in NewPage:', error);
  }
  
  return (
    <RealtimeStatsProvider>
      <Suspense fallback={<NewLoading />}>
        <NewClient />
      </Suspense>
    </RealtimeStatsProvider>
  );
} 