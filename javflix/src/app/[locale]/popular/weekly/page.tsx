import { Suspense } from 'react';
import WeeklyPopularClient from './WeeklyPopularClient';
import { Locale } from '@/i18n/types';
import { getTranslations, locales } from '@/i18n';
import { notFound } from 'next/navigation';
import { generateMetadata as baseGenerateMetadata } from '@/lib/seo';
import type { Metadata } from 'next';

// 为语言路由生成静态路径参数
export async function generateStaticParams() {
  return locales.map(locale => ({ locale }));
}

// 生成该语言的元数据
export async function generateMetadata(
  { params }: { params: Promise<{ locale: string }> }
): Promise<Metadata> {
  // 获取locale参数
  const resolvedParams = await params;
  const locale = resolvedParams.locale;
  
  // 验证locale是否是有效的支持语言
  if (!locale || !locales.includes(locale as Locale)) {
    return baseGenerateMetadata({
      title: '每周热门视频',
      description: '查看本周最受欢迎、观看量最高的视频，紧跟热门趋势',
      path: '/popular/weekly',
      keywords: ['热门视频', '每周热门', '热门排行', '视频排行榜', '最新热门']
    });
  }
  
  try {
    // 获取该语言的翻译
    const translations = await getTranslations(locale as Locale);
    
    // 使用翻译的站点标题和描述
    return baseGenerateMetadata({
      title: `${translations.common?.weekly || '每周'}${translations.nav?.popular || '热门'} - ${translations.site?.title || 'JAVFLIX.TV'}`,
      description: `查看本周最受欢迎、观看量最高的视频，紧跟热门趋势。${translations.site?.description || ''}`,
      path: `/${locale}/popular/weekly`,
      keywords: ['热门视频', '每周热门', '热门排行', '视频排行榜', '最新热门']
    });
  } catch (error) {
    console.error('Failed to load translations:', error);
    return baseGenerateMetadata({
      title: '每周热门视频',
      description: '查看本周最受欢迎、观看量最高的视频，紧跟热门趋势',
      path: `/${locale}/popular/weekly`,
      keywords: ['热门视频', '每周热门', '热门排行', '视频排行榜', '最新热门']
    });
  }
}

// 主页面组件
export default async function WeeklyPopularPage({ 
  params 
}: { 
  params: Promise<{ locale: string }> 
}) {
  // 获取locale参数
  const resolvedParams = await params;
  const locale = resolvedParams.locale as Locale;
  
  // 检验locale是否支持，否则显示404
  if (!locale || !locales.includes(locale)) {
    notFound();
  }

  try {
    // 获取该语言的翻译
    await getTranslations(locale);
  } catch (error) {
    console.error('Failed to load translations in WeeklyPopularPage:', error);
  }

  return (
    <Suspense fallback={<div className="h-screen flex items-center justify-center">Loading...</div>}>
      <WeeklyPopularClient locale={locale} />
    </Suspense>
  );
} 