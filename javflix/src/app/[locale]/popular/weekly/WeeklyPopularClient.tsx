'use client';

import { useEffect, useState, useCallback } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { FiTrendingUp, FiPlay, FiClock, FiEye, FiStar, FiCalendar } from 'react-icons/fi';
import { useClientTranslations } from '@/components/TranslationsProvider';
import type { Locale } from '@/i18n/types';

// 视频数据类型
interface Video {
  id: number;
  title: string;
  movie_id?: string;
  image_url?: string;
  cached_image_url?: string;
  duration?: number;
  view_count?: number;
  rating?: number;
  release_date?: string;
  stars?: Array<{ id: number; name: string }>;
  description?: string;
}

// API响应类型
interface VideoAPIResponse {
  success: boolean;
  data: {
    items: Video[];
    total: number;
    page: number;
    limit: number;
    total_pages: number;
  };
  message?: string;
}

interface WeeklyPopularClientProps {
  locale: Locale;
}

const WeeklyPopularClient = ({ locale }: WeeklyPopularClientProps) => {
  const { t } = useClientTranslations();
  
  // 状态管理
  const [videos, setVideos] = useState<Video[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalCount, setTotalCount] = useState(0);
  
  const itemsPerPage = 20;

  // 获取每周热门视频数据
  const fetchWeeklyPopularVideos = useCallback(async (page: number = 1) => {
    try {
      setLoading(true);
      setError(null);

      console.log('正在获取每周热门视频数据，页面:', page);

      // 使用数据库API获取热门视频，按周筛选
      // 首先尝试获取最近一周的热门视频
      const weekAgo = new Date();
      weekAgo.setDate(weekAgo.getDate() - 7);
      const weekAgoStr = weekAgo.toISOString().split('T')[0];

      let response;
      
      // 尝试按观看次数排序获取最近的热门视频
      response = await fetch(`/api/db/movies?page=${page}&limit=${itemsPerPage}&sort=view_count&order=desc&date_from=${weekAgoStr}`);
      
      if (!response.ok) {
        // 如果日期筛选不支持，则获取所有热门视频
        console.log('日期筛选不支持，获取所有热门视频');
        response = await fetch(`/api/db/movies?page=${page}&limit=${itemsPerPage}&sort=view_count&order=desc`);
      }
      
      if (!response.ok) {
        // 如果数据库API不可用，尝试使用热门视频API
        console.log('数据库API不可用，尝试热门视频API');
        response = await fetch(`/api/db/movies/popular?limit=${itemsPerPage}`);
      }

      if (response.ok) {
        const result: VideoAPIResponse = await response.json();
        console.log('每周热门视频API响应:', result);
        
        if (result.success && result.data && Array.isArray(result.data.items)) {
          setVideos(result.data.items);
          setTotalCount(result.data.total || result.data.items.length);
          setTotalPages(result.data.total_pages || Math.ceil((result.data.total || result.data.items.length) / itemsPerPage));
          console.log('成功获取每周热门视频:', result.data.items.length, '个');
        } else {
          throw new Error('API响应格式错误');
        }
      } else {
        throw new Error(`API请求失败: ${response.status}`);
      }

    } catch (error) {
      console.error('获取每周热门视频失败:', error);
      setError('无法加载每周热门视频数据');
      
      // 设置空数据避免界面崩溃
      setVideos([]);
      setTotalCount(0);
      setTotalPages(1);
    } finally {
      setLoading(false);
    }
  }, []);

  // 初始化数据
  useEffect(() => {
    fetchWeeklyPopularVideos(currentPage);
  }, [fetchWeeklyPopularVideos, currentPage]);

  // 获取视频图片URL
  const getVideoImageUrl = (video: Video): string => {
    if (video.cached_image_url) {
      return video.cached_image_url;
    }
    if (video.image_url) {
      return video.image_url;
    }
    return '/images/defaults/video_default.jpg';
  };

  // 获取视频详情页链接
  const getVideoLink = (video: Video): string => {
    const slug = video.movie_id || `video-${video.id}`;
    return `/${locale}/video/${slug}`;
  };

  // 格式化时长
  const formatDuration = (minutes?: number): string => {
    if (!minutes) return '未知';
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return `${hours}:${mins.toString().padStart(2, '0')}:00`;
  };

  // 格式化观看次数
  const formatViewCount = (count?: number): string => {
    if (!count) return '0';
    if (count >= 1000000) {
      return `${(count / 1000000).toFixed(1)}M`;
    } else if (count >= 1000) {
      return `${(count / 1000).toFixed(1)}K`;
    }
    return count.toString();
  };

  // 格式化日期
  const formatDate = (dateString?: string): string => {
    if (!dateString) return '未知';
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString('zh-CN');
    } catch {
      return dateString;
    }
  };

  // 处理页面变化
  const handlePageChange = (newPage: number) => {
    if (newPage < 1 || newPage > totalPages) return;
    setCurrentPage(newPage);
    // 滚动到顶部
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  // 渲染分页控件
  const renderPagination = () => {
    if (totalPages <= 1) return null;

    const showPages = 5; // 显示的页码数量
    const startPage = Math.max(1, currentPage - Math.floor(showPages / 2));
    const endPage = Math.min(totalPages, startPage + showPages - 1);
    const pages = Array.from({ length: endPage - startPage + 1 }, (_, index) => startPage + index);

    return (
      <div className="flex justify-center items-center space-x-2 mt-8">
        {/* 上一页 */}
        <button
          onClick={() => handlePageChange(currentPage - 1)}
          disabled={currentPage === 1}
          className="px-3 py-2 bg-gray-800 text-white rounded disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-700 transition-colors"
        >
          上一页
        </button>

        {/* 页码 */}
        {pages.map(page => (
          <button
            key={page}
            onClick={() => handlePageChange(page)}
            className={`px-3 py-2 rounded transition-colors ${
              page === currentPage
                ? 'bg-blue-600 text-white'
                : 'bg-gray-800 text-white hover:bg-gray-700'
            }`}
          >
            {page}
          </button>
        ))}

        {/* 下一页 */}
        <button
          onClick={() => handlePageChange(currentPage + 1)}
          disabled={currentPage === totalPages}
          className="px-3 py-2 bg-gray-800 text-white rounded disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-700 transition-colors"
        >
          下一页
        </button>
      </div>
    );
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-black text-white">
        <div className="container mx-auto px-4 py-8">
          <div className="h-screen flex items-center justify-center">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-white mx-auto mb-4"></div>
              <p>加载每周热门视频中...</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-black text-white">
        <div className="container mx-auto px-4 py-8">
          <div className="text-center py-12">
            <p className="text-red-400 mb-4">{error}</p>
            <button
              onClick={() => fetchWeeklyPopularVideos(currentPage)}
              className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
            >
              重试
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-black text-white">
      <div className="container mx-auto px-4 py-8">
        {/* 页面标题 */}
        <div className="mb-8">
          <div className="flex items-center mb-4">
            <FiTrendingUp className="text-red-500 text-2xl mr-3" />
            <h1 className="text-3xl font-bold">
              {t('common.weekly')}
            </h1>
          </div>
          <p className="text-gray-400">
            {t('common.discoveredContent')}
          </p>
          {totalCount > 0 && (
            <p className="text-sm text-gray-500 mt-2">
              {t('common.discoveredVideos', { count: totalCount })}
            </p>
          )}
        </div>

        {/* 视频列表 */}
        {videos.length > 0 ? (
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-6">
            {videos.map((video, index) => (
              <div key={video.id} className="group">
                <Link href={getVideoLink(video)}>
                  <div className="relative overflow-hidden rounded-lg bg-gray-900 transition-transform duration-300 group-hover:scale-105">
                    {/* 排名标识 */}
                    <div className="absolute top-2 left-2 z-10">
                      <div className={`px-2 py-1 rounded text-xs font-bold ${
                        index < 3 
                          ? 'bg-gradient-to-r from-yellow-400 to-orange-500 text-black'
                          : 'bg-black bg-opacity-70 text-white'
                      }`}>
                        #{index + 1 + (currentPage - 1) * itemsPerPage}
                      </div>
                    </div>

                    {/* 视频封面 */}
                    <div className="aspect-[3/4] relative">
                      <Image
                        src={getVideoImageUrl(video)}
                        alt={video.title}
                        fill
                        className="object-cover transition-transform duration-300 group-hover:scale-110"
                        sizes="(max-width: 640px) 100vw, (max-width: 768px) 50vw, (max-width: 1024px) 33vw, (max-width: 1280px) 25vw, 20vw"
                      />
                      
                      {/* 播放按钮 */}
                      <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-300 flex items-center justify-center">
                        <FiPlay className="text-white text-4xl opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                      </div>

                      {/* 时长 */}
                      {video.duration && (
                        <div className="absolute bottom-2 right-2 bg-black bg-opacity-70 text-white px-2 py-1 rounded text-xs">
                          <FiClock className="inline w-3 h-3 mr-1" />
                          {formatDuration(video.duration)}
                        </div>
                      )}
                    </div>

                    {/* 视频信息 */}
                    <div className="p-3">
                      <h3 className="text-sm font-medium line-clamp-2 mb-2 group-hover:text-blue-400 transition-colors">
                        {video.title}
                      </h3>
                      
                      <div className="flex items-center justify-between text-xs text-gray-400 mb-2">
                        <div className="flex items-center">
                          <FiEye className="w-3 h-3 mr-1" />
                          <span>{formatViewCount(video.view_count)}</span>
                        </div>
                        {video.rating && (
                          <div className="flex items-center">
                            <FiStar className="w-3 h-3 mr-1 text-yellow-400" />
                            <span>{video.rating}%</span>
                          </div>
                        )}
                      </div>

                      <div className="flex items-center text-xs text-gray-500">
                        <FiCalendar className="w-3 h-3 mr-1" />
                        <span>{formatDate(video.release_date)}</span>
                      </div>

                      {/* 演员信息 */}
                      {video.stars && video.stars.length > 0 && (
                        <div className="mt-2 text-xs text-gray-400">
                          <span className="line-clamp-1">
                            {video.stars.map(star => star.name).join(', ')}
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                </Link>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <p className="text-gray-400 text-lg">暂无热门视频数据</p>
          </div>
        )}

        {/* 分页控件 */}
        {renderPagination()}
      </div>
    </div>
  );
};

export default WeeklyPopularClient; 