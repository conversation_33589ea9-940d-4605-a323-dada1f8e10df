'use client';

import { Suspense, useEffect, useRef } from 'react';
import PopularVideos from '@/components/PopularVideos';
import gsap from 'gsap';
import { useClientTranslations } from '@/components/TranslationsProvider';
import { Locale } from '@/i18n/types';
import { FiTrendingUp } from 'react-icons/fi';
import { AiFillFire } from 'react-icons/ai';
import { LoadingSpinner, VideoGridSkeleton } from '@/components/loading';
import { useGSAPPageAnimation } from '@/hooks/animations';

interface PopularClientProps {
  locale: Locale;
}

export default function PopularClient({ locale }: PopularClientProps) {
  const { t } = useClientTranslations();
  
  const { titleRef, subtitleRef, contentRef, animatePageEntrance } = useGSAPPageAnimation();
  const backgroundRef = useRef<HTMLDivElement>(null);
  
  useEffect(() => {
    // 背景动画
    gsap.fromTo(
      backgroundRef.current,
      { scale: 0.8, opacity: 0 },
      { scale: 1, opacity: 1, duration: 1.2, ease: 'power2.out' }
    );
    
    // 使用统一的页面动画
    animatePageEntrance();
  }, [animatePageEntrance]);
  
  return (
    <main className="min-h-screen relative overflow-hidden bg-gradient-to-br from-gray-900 via-black to-gray-800 text-white pt-20 pb-12">
      {/* 现代化背景装饰 - 参考2025设计趋势 */}
      <div ref={backgroundRef} className="absolute inset-0 pointer-events-none">
        <div className="absolute top-1/4 right-1/4 w-72 h-72 bg-gradient-to-br from-red-500/15 to-orange-500/10 rounded-full filter blur-3xl"></div>
        <div className="absolute bottom-1/3 left-1/3 w-96 h-96 bg-gradient-to-tr from-purple-500/12 to-pink-500/8 rounded-full filter blur-3xl"></div>
        <div className="absolute top-1/2 right-1/2 w-64 h-64 bg-gradient-to-bl from-blue-500/10 to-cyan-500/8 rounded-full filter blur-2xl"></div>
      </div>

      <div className="relative container mx-auto px-4">
        {/* 现代化标题区域 - 参考2025年Bold字体趋势 */}
        <div className="text-center mb-16">
          <div className="relative">
            <h1 
              ref={titleRef}
              className="text-5xl md:text-7xl font-bold mb-6 text-white leading-tight"
            >
              {t('nav.popular') || '热门视频'}
            </h1>
          </div>
          
          <p className="text-xl text-gray-300 font-light mt-8">
            {t('common.discoveredContent')}
          </p>
        </div>
        
        {/* 现代化内容区域 */}
        <div ref={contentRef}>
          <Suspense fallback={
            <div className="space-y-8">
              <div className="flex justify-center items-center min-h-[200px] mb-8">
                <LoadingSpinner 
                  size="large"
                  text={t('common.loading') || '正在加载热门内容...'}
                />
              </div>
              <VideoGridSkeleton 
                count={20}
                gridCols="grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5"
                cardHeight="h-48"
              />
            </div>
          }>
            <PopularVideos locale={locale} />
          </Suspense>
        </div>
      </div>
    </main>
  );
} 