import { Suspense } from 'react';
import PopularClient from './PopularClient';
import { Locale } from '@/i18n/types';
import { getTranslations, locales } from '@/i18n';
import { notFound } from 'next/navigation';
import { generateMetadata as baseGenerateMetadata } from '@/lib/seo';
import type { Metadata } from 'next';
import { RealtimeStatsProvider } from '@/contexts/RealtimeStatsContext';

// Route Segment Config
export const dynamic = 'force-dynamic';

// 为语言路由生成静态路径参数
export async function generateStaticParams() {
  return locales.map(locale => ({ locale }));
}

// 生成该语言的元数据
export async function generateMetadata(
  { params }: { params: Promise<{ locale: string }> }
): Promise<Metadata> {
  // 获取locale参数
  const resolvedParams = await params;
  const locale = resolvedParams.locale;
  
  // 验证locale是否是有效的支持语言
  if (!locale || !locales.includes(locale as Locale)) {
    return baseGenerateMetadata({});
  }
  
  // 获取该语言的翻译
  const translations = await getTranslations(locale as Locale);
  
  // 使用翻译的站点标题和描述
  return baseGenerateMetadata({
    title: `${translations.nav.popular} - ${translations.site.title}`,
    description: translations.pages.popular.description || '查看最受欢迎、点击量最高的视频，一览最火热的内容',
  });
}

// 主页面是服务器组件，只用于包装客户端组件
export default async function PopularPage({ 
  params 
}: { 
  params: Promise<{ locale: string }> 
}) {
  // 获取locale参数
  const resolvedParams = await params;
  const locale = resolvedParams.locale as Locale;
  
  // 检验locale是否支持，否则显示404
  if (!locale || !locales.includes(locale)) {
    notFound();
  }

  return (
    <RealtimeStatsProvider>
      <Suspense fallback={<div className="h-screen flex items-center justify-center">Loading...</div>}>
        <PopularClient locale={locale} />
      </Suspense>
    </RealtimeStatsProvider>
  );
} 