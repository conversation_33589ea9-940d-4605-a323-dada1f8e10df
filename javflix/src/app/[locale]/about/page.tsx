import { Metadata } from 'next';
import { getTranslations, locales } from '@/i18n';
import { Locale } from '@/i18n/types';
import { notFound } from 'next/navigation';

interface AboutPageProps {
  params: Promise<{ locale: string }>;
}

export async function generateMetadata({
  params
}: AboutPageProps): Promise<Metadata> {
  const resolvedParams = await params;
  const locale = resolvedParams.locale as Locale;
  
  if (!locale || !locales.includes(locale)) {
    return {
      title: 'About Us',
      description: 'Learn about JAVFLIX.TV',
    };
  }
  
  const t = await getTranslations(locale);
  
  return {
    title: t.pages.about.title,
    description: t.pages.about.description,
  };
}

export default async function AboutPage({ params }: AboutPageProps) {
  const resolvedParams = await params;
  const locale = resolvedParams.locale as Locale;
  
  if (!locale || !locales.includes(locale)) {
    notFound();
  }
  
  const t = await getTranslations(locale);

  return (
    <div className="min-h-screen bg-gray-900 text-white">
      <div className="container mx-auto px-4 py-8">
        {/* Hero Section */}
        <div className="text-center mb-12">
          <h1 className="text-4xl md:text-6xl font-bold mb-4 bg-gradient-to-r from-red-500 to-red-700 bg-clip-text text-transparent">
            {t.about.heroTitle}
          </h1>
          <p className="text-xl md:text-2xl text-gray-300 max-w-3xl mx-auto">
            {t.about.heroSubtitle}
          </p>
        </div>

        {/* Content Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12">
          {/* Mission */}
          <div className="bg-gray-800 rounded-lg p-6">
            <h2 className="text-2xl font-bold mb-4 text-red-500">
              {t.about.mission.title}
            </h2>
            <p className="text-gray-300 leading-relaxed">
              {t.about.mission.content}
            </p>
          </div>

          {/* Vision */}
          <div className="bg-gray-800 rounded-lg p-6">
            <h2 className="text-2xl font-bold mb-4 text-red-500">
              {t.about.vision.title}
            </h2>
            <p className="text-gray-300 leading-relaxed">
              {t.about.vision.content}
            </p>
          </div>
        </div>

        {/* Features */}
        <div className="mb-12">
          <h2 className="text-3xl font-bold mb-8 text-center">
            {t.about.features.title}
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="bg-gray-800 rounded-lg p-6 text-center hover:bg-gray-700 transition-colors">
              <h3 className="text-lg font-semibold mb-3 text-red-400">
                {t.about.features.feature1.title}
              </h3>
              <p className="text-gray-300 text-sm">
                {t.about.features.feature1.description}
              </p>
            </div>
            <div className="bg-gray-800 rounded-lg p-6 text-center hover:bg-gray-700 transition-colors">
              <h3 className="text-lg font-semibold mb-3 text-red-400">
                {t.about.features.feature2.title}
              </h3>
              <p className="text-gray-300 text-sm">
                {t.about.features.feature2.description}
              </p>
            </div>
            <div className="bg-gray-800 rounded-lg p-6 text-center hover:bg-gray-700 transition-colors">
              <h3 className="text-lg font-semibold mb-3 text-red-400">
                {t.about.features.feature3.title}
              </h3>
              <p className="text-gray-300 text-sm">
                {t.about.features.feature3.description}
              </p>
            </div>
            <div className="bg-gray-800 rounded-lg p-6 text-center hover:bg-gray-700 transition-colors">
              <h3 className="text-lg font-semibold mb-3 text-red-400">
                {t.about.features.feature4.title}
              </h3>
              <p className="text-gray-300 text-sm">
                {t.about.features.feature4.description}
              </p>
            </div>
          </div>
        </div>

        {/* Contact */}
        <div className="bg-gray-800 rounded-lg p-8 text-center">
          <h2 className="text-2xl font-bold mb-4 text-red-500">
            {t.about.contact.title}
          </h2>
          <p className="text-gray-300 mb-4">
            {t.about.contact.content}
          </p>
          <p className="text-red-400 font-medium mb-2">
            {t.about.contact.email}
          </p>
          <p className="text-gray-400 text-sm">
            {t.about.contact.response}
          </p>
        </div>
      </div>
    </div>
  );
} 