import { Suspense } from 'react';
import { Locale } from '@/i18n/types';
import { getTranslations, locales } from '@/i18n';
import { notFound } from 'next/navigation';
import { generateMetadata as baseGenerateMetadata } from '@/lib/seo';
import type { Metadata } from 'next';
import ActressClient from '@/app/actress/ActressClient';

// Route Segment Config
export const dynamic = 'force-dynamic';

// 为语言路由生成静态路径参数
export async function generateStaticParams() {
  return locales.map(locale => ({ locale }));
}

// 生成该语言的元数据
export async function generateMetadata(
  { params }: { params: Promise<{ locale: string }> }
): Promise<Metadata> {
  // 获取locale参数
  const resolvedParams = await params;
  const locale = resolvedParams.locale;
  
  // 验证locale是否是有效的支持语言
  if (!locale || !locales.includes(locale as Locale)) {
    return baseGenerateMetadata({});
  }
  
  // 获取该语言的翻译
  const translations = await getTranslations(locale as Locale);
  
  // 使用翻译的站点标题和描述
  return baseGenerateMetadata({
    title: translations.pages.actress?.title || 'AV女优名鉴',
    description: translations.pages.actress?.description || 'JAVFLIX女优库，收录最全、最新的日本AV女优资料'
  });
}

// 主页面组件
export default async function ActressPage({ 
  params 
}: { 
  params: Promise<{ locale: string }> 
}) {
  // 获取locale参数
  const resolvedParams = await params;
  const locale = resolvedParams.locale as Locale;
  
  // 检验locale是否支持，否则显示404
  if (!locale || !locales.includes(locale)) {
    notFound();
  }

  // 获取该语言的翻译
  await getTranslations(locale);

  return (
    <Suspense fallback={<div className="h-screen flex items-center justify-center">Loading...</div>}>
      <ActressClient />
    </Suspense>
  );
}
