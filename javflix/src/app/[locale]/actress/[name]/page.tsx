'use client';

import React, { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import Link from 'next/link';
import Image from 'next/image';
import { FiUser, FiVideo, FiArrowLeft, FiEye, FiCalendar, FiHeart, FiStar, FiMapPin, FiClock, FiActivity, FiInfo, FiPlay } from 'react-icons/fi';
import { LoadingSpinner } from '@/components/loading';
import { formatDuration } from '@/lib/duration-format';
import { useClientTranslations } from '@/components/TranslationsProvider';

// Helper function for time formatting (copied from previous implementation)
function formatRelativeTime(dateString?: string, t?: any): string {
  if (!dateString) return t?.('common.unknown') || '未知时间';
  
  const date = new Date(dateString);
  const now = new Date();
  const seconds = Math.floor((now.getTime() - date.getTime()) / 1000);
  
  let interval = Math.floor(seconds / 31536000);
  if (interval >= 1) {
    return t?.('profile.time.yearsAgo', { years: interval }) || `${interval}年前`;
  }
  
  interval = Math.floor(seconds / 2592000);
  if (interval >= 1) {
    return t?.('profile.time.monthsAgo', { months: interval }) || `${interval}个月前`;
  }
  
  interval = Math.floor(seconds / 86400);
  if (interval >= 1) {
    return t?.('profile.time.daysAgo', { days: interval }) || `${interval}天前`;
  }
  
  interval = Math.floor(seconds / 3600);
  if (interval >= 1) {
    return t?.('profile.time.hoursAgo', { hours: interval }) || `${interval}小时前`;
  }
  
  interval = Math.floor(seconds / 60);
  if (interval > 1) { 
    return t?.('profile.time.minutesAgo', { minutes: interval }) || `${interval}分钟前`;
  }
  
  return t?.('profile.time.justNow') || '刚刚';
}

interface Movie {
  id: number;
  movie_id: string;
  title: string;
  image_url?: string;
  cached_image_url?: string;
  release_date?: string;
  duration?: string;
  view_count: number;
  genres: Array<{
    id: number;
    name: string;
  }>;
}

interface ActressData {
  id: number;
  star_id: string;
  name: string;
  image_url?: string;
  cached_image_url?: string;
  birthday?: string;
  age?: number;
  height?: number;
  birthplace?: string;
  debut_date?: string;
  hobby?: string;
  description?: string;
  javbus_id?: string;
  bust?: string;
  waist?: string;
  hip?: string;
  bust_size?: string;
  waist_size?: string;
  hip_size?: string;
  cup_size?: string;
  measurements?: string;
  waistline?: string;
  hipline?: string;
  movie_count: number;
  movies: Movie[];
}

// 添加实时统计获取函数
async function getRealtimeStats(videoIds: string[]) {
  try {
    const backendUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:4000';
    const response = await fetch(`${backendUrl}/api/video-stats/fused/batch`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        videoIds: videoIds
      }),
      // 使用较短的超时和无缓存策略获取实时数据
      cache: 'no-store',
      next: { revalidate: 0 }
    });
    
    if (response.ok) {
      const data = await response.json();
      return data.data || {};
    }
  } catch (error) {
    console.warn('获取实时统计失败，使用数据库数据:', error);
  }
  return {};
}

export default function ActressDetailPage() {
  const params = useParams();
  const { t } = useClientTranslations();
  const [actressData, setActressData] = useState<ActressData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [realtimeStats, setRealtimeStats] = useState<Record<string, any>>({});

  useEffect(() => {
    const fetchActressData = async () => {
      try {
        setLoading(true);
        
        const searchResponse = await fetch(`/api/db/stars?search=${encodeURIComponent(params.name as string)}&limit=1`);
        const searchData = await searchResponse.json();
        
        if (!searchData.success || searchData.data.items.length === 0) {
          throw new Error('女优不存在');
        }
        
        const actressId = searchData.data.items[0].id;
        
        const detailResponse = await fetch(`/api/db/stars/${actressId}`);
        const detailData = await detailResponse.json();
        
        if (!detailData.success) {
          throw new Error(detailData.message || '获取女优详情失败');
        }
        
        setActressData(detailData.data);
        
        // 获取作品的实时统计数据
        if (detailData.data.movies && detailData.data.movies.length > 0) {
          const videoIds = detailData.data.movies.map((movie: Movie) => movie.movie_id);
          const statsData = await getRealtimeStats(videoIds);
          setRealtimeStats(statsData);
        }
      } catch (err) {
        console.error('获取女优数据失败:', err);
        setError(err instanceof Error ? err.message : '获取女优信息失败');
      } finally {
        setLoading(false);
      }
    };

    fetchActressData();
  }, [params.name]);

  // 获取融合的观看次数
  const getViewCount = (movie: Movie): number => {
    const stats = realtimeStats[movie.movie_id];
    return stats?.views || movie.view_count || 0;
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-800 flex items-center justify-center">
        {/* 背景装饰 */}
        <div className="absolute inset-0 pointer-events-none">
          <div className="absolute top-1/4 left-1/4 w-64 h-64 bg-red-500/10 rounded-full filter blur-3xl"></div>
          <div className="absolute bottom-1/3 right-1/3 w-96 h-96 bg-blue-500/8 rounded-full filter blur-3xl"></div>
          <div className="absolute top-1/2 left-1/2 w-48 h-48 bg-purple-500/12 rounded-full filter blur-2xl"></div>
        </div>

        <div className="relative text-center">
          <div className="relative">
            <div className="border-4 border-gray-700 border-t-white rounded-full animate-spin mx-auto mb-6 w-20 h-20"></div>
            <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-10 h-10 bg-white/20 rounded-full animate-pulse"></div>
          </div>
          <p className="text-gray-300 font-light text-lg">{t('common.loading')}</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center">
        <div className="text-center max-w-md mx-auto px-4">
          <div className="text-red-400 text-6xl mb-4">⚠</div>
          <h1 className="text-xl font-bold text-white mb-2">{t('actress.loadError')}</h1>
          <p className="text-gray-400 mb-6">{error}</p>
          <Link 
            href={`/${params.locale}/actress`} 
            className="inline-flex items-center gap-2 bg-red-600 hover:bg-red-700 text-white px-6 py-3 rounded-lg transition-colors"
          >
            <FiArrowLeft />
            {t('actress.backToList')}
          </Link>
        </div>
      </div>
    );
  }

  if (!actressData) {
    return null;
  }

  return (
    <div className="min-h-screen bg-black text-white">
      <div className="absolute inset-0 bg-gradient-to-br from-gray-900 via-black to-gray-900">
        <div className="absolute top-20 left-10 w-72 h-72 bg-blue-600/5 rounded-full blur-3xl"></div>
        <div className="absolute bottom-20 right-10 w-96 h-96 bg-purple-600/5 rounded-full blur-3xl"></div>
      </div>
      
      <div className="relative z-10">
        <div className="container mx-auto px-4 pt-8">
          <Link 
            href={`/${params.locale}/actress`} 
            className="inline-flex items-center gap-2 text-gray-400 hover:text-white transition-colors mb-6"
          >
            <FiArrowLeft />
            {t('actress.backToList')}
          </Link>
        </div>

        {/* 紧凑的个人信息区域 */}
        <div className="container mx-auto px-4 mb-8">
          {/* 头部信息 - 水平布局 */}
          <div className="flex items-center justify-center mb-6">
            <div className="flex items-center gap-6">
              {/* 小头像 */}
              <div className="relative w-20 h-20 rounded-full overflow-hidden border border-gray-600/30">
                <Image
                  src={actressData.cached_image_url || actressData.image_url || '/images/defaults/actress_default.jpg'}
                  alt={actressData.name}
                  fill
                  className="object-cover"
                  sizes="80px"
                  onError={(e) => {
                    e.currentTarget.src = '/images/defaults/actress_default.jpg';
                  }}
                />
              </div>
              
              {/* 名字和统计 */}
              <div className="text-center">
                <h1 className="text-2xl font-light text-white mb-3">{actressData.name}</h1>
                <div className="flex items-center gap-8">
                  <div className="text-center">
                    <div className="text-lg font-light text-red-400">{actressData.movie_count}</div>
                    <div className="text-xs text-gray-400">{t('actress.videos')}</div>
                  </div>
                  {actressData.age && (
                    <div className="text-center">
                      <div className="text-lg font-light text-blue-400">{actressData.age}{t('common.ageUnit')}</div>
                      <div className="text-xs text-gray-400">{t('actress.age')}</div>
                    </div>
                  )}
                  {actressData.height && (
                    <div className="text-center">
                      <div className="text-lg font-light text-purple-400">{actressData.height}cm</div>
                      <div className="text-xs text-gray-400">{t('actress.height')}</div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* 个人详细信息 - 两列紧凑布局 */}
          <div className="max-w-4xl mx-auto">
            <div className="grid grid-cols-2 gap-4 text-sm">
              {actressData.birthday && (
                <div className="flex flex-col items-center py-2 border-b border-gray-800/30">
                  <div className="flex items-center text-gray-400 mb-1">
                    <FiCalendar className="w-3 h-3 mr-2" />
                    <span>{t('actress.birthdate')}</span>
                  </div>
                  <div className="text-white">{actressData.birthday}</div>
                </div>
              )}
              
              {actressData.birthplace && (
                <div className="flex flex-col items-center py-2 border-b border-gray-800/30">
                  <div className="flex items-center text-gray-400 mb-1">
                    <FiMapPin className="w-3 h-3 mr-2" />
                    <span>{t('actress.birthplace')}</span>
                  </div>
                  <div className="text-white">{actressData.birthplace}</div>
                </div>
              )}
              
              {actressData.debut_date && (
                <div className="flex flex-col items-center py-2 border-b border-gray-800/30">
                  <div className="flex items-center text-gray-400 mb-1">
                    <FiStar className="w-3 h-3 mr-2" />
                    <span>{t('actress.debutDate')}</span>
                  </div>
                  <div className="text-white">{actressData.debut_date}</div>
                </div>
              )}
              
              {(actressData.bust || actressData.measurements) && (
                <div className="flex flex-col items-center py-2 border-b border-gray-800/30">
                  <div className="flex items-center text-gray-400 mb-1">
                    <FiActivity className="w-3 h-3 mr-2" />
                    <span>{t('actress.measurements')}</span>
                  </div>
                  <div className="text-white text-xs">
                    {actressData.measurements || `${t('actress.bust')}:${actressData.bust||'?'} ${t('actress.waist')}:${actressData.waist||'?'} ${t('actress.hip')}:${actressData.hip||'?'}`}
                  </div>
                </div>
              )}
              
              {actressData.cup_size && (
                <div className="flex flex-col items-center py-2 border-b border-gray-800/30">
                  <div className="flex items-center text-gray-400 mb-1">
                    <FiInfo className="w-3 h-3 mr-2" />
                    <span>{t('actress.cupSize')}</span>
                  </div>
                  <div className="text-white">{actressData.cup_size}</div>
                </div>
              )}
              
              {actressData.hobby && (
                <div className="flex flex-col items-center py-2 border-b border-gray-800/30">
                  <div className="flex items-center text-gray-400 mb-1">
                    <FiHeart className="w-3 h-3 mr-2" />
                    <span>{t('actress.hobby')}</span>
                  </div>
                  <div className="text-white text-xs">{actressData.hobby}</div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* 作品区域 */}
        <div className="container mx-auto px-4 pb-16">
          <div className="mb-6 text-center">
            <h2 className="text-xl font-light text-white mb-2 flex items-center justify-center gap-3">
              <FiVideo className="text-red-400" />
              {t('actress.relatedVideos')}
              <span className="text-sm text-gray-400">({actressData.movies.length})</span>
            </h2>
            <div className="w-12 h-px bg-gradient-to-r from-transparent via-red-400 to-transparent mx-auto mt-2"></div>
          </div>

          {actressData.movies.length > 0 ? (
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-3 xl:grid-cols-4 gap-5">
              {actressData.movies.map((movie) => {
                const viewCount = getViewCount(movie);
                return (
                  <div key={movie.id} className="w-full group relative bg-gradient-to-br from-gray-900/80 to-black/60 backdrop-blur-xl rounded-2xl overflow-hidden transition-all duration-300 hover:scale-[1.03] hover:shadow-2xl hover:shadow-red-500/10">
                    <Link href={`/${params.locale}/video/${movie.movie_id}`} className="block">
                      <div className="relative w-full aspect-video bg-gray-800 overflow-hidden rounded-t-xl group-hover:shadow-xl">
                            <Image
                          src={movie.cached_image_url || movie.image_url || '/images/defaults/default_poster.jpg'}
                              alt={movie.title}
                              fill
                          className="object-cover transition-all duration-500 group-hover:scale-105"
                          sizes="(max-width: 639px) 100vw, (max-width: 767px) 50vw, (max-width: 1023px) 33vw, (max-width: 1279px) 33vw, 25vw"
                              onError={(e) => {
                            e.currentTarget.src = '/images/defaults/default_poster.jpg';
                              }}
                            />
                        <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-transparent to-black/10"></div>
                            {movie.duration && (
                          <div className="absolute bottom-2 right-2 bg-black/70 backdrop-blur-sm text-white px-2 py-0.5 rounded text-xs font-medium">
                                  {formatDuration(movie.duration)}
                              </div>
                            )}
                        <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                          <div className="bg-red-600/80 backdrop-blur-sm rounded-full p-3 shadow-xl">
                            <FiPlay className="w-5 h-5 text-white ml-0.5" />
                          </div>
                        </div>
                      </div>
                    </Link>
                    <div className="p-3 space-y-1.5">
                      <h3 className="font-semibold text-sm text-white line-clamp-2 group-hover:text-red-400 transition-colors duration-200 leading-tight">
                            {movie.title}
                          </h3>
                      <div className="flex items-center text-xs text-gray-400 space-x-3">
                        <div className="flex items-center">
                          <FiEye className="mr-1 text-gray-500" size={14} />
                          <span>{viewCount.toLocaleString()}</span>
                        </div>
                              {movie.release_date && (
                          <div className="flex items-center">
                            <FiCalendar className="mr-1 text-gray-500" size={14} />
                            <span>{formatRelativeTime(movie.release_date, t)}</span>
                          </div>
                        )}
                      </div>
                      <div className="pt-1">
                        <span className="text-[10px] text-gray-500 group-hover:text-gray-400 transition-colors duration-200">
                          {movie.movie_id}
                        </span>
                        </div>
                    </div>
                  </div>
                );
              })}
            </div>
          ) : (
            <div className="text-center py-12">
              <FiVideo className="w-12 h-12 text-gray-600 mx-auto mb-4" />
              <p className="text-gray-400">{t('actress.noVideos')}</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
} 