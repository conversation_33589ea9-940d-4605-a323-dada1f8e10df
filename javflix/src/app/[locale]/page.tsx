import type { Locale } from '@/i18n/types';
import { getTranslations, locales } from '@/i18n';
import { notFound } from 'next/navigation';
import { Suspense } from 'react';
import Hero from '@/components/Hero';

import PopularVideos from '@/components/PopularVideos';
import RecentVideos from '@/components/RecentVideos';
import RecommendedVideos from '@/components/RecommendedVideos';

import ActressShowcase from '@/components/ActressShowcase';
import { JsonLd } from '@/components/JsonLd';
import { generateMetadata as baseGenerateMetadata, generateWebsiteJsonLd } from '@/lib/seo';
import type { Metadata } from 'next';
import LanguageSwitcher from '@/components/LanguageSwitcher';
import api from '@/lib/api-client';
import { RealtimeStatsProvider } from '@/contexts/RealtimeStatsContext';

// Route Segment Config
export const dynamic = 'force-dynamic';

// 为语言路由生成静态路径参数
export async function generateStaticParams() {
  return locales.map(locale => ({ locale }));
}

// 生成该语言的元数据
export async function generateMetadata(
  { params }: { params: Promise<{ locale: string }> }
): Promise<Metadata> {
  // 获取locale参数
  const resolvedParams = await params;
  const locale = resolvedParams.locale;
  
  // 验证locale是否是有效的支持语言
  if (!locale || !locales.includes(locale as Locale)) {
    return baseGenerateMetadata({});
  }
  
  // 获取该语言的翻译
  const translations = await getTranslations(locale as Locale);
  
  // 使用翻译的站点标题和描述
  return baseGenerateMetadata({
    title: translations.site.title,
    description: translations.site.description,
    keywords: [
      '日本AV', '在线观看', '高清无码', '有码', '中文字幕', 
      '免费下载', '女优库', '番号搜索', '步兵', '骑兵', 
      '成人视频', '最新资源', '流畅播放', '无水印', '精选合集',
      'JAV', '成人电影', '无码流出', '性感女优', '巨乳',
      '熟女', '人妻', '制服', '素人', '3D', 'VR', '4K超清'
    ],
  });
}

export default async function Home({ 
  params 
}: { 
  params: Promise<{ locale: string }> 
}) {
  // 获取locale参数
  const resolvedParams = await params;
  const locale = resolvedParams.locale as Locale;
  
  // 验证locale是否支持，否则显示404
  if (!locale || !locales.includes(locale)) {
    notFound();
  }
  
  // 异步获取该语言的翻译
  const translations = await getTranslations(locale);
  
  // 使用SEO库生成网站结构化数据
  const websiteJsonLd = generateWebsiteJsonLd();
  
  // 组织结构化数据
  const organizationJsonLd = {
    '@context': 'https://schema.org',
    '@type': 'Organization',
    name: 'JAVFLIX.TV',
    url: 'https://javflix.tv',
    logo: 'https://javflix.tv/logo.png',
    sameAs: [
      'https://twitter.com/javflix',
      'https://www.instagram.com/javflix'
    ],
    description: translations.site.description,
  };
  
  return (
    <>
      {/* 结构化数据 */}
      <JsonLd data={websiteJsonLd} />
      <JsonLd data={organizationJsonLd} />
      
      {/* 主页内容 */}
      <Suspense fallback={<div className="h-screen flex items-center justify-center">Loading...</div>}>
        <RealtimeStatsProvider>
          <Hero />
          
          <div className="container mx-auto px-4 py-8 space-y-12">
            <section>
              <RecentVideos locale={locale} />
            </section>
            
            <section>
              <PopularVideos locale={locale} />
            </section>
            
            <section>
              <RecommendedVideos locale={locale} />
            </section>

            <section>
              <ActressShowcase locale={locale} />
            </section>
          </div>
        </RealtimeStatsProvider>
      </Suspense>
    </>
  );
}
