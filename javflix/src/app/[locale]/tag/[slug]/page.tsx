import React from 'react';
import { Metadata } from 'next';
import { generateMetadata as generateSeoMetadata } from '@/lib/seo';
import TagClient from './TagClient';
import { getTranslations, locales } from '@/i18n';
import type { Locale } from '@/i18n/types';
import { notFound } from 'next/navigation';

// Route Segment Config for Server-Side Rendering (SSR)
export const dynamic = 'force-dynamic'; // Enable SSR - render on each request
 // Ensure fresh data on each request
export const runtime = 'nodejs'; // Use Node.js runtime for better performance
export const preferredRegion = 'auto'; // Auto-select optimal region
export const revalidate = false; // Disable revalidation for true SSR

// 为语言路由生成静态路径参数
export async function generateStaticParams() {
  return locales.map(locale => ({ locale }));
}

// 从数据库API获取标签详情数据
async function getTagData(slug: string) {
  try {
    const decodedSlug = decodeURIComponent(slug);
    console.log(`Fetching tag data for slug: ${slug} (decoded: ${decodedSlug})`);
    
    const response = await fetch(`http://localhost:4000/api/db/tags/${slug}`, {
      next: { revalidate: 0 }
    });
    
    if (!response.ok) {
      console.error(`Failed to fetch tag data for slug ${slug}: ${response.status}`);
      return null;
    }
    
    const result = await response.json();
    
    if (result.success && result.data) {
      return {
        id: result.data.id,
        name: result.data.name,
        description: result.data.description || `${result.data.name}标签下的精彩视频内容`,
        movieCount: result.data.movie_count || 0,
        slug: slug,
        imageUrl: result.data.image_url || '/images/defaults/tag_default.jpg'
      };
    }
    
    return null;
  } catch (error) {
    console.error(`Error fetching tag data for slug ${slug}:`, error);
    return null;
  }
}

// 获取标签下的视频数据
async function getTagMovies(tagId: string, page: number = 1, limit: number = 20) {
  try {
    const response = await fetch(`http://localhost:4000/api/db/movies/tag/${tagId}?page=${page}&limit=${limit}`, {
      next: { revalidate: 0 }
    });
    
    if (!response.ok) {
      console.error(`Failed to fetch movies for tag ${tagId}: ${response.status}`);
      return { movies: [], pagination: null };
    }
    
    const result = await response.json();
    
    if (result.success && result.data) {
      return {
        movies: result.data.items || [],
        pagination: result.data.pagination || null
      };
    }
    
    return { movies: [], pagination: null };
  } catch (error) {
    console.error(`Error fetching movies for tag ${tagId}:`, error);
    return { movies: [], pagination: null };
  }
}

// 辅助函数：格式化时长
function formatDuration(minutes: number): string {
  const hours = Math.floor(minutes / 60);
  const mins = minutes % 60;
  return `${hours}:${mins.toString().padStart(2, '0')}:00`;
}

// 辅助函数：格式化观看次数
function formatViewCount(count: number): string {
  if (count >= 1000000) {
    return `${(count / 1000000).toFixed(1)}M`;
  } else if (count >= 1000) {
    return `${(count / 1000).toFixed(1)}K`;
  }
  return count.toString();
}

// 辅助函数：格式化日期
function formatDate(dateString: string): string {
  try {
    const date = new Date(dateString);
    return date.toISOString().split('T')[0];
  } catch {
    return dateString;
  }
}

// 生成标签页的元数据（国际化版本）
export async function generateMetadata({ 
  params 
}: { 
  params: Promise<{ locale: string; slug: string }> 
}): Promise<Metadata> {
  const resolvedParams = await params;
  const { locale, slug } = resolvedParams;
  
  // 获取标签数据
  const tagData = await getTagData(slug);
  
  // 如果没有找到标签数据，返回默认元数据
  if (!tagData) {
    return generateSeoMetadata({
      title: '标签详情 - 未找到',
      description: '未找到该标签的详细信息',
      path: `/${locale}/tag/${slug}`,
      keywords: ['标签', '视频标签', '内容标签'],
    });
  }
  
  // 验证locale是否是有效的支持语言
  if (!locale || !locales.includes(locale as Locale)) {
    return generateSeoMetadata({
      title: `${tagData.name} - 标签详情`,
      description: tagData.description,
      path: `/tag/${slug}`,
      imageUrl: tagData.imageUrl ? `https://javflix.tv${tagData.imageUrl}` : undefined,
      keywords: ['标签', '视频标签', '内容标签', tagData.name],
    });
  }

  try {
    // 获取该语言的翻译
    const translations = await getTranslations(locale as Locale);
    
    return generateSeoMetadata({
      title: `${tagData.name} - ${translations.tags?.categories || '标签详情'}`,
      description: tagData.description,
      path: `/${locale}/tag/${slug}`,
      imageUrl: tagData.imageUrl ? `https://javflix.tv${tagData.imageUrl}` : undefined,
      keywords: ['标签', '视频标签', '内容标签', tagData.name],
    });
  } catch (error) {
    console.error('Failed to load translations:', error);
    return generateSeoMetadata({
      title: `${tagData.name} - 标签详情`,
      description: tagData.description,
      path: `/${locale}/tag/${slug}`,
      imageUrl: tagData.imageUrl ? `https://javflix.tv${tagData.imageUrl}` : undefined,
      keywords: ['标签', '视频标签', '内容标签', tagData.name],
    });
  }
}

// 服务器端页面组件（国际化版本）
export default async function TagPage({ 
  params,
  searchParams
}: { 
  params: Promise<{ locale: string; slug: string }>;
  searchParams: Promise<{ page?: string }>;
}) {
  const resolvedParams = await params;
  const resolvedSearchParams = await searchParams;
  const { locale, slug } = resolvedParams;
  const page = parseInt(resolvedSearchParams.page || '1', 10);
  
  // 验证locale是否支持，否则显示404
  if (!locale || !locales.includes(locale as Locale)) {
    notFound();
  }

  try {
    // 异步获取该语言的翻译
    await getTranslations(locale as Locale);
  } catch (error) {
    console.error('Failed to load translations in TagPage:', error);
  }

  // 获取真实数据
  const tagData = await getTagData(slug);
  
  // 如果没有找到标签数据，显示404
  if (!tagData) {
    notFound();
  }
  
  const { movies, pagination } = await getTagMovies(tagData.id.toString(), page);
  
  return (
    <TagClient 
      tagData={{
        ...tagData,
        type: 'tag',
        movies,
        pagination
      }}
      locale={locale as Locale}
    />
  );
} 