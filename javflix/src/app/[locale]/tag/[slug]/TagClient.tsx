'use client';

import { useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { FiTag, FiPlay, FiClock, FiEye, FiCalendar, FiChevronLeft, FiChevronRight } from 'react-icons/fi';
import type { Locale } from '@/i18n/types';

// 标签数据类型
interface TagData {
  id: number;
  name: string;
  description: string;
  movieCount: number;
  slug: string;
  imageUrl?: string;
  type: string;
  movies: Array<{
    id: number;
    title: string;
    imageUrl: string;
    duration: string;
    views: string;
    date: string;
    slug: string;
    stars?: Array<{ id: number; name: string }>;
    description?: string;
  }>;
  pagination: {
    currentPage: number;
    totalPages: number;
    totalItems: number;
    hasNextPage: boolean;
  } | null;
}

interface TagClientProps {
  tagData: TagData;
  locale: Locale;
}

const TagClient = ({ tagData, locale }: TagClientProps) => {
  const [currentPage, setCurrentPage] = useState(tagData.pagination?.currentPage || 1);

  // 处理页面变化
  const handlePageChange = (newPage: number) => {
    if (!tagData.pagination || newPage < 1 || newPage > tagData.pagination.totalPages) return;
    
    // 构建新的URL并导航
    const url = new URL(window.location.href);
    url.searchParams.set('page', newPage.toString());
    window.location.href = url.toString();
  };

  // 渲染分页控件
  const renderPagination = () => {
    if (!tagData.pagination || tagData.pagination.totalPages <= 1) return null;

    const { currentPage, totalPages } = tagData.pagination;
    const pages: (number | string)[] = [];
    const showPages = 5; // 显示的页码数量
    const startPage = Math.max(1, currentPage - Math.floor(showPages / 2));
    const endPage = Math.min(totalPages, startPage + showPages - 1);

    for (let i = startPage; i <= endPage; i++) {
      pages.push(i);
    }

    return (
      <div className="flex justify-center items-center space-x-2 mt-8">
        {/* 上一页 */}
        <button
          onClick={() => handlePageChange(currentPage - 1)}
          disabled={currentPage === 1}
          className="flex items-center px-3 py-2 bg-gray-800 text-white rounded disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-700 transition-colors"
        >
          <FiChevronLeft className="w-4 h-4 mr-1" />
          上一页
        </button>

        {/* 页码 */}
        {pages.map(page => (
          <button
            key={page}
            onClick={() => typeof page === 'number' ? handlePageChange(page) : undefined}
            disabled={typeof page === 'string'}
            className={`px-3 py-2 rounded transition-colors ${
              page === currentPage
                ? 'bg-blue-600 text-white'
                : typeof page === 'string'
                ? 'bg-gray-800 text-gray-400 cursor-default'
                : 'bg-gray-800 text-white hover:bg-gray-700'
            }`}
          >
            {page}
          </button>
        ))}

        {/* 下一页 */}
        <button
          onClick={() => handlePageChange(currentPage + 1)}
          disabled={currentPage === totalPages}
          className="flex items-center px-3 py-2 bg-gray-800 text-white rounded disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-700 transition-colors"
        >
          下一页
          <FiChevronRight className="w-4 h-4 ml-1" />
        </button>
      </div>
    );
  };

  return (
    <div className="min-h-screen bg-black text-white">
      <div className="container mx-auto px-4 py-8">
        {/* 标签头部 */}
        <div className="mb-8">
          <div className="flex items-center mb-4">
            <FiTag className="text-blue-500 text-2xl mr-3" />
            <h1 className="text-3xl font-bold">{tagData.name}</h1>
          </div>
          
          <p className="text-gray-400 mb-4">{tagData.description}</p>
          
          <div className="flex items-center text-sm text-gray-500">
            <span>共 <span className="text-blue-400 font-semibold">{tagData.movieCount}</span> 个相关视频</span>
            {tagData.pagination && (
              <span className="ml-4">
                第 <span className="text-blue-400">{tagData.pagination.currentPage}</span> 页，共 <span className="text-blue-400">{tagData.pagination.totalPages}</span> 页
              </span>
            )}
          </div>
        </div>

        {/* 视频列表 */}
        {tagData.movies.length > 0 ? (
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-6">
            {tagData.movies.map((video) => (
              <div key={video.id} className="group">
                <Link href={`/${locale}/video/${video.slug}`}>
                  <div className="relative overflow-hidden rounded-lg bg-gray-900 transition-transform duration-300 group-hover:scale-105">
                    {/* 视频封面 */}
                    <div className="aspect-[3/4] relative">
                      <Image
                        src={video.imageUrl}
                        alt={video.title}
                        fill
                        className="object-cover transition-transform duration-300 group-hover:scale-110"
                        sizes="(max-width: 640px) 100vw, (max-width: 768px) 50vw, (max-width: 1024px) 33vw, (max-width: 1280px) 25vw, 20vw"
                      />
                      
                      {/* 播放按钮 */}
                      <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-300 flex items-center justify-center">
                        <FiPlay className="text-white text-4xl opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                      </div>

                      {/* 时长 */}
                      <div className="absolute bottom-2 right-2 bg-black bg-opacity-70 text-white px-2 py-1 rounded text-xs">
                        <FiClock className="inline w-3 h-3 mr-1" />
                        {video.duration}
                      </div>
                    </div>

                    {/* 视频信息 */}
                    <div className="p-3">
                      <h3 className="text-sm font-medium line-clamp-2 mb-2 group-hover:text-blue-400 transition-colors">
                        {video.title}
                      </h3>
                      
                      <div className="flex items-center justify-between text-xs text-gray-400 mb-2">
                        <div className="flex items-center">
                          <FiEye className="w-3 h-3 mr-1" />
                          <span>{video.views}</span>
                        </div>
                        <div className="flex items-center">
                          <FiCalendar className="w-3 h-3 mr-1" />
                          <span>{video.date}</span>
                        </div>
                      </div>

                      {/* 演员信息 */}
                      {video.stars && video.stars.length > 0 && (
                        <div className="text-xs text-gray-400">
                          <span className="line-clamp-1">
                            {video.stars.map(star => star.name).join(', ')}
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                </Link>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <FiTag className="text-gray-600 text-6xl mx-auto mb-4" />
            <p className="text-gray-400 text-lg mb-2">该标签下暂无视频内容</p>
            <p className="text-gray-500 text-sm">请尝试其他标签或稍后再来查看</p>
          </div>
        )}

        {/* 分页控件 */}
        {renderPagination()}
      </div>
    </div>
  );
};

export default TagClient; 