import { Suspense } from 'react';
import { defaultLocale } from '@/i18n';
import { getTranslations } from '@/i18n';
import { generateMetadata as baseGenerateMetadata } from '@/lib/seo';
import type { Metadata } from 'next';
import { RealtimeStatsProvider } from '@/contexts/RealtimeStatsContext';
import NewClient from '@/app/new/NewClient';

// Route Segment Config
export const dynamic = 'force-dynamic';

// 生成元数据
export async function generateMetadata(): Promise<Metadata> {
  // 使用默认语言的翻译
  const translations = await getTranslations(defaultLocale);
  
  // 默认元数据内容
  const defaultMetadata = {
    title: '最近更新视频',
    description: '查看JAVFLIX最近更新的日本AV影片，获取最新资源，高清画质，流畅播放体验',
    path: '/recent',
    keywords: [
      '最新AV', '日本AV', '在线观看', '高清无码', '中文字幕', 
      '每日更新', '新片', 'JAV', '成人视频'
    ],
  };
  
  // 使用翻译的站点标题和描述
  return baseGenerateMetadata({
    title: translations?.pages?.recent?.title || defaultMetadata.title,
    description: translations?.pages?.recent?.description || defaultMetadata.description,
    path: '/recent',
    keywords: defaultMetadata.keywords,
  });
}

// 加载占位符组件
function NewLoading() {
  return (
    <div className="min-h-screen bg-gray-900 text-white pt-20 pb-12">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <div className="h-12 bg-gray-800 rounded-lg animate-pulse mb-4 mx-auto max-w-md"></div>
          <div className="h-6 bg-gray-700 rounded-lg animate-pulse mx-auto max-w-sm"></div>
        </div>
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-6">
          {[...Array(20)].map((_, i) => (
            <div key={i} className="bg-gray-800 rounded-lg p-4 animate-pulse">
              <div className="h-48 bg-gray-700 rounded-lg mb-4"></div>
              <div className="h-4 bg-gray-700 rounded mb-2"></div>
              <div className="h-3 bg-gray-600 rounded w-3/4"></div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}

// 主页面组件
export default async function RecentPage() {
  return (
    <RealtimeStatsProvider>
      <Suspense fallback={<NewLoading />}>
        <NewClient />
      </Suspense>
    </RealtimeStatsProvider>
  );
} 