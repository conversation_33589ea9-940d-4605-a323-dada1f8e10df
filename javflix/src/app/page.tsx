import { redirect } from 'next/navigation';
import { defaultLocale } from '@/i18n';
import { Suspense } from 'react';
import Hero from '@/components/Hero';

import RecentVideos from '@/components/RecentVideos';
import RecommendedVideos from '@/components/RecommendedVideos';
import Categories from '@/components/Categories';
import ActressShowcase from '@/components/ActressShowcase';
import { JsonLd } from '@/components/JsonLd';
import { generateMetadata, generateWebsiteJsonLd } from '@/lib/seo';
import type { Metadata } from 'next';

// 生成首页的元数据
export const metadata: Metadata = generateMetadata({
  path: '/',
  description: '最新、最全、最优质的日本成人AV影片在线观看与下载平台，提供无码有码高清视频，中文字幕，每日更新最新影片。收录最全的女优作品，步兵骑兵资源应有尽有，高速稳定，满足您的所有观影需求',
  keywords: [
    '日本AV', '在线观看', '高清无码', '有码', '中文字幕', 
    '免费下载', '女优库', '番号搜索', '步兵', '骑兵', 
    '成人视频', '最新资源', '流畅播放', '无水印', '精选合集',
    'JAV', '成人电影', '无码流出', '性感女优', '巨乳',
    '熟女', '人妻', '制服', '素人', '3D', 'VR', '4K超清'
  ],
});

// 使用SEO库生成网站结构化数据
const websiteJsonLd = generateWebsiteJsonLd();

// 组织结构化数据
const organizationJsonLd = {
  '@context': 'https://schema.org',
  '@type': 'Organization',
  name: 'JAVFLIX.TV',
  url: 'https://javflix.tv',
  logo: 'https://javflix.tv/logo.png',
  sameAs: [
    'https://twitter.com/javflix',
    'https://www.instagram.com/javflix'
  ],
  description: '最新最全的日本成人AV影片在线观看与下载平台，提供无码有码高清视频，丰富女优作品分类，中文字幕，高速稳定，全网资源收录最全，更新最快',
};

// 根路径重定向到默认语言
export default function Home() {
  // 重定向到默认语言版本
  redirect(`/${defaultLocale}`);
}
