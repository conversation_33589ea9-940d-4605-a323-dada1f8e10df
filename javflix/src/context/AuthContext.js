'use client';

import { createContext, useContext, useState, useEffect } from 'react';
import { getCurrentUser, loginUser, registerUser, logoutUser, updateUserProfile } from '@/lib/auth-client';

// 创建认证上下文
const AuthContext = createContext();

// 认证提供者组件
export function AuthProvider({ children }) {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // 获取当前用户信息的函数
  const fetchUserData = async () => {
    try {

      
      // 首先检查客户端cookie状态（非httpOnly）
      let isAuthStateCookiePresent = false;
      if (typeof document !== 'undefined') {
        // 检查是否存在auth_state cookie (非httpOnly)
        const cookies = document.cookie.split(';');
        for (let cookie of cookies) {
          const [name, value] = cookie.trim().split('=');
          if (name === 'auth_state' && value === 'true') {
            isAuthStateCookiePresent = true;
            break;
          }
        }
      }
      

      
      // 无论cookie状态如何，都尝试获取用户信息
      // getCurrentUser会处理各种情况，包括API请求失败时使用缓存等
      const userData = await getCurrentUser();

      
      // 用户数据直接从API返回，不再包装在response.success.user中
      if (userData && userData.id) {
        setUser(userData);
        
        // 用户信息加载成功时更新本地存储
        if (typeof window !== 'undefined') {
          try {
            localStorage.setItem('current_user', JSON.stringify(userData));
            localStorage.setItem('auth_timestamp', Date.now().toString());
          } catch (e) {
            console.warn('无法存储用户数据到本地存储');
          }
          
          // 如果API返回用户数据但前端cookie不存在，触发页面刷新
          // 这可以解决后端认证成功但前端状态不同步的问题
          if (!isAuthStateCookiePresent) {

            // 由于安全限制，客户端JavaScript无法直接设置SameSite和Path等属性
            // 简单设置一个临时cookie，下次API请求时，后端会正确设置它
            document.cookie = "auth_state=true; path=/;";
          }
        }
      } else {
        setUser(null);
        
        // 清除本地存储的用户数据
        if (typeof window !== 'undefined') {
          try {
            localStorage.removeItem('current_user');
            localStorage.removeItem('auth_timestamp');
            
            // 如果API没有返回用户数据但前端cookie存在，清除前端cookie
            if (isAuthStateCookiePresent) {

              document.cookie = "auth_state=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT";
            }
          } catch (e) {
            console.warn('无法从本地存储中删除用户数据');
          }
        }
      }
    } catch (err) {
      console.error('AuthContext: 获取用户数据失败:', err);
      setUser(null);
    }
  };

  // 初始化 - 检查用户登录状态
  useEffect(() => {
    const initAuth = async () => {
      try {
        await fetchUserData();
      } catch (err) {
        setError('认证状态初始化失败');
        console.error('认证初始化错误:', err);
      } finally {
        setLoading(false);
      }
    };

    initAuth();
  }, []);
  
  // 添加一个事件监听器，用于响应认证状态变更事件
  useEffect(() => {
    const handleAuthStateChanged = async () => {

      setLoading(true);
      await fetchUserData();
      setLoading(false);
    };
    
    // 添加事件监听器
    window.addEventListener('auth-state-changed', handleAuthStateChanged);
    
    // 清理函数
    return () => {
      window.removeEventListener('auth-state-changed', handleAuthStateChanged);
    };
  }, []);

  // 登录
  const login = async (email, password, rememberMe = false) => {
    setLoading(true);
    setError(null);
    
    try {
      const result = await loginUser(email, password, rememberMe);
      
      if (result.success && result.user) {
        setUser(result.user);
        return { success: true };
      } else {
        throw new Error(result.error || '登录失败');
      }
    } catch (err) {
      setError(err.message || '登录失败');
      return { success: false, error: err.message || '登录失败' };
    } finally {
      setLoading(false);
    }
  };

  // 注册
  const register = async (username, email, password) => {
    setLoading(true);
    setError(null);
    
    try {
      const result = await registerUser(username, email, password);
      
      if (result.success && result.user) {
        setUser(result.user);
        return { success: true };
      } else {
        throw new Error(result.error || '注册失败');
      }
    } catch (err) {
      setError(err.message || '注册失败');
      return { success: false, error: err.message || '注册失败' };
    } finally {
      setLoading(false);
    }
  };

  // 登出
  const logout = async () => {
    try {
      await logoutUser();
    } catch (error) {
      console.error('登出请求失败:', error);
    } finally {
      // 无论服务器请求是否成功，都清除本地状态
      setUser(null);
      
      // 清除localStorage中的认证数据
      if (typeof window !== 'undefined') {
        try {
          localStorage.removeItem('current_user');
          localStorage.removeItem('auth_timestamp');
          localStorage.removeItem('auth_token');

        } catch (e) {
          console.warn('AuthContext: 清除localStorage失败:', e);
        }
      }
    }
  };

  // 更新用户信息
  const updateProfile = async (userData) => {
    setLoading(true);
    setError(null);
    
    try {
      const result = await updateUserProfile(userData);
      
      if (result.success && result.user) {
        setUser(result.user);
        return { success: true };
      } else {
        throw new Error(result.error || '更新资料失败');
      }
    } catch (err) {
      setError(err.message || '更新资料失败');
      return { success: false, error: err.message || '更新资料失败' };
    } finally {
      setLoading(false);
    }
  };

  // 检查是否已登录
  const isAuthenticated = !!user;

  // 公开的上下文值
  const value = {
    user,
    loading,
    error,
    isAuthenticated,
    login,
    register,
    logout,
    updateProfile
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

// 自定义钩子，用于在组件中访问认证上下文
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth必须在AuthProvider内部使用');
  }
  return context;
};