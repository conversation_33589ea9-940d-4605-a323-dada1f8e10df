'use client';

import React, { createContext, useContext, useEffect, useState, useCallback } from 'react';

interface User {
  id: string;
  username: string;
  email: string;
  firstName?: string;
  lastName?: string;
  avatar?: string;
}

interface AuthContextType {
  user: User | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  login: (email: string, password: string) => Promise<{ success: boolean; error?: string }>;
  register: (username: string, email: string, password: string) => Promise<{ success: boolean; error?: string }>;
  logout: () => Promise<void>;
  refreshAuth: () => Promise<void>;
  setUserImmediate: (user: User | null) => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: React.ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  const setUserImmediate = useCallback((userData: User | null) => {
    console.log('AuthContext: 立即设置用户状态:', userData?.username || 'null');
    setUser(userData);
    setIsAuthenticated(!!userData);
    
    if (userData) {
      try {
        localStorage.setItem('auth_user', JSON.stringify(userData));
        localStorage.setItem('auth_timestamp', Date.now().toString());
      } catch (e) {
        console.warn('Failed to store auth data in localStorage:', e);
      }
    } else {
      try {
        localStorage.removeItem('auth_user');
        localStorage.removeItem('auth_timestamp');
      } catch (e) {
        console.warn('Failed to clear localStorage:', e);
      }
    }
  }, []);

  const checkAuthStatus = useCallback(async (): Promise<boolean> => {
    try {
      const response = await fetch('/api/auth/user', {
        method: 'GET',
        credentials: 'include',
        headers: {
          'Cache-Control': 'no-cache',
        },
      });

      if (response.ok) {
        const data = await response.json();
        console.log('AuthContext checkAuthStatus: API响应数据:', data);
        
        if (data.success && data.user) {
          setUser(data.user);
          setIsAuthenticated(true);
          
          try {
            localStorage.setItem('auth_user', JSON.stringify(data.user));
            localStorage.setItem('auth_timestamp', Date.now().toString());
          } catch (e) {
            console.warn('Failed to store auth data in localStorage:', e);
          }
          
          return true;
        }
      }
      
      setUser(null);
      setIsAuthenticated(false);
      try {
        localStorage.removeItem('auth_user');
        localStorage.removeItem('auth_timestamp');
      } catch (e) {
        console.warn('Failed to clear localStorage:', e);
      }
      
      return false;
    } catch (error) {
      console.error('Auth status check failed:', error);
      setUser(null);
      setIsAuthenticated(false);
      return false;
    }
  }, []);

  const refreshAuth = useCallback(async () => {
    setIsLoading(true);
    await checkAuthStatus();
    setIsLoading(false);
  }, [checkAuthStatus]);

  const login = useCallback(async (email: string, password: string): Promise<{ success: boolean; error?: string }> => {
    try {
      setIsLoading(true);
      
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({ email, password }),
      });

      const data = await response.json();

      if (response.ok && data.success) {
        if (data.user) {
          setUserImmediate(data.user);
          console.log('AuthContext: 登录成功，立即设置用户状态');
        }
        
        setIsLoading(false);
        return { success: true };
      } else {
        setIsLoading(false);
        return { success: false, error: data.error || 'Login failed' };
      }
    } catch (error) {
      console.error('Login error:', error);
      setIsLoading(false);
      return { success: false, error: 'Network error occurred' };
    }
  }, [setUserImmediate]);

  const register = useCallback(async (username: string, email: string, password: string): Promise<{ success: boolean; error?: string }> => {
    try {
      setIsLoading(true);
      
      const response = await fetch('/api/auth/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({ username, email, password }),
      });

      const data = await response.json();

      if (response.ok && data.success) {
        console.log('AuthContext: 注册API调用成功，返回数据:', data);
        
        if (data.user) {
          setUserImmediate(data.user);
          console.log('AuthContext: 注册成功，立即设置用户状态');
        }
        
        setIsLoading(false);
        return { success: true };
      } else {
        setIsLoading(false);
        return { success: false, error: data.error || 'Registration failed' };
      }
    } catch (error) {
      console.error('Registration error:', error);
      setIsLoading(false);
      return { success: false, error: 'Network error occurred' };
    }
  }, [setUserImmediate]);

  const logout = useCallback(async () => {
    try {
      setIsLoading(true);
      
      await fetch('/api/auth/logout', {
        method: 'POST',
        credentials: 'include',
      });
      
      setUserImmediate(null);
      console.log('AuthContext: 登出成功，立即清除用户状态');
      
      setIsLoading(false);
    } catch (error) {
      console.error('Logout error:', error);
      setUserImmediate(null);
      setIsLoading(false);
    }
  }, [setUserImmediate]);

  useEffect(() => {
    let mounted = true;
    
    const initializeAuth = async () => {
      try {
        const storedUser = localStorage.getItem('auth_user');
        const storedTimestamp = localStorage.getItem('auth_timestamp');
        
        if (storedUser && storedTimestamp) {
          const timestamp = parseInt(storedTimestamp);
          const now = Date.now();
          const maxAge = 24 * 60 * 60 * 1000;
          
          if (now - timestamp < maxAge) {
            try {
              const userData = JSON.parse(storedUser);
              setUser(userData);
              setIsAuthenticated(true);
              console.log('AuthContext: 使用localStorage缓存的用户数据');
            } catch (e) {
              console.warn('Failed to parse stored user data:', e);
            }
          }
        }
        
        if (mounted) {
          await checkAuthStatus();
          setIsLoading(false);
        }
      } catch (error) {
        console.error('Auth initialization error:', error);
        if (mounted) {
          setIsLoading(false);
        }
      }
    };

    initializeAuth();
    
    return () => {
      mounted = false;
    };
  }, [checkAuthStatus]);

  useEffect(() => {
    const handleAuthStateChanged = async (event: Event) => {
      console.log('AuthContext: 检测到认证状态变更事件');
      
      const customEvent = event as CustomEvent;
      if (customEvent.detail && customEvent.detail.user) {
        setUserImmediate(customEvent.detail.user);
        console.log('AuthContext: 使用事件中的用户数据，跳过API调用');
        return;
      }
      
      if (!user) {
        setIsLoading(true);
        await checkAuthStatus();
        setIsLoading(false);
      }
    };
    
    window.addEventListener('auth-state-changed', handleAuthStateChanged);
    
    return () => {
      window.removeEventListener('auth-state-changed', handleAuthStateChanged);
    };
  }, [checkAuthStatus, user, setUserImmediate]);

  useEffect(() => {
    if (!isAuthenticated) return;
    
    const interval = setInterval(() => {
      checkAuthStatus();
    }, 5 * 60 * 1000);
    
    return () => clearInterval(interval);
  }, [isAuthenticated, checkAuthStatus]);

  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible' && isAuthenticated) {
        checkAuthStatus();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    return () => document.removeEventListener('visibilitychange', handleVisibilityChange);
  }, [isAuthenticated, checkAuthStatus]);

  const value: AuthContextType = {
    user,
    isLoading,
    isAuthenticated,
    login,
    register,
    logout,
    refreshAuth,
    setUserImmediate,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}; 