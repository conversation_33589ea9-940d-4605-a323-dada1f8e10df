'use client';

import React, { useEffect, useRef, useState } from 'react';
import { usePathname } from 'next/navigation';
import { gsap } from 'gsap';
import { usePageTransitions, useAnimationPerformance } from '@/hooks/useEnhancedAnimations';

interface PageTransitionProps {
  children: React.ReactNode;
  className?: string;
}

const PageTransition: React.FC<PageTransitionProps> = ({ 
  children, 
  className = '' 
}) => {
  const pathname = usePathname();
  const [displayChildren, setDisplayChildren] = useState(children);
  const [isTransitioning, setIsTransitioning] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);
  const { animatePageIn, animatePageOut } = usePageTransitions();
  const { getOptimizedConfig, prefersReducedMotion } = useAnimationPerformance();

  // 页面切换效果
  useEffect(() => {
    if (!containerRef.current) return;

    const config = getOptimizedConfig();
    
    // 如果用户偏好减少动画，直接更新内容
    if (prefersReducedMotion()) {
      setDisplayChildren(children);
      return;
    }

    // 执行页面过渡动画
    const performTransition = async () => {
      setIsTransitioning(true);

      // 退出动画
      await animatePageOut([containerRef.current!]);
      
      // 更新内容
      setDisplayChildren(children);
      
      // 等待DOM更新
      await new Promise(resolve => setTimeout(resolve, 50));
      
      // 进入动画
      animatePageIn([containerRef.current!]);
      
      setIsTransitioning(false);
    };

    performTransition();
  }, [pathname, children, animatePageIn, animatePageOut, getOptimizedConfig, prefersReducedMotion]);

  return (
    <div 
      ref={containerRef}
      className={`page-transition-container ${className} ${isTransitioning ? 'transitioning' : ''}`}
      style={{
        minHeight: '100vh',
        position: 'relative'
      }}
    >
      {displayChildren}
      
      {/* 过渡加载指示器 */}
      {isTransitioning && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/20 backdrop-blur-sm">
          <div className="animate-spin rounded-full h-8 w-8 border-2 border-red-500 border-t-transparent"></div>
        </div>
      )}
    </div>
  );
};

// 路由过渡包装器
export const RouteTransitionWrapper: React.FC<{ children: React.ReactNode }> = ({ 
  children 
}) => {
  return (
    <PageTransition className="route-transition">
      {children}
    </PageTransition>
  );
};

// 内容切换组件
interface ContentSwitcherProps {
  children: React.ReactNode;
  transitionKey: string | number;
  className?: string;
  animationType?: 'fade' | 'slide' | 'scale';
}

export const ContentSwitcher: React.FC<ContentSwitcherProps> = ({
  children,
  transitionKey,
  className = '',
  animationType = 'fade'
}) => {
  const [displayChildren, setDisplayChildren] = useState(children);
  const [currentKey, setCurrentKey] = useState(transitionKey);
  const containerRef = useRef<HTMLDivElement>(null);
  const { getOptimizedConfig, prefersReducedMotion } = useAnimationPerformance();

  useEffect(() => {
    if (currentKey === transitionKey) return;
    if (!containerRef.current) return;

    const config = getOptimizedConfig();

    if (prefersReducedMotion()) {
      setDisplayChildren(children);
      setCurrentKey(transitionKey);
      return;
    }

    const performSwitch = async () => {
      const element = containerRef.current!;

      // 退出动画
      const exitAnimation = () => {
        switch (animationType) {
          case 'slide':
            return gsap.to(element, {
              x: -50,
              opacity: 0,
              duration: config.duration,
              ease: config.ease
            });
          case 'scale':
            return gsap.to(element, {
              scale: 0.9,
              opacity: 0,
              duration: config.duration,
              ease: config.ease
            });
          default:
            return gsap.to(element, {
              opacity: 0,
              duration: config.duration,
              ease: config.ease
            });
        }
      };

      await exitAnimation();
      
      // 更新内容
      setDisplayChildren(children);
      setCurrentKey(transitionKey);
      
      // 等待DOM更新
      await new Promise(resolve => setTimeout(resolve, 50));

      // 进入动画
      const enterAnimation = () => {
        switch (animationType) {
          case 'slide':
            gsap.fromTo(element, 
              { x: 50, opacity: 0 },
              { x: 0, opacity: 1, duration: config.duration, ease: config.ease }
            );
            break;
          case 'scale':
            gsap.fromTo(element,
              { scale: 1.1, opacity: 0 },
              { scale: 1, opacity: 1, duration: config.duration, ease: config.ease }
            );
            break;
          default:
            gsap.fromTo(element,
              { opacity: 0 },
              { opacity: 1, duration: config.duration, ease: config.ease }
            );
        }
      };

      enterAnimation();
    };

    performSwitch();
  }, [transitionKey, children, animationType, currentKey, getOptimizedConfig, prefersReducedMotion]);

  return (
    <div 
      ref={containerRef}
      className={`content-switcher ${className}`}
    >
      {displayChildren}
    </div>
  );
};

// 加载状态过渡组件
interface LoadingTransitionProps {
  isLoading: boolean;
  children: React.ReactNode;
  loadingComponent?: React.ReactNode;
  className?: string;
}

export const LoadingTransition: React.FC<LoadingTransitionProps> = ({
  isLoading,
  children,
  loadingComponent,
  className = ''
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const { getOptimizedConfig, prefersReducedMotion } = useAnimationPerformance();

  const defaultLoadingComponent = (
    <div className="flex items-center justify-center py-16">
      <div className="relative">
        <div className="border-4 border-gray-700 border-t-white rounded-full animate-spin w-12 h-12"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-6 h-6 bg-white/20 rounded-full animate-pulse"></div>
      </div>
    </div>
  );

  useEffect(() => {
    if (!containerRef.current) return;

    const config = getOptimizedConfig();
    const element = containerRef.current;

    if (prefersReducedMotion()) return;

    if (isLoading) {
      gsap.fromTo(element,
        { opacity: 0, y: 10 },
        { opacity: 1, y: 0, duration: config.duration, ease: config.ease }
      );
    } else {
      gsap.fromTo(element,
        { opacity: 0, scale: 0.98 },
        { opacity: 1, scale: 1, duration: config.duration, ease: config.ease }
      );
    }
  }, [isLoading, getOptimizedConfig, prefersReducedMotion]);

  return (
    <div ref={containerRef} className={`loading-transition ${className}`}>
      {isLoading ? (loadingComponent || defaultLoadingComponent) : children}
    </div>
  );
};

// 错开显示组件
interface StaggeredRevealProps {
  children: React.ReactNode[];
  staggerDelay?: number;
  className?: string;
}

export const StaggeredReveal: React.FC<StaggeredRevealProps> = ({
  children,
  staggerDelay = 0.1,
  className = ''
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const { getOptimizedConfig, prefersReducedMotion } = useAnimationPerformance();

  useEffect(() => {
    if (!containerRef.current) return;

    const config = getOptimizedConfig();
    const childElements = containerRef.current.children;

    if (prefersReducedMotion() || childElements.length === 0) return;

    gsap.fromTo(childElements,
      { opacity: 0, y: 20 },
      {
        opacity: 1,
        y: 0,
        duration: config.duration,
        ease: config.ease,
        stagger: staggerDelay
      }
    );
  }, [children, staggerDelay, getOptimizedConfig, prefersReducedMotion]);

  return (
    <div ref={containerRef} className={`staggered-reveal ${className}`}>
      {children.map((child, index) => (
        <div key={index} className="stagger-item">
          {child}
        </div>
      ))}
    </div>
  );
};

export default PageTransition;
