'use client';

import React, { useState, useEffect, useRef } from 'react';
import Link from 'next/link';
import { FiChevronRight } from 'react-icons/fi';
import { useClientTranslations } from '@/components/TranslationsProvider';
import gsap from 'gsap';

interface Actress {
  id: string | number;
  name: string;
  star_id?: string;
  image_url?: string;
  cached_image_url?: string;
  movie_count?: number;
  rank?: number;
}

interface ActressShowcaseProps {
  locale?: string;
}

const ActressShowcase = ({ locale = 'zh' }: ActressShowcaseProps) => {
  const { t } = useClientTranslations();
  const [actresses, setActresses] = useState<Actress[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  // 添加refs
  const titleRef = useRef<HTMLHeadingElement>(null);
  const actressRefs = useRef<Array<HTMLAnchorElement | null>>([]);

  useEffect(() => {
    const fetchActresses = async () => {
      setIsLoading(true);
      setError(null);
      
      try {

        
        // 尝试旧版API，因为它在日志中显示是成功的
        let response = await fetch('/api/actresses?limit=10');
        
        if (response.ok) {
          const result = await response.json();

          
          if (result.status === 'success' && Array.isArray(result.data)) {
            const actressesWithRank = result.data.map((actress: any, index: number) => ({
              id: actress.id,
              name: actress.name,
              star_id: actress.star_id,
              image_url: actress.image_url,
              cached_image_url: actress.cached_image_url,
              movie_count: actress.videoCount || 0,
              rank: index + 1
            }));
            
            setActresses(actressesWithRank);

            return;
          }
        }
        
        throw new Error(`API请求失败: ${response.status} ${response.statusText}`);
        
      } catch (error) {
        console.error('❌ 获取女优数据失败:', error);
        const errorMessage = error instanceof Error ? error.message : '未知错误';
        setError(errorMessage);
        
        // 使用备用数据
        setActresses(getFallbackActresses());
      } finally {
        setIsLoading(false);
      }
    };

    fetchActresses();
  }, []);
  
  // 添加动画效果
  useEffect(() => {
    if (isLoading || actresses.length === 0) return;
    
    // 确保refs数组长度与actresses匹配
    actressRefs.current = actressRefs.current.slice(0, actresses.length);
    
    // 标题动画
    if (titleRef.current) {
      gsap.fromTo(
        titleRef.current,
        { x: -20, opacity: 0 },
        { x: 0, opacity: 1, duration: 0.5 }
      );
    }
    
    // 女优头像动画 - 使用stagger使其逐个出现
    gsap.fromTo(
      actressRefs.current,
      { 
        y: 20, 
        opacity: 0,
        scale: 0.8
      },
      { 
        y: 0, 
        opacity: 1,
        scale: 1,
        duration: 0.4,
        stagger: 0.05, // 每个元素之间的延迟
        ease: "back.out(1.2)" // 添加弹性动画效果
      }
    );
    
  }, [isLoading, actresses]);
  
  // 设置ref函数
  const setRef = (el: HTMLAnchorElement | null, index: number) => {
    actressRefs.current[index] = el;
  };

  // 获取图片URL
  const getImageUrl = (actress: Actress): string => {
    // 如果有缓存图片，直接使用
    if (actress.cached_image_url) {
      return actress.cached_image_url;
    }
    
    // 如果有原始图片URL，使用它
    if (actress.image_url) {
      return actress.image_url;
    }
    
    // 如果有star_id，通过image-proxy API获取图片
    if (actress.star_id) {
      return `/api/image-proxy?type=actress&star_id=${actress.star_id}`;
    }
    
    // 使用默认占位图
    return '/images/placeholder-actress.svg';
  };

  // 如果有错误且没有数据，显示错误状态
  if (error && actresses.length === 0) {
    return (
      <div className="w-full">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-bold text-white flex items-center">
            <div className="w-1 h-6 bg-red-600 rounded-full mr-3"></div>
            {t('actress.popular')}
          </h2>
        </div>
        <div className="bg-gray-800 rounded-lg p-4 border border-red-500/20">
          <div className="text-center py-4">
            <div className="text-red-400 text-lg mb-2">⚠️ 暂时无法加载数据</div>
            <p className="text-gray-400 text-sm mb-3">请稍后重试</p>
            <p className="text-gray-500 text-xs">{error}</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full">
        <div className="flex items-center justify-between mb-4">
          <h2 
            ref={titleRef}
            className="text-xl font-bold text-white flex items-center"
          >
            <div className="w-1 h-6 bg-red-600 rounded-full mr-3"></div>
            {t('actress.popular')}
          </h2>
          <Link href={`/${locale}/actress`} className="group flex items-center text-sm text-gray-400 hover:text-red-500 transition-colors duration-200">
            {t('common.viewMore')} 
            <FiChevronRight className="ml-1 transform group-hover:translate-x-1 transition-transform duration-200" size={14} />
          </Link>
        </div>

        {isLoading ? (
          <div className="flex overflow-x-auto space-x-4 pb-2 scrollbar-hide">
            {[...Array(10)].map((_, index) => (
              <div key={index} className="flex-shrink-0 w-16 animate-pulse">
                <div className="h-16 w-16 rounded-full bg-gray-700 mb-2"></div>
                <div className="h-3 bg-gray-700 rounded w-full mb-1"></div>
                <div className="h-2 bg-gray-700 rounded w-2/3 mx-auto"></div>
              </div>
            ))}
          </div>
        ) : (
          <div className="flex overflow-x-auto space-x-6 pb-3 scrollbar-hide">
            {actresses.map((actress, index) => (
              <Link 
                key={actress.id} 
                ref={(el) => setRef(el, index)}
                href={`/${locale}/actress/${actress.star_id || actress.id}`} 
                className="flex-shrink-0 w-16 flex flex-col items-center group"
              >
                <div className="relative">
                  <div className="h-16 w-16 rounded-full overflow-hidden border-2 border-gray-700 group-hover:border-red-600 transition-colors duration-300">
                    <img
                      src={getImageUrl(actress)}
                      alt={actress.name}
                      width={64}
                      height={64}
                      className="object-cover w-full h-full group-hover:scale-110 transition-transform duration-300"
                      onError={(e) => {
                        e.currentTarget.src = '/images/placeholder-actress.svg';
                      }}
                    />
                  </div>
                  {actress.rank && actress.rank <= 3 && (
                    <div className="absolute -top-1 -right-1 w-5 h-5 rounded-full bg-red-600 text-white text-xs flex items-center justify-center font-bold border border-gray-900">
                      {actress.rank}
                    </div>
                  )}
                </div>
                <span className="text-xs font-medium text-white mt-2 text-center group-hover:text-red-500 transition-colors truncate w-full">
                  {actress.name}
                </span>
                <span className="text-[10px] text-gray-400 text-center">
                  {actress.movie_count || 0}{t('common.videosCount')}
                </span>
              </Link>
            ))}
          </div>
        )}
    </div>
  );
};

// 备用女优数据，用于API完全失败的情况
function getFallbackActresses(): Actress[] {
  return [
    {
      id: 'fallback-1',
      name: '三上悠亚',
      star_id: 'mikami-yua',
      image_url: '/images/placeholder-actress.svg',
      movie_count: 86,
      rank: 1
    },
    {
      id: 'fallback-2',
      name: '深田咏美',
      star_id: 'fukada-eimi',
      image_url: '/images/placeholder-actress.svg',
      movie_count: 94,
      rank: 2
    },
    {
      id: 'fallback-3',
      name: '桥本有菜',
      star_id: 'hashimoto-arina',
      image_url: '/images/placeholder-actress.svg',
      movie_count: 78,
      rank: 3
    },
    {
      id: 'fallback-4',
      name: '水卜樱',
      star_id: 'miura-sakura',
      image_url: '/images/placeholder-actress.svg',
      movie_count: 65,
      rank: 4
    },
    {
      id: 'fallback-5',
      name: '明日花绮罗',
      star_id: 'asuka-kirara',
      image_url: '/images/placeholder-actress.svg',
      movie_count: 120,
      rank: 5
    },
    {
      id: 'fallback-6',
      name: '白石茉莉奈',
      star_id: 'shiraishi-marina',
      image_url: '/images/placeholder-actress.svg',
      movie_count: 156,
      rank: 6
    },
    {
      id: 'fallback-7',
      name: '波多野结衣',
      star_id: 'hatano-yui',
      image_url: '/images/placeholder-actress.svg',
      movie_count: 200,
      rank: 7
    },
    {
      id: 'fallback-8',
      name: '小岛南',
      star_id: 'kojima-minami',
      image_url: '/images/placeholder-actress.svg',
      movie_count: 89,
      rank: 8
    },
    {
      id: 'fallback-9',
      name: '天使萌',
      star_id: 'tenshi-moe',
      image_url: '/images/placeholder-actress.svg',
      movie_count: 67,
      rank: 9
    },
    {
      id: 'fallback-10',
      name: '松本一香',
      star_id: 'matsumoto-ichika',
      image_url: '/images/placeholder-actress.svg',
      movie_count: 45,
      rank: 10
    }
  ];
}

export default ActressShowcase;