'use client';

import { useState, useEffect, useRef } from 'react';
import { useClientTranslations } from '@/components/TranslationsProvider';
import { FiChevronRight, FiChevronLeft } from 'react-icons/fi';
import { getPopularVideos } from '@/lib/video-client';
import { type Locale } from '@/i18n/types';
import Link from 'next/link';
import { useInView } from 'react-intersection-observer';
import gsap from 'gsap';
import { getSocketClient } from '@/lib/socket-client';
import { buildApiUrl } from '@/lib/api-config';
import VideoCard from '@/components/VideoCard';
import SectionHeader from '@/components/SectionHeader';

interface PopularVideosProps {
  locale?: Locale;
}

interface VideoData {
  id: string | number;
  title: string;
  slug?: string;
  code?: string;
  thumbnail?: string;
  thumbnail_url?: string;
  cached_image_url?: string;
  movie_id?: string;
  duration?: string;
  views?: number;
  view_count?: number;
  timeAgo?: string;
  release_date?: string;
  created_at?: string;
  cover_image?: string;
  image_url?: string;
}

interface VideoStats {
  views: number;
  likes: number;
  favorites: number;
}

interface FusedVideoStats {
  videoId: string;
  numericVideoId: number;
  views: number;
  likes: number;
  favorites: number;
  dbBase: {
    views: number;
    likes: number;
    favorites: number;
  };
  redisIncrement: {
    views: number;
    likes: number;
    favorites: number;
  };
  lastUpdated: number;
  fusionTimestamp: number;
}

const PopularVideos = ({ locale = 'zh' }: PopularVideosProps) => {
  const { t } = useClientTranslations();
  const [videos, setVideos] = useState<VideoData[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  
  // 融合统计状态
  const [fusedStats, setFusedStats] = useState<Record<string, FusedVideoStats>>({});
  const socketClient = useRef(getSocketClient());
  
  const titleRef = useRef<HTMLHeadingElement>(null);
  const videoRefs = useRef<Array<HTMLDivElement | null>>([]);
  const scrollRef = useRef<HTMLDivElement>(null);
  
  // 使用ref记录视频元素
  const setRef = (el: HTMLDivElement | null, index: number) => {
    videoRefs.current[index] = el;
  };
  
  // 使用IntersectionObserver检测标题是否可见
  const { ref: inViewRef, inView } = useInView({
    threshold: 0.1,
    triggerOnce: true,
  });
  


  // 获取融合观看次数（DB累计 + Redis增量）
  const getViewCount = (video: VideoData): number => {
    const videoId = video.id.toString();
    const fusedData = fusedStats[videoId];
    
    const originalViews = video.view_count || video.views || 0;
    const fusedViews = fusedData?.views || originalViews;
    

    
    // 优先使用融合统计数据，否则使用初始数据
    return fusedViews;
  };

  // 批量获取融合统计数据
  const fetchFusedStats = async (videoList: VideoData[]) => {
    try {
      if (videoList.length === 0) return;

      const videoIds = videoList.map(video => video.id.toString());
      

      
      const response = await fetch(buildApiUrl('/api/video-stats/fused/batch'), {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ videoIds }),
      });



      if (!response.ok) {
        console.warn('融合统计API失败，使用初始数据');
        return;
      }

      const result = await response.json();
      
      if (result.success && result.data) {
        setFusedStats(result.data);
      }
    } catch (error) {
      console.warn('获取融合统计数据失败:', error);
    }
  };
  
  // 获取热门视频数据
  useEffect(() => {
    const fetchVideos = async () => {
      try {
        setIsLoading(true);
        const result = await getPopularVideos(12, locale);

        // 处理返回的数据结构
        let videos = [];
        if (result && typeof result === 'object') {
          if (result.success && result.data) {
            videos = result.data;
          } else if (Array.isArray(result)) {
            videos = result;
          }
        }

        if (videos && videos.length > 0) {
          setVideos(videos);

          // 获取融合统计数据
          await fetchFusedStats(videos);
        } else {
          setError(t('common.failedToLoadVideos'));
        }
      } catch (err) {
        console.error('获取热门视频出错:', err);
        setError(t('common.errorLoadingVideos'));
      } finally {
        setIsLoading(false);
      }
    };

    fetchVideos();
  }, [locale, t]);

  // 设置Socket.IO实时统计监听
  useEffect(() => {
    if (videos.length === 0) return;



    // 为每个视频设置统计更新监听
    const unsubscribeFunctions: (() => void)[] = [];

    videos.forEach(video => {
      const videoId = video.id.toString();
      
      const handleStatsUpdate = (updatedStats: VideoStats) => {

        
        // 更新融合统计数据
        setFusedStats(prevStats => {
          const existingFused = prevStats[videoId];
          if (existingFused) {
            return {
              ...prevStats,
              [videoId]: {
                ...existingFused,
                views: updatedStats.views,
                likes: updatedStats.likes,
                favorites: updatedStats.favorites,
                fusionTimestamp: Date.now()
              }
            };
          }
          // 如果没有融合数据，创建新的
          return {
            ...prevStats,
            [videoId]: {
              videoId,
              numericVideoId: parseInt(videoId),
              views: updatedStats.views,
              likes: updatedStats.likes,
              favorites: updatedStats.favorites,
              dbBase: { views: 0, likes: 0, favorites: 0 },
              redisIncrement: updatedStats,
              lastUpdated: Date.now(),
              fusionTimestamp: Date.now()
            }
          };
        });
      };

      // 订阅统计更新
      socketClient.current.subscribeToStats(videoId, handleStatsUpdate);
      
      // 记录取消订阅函数
      unsubscribeFunctions.push(() => {
        socketClient.current.unsubscribeFromStats(videoId);
      });
    });

    // 清理函数：取消所有订阅
    return () => {

      unsubscribeFunctions.forEach(unsubscribe => unsubscribe());
    };
  }, [videos]);

  // 定期刷新融合数据（确保数据一致性）
  useEffect(() => {
    if (videos.length === 0) return;

    const refreshInterval = setInterval(() => {
      fetchFusedStats(videos);
    }, 30000); // 每30秒刷新一次

    return () => clearInterval(refreshInterval);
  }, [videos]);
  
  // 高效的CSS动画，仅在需要时触发
  useEffect(() => {
    if (inView && titleRef.current) {
      titleRef.current.classList.add('animate-fade-in-up');
      
      // 动画完成后清理will-change
      setTimeout(() => {
        if (titleRef.current) {
          titleRef.current.classList.add('animation-complete');
        }
      }, 600);
      
      // 批量添加动画类，避免逐个操作DOM
      videoRefs.current.forEach((ref, index) => {
        if (ref) {
          setTimeout(() => {
            ref.classList.add('animate-fade-in-up');
            // 动画完成后清理will-change
            setTimeout(() => {
              if (ref) {
                ref.classList.add('animation-complete');
              }
            }, 600);
          }, index * 40); // 更快的错开时间
        }
      });
    }
  }, [inView, videos]);


  
  // 本地化URL
  const localizedUrl = (path: string) => {
    return `/${locale}${path}`;
  };
  
  return (
    <div className="w-full">
      <div ref={inViewRef}>
        <SectionHeader
          title={t('nav.popular')}
          viewMoreHref={localizedUrl("/popular")}
          viewMoreText={t('common.viewMore')}
        />
      </div>
      
      {isLoading && (
        <div className="flex justify-center items-center min-h-[400px]">
          <div className="text-center">
            <div className="relative">
              <div className="w-16 h-16 border-4 border-gray-700 border-t-white rounded-full animate-spin mx-auto mb-6"></div>
              <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-8 h-8 bg-white/20 rounded-full animate-pulse"></div>
            </div>
            <p className="text-gray-300 text-lg font-light">{t('common.loading') || '正在加载...'}</p>
          </div>
        </div>
      )}
      
      {error && (
        <div className="p-4 text-center text-red-500">
          {error}
        </div>
      )}
      
      {!isLoading && videos.length === 0 && !error && (
        <div className="p-4 text-center text-gray-400">
          {t('common.noVideosFound')}
        </div>
      )}
      
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        {videos.slice(0, 12).map((video, index) => (
          <div
            key={video.id}
            ref={(el) => setRef(el, index)}
            className="overflow-hidden"
          >
            <VideoCard
              video={video}
              locale={locale}
              size="small"
              fusedStats={fusedStats[video.id.toString()]}
            />
          </div>
        ))}
      </div>
    </div>
  );
};

export default PopularVideos; 