'use client';

import { ReactNode, createContext, useContext, useCallback } from 'react';
import { Translations } from '@/i18n/types';
import { defaultLocale } from '@/i18n';

// 创建翻译上下文并导出
export const TranslationsContext = createContext<Translations | null>(null);

// 翻译提供器组件
export function TranslationsProvider({
  children,
  translations,
}: {
  children: ReactNode;
  translations: Translations;
}) {
  return (
    <TranslationsContext.Provider value={translations}>
      {children}
    </TranslationsContext.Provider>
  );
}

// 多语言默认翻译
const defaultTranslations: Record<string, Partial<Translations>> = {
  'zh-CN': {
    common: {
      viewMore: '查看更多',
      relatedVideos: '相关视频',
      moreVideos: '更多视频',
      recommendations: '推荐视频',
      noResults: '没有找到结果',
      loading: '加载中...',
      watchNow: '立即观看',
      addToFavorites: '添加到收藏',
      removeFromFavorites: '从收藏中移除',
      published: '发布于',
      weekly: '每周',
      monthly: '每月',
      yearly: '年度',
      newcomers: '新人排行',
      videos: '视频',
      popular: '热门',
      nextVideo: '下一个视频',
      previousVideo: '上一个视频',
      switchToVideo: '切换到视频',
      moreRecommendations: '更多推荐',
      recommendedVideo: '推荐视频',
      recommendedPopular: '推荐热门视频',
      scrollLeft: '向左滚动',
      scrollRight: '向右滚动'
    },
    search: {
      placeholder: '搜索影片、演员...',
      searching: '搜索中',
      noSuggestions: '暂无搜索建议',
      noResults: '没有找到结果',
      trending: '热门搜索',
      recentSearches: '最近搜索',
      search: '搜索',
      advancedSearch: '高级搜索',
      filters: '筛选器',
      clearFilters: '清除筛选器'
    },
    profile: {
      tabs: {
        favorites: '我的收藏',
        history: '观看历史',
        following: '关注演员',
        recommendations: '个性化推荐',
        settings: '设置偏好'
      },
      following: {
        title: '关注的演员',
        browse: '浏览更多演员',
        empty: '您还没有关注任何演员',
        discover: '发现演员',
        unfollow: '取消关注'
      },
      recommendations: {
        title: '为您推荐',
        refresh: '刷新推荐',
        empty: '正在为您生成推荐...',
        like: '喜欢',
        dislike: '不喜欢'
      },
      settings: {
        title: '设置偏好',
        subtitle: '自定义您的使用体验',
        appearance: '外观设置',
        theme: '主题',
        light: '浅色',
        dark: '深色',
        language: '语言',
        playback: '播放设置',
        autoplay: '自动播放',
        subtitles: '默认显示字幕',
        quality: '默认视频质量',
        notifications: '通知设置',
        enable_notifications: '启用通知'
      }
    }
  },
  'en-US': {
    common: {
      viewMore: 'View More',
      relatedVideos: 'Related Videos',
      moreVideos: 'More Videos',
      recommendations: 'Recommendations',
      noResults: 'No results found',
      loading: 'Loading...',
      watchNow: 'Watch Now',
      addToFavorites: 'Add to Favorites',
      removeFromFavorites: 'Remove from Favorites',
      published: 'Published on',
      weekly: 'Weekly',
      monthly: 'Monthly',
      yearly: 'Yearly',
      newcomers: 'Newcomers',
      videos: 'Videos',
      popular: 'Popular',
      nextVideo: 'Next Video',
      previousVideo: 'Previous Video',
      switchToVideo: 'Switch to Video',
      moreRecommendations: 'More Recommendations',
      recommendedVideo: 'Recommended Video',
      recommendedPopular: 'Recommended Popular',
      scrollLeft: 'Scroll Left',
      scrollRight: 'Scroll Right'
    },
    search: {
      placeholder: 'Search movies, actresses...',
      searching: 'Searching',
      noSuggestions: 'No suggestions available',
      noResults: 'No results found',
      trending: 'Trending Searches',
      recentSearches: 'Recent Searches',
      search: 'Search',
      advancedSearch: 'Advanced Search',
      filters: 'Filters',
      clearFilters: 'Clear Filters'
    },
    profile: {
      tabs: {
        favorites: 'Favorites',
        history: 'History',
        following: 'Following',
        recommendations: 'Recommendations',
        settings: 'Settings'
      },
      following: {
        title: 'Following Actresses',
        browse: 'Browse More Actresses',
        empty: "You haven't followed any actresses yet",
        discover: 'Discover Actresses',
        unfollow: 'Unfollow'
      },
      recommendations: {
        title: 'Recommended For You',
        refresh: 'Refresh Recommendations',
        empty: 'Generating recommendations for you...',
        like: 'Like',
        dislike: 'Dislike'
      },
      settings: {
        title: 'Settings',
        subtitle: 'Customize your experience',
        appearance: 'Appearance',
        theme: 'Theme',
        light: 'Light',
        dark: 'Dark',
        language: 'Language',
        playback: 'Playback',
        autoplay: 'Autoplay',
        subtitles: 'Default subtitles',
        quality: 'Default video quality',
        notifications: 'Notifications',
        enable_notifications: 'Enable notifications'
      }
    }
  },
  'ja-JP': {
    common: {
      viewMore: 'もっと見る',
      relatedVideos: '関連動画',
      moreVideos: 'その他の動画',
      recommendations: 'おすすめ動画',
      noResults: '結果が見つかりません',
      loading: '読み込み中...',
      watchNow: '今すぐ見る',
      addToFavorites: 'お気に入りに追加',
      removeFromFavorites: 'お気に入りから削除',
      published: '公開日',
      weekly: '週間',
      monthly: '月間',
      yearly: '年間',
      newcomers: '新人ランキング',
      videos: '動画',
      popular: '人気',
      nextVideo: '次の動画',
      previousVideo: '前の動画',
      switchToVideo: '動画に切り替え',
      moreRecommendations: 'その他のおすすめ',
      recommendedVideo: 'おすすめ動画',
      recommendedPopular: '人気のおすすめ',
      scrollLeft: '左にスクロール',
      scrollRight: '右にスクロール'
    },
    search: {
      placeholder: '映画、女優を検索...',
      searching: '検索中',
      noSuggestions: '検索候補はありません',
      noResults: '結果が見つかりません',
      trending: '人気の検索',
      recentSearches: '最近の検索',
      search: '検索',
      advancedSearch: '詳細検索',
      filters: 'フィルター',
      clearFilters: 'フィルターをクリア'
    },
    profile: {
      tabs: {
        favorites: 'お気に入り',
        history: '視聴履歴',
        following: 'フォロー中',
        recommendations: 'おすすめ',
        settings: '設定'
      },
      following: {
        title: 'フォロー中の女優',
        browse: '女優をもっと見る',
        empty: 'まだ女優をフォローしていません',
        discover: '女優を発見',
        unfollow: 'フォロー解除'
      },
      recommendations: {
        title: 'あなたへのおすすめ',
        refresh: 'おすすめを更新',
        empty: 'おすすめを生成中...',
        like: 'いいね',
        dislike: 'よくない'
      },
      settings: {
        title: '設定',
        subtitle: 'お好みに合わせてカスタマイズ',
        appearance: '外観設定',
        theme: 'テーマ',
        light: 'ライト',
        dark: 'ダーク',
        language: '言語',
        playback: '再生設定',
        autoplay: '自動再生',
        subtitles: 'デフォルト字幕',
        quality: 'デフォルト動画品質',
        notifications: '通知設定',
        enable_notifications: '通知を有効にする'
      }
    }
  }
};

// 获取当前语言环境
function getCurrentLocale(): string {
  if (typeof window !== 'undefined') {
    const pathname = window.location.pathname;
    // 先尝试匹配 /zh-CN/ 格式
    let localeMatch = pathname.match(/^\/([a-z]{2}-[A-Z]{2})\//);
    if (localeMatch) {
      return localeMatch[1];
    }
    // 再尝试匹配 /zh/ 格式
    localeMatch = pathname.match(/^\/([a-z]{2})\//);
    if (localeMatch) {
      const simpleLocale = localeMatch[1];
      // 简单语言代码映射到完整语言代码
      const localeMap: Record<string, string> = {
        'zh': 'zh',
        'zh-Hans': 'zh-Hans',
        'en': 'en',
        'ja': 'ja'
      };
      return localeMap[simpleLocale] || defaultLocale;
    }
  }
  return defaultLocale;
}

// 自定义钩子获取翻译内容 - 确保在顶层无条件调用
export function useClientTranslations() {
  const translations = useContext(TranslationsContext);
  
  // 翻译函数 - 使用 useCallback 稳定函数引用
  const t = useCallback((key: string, params?: Record<string, string | number>): string => {
    try {
      // 尝试从上下文获取翻译
      if (translations) {
        // 使用点表示法解析嵌套属性
        const segments = key.split('.');
        let value: any = translations;
        
        for (const segment of segments) {
          if (!value || typeof value !== 'object') {
            value = undefined;
            break;
          }
          value = value[segment];
        }
        
        // 如果找到翻译
        if (typeof value === 'string') {
          // 如果有参数，替换占位符
          if (params) {
            let result = value;
            Object.entries(params).forEach(([paramKey, paramValue]) => {
              result = result.replace(`{{${paramKey}}}`, String(paramValue));
            });
            return result;
          }
          
          return value;
        }
      }
      
      // 如果上下文中没有找到翻译，尝试从默认翻译中获取
      const currentLocale = getCurrentLocale();
      const localeDefaults = defaultTranslations[currentLocale] || defaultTranslations[defaultLocale];
      
      if (localeDefaults) {
        const segments = key.split('.');
        if (segments.length >= 2) {
          const section = segments[0] as keyof typeof localeDefaults;
          const subKey = segments.slice(1).join('.'); // 处理嵌套路径
          
          const sectionTranslations = localeDefaults[section];
          if (sectionTranslations && typeof sectionTranslations === 'object') {
            // 递归处理嵌套路径
            let nestedValue: any = sectionTranslations;
            const subSegments = subKey.split('.');
            
            for (const segment of subSegments) {
              if (nestedValue && typeof nestedValue === 'object') {
                nestedValue = (nestedValue as Record<string, unknown>)[segment];
              } else {
                nestedValue = undefined;
                break;
              }
            }
            
            // 处理参数替换
            if (params && typeof nestedValue === 'string') {
              let result = nestedValue;
              Object.entries(params).forEach(([paramKey, paramValue]) => {
                result = result.replace(`{{${paramKey}}}`, String(paramValue));
              });
              return result;
            }
            
            if (typeof nestedValue === 'string') {
              return nestedValue;
            }
          }
        }
      }
      
      // 如果都没有找到，返回键名
      return key.split('.').pop() || key;
    } catch (error) {
      console.error(`Translation error for key: ${key}`, error);
      return key.split('.').pop() || key;
    }
  }, [translations]); // 只有当 translations 改变时才会创建新的函数引用
  
  return { t };
} 