'use client';

import { Suspense, useEffect, useState } from 'react';
import dynamic from 'next/dynamic';

// 使用共享加载状态
let isPlayerStylesLoaded = false;

// 自定义加载组件，提供更好的用户体验
const LoadingFallback = () => (
  <div className="aspect-video bg-black rounded-xl flex items-center justify-center overflow-hidden relative max-w-4xl mx-auto shadow-2xl border border-gray-700/20">
    {/* 静态背景渐变 */}
    <div className="absolute inset-0 bg-gradient-to-b from-gray-800 to-black opacity-80"></div>
    
    {/* 加载动画 */}
    <div className="relative z-10 flex flex-col items-center space-y-4">
      <div className="w-14 h-14 border-4 border-red-500 border-t-transparent rounded-full animate-spin"></div>
      <div className="text-white font-medium text-center max-w-xs">
        <p className="text-lg">正在准备视频播放器...</p>
        <p className="text-gray-400 text-sm mt-2">首次加载可能需要几秒钟</p>
      </div>
    </div>
  </div>
);

// 动态导入VideoPlayer组件，禁用SSR
const VideoPlayer = dynamic(() => import('@/components/VideoPlayer'), {
  ssr: false,
  loading: () => <LoadingFallback />
});

interface VideoSectionProps {
  videoUrl: string;
  posterUrl: string;
}

export default function VideoSection({ videoUrl, posterUrl }: VideoSectionProps) {
  const [isStylesLoaded, setIsStylesLoaded] = useState(isPlayerStylesLoaded);
  
  // 确保客户端加载时应用Plyr样式并只加载一次
  useEffect(() => {
    // 如果全局状态已经表明样式已加载，则跳过
    if (isPlayerStylesLoaded) return;
    
    // 检查样式是否已加载
    const plyrStyleCheck = document.getElementById('plyr-styles');
    if (!plyrStyleCheck) {
      // 动态创建样式标签
      const linkElem = document.createElement('link');
      linkElem.id = 'plyr-styles';
      linkElem.rel = 'stylesheet';
      linkElem.href = 'https://cdn.plyr.io/3.7.8/plyr.css';
      
      // 监听样式加载完成事件
      linkElem.onload = () => {
        isPlayerStylesLoaded = true;
        setIsStylesLoaded(true);
      };
      
      document.head.appendChild(linkElem);
    } else {
      // 样式已存在
      isPlayerStylesLoaded = true;
      setIsStylesLoaded(true);
    }
  }, []);

  return (
    <div className="w-full flex justify-center">
      <div className="transition-all duration-500 ease-in-out w-full max-w-5xl shadow-2xl rounded-xl overflow-hidden transform hover:scale-[1.01]">
        <Suspense fallback={<LoadingFallback />}>
          <VideoPlayer videoUrl={videoUrl} posterUrl={posterUrl} />
        </Suspense>
      </div>
    </div>
  );
} 