'use client';

import { useState, useEffect, useRef } from 'react';
import Link from 'next/link';
import gsap from 'gsap';
import { FiSearch, FiMenu, FiX, FiUser, FiLogIn, FiTag, FiChevronDown, FiHeart, FiChevronRight, FiTrendingUp, FiList, FiUsers, FiLogOut, FiSettings } from 'react-icons/fi';
import TagsDrawer from './TagsDrawer';
import { useRouter, usePathname } from 'next/navigation';
import LanguageSwitcher from './LanguageSwitcher';
import { useClientTranslations } from './TranslationsProvider';
import { Locale } from '@/i18n/types';
import { locales } from '@/i18n';
import Image from 'next/image';
import { useAuth } from '@/context/AuthContext';
import { useMicroInteractions, useAnimationPerformance } from '@/hooks/useEnhancedAnimations';

// 下拉菜单数据
const Navbar = () => {
  const [isScrolled, setIsScrolled] = useState(false);
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [isTagsDrawerOpen, setIsTagsDrawerOpen] = useState(false);
  const [activeDropdown, setActiveDropdown] = useState<string | null>(null);
  const [hoveredDropdown, setHoveredDropdown] = useState<string | null>(null);
  const [isNavVisible, setIsNavVisible] = useState(true);
  const [isUserDropdownOpen, setIsUserDropdownOpen] = useState(false);
  
  // 使用AuthContext替代独立的用户状态管理
  const { user, isLoading, logout: authLogout } = useAuth();

  // 使用增强动画hooks
  const { createRippleEffect } = useMicroInteractions();
  const { getOptimizedConfig, prefersReducedMotion } = useAnimationPerformance();
  
  // 添加搜索建议相关状态
  const [suggestions, setSuggestions] = useState<any[]>([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [isSearchFocused, setIsSearchFocused] = useState(false);
  const [suggestionLoading, setSuggestionLoading] = useState(false);
  
  // 添加动态菜单数据状态
  const [dynamicMenus, setDynamicMenus] = useState<{
    popularCategories: Array<{name: string, link: string}>;
    topCategories: Array<{name: string, link: string}>;
    popularActresses: Array<{name: string, link: string}>;
  }>({
    popularCategories: [],
    topCategories: [],
    popularActresses: []
  });
  
  const router = useRouter();
  const pathname = usePathname();
  
  // 在顶层无条件调用useClientTranslations钩子
  const { t } = useClientTranslations();
  
  // 获取当前语言环境
  const segments = pathname?.split('/') || [];
  const currentLocale = (segments[1] && locales.includes(segments[1] as Locale)) ? segments[1] as Locale : 'zh-Hans';

  // 添加refs
  const searchInputRef = useRef<HTMLInputElement>(null);
  const searchSuggestionsRef = useRef<HTMLDivElement>(null);
  const mobileSearchInputRef = useRef<HTMLInputElement>(null);
  const mobileSearchSuggestionsRef = useRef<HTMLDivElement>(null);
  const suggestionDebounceRef = useRef<NodeJS.Timeout | null>(null);
  
  // 添加初始化useEffect
  useEffect(() => {
    // 获取动态菜单数据
    fetchDynamicMenuData();
  }, [pathname]);
  
  // 检查导航到的路径是否应包含语言前缀
  const getLocalizedPath = (path: string): string => {
    // 如果路径已经包含语言前缀，直接返回
    if (path.match(/^\/(zh-CN|en-US|ja-JP)/)) {
      return path;
    }
    
    // 否则添加当前语言前缀
    const cleanPath = path.startsWith('/') ? path.substring(1) : path;
    return `/${currentLocale}/${cleanPath}`;
  };
  
  // 获取当前语言的本地化URL
  const localizedUrl = (path: string) => {
    // 验证是否是有效的语言代码
    if (locales.includes(currentLocale as Locale)) {
      return `/${currentLocale}${path}`;
    }
    
    // 默认使用简体中文
    return `/zh-Hans${path}`;
  };
  
  // 获取动态菜单数据
  const fetchDynamicMenuData = async () => {
    try {
      // 使用简单的静态数据来避免500错误
      const staticCategories = [
        { name: '无码高清', link: localizedUrl('/category/uncensored'), count: 1280 },
        { name: 'VR特辑', link: localizedUrl('/category/vr'), count: 580 },
        { name: '中文字幕', link: localizedUrl('/category/subtitle'), count: 890 },
        { name: '新人首秀', link: localizedUrl('/category/newcomer'), count: 320 },
      ];

      const staticActresses = [
        { name: '三上悠亚', link: localizedUrl('/actress/mikami-yua') },
        { name: '深田咏美', link: localizedUrl('/actress/fukada-eimi') },
        { name: '桥本有菜', link: localizedUrl('/actress/hashimoto-arina') },
        { name: '水卜樱', link: localizedUrl('/actress/miura-sakura') },
        { name: '明日花绮罗', link: localizedUrl('/actress/asuka-kirara') },
        { name: '白石茉莉奈', link: localizedUrl('/actress/shiraishi-marina') },
      ];

      setDynamicMenus({
        popularCategories: staticCategories.slice(0, 2).map(cat => ({
          name: `${cat.name} (${cat.count}部)`,
          link: cat.link
        })),
        topCategories: staticCategories.slice(2).map(cat => ({
          name: cat.name,
          link: cat.link
        })),
        popularActresses: staticActresses.slice(0, 6)
      });

    } catch (error) {
      console.error('获取动态菜单数据失败:', error);
      // 使用空数据作为备用
      setDynamicMenus({
        popularCategories: [],
        topCategories: [],
        popularActresses: []
      });
    }
  };

  // 生成slug的工具函数
  const generateSlug = (name: string): string => {
    return name
      .toLowerCase()
      .replace(/\s+/g, '-')
      .replace(/[^\w\-\u4e00-\u9fa5]/g, '')
      .replace(/\-+/g, '-')
      .trim();
  };
  
  // 初始Logo动画
  useEffect(() => {
    gsap.fromTo(
      logoRef.current,
      { scale: 0.8, opacity: 0 },
      { scale: 1, opacity: 1, duration: 0.5 }
    );
  }, []);
  
  // 处理滚动效果和导航栏隐藏/显示
  useEffect(() => {
    const handleScroll = () => {
      const currentScrollY = window.scrollY;
      
      // 设置滚动状态（用于背景颜色变化）
      setIsScrolled(currentScrollY > 10);
      
      // 只有滚动超过阈值才考虑隐藏/显示导航栏
      if (Math.abs(currentScrollY - prevScrollY.current) > scrollThreshold) {
        // 向下滚动且不在顶部，隐藏导航栏
        if (currentScrollY > prevScrollY.current && currentScrollY > 100) {
          setIsNavVisible(false);
        } 
        // 向上滚动，显示导航栏
        else if (currentScrollY < prevScrollY.current) {
          setIsNavVisible(true);
        }
        
        prevScrollY.current = currentScrollY;
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);
  
  // 初始化导航栏位置
  useEffect(() => {
    if (navbarRef.current) {
      // 确保导航栏在初始加载时处于正确位置
      gsap.set(navbarRef.current, { y: 0, clearProps: "transform" });
    }
  }, []);

  // 处理导航栏显示/隐藏动画
  useEffect(() => {
    if (navbarRef.current) {
      gsap.to(navbarRef.current, {
        y: isNavVisible ? 0 : -100,
        duration: 0.3,
        ease: "power2.out",
        // 确保动画完成后重置transform
        onComplete: () => {
          if (navbarRef.current && isNavVisible) {
            gsap.set(navbarRef.current, { clearProps: "transform" });
          }
        }
      });
    }
  }, [isNavVisible]);
  
  // 处理移动菜单动画
  useEffect(() => {
    if (mobileMenuRef.current) {
      if (isMenuOpen) {
        // 显示菜单
        gsap.set(mobileMenuRef.current, { 
          height: 'auto',
          opacity: 0 
        });
        gsap.to(mobileMenuRef.current, {
          height: 'auto',
          opacity: 1,
          duration: 0.3,
          onStart: () => {
            if (mobileMenuRef.current) {
              mobileMenuRef.current.style.display = 'block';
            }
          }
        });
      } else {
        // 隐藏菜单
        gsap.to(mobileMenuRef.current, {
          height: 0,
          opacity: 0,
          duration: 0.2,
          onComplete: () => {
            if (mobileMenuRef.current) {
              mobileMenuRef.current.style.display = 'none';
            }
          }
        });
      }
    }
  }, [isMenuOpen]);

  // 处理用户下拉菜单显示/隐藏
  useEffect(() => {
    const handleOutsideClick = (e: MouseEvent) => {
      if (
        userDropdownRef.current && 
        !userDropdownRef.current.contains(e.target as Node) &&
        isUserDropdownOpen
      ) {
        setIsUserDropdownOpen(false);
      }
    };
    
    document.addEventListener('mousedown', handleOutsideClick);
    
    return () => {
      document.removeEventListener('mousedown', handleOutsideClick);
    };
  }, [isUserDropdownOpen]);

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchTerm.trim()) {
      // 使用localizedUrl生成带有语言前缀的搜索路径
      const searchPath = localizedUrl(`/search?q=${encodeURIComponent(searchTerm)}`);
      
      router.push(searchPath);
      setSearchTerm('');
      setIsMenuOpen(false);
      setShowSuggestions(false);
    }
  };

  // 获取搜索建议
  const fetchSuggestions = async (term: string) => {
    if (term.trim().length === 0) {
      setSuggestions([]);
      setShowSuggestions(false);
      return;
    }

    try {
      setSuggestionLoading(true);
      const response = await fetch(`/api/search/suggestions?q=${encodeURIComponent(term)}&limit=6`);
      if (response.ok) {
        const data = await response.json();
        if (data.suggestions) {
          setSuggestions(data.suggestions);
          setShowSuggestions(data.suggestions.length > 0 && isSearchFocused);
        }
      }
    } catch (error) {
      console.error('获取搜索建议失败:', error);
    } finally {
      setSuggestionLoading(false);
    }
  };

  // 处理搜索输入变化
  const handleSearchInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchTerm(value);
    
    // 防抖获取建议
    if (suggestionDebounceRef.current) {
      clearTimeout(suggestionDebounceRef.current);
    }
    
    suggestionDebounceRef.current = setTimeout(() => {
      if (value.trim() && isSearchFocused) {
        fetchSuggestions(value);
      }
    }, 300);
  };

  // 处理建议点击
  const handleSuggestionClick = (suggestion: any) => {
    const searchText = typeof suggestion === 'string' ? suggestion : suggestion.text;
    setSearchTerm(searchText);
    setShowSuggestions(false);
    
    // 跳转到搜索页面
    const searchPath = localizedUrl(`/search?q=${encodeURIComponent(searchText)}`);
    router.push(searchPath);
    setSearchTerm('');
  };

  // 处理搜索框焦点
  const handleSearchFocus = () => {
    setIsSearchFocused(true);
    if (searchTerm.trim()) {
      fetchSuggestions(searchTerm);
    }
  };

  const handleSearchBlur = () => {
    // 延迟隐藏建议，以便点击建议有时间执行
    setTimeout(() => {
      setIsSearchFocused(false);
      setShowSuggestions(false);
    }, 200);
  };

  // 点击外部关闭建议
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        searchSuggestionsRef.current && 
        !searchSuggestionsRef.current.contains(event.target as Node) &&
        searchInputRef.current &&
        !searchInputRef.current.contains(event.target as Node) &&
        mobileSearchSuggestionsRef.current &&
        !mobileSearchSuggestionsRef.current.contains(event.target as Node) &&
        mobileSearchInputRef.current &&
        !mobileSearchInputRef.current.contains(event.target as Node)
      ) {
        setShowSuggestions(false);
        setIsSearchFocused(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleMouseEnter = (dropdown: string) => {
    if (closeTimeoutRef.current) clearTimeout(closeTimeoutRef.current);
    setHoveredDropdown(dropdown);
    setActiveDropdown(dropdown);
  };

  const handleMouseLeave = () => {
    closeTimeoutRef.current = setTimeout(() => {
      setActiveDropdown(null);
    }, 300);
    
    setHoveredDropdown(null);
  };

  const setRef = (key: string) => (el: HTMLDivElement | null) => {
    dropdownRefs.current[key] = el;
  };
  
  // 下拉菜单数据 - 使用动态数据和翻译文本
  const dropdownMenus = {
    popular: [
      { name: `${t('nav.popular')} - ${t('common.weekly')}`, link: localizedUrl('/popular/weekly') },
      { name: `${t('nav.popular')} - ${t('common.monthly')}`, link: localizedUrl('/popular/monthly') },
      { name: `${t('nav.popular')} - ${t('common.yearly')}`, link: localizedUrl('/popular/yearly') },
      { name: `${t('common.newcomers')}`, link: localizedUrl('/popular/newcomers') },
      ...dynamicMenus.popularCategories.slice(0, 2), // 添加真实的热门分类
    ],
    actresses: [
      { name: t('actress.popular'), link: localizedUrl('/actress/popular') },
      { name: t('actress.newcomer'), link: localizedUrl('/actress/newcomer') },
      { name: t('actress.ranking'), link: localizedUrl('/actress/ranking') },
      ...dynamicMenus.popularActresses.slice(0, 3), // 添加真实的女优数据
    ]
  };
  
  const prevScrollY = useRef<number>(0);
  const navbarRef = useRef<HTMLDivElement>(null);
  const logoRef = useRef<HTMLDivElement>(null);
  const mobileMenuRef = useRef<HTMLDivElement>(null);
  const tagButtonRef = useRef<HTMLButtonElement>(null);
  const userDropdownRef = useRef<HTMLDivElement>(null);
  const dropdownRefs = useRef<{[key: string]: HTMLDivElement | null}>({
    popular: null,
    actresses: null
  });
  const closeTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const scrollThreshold = 50; // 滚动阈值，超过这个值才触发导航栏隐藏/显示

  // 检查导航到的路径是否应包含语言前缀
  const shouldIncludeLocale = (path: string): boolean => {
    return !locales.some(locale => path.startsWith(`/${locale}`));
  };

  return (
    <div
        ref={navbarRef}
      className={`fixed top-0 left-0 w-full z-50 transition-all duration-500 ${
        isScrolled ? 'glass-effect-strong shadow-strong py-2' : 'bg-gradient-to-b from-gray-900 to-transparent py-3'
        }`}
      style={{ transform: 'translateY(0px)' }}
      >
      <div className="container mx-auto px-4">
          <div className="flex items-center justify-between">
            {/* Logo */}
          <div className="flex items-center">
            <div ref={logoRef} className="mr-8">
              <Link 
                href={localizedUrl('/')}
                className="text-white font-bold text-xl tracking-tight flex items-center"
              >
                <span className="text-red-500">JAV</span>
                <span className="text-white">FLIX</span>
                <span className="text-xs ml-1 text-gray-400">.TV</span>
              </Link>
              </div>
            
            {/* 桌面导航 */}
            <nav className="hidden md:flex items-center space-x-4">
              <Link
                href={localizedUrl('/')}
                className="btn-micro text-white hover:text-red-400 transition-all duration-300 px-2 py-1 rounded-lg whitespace-nowrap"
                onClick={!prefersReducedMotion() ? createRippleEffect : undefined}
              >
                {t('nav.home')}
              </Link>
              <Link
                href={localizedUrl('/new')}
                className="btn-micro text-white hover:text-red-400 transition-all duration-300 px-2 py-1 rounded-lg whitespace-nowrap"
                onClick={!prefersReducedMotion() ? createRippleEffect : undefined}
              >
                {t('nav.new')}
            </Link>
              <div
                className="relative group"
                onMouseEnter={() => handleMouseEnter('popular')}
                onMouseLeave={handleMouseLeave}
              >
                <Link href={localizedUrl('/popular')} className="text-white hover:text-red-400 transition flex items-center px-2 py-1 whitespace-nowrap">
                  {t('nav.popular')} <FiChevronDown className="ml-1" />
                </Link>
                {activeDropdown === 'popular' && (
                <div 
                  ref={setRef('popular')}
                    className="absolute top-full left-0 bg-gray-800 py-2 rounded-md shadow-lg w-48 z-10"
                >
                  {dropdownMenus.popular.map((item, index) => (
                    <Link 
                      key={index} 
                      href={item.link} 
                        className="block px-4 py-2 text-white hover:bg-gray-700 transition"
                    >
                      {item.name}
                    </Link>
                  ))}
                </div>
                )}
              </div>
              <div
                className="relative group"
                onMouseEnter={() => handleMouseEnter('actresses')}
                onMouseLeave={handleMouseLeave}
              >
                <Link href={localizedUrl('/actress')} className="text-white hover:text-red-400 transition flex items-center px-2 py-1 whitespace-nowrap">
                  {t('nav.actresses')} <FiChevronDown className="ml-1" />
                </Link>
                {activeDropdown === 'actresses' && (
                <div 
                  ref={setRef('actresses')}
                    className="absolute top-full left-0 bg-gray-800 py-2 rounded-md shadow-lg w-48 z-10"
                >
                  {dropdownMenus.actresses.map((item, index) => (
                    <Link 
                      key={index} 
                      href={item.link} 
                        className="block px-4 py-2 text-white hover:bg-gray-700 transition"
                    >
                      {item.name}
                    </Link>
                  ))}
                </div>
                )}
              </div>
              <button
                ref={tagButtonRef}
                onClick={() => setIsTagsDrawerOpen(true)}
                className="flex items-center text-white hover:text-red-400 transition px-2 py-1 whitespace-nowrap"
              >
                <FiTag className="mr-1" /> {t('category.allTags')}
              </button>
            </nav>
          </div>

          {/* 右侧工具栏 */}
          <div className="flex items-center space-x-3 lg:space-x-4">
            {/* 语言切换器 */}
            <div className="hidden md:block">
              <LanguageSwitcher />
            </div>

            {/* 搜索框 */}
            <form onSubmit={handleSearch} className="hidden md:flex items-center relative">
                <input
                  ref={searchInputRef}
                  type="text"
                placeholder={t('search.placeholder')}
                  value={searchTerm}
                  onChange={handleSearchInputChange}
                  onFocus={handleSearchFocus}
                  onBlur={handleSearchBlur}
                className="bg-gray-800 text-white rounded-full py-1 px-4 pr-10 focus:outline-none focus:ring-1 focus:ring-red-500 w-32 lg:w-48 xl:w-64 text-sm"
                />
              <button
                type="submit"
                className="absolute right-0 top-0 bottom-0 px-3 text-gray-400 hover:text-white"
              >
                <FiSearch />
              </button>
              
              {/* 搜索建议下拉框 */}
              {showSuggestions && (
                <div 
                  ref={searchSuggestionsRef}
                  className="absolute top-full left-0 right-0 mt-1 bg-gray-800 border border-gray-700 rounded-md shadow-lg z-50 max-h-80 overflow-y-auto"
                >
                  {suggestionLoading ? (
                    <div className="px-4 py-2 text-gray-400 text-sm">
                      {t('search.searching')}...
                    </div>
                  ) : suggestions.length > 0 ? (
                    suggestions.map((suggestion, index) => (
                      <div
                        key={suggestion.id || index}
                        onClick={() => handleSuggestionClick(suggestion)}
                        className="px-4 py-2 hover:bg-gray-700 cursor-pointer text-white text-sm border-b border-gray-700 last:border-b-0 flex items-center space-x-3"
                      >
                        {suggestion.image && (
                          <div className="flex-shrink-0 w-8 h-8 rounded overflow-hidden">
                            <Image 
                              src={suggestion.image} 
                              alt=""
                              width={32}
                              height={32}
                              className="w-full h-full object-cover"
                            />
                          </div>
                        )}
                        <div className="flex-1 min-w-0">
                          <div className="font-medium truncate">
                            {suggestion.text || suggestion}
                          </div>
                          {suggestion.meta && (
                            <div className="text-xs text-gray-400 truncate">
                              {suggestion.meta}
                            </div>
                          )}
                        </div>
                        {suggestion.type && (
                          <div className="flex-shrink-0">
                            <span className={`px-2 py-1 rounded text-xs ${
                              suggestion.type === 'movie' 
                                ? 'bg-blue-600 text-blue-100' 
                                : 'bg-purple-600 text-purple-100'
                            }`}>
                              {suggestion.type === 'movie' ? (t('common.videos') || '影片') : (t('nav.actresses') || '演员')}
                            </span>
                          </div>
                        )}
                      </div>
                    ))
                  ) : (
                    <div className="px-4 py-2 text-gray-400 text-sm">
                      {t('search.noSuggestions')}
                    </div>
                  )}
                </div>
              )}
              </form>
              
            {/* 用户区域 - 桌面 */}
            <div className="hidden md:block relative">
              {!isLoading && (
                user ? (
                  <div className="relative" ref={userDropdownRef}>
                  <button 
                    onClick={() => setIsUserDropdownOpen(!isUserDropdownOpen)}
                      className="flex items-center space-x-2 text-white bg-gray-800 hover:bg-gray-700 transition rounded-full py-1 px-3"
                  >
                      <div className="w-7 h-7 rounded-full bg-red-500 flex items-center justify-center overflow-hidden user-avatar">
                        {user.avatar ? (
                          <Image 
                            src={user.avatar} 
                            alt={user.username}
                            width={28}
                            height={28}
                            className="w-full h-full object-cover"
                          />
                        ) : (
                          <FiUser className="text-white" />
                        )}
                    </div>
                      <span className="text-sm font-medium user-name">{user.username}</span>
                  </button>
                  
                    {isUserDropdownOpen && (
                      <div className="absolute right-0 mt-2 w-48 bg-gray-800 rounded-md shadow-lg py-1 z-10">
                        <Link
                          href={localizedUrl('/profile')}
                          className="block px-4 py-2 text-sm text-white hover:bg-gray-700"
                        >
                          <FiUser className="inline mr-2" /> {t('user.profile')}
                        </Link>
                        <Link
                          href={localizedUrl('/profile/favorites')}
                          className="block px-4 py-2 text-sm text-white hover:bg-gray-700"
                        >
                          <FiHeart className="inline mr-2" /> {t('user.favorites')}
                        </Link>
                    <Link 
                          href={localizedUrl('/profile/history')}
                          className="block px-4 py-2 text-sm text-white hover:bg-gray-700"
                    >
                          <FiList className="inline mr-2" /> {t('user.history')}
                    </Link>
                    <Link 
                          href={localizedUrl('/profile/settings')}
                          className="block px-4 py-2 text-sm text-white hover:bg-gray-700"
                        >
                          <FiSettings className="inline mr-2" /> {t('user.settings')}
                        </Link>
                        <div className="border-t border-gray-700 my-1"></div>
                        <button
                          onClick={authLogout}
                          className="block w-full text-left px-4 py-2 text-sm text-white hover:bg-gray-700"
                        >
                          <FiLogOut className="inline mr-2" /> {t('nav.logout')}
                        </button>
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="flex space-x-2">
                    <Link
                      href={localizedUrl('/auth/login')}
                      className="flex items-center px-3 py-1 text-white hover:text-red-400 transition"
                    >
                      <FiLogIn className="mr-1" /> {t('nav.login')}
                    </Link>
                    <Link 
                      href={localizedUrl('/auth/signup')}
                      className="bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded-md transition"
                    >
                      {t('nav.signup')}
                    </Link>
                  </div>
                )
              )}
            </div>

            {/* 移动端菜单按钮 */}
              <button 
                onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="md:hidden text-white text-xl focus:outline-none"
              >
              {isMenuOpen ? <FiX /> : <FiMenu />}
              </button>
          </div>
        </div>

        {/* 移动端菜单 */}
        <div 
          ref={mobileMenuRef} 
          className="md:hidden bg-gray-800 rounded-lg mt-4 shadow-lg px-4 overflow-hidden"
          style={{ display: 'none', opacity: 0, height: 0 }}
        >
          {/* 移动端搜索框 */}
          <form onSubmit={handleSearch} className="my-4 flex items-center relative">
              <input
                ref={mobileSearchInputRef}
                type="text"
              placeholder={t('search.placeholder')}
                value={searchTerm}
                onChange={handleSearchInputChange}
                onFocus={handleSearchFocus}
                onBlur={handleSearchBlur}
              className="bg-gray-700 text-white rounded-md py-2 px-4 pr-10 w-full focus:outline-none focus:ring-1 focus:ring-red-500"
              />
            <button
              type="submit"
              className="absolute right-0 top-0 bottom-0 px-3 text-gray-400 hover:text-white"
            >
              <FiSearch />
            </button>
            
            {/* 移动端搜索建议下拉框 */}
            {showSuggestions && (
              <div 
                ref={mobileSearchSuggestionsRef}
                className="absolute top-full left-0 right-0 mt-1 bg-gray-700 border border-gray-600 rounded-md shadow-lg z-50 max-h-60 overflow-y-auto"
              >
                {suggestionLoading ? (
                  <div className="px-4 py-2 text-gray-400 text-sm">
                    {t('search.searching')}...
                  </div>
                ) : suggestions.length > 0 ? (
                  suggestions.map((suggestion, index) => (
                    <div
                      key={suggestion.id || index}
                      onClick={() => handleSuggestionClick(suggestion)}
                      className="px-4 py-2 hover:bg-gray-600 cursor-pointer text-white text-sm border-b border-gray-600 last:border-b-0 flex items-center space-x-2"
                    >
                      {suggestion.image && (
                        <div className="flex-shrink-0 w-6 h-6 rounded overflow-hidden">
                          <Image 
                            src={suggestion.image} 
                            alt=""
                            width={24}
                            height={24}
                            className="w-full h-full object-cover"
                          />
                        </div>
                      )}
                      <div className="flex-1 min-w-0">
                        <div className="font-medium truncate text-xs">
                          {suggestion.text || suggestion}
                        </div>
                        {suggestion.meta && (
                          <div className="text-xs text-gray-400 truncate">
                            {suggestion.meta}
                          </div>
                        )}
                      </div>
                      {suggestion.type && (
                        <div className="flex-shrink-0">
                          <span className={`px-1 py-0.5 rounded text-xs ${
                            suggestion.type === 'movie' 
                              ? 'bg-blue-600 text-blue-100' 
                              : 'bg-purple-600 text-purple-100'
                          }`}>
                            {suggestion.type === 'movie' ? (t('common.videos') || '影片') : (t('nav.actresses') || '演员')}
                          </span>
                        </div>
                      )}
                    </div>
                  ))
                ) : (
                  <div className="px-4 py-2 text-gray-400 text-sm">
                    {t('search.noSuggestions')}
                  </div>
                )}
              </div>
            )}
            </form>
            
          {/* 移动端导航链接 */}
          <nav className="space-y-2 py-2 border-t border-gray-700">
              <Link 
              href={localizedUrl('/')}
              className="block py-2 px-3 text-white hover:bg-gray-700 rounded-md"
                onClick={() => setIsMenuOpen(false)}
              >
              {t('nav.home')}
              </Link>
              <Link 
              href={localizedUrl('/new')}
              className="block py-2 px-3 text-white hover:bg-gray-700 rounded-md"
                onClick={() => setIsMenuOpen(false)}
              >
              {t('nav.new')}
              </Link>
                <Link 
              href={localizedUrl('/popular')}
              className="block py-2 px-3 text-white hover:bg-gray-700 rounded-md"
                  onClick={() => setIsMenuOpen(false)}
                >
              {t('nav.popular')}
                </Link>
                    <Link 
              href={localizedUrl('/actress')}
              className="block py-2 px-3 text-white hover:bg-gray-700 rounded-md"
                  onClick={() => setIsMenuOpen(false)}
                >
              {t('nav.actresses')}
            </Link>
            <button
              onClick={() => {
                setIsTagsDrawerOpen(true);
                setIsMenuOpen(false);
              }}
              className="w-full text-left py-2 px-3 text-white hover:bg-gray-700 rounded-md flex items-center"
            >
              <FiTag className="mr-2" /> {t('category.allTags')}
            </button>
            
            {/* 移动端语言切换 */}
            <div className="py-2 px-3">
              <LanguageSwitcher />
            </div>
          </nav>
          
          {/* 移动端用户区域 */}
          <div className="py-3 border-t border-gray-700">
            {!isLoading && (
              user ? (
                <div>
                  <div className="flex items-center space-x-3 p-2">
                    <div className="w-10 h-10 rounded-full bg-red-500 flex items-center justify-center overflow-hidden">
                      {user.avatar ? (
                        <Image 
                          src={user.avatar} 
                          alt={user.username}
                          width={40}
                          height={40}
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <FiUser className="text-white text-xl" />
                      )}
                    </div>
                    <div>
                      <div className="font-medium text-white">{user.username}</div>
                      <div className="text-xs text-gray-400">{user.email}</div>
                    </div>
                  </div>
                  
                  <div className="mt-2 space-y-1">
                    <Link 
                      href={localizedUrl('/profile')}
                      className="block py-2 px-3 text-white hover:bg-gray-700 rounded-md flex items-center"
                      onClick={() => setIsMenuOpen(false)}
                    >
                      <FiUser className="mr-2" /> {t('user.profile')}
                    </Link>
                <Link 
                      href={localizedUrl('/profile/favorites')}
                      className="block py-2 px-3 text-white hover:bg-gray-700 rounded-md flex items-center"
                      onClick={() => setIsMenuOpen(false)}
                    >
                      <FiHeart className="mr-2" /> {t('user.favorites')}
                    </Link>
              <Link 
                      href={localizedUrl('/profile/history')}
                      className="block py-2 px-3 text-white hover:bg-gray-700 rounded-md flex items-center"
                      onClick={() => setIsMenuOpen(false)}
                    >
                      <FiList className="mr-2" /> {t('user.history')}
                    </Link>
                    <Link 
                      href={localizedUrl('/profile/settings')}
                      className="block py-2 px-3 text-white hover:bg-gray-700 rounded-md flex items-center"
                      onClick={() => setIsMenuOpen(false)}
                    >
                      <FiSettings className="mr-2" /> {t('user.settings')}
                    </Link>
                    <button 
                      onClick={authLogout}
                      className="w-full text-left py-2 px-3 text-white hover:bg-gray-700 rounded-md flex items-center"
                    >
                      <FiLogOut className="mr-2" /> {t('nav.logout')}
                    </button>
                  </div>
                </div>
                ) : (
                <div className="flex flex-col space-y-2 p-2">
                    <Link 
                    href={localizedUrl('/auth/login')}
                    className="py-2 px-4 bg-gray-700 hover:bg-gray-600 text-white rounded-md text-center flex items-center justify-center"
                      onClick={() => setIsMenuOpen(false)}
                    >
                    <FiLogIn className="mr-2" /> {t('nav.login')}
                    </Link>
                    <Link 
                    href={localizedUrl('/auth/signup')}
                    className="py-2 px-4 bg-red-600 hover:bg-red-700 text-white rounded-md text-center"
                      onClick={() => setIsMenuOpen(false)}
                    >
                    {t('nav.signup')}
                    </Link>
                </div>
              )
                )}
          </div>
        </div>
      </div>

      {/* 标签抽屉组件 */}
      <TagsDrawer 
        isOpen={isTagsDrawerOpen} 
        onClose={() => setIsTagsDrawerOpen(false)} 
      />
    </div>
  );
};

export default Navbar;