'use client';

import { useEffect, useState } from 'react';

interface JsonLdProps {
  data: Record<string, any>;
}

// 这个组件用于在页面中添加JSON-LD结构化数据
// 接收一个data对象，转换为JSON-LD并添加到head中的script标签
export const JsonLd: React.FC<JsonLdProps> = ({ data }) => {
  const [jsonLdElement, setJsonLdElement] = useState<HTMLScriptElement | null>(null);

  useEffect(() => {
    // 避免服务器端渲染时报错
    if (typeof window === 'undefined') return;

    // 查找已有的同类型JSON-LD标签
    const existingScript = document.querySelector('script[data-json-ld]');
    if (existingScript) {
      document.head.removeChild(existingScript);
    }

    // 创建新的script标签
    const script = document.createElement('script');
    script.setAttribute('type', 'application/ld+json');
    script.setAttribute('data-json-ld', 'true');
    script.textContent = JSON.stringify(data);
    document.head.appendChild(script);
    setJsonLdElement(script);

    // 清理函数
    return () => {
      if (script && script.parentNode) {
        script.parentNode.removeChild(script);
      }
    };
  }, [data]);

  // 这个组件不需要渲染任何UI
  return null;
};

// 组合多个JSON-LD对象的组件
interface MultipleJsonLdProps {
  dataArray: Record<string, any>[];
}

export const MultipleJsonLd: React.FC<MultipleJsonLdProps> = ({ dataArray }) => {
  const [elements, setElements] = useState<HTMLScriptElement[]>([]);

  useEffect(() => {
    if (typeof window === 'undefined' || !dataArray.length) return;

    // 查找并移除已有的标签
    const existingScripts = document.querySelectorAll('script[data-json-ld-multi]');
    existingScripts.forEach(script => {
      if (script.parentNode) {
        script.parentNode.removeChild(script);
      }
    });

    // 创建新的script标签
    const newElements: HTMLScriptElement[] = [];
    dataArray.forEach((data, index) => {
      const script = document.createElement('script');
      script.setAttribute('type', 'application/ld+json');
      script.setAttribute('data-json-ld-multi', `${index}`);
      script.textContent = JSON.stringify(data);
      document.head.appendChild(script);
      newElements.push(script);
    });

    setElements(newElements);

    // 清理函数
    return () => {
      newElements.forEach(script => {
        if (script.parentNode) {
          script.parentNode.removeChild(script);
        }
      });
    };
  }, [dataArray]);

  return null;
};

export default JsonLd; 