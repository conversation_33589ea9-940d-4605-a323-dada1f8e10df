'use client';

import { useRef, useEffect, useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import gsap from 'gsap';
import { FiPlay, FiClock, FiTrendingUp, FiEye } from 'react-icons/fi';
import { useClientTranslations } from './TranslationsProvider';
import { getSocketClient } from '@/lib/socket-client';
import { buildApiUrl } from '@/lib/api-config';

interface RelatedVideosProps {
  videoId: number | string;
  code?: string;
  locale?: string;
}

// 视频数据接口
interface VideoItem {
  id: number | string;
  code: string;
  title: string;
  slug?: string;
  image_url?: string;
  cover_image?: string;
  imageUrl?: string;
  duration: string;
  views?: number;
  view_count?: number;
  uncensored?: boolean;
  rating?: number;
  movie_id?: string;
}

interface VideoStats {
  views: number;
  likes: number;
  favorites: number;
}

interface FusedVideoStats {
  videoId: string;
  numericVideoId: number;
  views: number;
  likes: number;
  favorites: number;
  dbBase: {
    views: number;
    likes: number;
    favorites: number;
  };
  redisIncrement: {
    views: number;
    likes: number;
    favorites: number;
  };
  lastUpdated: number;
  fusionTimestamp: number;
}

const RelatedVideos = ({ videoId, code, locale = 'zh' }: RelatedVideosProps) => {
  const [videos, setVideos] = useState<VideoItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [hoveredVideoId, setHoveredVideoId] = useState<number | string | null>(null);
  const { t } = useClientTranslations();
  
  // 融合统计状态
  const [fusedStats, setFusedStats] = useState<Record<string, FusedVideoStats>>({});
  const socketClient = useRef(getSocketClient());
  
  const videoRefs = useRef<Array<HTMLDivElement | null>>([]);
  const containerRef = useRef<HTMLDivElement>(null);

  // 获取融合观看次数（DB累计 + Redis增量）
  const getViewCount = (video: VideoItem): number => {
    const videoId = video.id.toString();
    const fusedData = fusedStats[videoId];
    
    const originalViews = video.view_count || video.views || 0;
    const fusedViews = fusedData?.views || originalViews;
    
    // 优先使用融合统计数据，否则使用初始数据
    return fusedViews;
  };

  // 批量获取融合统计数据
  const fetchFusedStats = async (videoList: VideoItem[]) => {
    try {
      if (videoList.length === 0) return;

      const videoIds = videoList.map(video => video.id.toString());
      
      
      
      const response = await fetch(buildApiUrl('/api/video-stats/fused/batch'), {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ videoIds }),
      });



              if (!response.ok) {
          return;
        }

        const result = await response.json();
      
      if (result.success && result.data) {
        setFusedStats(result.data);
        
      }
    } catch (error) {
              // Silently handle fusion stats errors
    }
  };

  // 获取图片URL
  const getImageUrl = (video: VideoItem): string => {
    if (video.cover_image) return video.cover_image;
    if (video.image_url) return video.image_url;
    if (video.imageUrl) return video.imageUrl;
    return '/images/placeholder.jpg';
  };
  
  // 获取相关视频数据 - 使用推荐视频API作为替代
  useEffect(() => {
    const fetchRelatedVideos = async () => {
      try {
        setLoading(true);
        
        // 使用推荐视频API获取相关视频
        const apiUrl = buildApiUrl('/api/recommended-videos');
        const response = await fetch(apiUrl, {
          cache: 'no-store'
        });

        if (!response.ok) {
          throw new Error(`获取相关视频失败: ${response.status}`);
        }

        const result = await response.json();
        
        if (result.success && Array.isArray(result.data)) {
          // 过滤掉当前视频，取前6个作为相关视频
          const relatedVideos = result.data
            .filter((video: any) => video.id.toString() !== videoId.toString())
            .slice(0, 6)
            .map((video: any) => ({
              id: video.id,
              code: video.code || video.movie_id,
              title: video.title,
              slug: video.code || video.movie_id,
              image_url: video.image_url,
              cover_image: video.cover_image,
              imageUrl: getImageUrl(video),
              duration: video.duration || '00:00:00',
              views: video.views || 0,
              view_count: video.view_count || 0,
              uncensored: false,
              rating: 4.0,
              movie_id: video.movie_id
            }));

          setVideos(relatedVideos);
          
          // 获取融合统计数据
          await fetchFusedStats(relatedVideos);
        }
      } catch (error) {
        console.error('获取相关视频失败:', error);
      } finally {
        setLoading(false);
      }
    };
    
    fetchRelatedVideos();
  }, [videoId]);

  // 设置Socket.IO实时统计监听
  useEffect(() => {
    if (videos.length === 0) return;



    // 为每个视频设置统计更新监听
    const unsubscribeFunctions: (() => void)[] = [];

    videos.forEach(video => {
      const videoId = video.id.toString();
      
      const handleStatsUpdate = (updatedStats: VideoStats) => {
        
        // 更新融合统计数据
        setFusedStats(prevStats => {
          const existingFused = prevStats[videoId];
          if (existingFused) {
            return {
              ...prevStats,
              [videoId]: {
                ...existingFused,
                views: updatedStats.views,
                likes: updatedStats.likes,
                favorites: updatedStats.favorites,
                fusionTimestamp: Date.now()
              }
            };
          }
          // 如果没有融合数据，创建新的
          return {
            ...prevStats,
            [videoId]: {
              videoId,
              numericVideoId: parseInt(videoId),
              views: updatedStats.views,
              likes: updatedStats.likes,
              favorites: updatedStats.favorites,
              dbBase: { views: 0, likes: 0, favorites: 0 },
              redisIncrement: updatedStats,
              lastUpdated: Date.now(),
              fusionTimestamp: Date.now()
            }
          };
        });
      };

      // 订阅统计更新
      socketClient.current.subscribeToStats(videoId, handleStatsUpdate);
      
      // 记录取消订阅函数
      unsubscribeFunctions.push(() => {
        socketClient.current.unsubscribeFromStats(videoId);
      });
    });

    // 清理函数：取消所有订阅
    return () => {
      unsubscribeFunctions.forEach(unsubscribe => unsubscribe());
    };
  }, [videos]);

  // 定期刷新融合数据（确保数据一致性）
  useEffect(() => {
    if (videos.length === 0) return;

    const refreshInterval = setInterval(() => {
      fetchFusedStats(videos);
    }, 30000); // 每30秒刷新一次

    return () => clearInterval(refreshInterval);
  }, [videos]);
  
  // 设置refs数组并执行动画
  useEffect(() => {
    if (!videos.length) return;
    
    videoRefs.current = videoRefs.current.slice(0, videos.length);
    
    if (containerRef.current) {
      gsap.fromTo(
        containerRef.current,
        { opacity: 0, y: 10 },
        { opacity: 1, y: 0, duration: 0.4, ease: 'power2.out' }
      );
    }
    
    // 视频项动画
    videoRefs.current.forEach((ref, index) => {
      if (ref) {
        gsap.fromTo(
          ref,
          { opacity: 0, y: 15 },
          { 
            opacity: 1, 
            y: 0, 
            duration: 0.3, 
            delay: 0.1 + index * 0.05,
            ease: 'power2.out'
          }
        );
      }
    });
  }, [videos.length]);
  
  const setRef = (el: HTMLDivElement | null, index: number) => {
    videoRefs.current[index] = el;
  };
  
  if (loading) {
    return (
      <div className="py-2">
        <h2 className="text-xl font-bold text-white mb-4 flex items-center">
          <FiTrendingUp className="mr-2 text-red-500" />
          {t('video.relatedVideos')}
        </h2>
        <div className="space-y-3.5">
          {[...Array(6)].map((_, index) => (
            <div key={index} className="rounded-lg overflow-hidden bg-gray-800/30 animate-pulse">
              <div className="p-1.5">
                <div className="relative flex items-stretch">
                  <div className="w-44 h-24 flex-shrink-0 rounded-md bg-gray-700/80"></div>
                  <div className="ml-3 flex-1">
                    <div className="h-4 bg-gray-700/80 rounded w-20 mb-2"></div>
                    <div className="h-4 bg-gray-700/80 rounded w-full mb-1"></div>
                    <div className="h-4 bg-gray-700/80 rounded w-3/4"></div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }
  
  if (!videos.length) {
    return null;
  }
  
  return (
    <div ref={containerRef} className="py-2">
      <h2 className="text-xl font-bold text-white mb-4 flex items-center">
        <FiTrendingUp className="mr-2 text-red-500" />
        {t('video.relatedVideos')}
      </h2>
      
      <div className="space-y-3.5">
        {videos.map((video, index) => (
          <div
            key={video.id}
            ref={(el) => setRef(el, index)}
            className="group rounded-lg overflow-hidden transition-all duration-300 hover:bg-gray-800/30"
            onMouseEnter={() => setHoveredVideoId(video.id)}
            onMouseLeave={() => setHoveredVideoId(null)}
          >
            <Link
              href={`/${locale}/video/${video.slug || video.code}`}
              className="block p-1.5"
            >
              <div className="relative flex items-stretch">
                {/* 左侧图片区域 */}
                <div className="relative w-44 h-24 flex-shrink-0 rounded-md overflow-hidden">
                  <Image
                    src={getImageUrl(video)}
                    alt={video.title}
                    fill
                    sizes="(max-width: 768px) 100vw, 176px"
                    className="object-cover transition-transform duration-500 group-hover:scale-105"
                  />
                  
                  {/* 渐变遮罩 */}
                  <div className="absolute inset-0 bg-gradient-to-b from-black/20 to-black/60 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  
                  {/* 时长标签 */}
                  <div className="absolute bottom-1 right-1 bg-black/80 text-white text-xs px-1.5 py-0.5 rounded">
                    {video.duration}
                  </div>
                  
                  {/* 播放按钮悬停效果 */}
                  <div className="absolute inset-0 bg-black/30 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                    <div className="w-10 h-10 rounded-full bg-red-600/80 flex items-center justify-center transform scale-90 group-hover:scale-100 transition-transform duration-300">
                      <FiPlay className="text-white ml-0.5" />
                    </div>
                  </div>
                </div>
                
                {/* 右侧信息区域 */}
                <div className="ml-3 flex-1 flex flex-col justify-center">
                  {/* 视频编号 */}
                  <div className="text-red-500 text-xs font-medium mb-1 flex items-center">
                    <span className="border border-red-500/30 px-1.5 py-0.5 rounded">
                      {video.code}
                    </span>
                    
                    {/* 评分系统 - 星星 */}
                    <div className="ml-2 flex items-center">
                      <div className="text-yellow-500 text-xs">{(video.rating || 4.0).toFixed(1)}</div>
                    </div>
                  </div>
                  
                  {/* 视频标题 */}
                  <h3 className="text-white text-sm font-medium line-clamp-2 group-hover:text-red-400 transition-colors leading-tight">
                    {video.title}
                  </h3>
                  
                  {/* 实时观看次数 */}
                  <div className={`mt-1 text-xs text-gray-400 flex items-center transition-opacity duration-300 ${hoveredVideoId === video.id ? 'opacity-100' : 'opacity-0'}`}>
                    <FiEye className="mr-1" />
                    <span className="realtime-stats">
                      {getViewCount(video)} {t('video.views')}
                    </span>
                  </div>
                </div>
              </div>
            </Link>
          </div>
        ))}
      </div>
    </div>
  );
};

export default RelatedVideos;