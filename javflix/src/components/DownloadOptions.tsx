'use client';

import { useState, useMemo } from 'react';
import { FiDownload, FiLink, FiCopy, FiCheck, FiAlertCircle, FiInfo, FiX, FiFilter, FiChevronDown, FiStar, FiHardDrive, FiLayers } from 'react-icons/fi';
import { useClientTranslations } from './TranslationsProvider';

interface DownloadOption {
  label: string;
  size: string;
  quality: string;
  url: string;
  format?: string;
  hasSubtitle?: boolean;
  speedRank?: number;
  uploadDate?: string;
}

interface DownloadOptionsProps {
  options: DownloadOption[];
  magnetUri: string;
  onClose: () => void;
}

const DownloadOptions = ({ options, magnetUri, onClose }: DownloadOptionsProps) => {
  const [activeMagnetTooltip, setActiveMagnetTooltip] = useState(false);
  const [copiedMagnet, setCopiedMagnet] = useState(false);
  const { t } = useClientTranslations();
  const [sortBy, setSortBy] = useState<'quality' | 'size' | 'date'>('quality');
  const [showSortMenu, setShowSortMenu] = useState(false);

  const sortedOptions = useMemo(() => {
    const enhancedOptions = options.map(option => ({
      ...option,
      format: option.format || (option.quality === '4K' || option.quality === '1080p' ? 'MP4' : 'AVI'),
      hasSubtitle: option.hasSubtitle !== undefined ? option.hasSubtitle : false,
      speedRank: option.speedRank || Math.floor(Math.random() * 5) + 1,
    }));

    return [...enhancedOptions].sort((a, b) => {
      if (sortBy === 'quality') {
        const qualityRank = { '4K': 3, '1080p': 2, '720p': 1 };
        return (qualityRank[b.quality as keyof typeof qualityRank] || 0) - 
               (qualityRank[a.quality as keyof typeof qualityRank] || 0);
      } else if (sortBy === 'size') {
        const getSize = (sizeStr: string) => {
          const size = parseFloat(sizeStr);
          if (sizeStr.toLowerCase().includes('gb')) return size * 1000;
          return size;
        };
        return getSize(b.size) - getSize(a.size);
      } else {
        if (!a.uploadDate || !b.uploadDate) return 0;
        return new Date(b.uploadDate).getTime() - new Date(a.uploadDate).getTime();
      }
    });
  }, [options, sortBy]);

  const handleCopyMagnet = () => {
    navigator.clipboard.writeText(magnetUri)
      .then(() => {
        setCopiedMagnet(true);
        setTimeout(() => setCopiedMagnet(false), 2000);
      })
      .catch(err => console.error('无法复制磁力链接: ', err));
  };

  const renderSpeedRank = (rank: number) => {
    return (
      <div className="flex items-center">
        {Array(5).fill(0).map((_, i) => (
          <FiStar key={i} className={`w-3 h-3 ${i < rank ? 'text-yellow-500 fill-current' : 'text-gray-600'}`} />
        ))}
      </div>
    );
  };

  return (
    <div className="py-6 px-4 md:px-6">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-2xl font-bold text-white flex items-center">
          <FiDownload className="mr-2 text-red-500" />
          {t('download.options')}
        </h2>
        
        <div className="flex items-center gap-3">
          <div className="relative">
            <button 
              className="flex items-center px-4 py-2 bg-gray-800 text-gray-300 rounded-full text-sm hover:bg-gray-700 transition-all duration-300"
              onClick={() => setShowSortMenu(!showSortMenu)}
            >
              <FiFilter className="mr-1.5" />
              <span>{t('download.sortBy') || '排序'}</span>
              <FiChevronDown className="ml-1.5" />
            </button>
            
            {showSortMenu && (
              <div className="absolute right-0 mt-2 py-2 w-48 bg-gray-800 border border-gray-700 rounded-lg shadow-xl z-10 animate-fadeIn">
                <button 
                  className={`w-full text-left px-4 py-2 text-sm ${sortBy === 'quality' ? 'text-red-400' : 'text-gray-300'} hover:bg-gray-700/50`}
                  onClick={() => {
                    setSortBy('quality');
                    setShowSortMenu(false);
                  }}
                >
                  {t('download.sortByQuality') || '按画质排序'}
                </button>
                <button 
                  className={`w-full text-left px-4 py-2 text-sm ${sortBy === 'size' ? 'text-red-400' : 'text-gray-300'} hover:bg-gray-700/50`}
                  onClick={() => {
                    setSortBy('size');
                    setShowSortMenu(false);
                  }}
                >
                  {t('download.sortBySize') || '按大小排序'}
                </button>
                <button 
                  className={`w-full text-left px-4 py-2 text-sm ${sortBy === 'date' ? 'text-red-400' : 'text-gray-300'} hover:bg-gray-700/50`}
                  onClick={() => {
                    setSortBy('date');
                    setShowSortMenu(false);
                  }}
                >
                  {t('download.sortByDate') || '按日期排序'}
                </button>
              </div>
            )}
          </div>

          <div className="relative">
            <button
              className="flex items-center px-4 py-2 bg-gray-800 text-gray-300 rounded-full text-sm hover:bg-gray-700 transition-all duration-300"
              onMouseEnter={() => setActiveMagnetTooltip(true)}
              onMouseLeave={() => setActiveMagnetTooltip(false)}
              onClick={handleCopyMagnet}
            >
              <FiLink className="mr-1.5" />
              <span>{t('video.magnetLink')}</span>
              {copiedMagnet ? <FiCheck className="ml-1.5 text-green-500" /> : null}
            </button>
            
            {activeMagnetTooltip && (
              <div className="absolute right-0 mt-3 p-4 bg-gray-800 border border-gray-700 rounded-lg shadow-xl text-gray-300 text-sm z-10 w-72 animate-fadeIn">
                <div className="flex items-start mb-3">
                  <FiInfo className="text-blue-400 mt-1 mr-2 flex-shrink-0" />
                  <p>{t('download.magnetLinkHelp')}</p>
                </div>
                <div className="relative">
                  <div className="bg-gray-900 p-3 rounded text-xs overflow-hidden overflow-ellipsis whitespace-nowrap pr-10 border border-gray-700/50">
                    {magnetUri.substring(0, 40)}...
                  </div>
                  <button 
                    onClick={handleCopyMagnet}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white transition-colors"
                  >
                    {copiedMagnet ? <FiCheck className="text-green-500" /> : <FiCopy />}
                  </button>
                </div>
              </div>
            )}
          </div>
          
          <button 
            onClick={onClose}
            className="bg-gray-800 hover:bg-gray-700 p-2 rounded-full transition-all duration-300 text-gray-400 hover:text-white"
            aria-label="关闭"
          >
            <FiX size={20} />
          </button>
        </div>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {sortedOptions.map((option, index) => (
          <div 
            key={index}
            className="flex flex-col p-4 bg-gray-800/50 hover:bg-gray-700/50 border border-gray-700/30 rounded-lg transition-all duration-300 transform hover:scale-[1.02] hover:shadow-lg card-hover-effect"
          >
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center">
                <span className={`text-white font-medium mr-2 ${option.quality === '4K' ? 'text-red-400' : option.quality === '1080p' ? 'text-blue-400' : 'text-gray-400'}`}>
                  {option.label}
                </span>
                <span className={`text-xs px-2 py-0.5 rounded-full ${option.quality === '4K' ? 'bg-red-900/40 text-red-400' : option.quality === '1080p' ? 'bg-blue-900/40 text-blue-400' : 'bg-gray-700 text-gray-400'}`}>
                  {option.quality}
                </span>
              </div>
              <span className="text-xs text-gray-400">{option.size}</span>
            </div>

            <div className="flex flex-wrap gap-2 mb-3">
              {option.format && (
                <div className="flex items-center bg-gray-700/50 rounded px-1.5 py-0.5 text-xs text-gray-300">
                  <FiLayers className="mr-1 text-blue-400" size={12} />
                  {option.format}
                </div>
              )}
              {option.hasSubtitle && (
                <div className="flex items-center bg-gray-700/50 rounded px-1.5 py-0.5 text-xs text-green-400">
                  <span className="mr-1">CC</span>
                  {t('download.subtitles') || '字幕'}
                </div>
              )}
              <div className="flex items-center bg-gray-700/50 rounded px-1.5 py-0.5 text-xs text-yellow-400">
                <FiHardDrive className="mr-1" size={12} />
                {option.speedRank && renderSpeedRank(option.speedRank)}
              </div>
            </div>
            
            <div className="mt-auto">
              <a
                href={option.url}
                download
                className={`w-full bg-gradient-to-r ${option.quality === '4K' ? 'from-red-600 to-red-700 hover:from-red-700 hover:to-red-800' : option.quality === '1080p' ? 'from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800' : 'from-gray-600 to-gray-700 hover:from-gray-700 hover:to-gray-800'} text-white px-4 py-2.5 rounded-lg flex items-center justify-center transition-all duration-300 transform hover:scale-105 shadow-lg btn-interaction`}
              >
                <FiDownload className="mr-1.5" />
                <span>{t('download.downloadButton')}</span>
              </a>
            </div>
          </div>
        ))}
      </div>
      
      <div className="mt-6 p-4 bg-yellow-600/10 border border-yellow-600/20 rounded-lg flex items-start">
        <FiAlertCircle className="text-yellow-500 mr-2 mt-1 flex-shrink-0" />
        <p className="text-gray-300 text-sm leading-relaxed">
          {t('download.disclaimer')}
        </p>
      </div>
    </div>
  );
};

export default DownloadOptions; 