import React from 'react';

type CachedImageProps = {
  src: string;
  cachedSrc?: string;
  alt: string;
  className?: string;
  width?: number;
  height?: number;
};

/**
 * 智能图片组件，优先使用缓存的本地图片
 * @param src 原始图片URL
 * @param cachedSrc 缓存的本地图片URL
 * @param alt 替代文本
 * @param className CSS类名
 * @param width 宽度
 * @param height 高度
 */
export default function CachedImage({
  src,
  cachedSrc,
  alt,
  className = '',
  width,
  height
}: CachedImageProps) {
  // 确定使用的图片源：优先使用缓存图片，否则使用原始URL
  const imageSrc = cachedSrc || src;
  
  // 默认错误图片
  const fallbackSrc = '/images/placeholder.jpg';
  
  // 处理图片加载错误
  const handleError = (e: React.SyntheticEvent<HTMLImageElement, Event>) => {
    e.currentTarget.src = fallbackSrc;
  };

  return (
    <img
      src={imageSrc}
      alt={alt}
      className={className}
      width={width}
      height={height}
      onError={handleError}
      loading="lazy"
    />
  );
} 