'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { FiClock, FiPlayCircle, FiRefreshCcw, FiX } from 'react-icons/fi';

interface HistoryVideo {
  id: string;
  title: string;
  thumbnail: string;
  duration: string;
  progress: number; // 0-100
  watchedAt: Date;
  category: string;
}

const WatchHistory = () => {
  const [history, setHistory] = useState<HistoryVideo[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  
  useEffect(() => {
    // 模拟从本地存储或API获取观看历史
    const fetchHistory = async () => {
      setIsLoading(true);
      try {
        // 模拟延迟
        await new Promise(resolve => setTimeout(resolve, 500));
        
        // 模拟历史数据
        const now = new Date();
        const mockHistory: HistoryVideo[] = [
          {
            id: 'vid001',
            title: '身材苗条的女大学生浴室被教授强行进入',
            thumbnail: '/images/history/hist1.jpg',
            duration: '1:32:45',
            progress: 65,
            watchedAt: new Date(now.getTime() - 1000 * 60 * 30), // 30分钟前
            category: '强制'
          },
          {
            id: 'vid002',
            title: '巨乳少妇被按摩师诱惑，禁不住诱惑的激情',
            thumbnail: '/images/history/hist2.jpg',
            duration: '2:05:10',
            progress: 45,
            watchedAt: new Date(now.getTime() - 1000 * 60 * 60 * 3), // 3小时前
            category: '按摩'
          },
          {
            id: 'vid003',
            title: '公司女秘书加班时间的特殊服务',
            thumbnail: '/images/history/hist3.jpg',
            duration: '1:48:23',
            progress: 92,
            watchedAt: new Date(now.getTime() - 1000 * 60 * 60 * 12), // 12小时前
            category: 'OL'
          },
          {
            id: 'vid004',
            title: '两位女大学生的温泉假期，意外的女同体验',
            thumbnail: '/images/history/hist4.jpg',
            duration: '1:55:30',
            progress: 23,
            watchedAt: new Date(now.getTime() - 1000 * 60 * 60 * 24), // 1天前
            category: '女同'
          },
        ];
        
        setHistory(mockHistory);
      } catch (error) {
        console.error('Failed to fetch watch history:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchHistory();
  }, []);

  // 格式化观看时间
  const formatWatchTime = (date: Date) => {
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);
    
    if (diffInSeconds < 60) {
      return `${diffInSeconds}秒前`;
    } else if (diffInSeconds < 3600) {
      return `${Math.floor(diffInSeconds / 60)}分钟前`;
    } else if (diffInSeconds < 86400) {
      return `${Math.floor(diffInSeconds / 3600)}小时前`;
    } else {
      return `${Math.floor(diffInSeconds / 86400)}天前`;
    }
  };

  // 移除历史记录
  const removeFromHistory = (id: string, e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setHistory(prev => prev.filter(item => item.id !== id));
  };

  // 清空所有历史
  const clearAllHistory = () => {
    if (window.confirm('确定要清空所有观看历史吗？')) {
      setHistory([]);
    }
  };

  return (
    <section className="py-8">
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl md:text-2xl font-bold text-white flex items-center">
            <FiClock className="mr-2 text-red-600" />
            最近观看
          </h2>
          <div className="flex items-center space-x-4">
            <button 
              onClick={clearAllHistory}
              className="text-sm text-gray-400 hover:text-white transition flex items-center"
              disabled={history.length === 0}
            >
              <FiRefreshCcw className="mr-1" size={14} />
              清空记录
            </button>
            <Link href="/history" className="text-sm text-gray-400 hover:text-white transition">
              全部历史 &gt;
            </Link>
          </div>
        </div>

        {isLoading ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {[...Array(4)].map((_, index) => (
              <div key={index} className="bg-gray-800 rounded-lg overflow-hidden animate-pulse">
                <div className="aspect-video bg-gray-700"></div>
                <div className="p-3">
                  <div className="h-4 bg-gray-700 rounded mb-2"></div>
                  <div className="h-4 bg-gray-700 rounded w-3/4 mb-2"></div>
                  <div className="h-2 bg-gray-700 rounded w-full"></div>
                </div>
              </div>
            ))}
          </div>
        ) : history.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {history.map((video) => (
              <div key={video.id} className="bg-gray-800/60 rounded-lg overflow-hidden hover:bg-gray-800 transition group relative">
                <Link href={`/video/${video.id}?resume=true`} className="block">
                  <div className="relative aspect-video overflow-hidden">
                    <Image
                      src={video.thumbnail}
                      alt={video.title}
                      fill
                      sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 25vw"
                      className="object-cover group-hover:scale-105 transition-transform duration-300"
                    />
                    <div className="absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
                      <FiPlayCircle size={48} className="text-white/90" />
                    </div>
                    <div className="absolute bottom-2 right-2 bg-black/80 text-white text-xs px-2 py-1 rounded">
                      {video.duration}
                    </div>
                    <div className="absolute top-2 left-2 bg-red-600/90 text-white text-xs px-2 py-1 rounded">
                      {formatWatchTime(video.watchedAt)}
                    </div>
                    <button 
                      onClick={(e) => removeFromHistory(video.id, e)}
                      className="absolute top-2 right-2 bg-black/70 text-white rounded-full p-1.5 opacity-0 group-hover:opacity-100 transition-opacity hover:bg-red-600"
                    >
                      <FiX size={14} />
                    </button>
                    <div className="absolute bottom-0 left-0 right-0 h-1 bg-gray-700">
                      <div 
                        className="h-full bg-red-600"
                        style={{ width: `${video.progress}%` }}
                      ></div>
                    </div>
                  </div>
                  <div className="p-3">
                    <h3 className="text-sm font-medium text-white line-clamp-2 mb-1">
                      {video.title}
                    </h3>
                    <div className="flex justify-between items-center">
                      <span className="text-xs text-gray-400">{video.category}</span>
                      <span className="text-xs text-gray-400">{video.progress}%</span>
                    </div>
                  </div>
                </Link>
              </div>
            ))}
          </div>
        ) : (
          <div className="bg-gray-800/30 rounded-lg p-8 text-center">
            <p className="text-gray-400">暂无观看历史</p>
            <Link href="/new" className="mt-4 inline-block bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm transition">
              开始浏览
            </Link>
          </div>
        )}
      </div>
    </section>
  );
};

export default WatchHistory; 