'use client';

import { useEffect, useRef, useState } from 'react';
import gsap from 'gsap';
// 动态导入Plyr，避免服务器端引用
import type Plyr from 'plyr';
import { useClientTranslations } from '@/components/TranslationsProvider';

interface VideoPlayerProps {
  videoUrl: string;
  posterUrl: string;
}

const VideoPlayer = ({ videoUrl, posterUrl }: VideoPlayerProps) => {
  const videoRef = useRef<HTMLVideoElement>(null);
  const playerRef = useRef<any>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isPlyrLoaded, setIsPlyrLoaded] = useState(false);
  const [loadingPhase, setLoadingPhase] = useState<'initializing'|'loading'|'ready'|'error'>('initializing');
  const [isPlaying, setIsPlaying] = useState(false);
  const { t } = useClientTranslations();
  
  useEffect(() => {
    // 动态导入Plyr库
    let isMounted = true;
    let loadingTimeout: NodeJS.Timeout;
    
    // 如果加载时间过长，更新提示信息
    loadingTimeout = setTimeout(() => {
      if (isMounted && isLoading) {
        setLoadingPhase('loading');
      }
    }, 2000);
    
    const loadPlyr = async () => {
      try {
        // 设置更详细的加载状态
        setLoadingPhase('initializing');
        
        const { default: Plyr } = await import('plyr');
        
        if (!isMounted) return;
        
        // 播放器容器入场动画
        gsap.fromTo(
          containerRef.current,
          { opacity: 0, scale: 0.98 },
          { opacity: 1, scale: 1, duration: 0.5, ease: 'power2.out' }
        );
        
        // 确保元素存在
        if (!videoRef.current) return;

        // 如果已经初始化了播放器，先销毁它
        if (playerRef.current) {
          playerRef.current.destroy();
        }

        // 创建新的播放器实例
        const player = new Plyr(videoRef.current, {
          captions: { active: false, update: false },
          quality: { default: 720, options: [576, 720, 1080] },
          speed: { selected: 1, options: [0.5, 0.75, 1, 1.25, 1.5, 2] },
          controls: [
            'play-large', 'play', 'progress', 'current-time', 'mute', 
            'volume', 'captions', 'settings', 'pip', 'airplay', 'fullscreen'
          ],
          seekTime: 10,
          keyboard: { focused: true, global: false },
          tooltips: { controls: true, seek: true },
          // 禁用可能导致XMLHttpRequest错误的功能
          loadSprite: false,
          iconUrl: '',
          blankVideo: '',
          autoplay: false,
          autopause: true,
          clickToPlay: true,
          disableContextMenu: false,
          hideControls: true,
          resetOnEnd: false,
          debug: false,
          // 使用国际化翻译配置
          i18n: {
            play: t('common.player.play'),
            pause: t('common.player.pause'),
            mute: t('common.player.mute'),
            unmute: t('common.player.unmute'),
            enterFullscreen: t('common.player.enterFullscreen'),
            exitFullscreen: t('common.player.exitFullscreen'),
            settings: t('common.player.settings'),
            speed: t('common.player.speed'),
            normal: t('common.player.normal'),
            quality: t('common.player.quality')
          }
        });

        playerRef.current = player;

        // 播放器准备好后
        player.on('ready', () => {
          if (!isMounted) return;
          setLoadingPhase('ready');
          setIsLoading(false);
          setIsPlyrLoaded(true);
          
          // 播放器内部元素淡入动画
          gsap.fromTo(
            videoRef.current,
            { opacity: 0.6 },
            { opacity: 1, duration: 0.4, ease: 'power1.inOut' }
          );
        });
        
        // 播放状态改变
        player.on('play', () => {
          if (!isMounted) return;
          setIsPlaying(true);
          
          // 播放状态变化时的动画
          gsap.to(
            containerRef.current,
            { boxShadow: '0 25px 50px -12px rgba(220, 38, 38, 0.15)', duration: 0.5 }
          );
        });
        
        player.on('pause', () => {
          if (!isMounted) return;
          setIsPlaying(false);
          
          // 暂停状态变化时的动画
          gsap.to(
            containerRef.current,
            { boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.3)', duration: 0.5 }
          );
        });

        // 播放器错误处理
        player.on('error', (error: any) => {
          if (!isMounted) return;
          console.error('Plyr error:', error);
          setLoadingPhase('error');
          setIsLoading(false);
        });
      } catch (error) {
        if (!isMounted) return;
        console.error('无法加载Plyr:', error);
        setLoadingPhase('error');
        setIsLoading(false);
      }
    };
    
    loadPlyr();
    
    // 加载中
    if (videoRef.current) {
      // 使用Promise.race确保不阻塞UI
      Promise.race([
        new Promise(resolve => {
          if (!videoRef.current) return resolve(null);
          videoRef.current.addEventListener('loadstart', () => {
            if (isMounted) setIsLoading(true);
            resolve(null);
          }, { once: true });
        }),
        new Promise(resolve => {
          if (!videoRef.current) return resolve(null);
          videoRef.current.addEventListener('loadeddata', () => {
            if (isMounted) {
              setIsLoading(false);
              setLoadingPhase('ready');
            }
            resolve(null);
          }, { once: true });
        })
      ]).catch(err => console.error('视频加载事件错误:', err));
    }
    
    // 清理函数
    return () => {
      isMounted = false;
      clearTimeout(loadingTimeout);
      if (playerRef.current) {
        playerRef.current.destroy();
      }
    };
  }, [videoUrl, isLoading, t]);

  const handlePlayButtonClick = () => {
    if (playerRef.current) {
      if (isPlaying) {
        playerRef.current.pause();
      } else {
        playerRef.current.play();
      }
    }
  };

  return (
    <div 
      ref={containerRef}
      className="relative aspect-video rounded-xl overflow-hidden bg-black shadow-2xl border border-gray-700/30 group"
    >
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center z-10 bg-black bg-opacity-70 backdrop-blur-sm">
          <div className="flex flex-col items-center">
            <div className="w-16 h-16 border-4 border-red-500 border-t-transparent rounded-full animate-spin"></div>
            <p className="text-white mt-4 font-medium text-lg">
              {loadingPhase === 'initializing' && t('common.player.initializingPlayer')}
              {loadingPhase === 'loading' && t('common.player.loadingVideo')}
              {loadingPhase === 'ready' && t('common.player.ready')}
              {loadingPhase === 'error' && t('common.player.loadingFailed')}
            </p>
          </div>
        </div>
      )}
      
      {/* 自定义播放按钮覆盖层 - 这个解决了Plyr原生播放按钮不稳定的问题 */}
      {isPlyrLoaded && !isPlaying && !isLoading && (
        <div 
          className="absolute inset-0 z-20 flex items-center justify-center cursor-pointer"
          onClick={handlePlayButtonClick}
        >
          <div className="absolute inset-0 bg-black/40 backdrop-blur-sm"></div>
          <div className="w-24 h-24 bg-red-600/80 rounded-full flex items-center justify-center transform transition-all duration-500 hover:scale-110 hover:bg-red-600 shadow-lg shadow-red-900/30">
            <svg xmlns="http://www.w3.org/2000/svg" width="36" height="36" viewBox="0 0 24 24" fill="white" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-white ml-1.5">
              <polygon points="5 3 19 12 5 21 5 3"></polygon>
            </svg>
          </div>
        </div>
      )}
      
      {/* 播放器悬停效果 */}
      <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-black/40 z-0 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
      
      <div className={`plyr__video-embed ${isPlyrLoaded ? 'plyr--ready' : ''}`}>
        <video
          ref={videoRef}
          poster={posterUrl}
          controls
          crossOrigin="anonymous"
          playsInline
          preload="metadata"
          className="w-full h-full object-cover transition-transform duration-700 ease-out plyr__video group-hover:scale-[1.02]"
          data-plyr-provider="html5"
        >
          <source src={videoUrl} type="video/mp4" />
          {t('common.player.browserNotSupported')}
        </video>
      </div>
      
      <style jsx global>{`
        /* 自定义播放器样式 */
        .plyr {
          --plyr-color-main: #dc2626;
          --plyr-control-radius: 8px;
          --plyr-range-fill-background: #dc2626;
          --plyr-video-control-color: white;
          --plyr-video-control-background-hover: rgba(220, 38, 38, 0.7);
          width: 100%;
          height: 100%;
        }
        
        /* 隐藏Plyr原生的大播放按钮，使用我们自定义的 */
        .plyr__control--overlaid {
          display: none !important;
        }
        
        .plyr--full-ui input[type='range'] {
          color: #dc2626;
        }
        
        /* 自定义进度条 */
        .plyr--full-ui input[type='range']::-webkit-slider-thumb {
          background: #dc2626;
          box-shadow: 0 0 10px rgba(220, 38, 38, 0.5);
        }
        
        /* 控制条底部渐变 */
        .plyr__controls {
          background: linear-gradient(0deg, rgba(0,0,0,0.6) 0%, rgba(0,0,0,0.3) 60%, transparent 100%) !important;
        }
        
        /* 静音/音量控制按钮 */
        .plyr__control[data-plyr='mute']:hover {
          background: rgba(220, 38, 38, 0.5);
        }
        
        /* 设置按钮 */
        .plyr__control--overlaid[data-plyr='play'] {
          background: rgba(220, 38, 38, 0.9);
        }
        
        /* 使控制条在悬停时更明显 */
        .plyr--video .plyr__controls {
          opacity: 0.8;
          transition: opacity 0.3s ease;
        }
        
        .plyr--video:hover .plyr__controls {
          opacity: 1;
        }
        
        /* 进度条优化 */
        .plyr__progress {
          margin-right: 15px;
        }
        
        .plyr__progress input[type='range'] {
          height: 6px;
        }
        
        .plyr__progress__buffer {
          height: 6px;
        }
      `}</style>
    </div>
  );
};

export default VideoPlayer; 