'use client';

import { useRef, useEffect, useState } from 'react';
import Link from 'next/link';
import { FiX, FiSearch } from 'react-icons/fi';
import gsap from 'gsap';
import { useClientTranslations } from './TranslationsProvider';
import { useParams } from 'next/navigation';

interface TagsDrawerProps {
  isOpen: boolean;
  onClose: () => void;
}

interface Tag {
  id: string;
  name: string;
  link: string;
  count?: number;
}

interface TagCategory {
  id: string;
  title: string;
  tags: Tag[];
}

const TagsDrawer: React.FC<TagsDrawerProps> = ({ isOpen, onClose }) => {
  const { t } = useClientTranslations();
  const params = useParams();
  const locale = (params?.locale as string) || 'zh';
  
  const overlayRef = useRef<HTMLDivElement>(null);
  const drawerRef = useRef<HTMLDivElement>(null);
  const drawerContentRef = useRef<HTMLDivElement>(null);
  
  const [searchTerm, setSearchTerm] = useState('');
  const [isLoadingTags, setIsLoadingTags] = useState(false);
  const [allTags, setAllTags] = useState<Tag[]>([]);

  // 硬编码的分类配置
  const categoryConfig = [
    {
      id: 'quality',
      title: t('tags.categories.quality'),
      keywords: ['高画质', '4K', '单体作品', '集锦', 'HD', 'VR', '4k', '高清']
    },
    {
      id: 'genre',
      title: t('tags.categories.genre'),
      keywords: ['企划', '纪录片', '角色扮演', '主观视角', '第一人称', '幻想', 'POV', '企业', '单体', '合集']
    },
    {
      id: 'actress',
      title: t('tags.categories.actress'),
      keywords: ['美少女', '成熟', '已婚', '新婚', '女大学生', 'OL', '姐姐', '人妻', '熟女', '少女', '学生', '制服']
    },
    {
      id: 'bodytype',
      title: t('tags.categories.bodytype'),
      keywords: ['巨乳', '巨尻', '苗条', '无毛', '屁股', '美腿', '美尻', '丰满', '纤细', '巨臀', '美胸']
    },
    {
      id: 'actions',
      title: t('tags.categories.actions'),
      keywords: ['中出', '口交', '颜射', '潮吹', '女上位', '高潮', '出轨', '按摩', '自慰', '接吻', '内射', '射精', '做爱']
    },
    {
      id: 'multiplayer',
      title: t('tags.categories.multiplayer'),
      keywords: ['多P', '乱交', '群体', '3P', '4P', '群交', '轮奸']
    },
    {
      id: 'props',
      title: t('tags.categories.props'),
      keywords: ['玩具', '乳液', '按摩油', '道具', '润滑', '情趣']
    },
    {
      id: 'scenes',
      title: t('tags.categories.scenes'),
      keywords: ['医院', '颜面骑乘', 'DMM', '学校', '办公室', '家庭', '户外', '温泉', '酒店']
    }
  ];

  // 获取所有动态标签数据
  const fetchAllTags = async () => {
    setIsLoadingTags(true);
    try {
      // 获取分类数据
      const genresResponse = await fetch('/api/db/genres/popular?limit=100').catch(() => null);

      const allTagsData: Tag[] = [];

      // 处理分类数据
      if (genresResponse?.ok) {
        const genresResult = await genresResponse.json();
        if (genresResult.success && genresResult.data?.items) {
          const genreTags = genresResult.data.items.map((genre: any) => ({
            id: `genre-${genre.id}`,
            name: `#${genre.name}`,
            link: `/${locale}/category/${encodeURIComponent(genre.name)}`,
            count: genre.movie_count || 0
          }));
          allTagsData.push(...genreTags);
        }
      }

      // 按计数排序，热门的在前面
      allTagsData.sort((a, b) => (b.count || 0) - (a.count || 0));
      
      setAllTags(allTagsData);
    } catch (error) {
      console.error('获取标签数据失败:', error);
      setAllTags([]);
    } finally {
      setIsLoadingTags(false);
    }
  };

  // 将标签按硬编码分类进行分组
  const categorizeRealTags = (): TagCategory[] => {
    const categories: TagCategory[] = categoryConfig.map(config => ({
      id: config.id,
      title: config.title,
      tags: []
    }));

    // 未分类的标签
    const uncategorized: Tag[] = [];

    allTags.forEach(tag => {
      let categorized = false;
      
      // 检查标签属于哪个分类
      for (const category of categories) {
        const config = categoryConfig.find(c => c.id === category.id);
        if (config) {
          // 检查标签名称（去掉#号）是否包含分类关键词
          const tagName = tag.name.replace('#', '');
          const matchesKeyword = config.keywords.some(keyword => 
            tagName.includes(keyword) || keyword.includes(tagName)
          );
          
          if (matchesKeyword) {
            category.tags.push(tag);
            categorized = true;
            break;
          }
        }
      }
      
      if (!categorized) {
        uncategorized.push(tag);
      }
    });

    // 添加未分类的标签到"其他"分类
    if (uncategorized.length > 0) {
      categories.push({
        id: 'others',
        title: t('tags.categories.others'),
        tags: uncategorized
      });
    }

    // 过滤掉空分类
    return categories.filter(category => category.tags.length > 0);
  };

  useEffect(() => {
    if (isOpen) {
      // 获取动态数据
      fetchAllTags();
      
      // 打开抽屉动画 - 从上往下滑入
      document.body.style.overflow = 'hidden'; // 防止背景滚动
      
      // 确保覆盖层立即显示
      if (overlayRef.current) {
        overlayRef.current.style.display = 'block';
      }
      
      if (overlayRef.current) {
        gsap.to(overlayRef.current, {
          opacity: 1,
          duration: 0.3
        });
      }
      
      if (drawerRef.current) {
        gsap.to(drawerRef.current, {
          y: '0%',
          duration: 0.4,
          ease: 'power3.out'
        });
      }
      
      // 添加点击外部关闭抽屉的处理函数
      const handleOutsideClick = (e: MouseEvent) => {
        if (drawerContentRef.current && !drawerContentRef.current.contains(e.target as Node)) {
          onClose();
        }
      };
      
      // 添加ESC键关闭
      const handleEscKey = (e: KeyboardEvent) => {
        if (e.key === 'Escape') {
          onClose();
        }
      };
      
      // 添加事件监听器
      document.addEventListener('mousedown', handleOutsideClick);
      document.addEventListener('keydown', handleEscKey);
      
      // 清理函数
      return () => {
        document.removeEventListener('mousedown', handleOutsideClick);
        document.removeEventListener('keydown', handleEscKey);
      };
    } else {
      // 关闭抽屉动画 - 向上滑出
      document.body.style.overflow = 'unset'; // 恢复背景滚动
      
      if (drawerRef.current) {
        gsap.to(drawerRef.current, {
          y: '-100%',
          duration: 0.3,
          ease: 'power3.in'
        });
      }
      
      if (overlayRef.current) {
        gsap.to(overlayRef.current, {
          opacity: 0,
          duration: 0.3,
          onComplete: () => {
            if (overlayRef.current) {
              overlayRef.current.style.display = 'none';
            }
          }
        });
      }
    }
  }, [isOpen]);

  // 过滤标签
  const getFilteredCategories = () => {
    const categorizedTags = categorizeRealTags();
    
    if (!searchTerm) return categorizedTags;
    
    return categorizedTags.map(category => ({
      ...category,
      tags: category.tags.filter(tag =>
        tag.name.toLowerCase().includes(searchTerm.toLowerCase())
      )
    })).filter(category => category.tags.length > 0);
  };

  const filteredCategories = getFilteredCategories();

  const formatCount = (count: number) => {
    if (count >= 1e9) {
      return `${(count / 1e9).toFixed(1)}B`;
    } else if (count >= 1e6) {
      return `${(count / 1e6).toFixed(1)}M`;
    } else if (count >= 1e3) {
      return `${(count / 1e3).toFixed(1)}K`;
    } else {
      return count.toString();
    }
  };

  return (
    <>
      {/* 覆盖层 */}
      <div
        ref={overlayRef}
        className="fixed inset-0 bg-black bg-opacity-30 z-[100]"
        style={{ display: 'none', opacity: 0 }}
      />

      {/* 抽屉 - 从上往下全屏弹出 */}
      <div
        ref={drawerRef}
        className="fixed top-0 left-0 w-full h-full bg-gray-900/85 backdrop-blur-md shadow-2xl z-[100] transform -translate-y-full"
        style={{ height: '100dvh' }}
      >
        <div ref={drawerContentRef} className="h-full flex flex-col">
          {/* 头部 */}
          <div className="flex items-center justify-between p-6 border-b border-gray-800/50 flex-shrink-0">
            <div className="flex items-center">
              <div className="w-1 h-8 bg-red-600 rounded-full mr-4"></div>
              <div>
                <h2 className="text-2xl font-bold text-white">{t('tags.trending') || '所有标签'}</h2>
                <p className="text-sm text-gray-400 mt-1">
                  浏览所有分类标签
                </p>
              </div>
            </div>
            <button
              onClick={onClose}
              className="p-2 hover:bg-white/10 rounded-lg transition-colors group flex-shrink-0"
            >
              <FiX className="w-6 h-6 text-gray-400 group-hover:text-white" />
            </button>
          </div>

          {/* 搜索框 */}
          <div className="p-6 border-b border-gray-800/50 flex-shrink-0">
            <div className="relative">
              <FiSearch className="absolute left-4 top-4 w-5 h-5 text-gray-400" />
              <input
                type="text"
                placeholder={t('tags.searchPlaceholder') || '搜索标签...'}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-12 pr-4 py-4 bg-gray-800/30 border border-gray-700/30 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:border-white/50 focus:ring-2 focus:ring-white/20 transition-all backdrop-blur-sm"
              />
            </div>
          </div>

          {/* 标签内容区域 */}
          <div className="flex-1 overflow-y-auto p-6 min-h-0">
            {isLoadingTags ? (
              <div className="flex items-center justify-center h-64">
                <div className="text-center">
                  <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-red-500 mx-auto mb-4"></div>
                  <p className="text-gray-400">{t('common.loading') || '正在加载标签...'}</p>
                </div>
              </div>
            ) : filteredCategories.length > 0 ? (
              <div className="space-y-8">
                {filteredCategories.map((category) => (
                  <div key={category.id} className="space-y-4">
                    {/* 分类标题 */}
                    <div className="flex items-center space-x-3">
                      <div className="w-1 h-6 bg-red-600 rounded-full"></div>
                      <h3 className="text-xl font-bold text-white">{category.title}</h3>
                    </div>

                    {/* 分类标签 */}
                    <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 2xl:grid-cols-8 gap-3">
                      {category.tags.map((tag) => (
                        <Link
                          key={tag.id}
                          href={tag.link}
                          onClick={onClose}
                          className="group block"
                        >
                          <div className="relative px-4 py-3 text-center transition-all duration-300 hover:scale-105 hover:-translate-y-1">
                            <div className="font-medium text-gray-300 group-hover:text-white transition-colors duration-300 text-sm leading-tight mb-1">
                              {tag.name}
                            </div>
                            {tag.count && tag.count > 0 && (
                              <div className="text-xs text-gray-500 group-hover:text-gray-300 transition-colors duration-300">
                                {formatCount(tag.count)} 部
                              </div>
                            )}
                          </div>
                        </Link>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center text-gray-400 py-16">
                <FiSearch className="w-16 h-16 mx-auto mb-4 opacity-50" />
                <p className="text-xl mb-2">
                  {searchTerm ? (t('tags.noResults') || '未找到匹配的标签') : (t('common.noResults') || '暂无标签数据')}
                </p>
                {searchTerm && (
                  <p className="text-sm">
                    {t('search.tryAdjustSearch') || '尝试调整搜索关键词'}
                  </p>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    </>
  );
};

export default TagsDrawer;