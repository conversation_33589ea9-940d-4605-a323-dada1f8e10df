'use client';

import { useRef, useEffect, useState, useCallback } from 'react';
import Link from 'next/link';
import gsap from 'gsap';
import { FiChevronRight, FiChevronLeft, FiEye } from 'react-icons/fi';
import { useClientTranslations } from '@/components/TranslationsProvider';
import { usePathname } from 'next/navigation';
import { locales } from '@/i18n';
import { type Locale } from '@/i18n/types';
import { getRecentVideos } from '@/lib/video-client';
import { useInView } from 'react-intersection-observer';
import { useRealtimeStats } from '@/contexts/RealtimeStatsContext';
import { getSocketClient } from '@/lib/socket-client';
import { buildApiUrl } from '@/lib/api-config';
import VideoCard from '@/components/VideoCard';
import SectionHeader from '@/components/SectionHeader';
import { usePhysicsScroll } from '@/hooks/usePhysicsScroll';

interface RecentVideosProps {
  locale?: Locale;
}

interface VideoData {
  id: string | number;
  title: string;
  slug?: string;
  code?: string;
  thumbnail?: string;
  thumbnail_url?: string;
  cached_image_url?: string;
  movie_id?: string;
  duration?: string;
  views?: number;
  view_count?: number;
  timeAgo?: string;
  release_date?: string;
  created_at?: string;
  cover_image?: string;
  image_url?: string;
}

interface VideoStats {
  views: number;
  likes: number;
  favorites: number;
}

interface FusedVideoStats {
  videoId: string;
  numericVideoId: number;
  views: number;
  likes: number;
  favorites: number;
  dbBase: {
    views: number;
    likes: number;
    favorites: number;
  };
  redisIncrement: {
    views: number;
    likes: number;
    favorites: number;
  };
  lastUpdated: number;
  fusionTimestamp: number;
}

const RecentVideos = ({ locale = 'zh' }: RecentVideosProps) => {
  const { t } = useClientTranslations();
  const [videos, setVideos] = useState<VideoData[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  // 分页相关状态
  const [currentPage, setCurrentPage] = useState(0);
  const VIDEOS_PER_PAGE = 8; // 每页8个视频（双排4个）
  const PAGE_WIDTH = 1496; // 每页宽度 (362px * 4 + 16px * 3)
  
  // 防抖标识
  const dragTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const animationFrameRef = useRef<number | null>(null);
  
  // 使用全局实时统计上下文
  const { videoStats, subscribeToVideo, unsubscribeFromVideo, getViewCount: getRealtimeViewCount } = useRealtimeStats();
  
  // 融合统计状态
  const [fusedStats, setFusedStats] = useState<Record<string, FusedVideoStats>>({});
  const socketClient = useRef(getSocketClient());
  
  const titleRef = useRef<HTMLHeadingElement>(null);
  const videoRefs = useRef<Array<HTMLDivElement | null>>([]);
  const scrollRef = useRef<HTMLDivElement>(null);
  
  // 使用物理滚动Hook，配置自然的滑动效果
  const {
    isDragging,
    hasDragged,
    handleMouseDown,
    handleTouchStart,
    handleDragStart,
    handleCardClick
  } = usePhysicsScroll(scrollRef, {
    friction: 0.96,           // 降低摩擦力，延长惯性滑动
    velocityThreshold: 0.1,   // 降低速度阈值，更容易触发惯性
    maxVelocity: 50,          // 提高最大速度，允许更快的滑动
    bounceDamping: 0.4,       // 边界反弹阻尼
    enableBounce: true        // 启用边界反弹效果
  });
  
  // 使用ref记录视频元素
  const setRef = (el: HTMLDivElement | null, index: number) => {
    videoRefs.current[index] = el;
  };
  
  // 使用IntersectionObserver检测标题是否可见
  const { ref: inViewRef, inView } = useInView({
    threshold: 0.1,
    triggerOnce: true,
  });

  // 防抖的滚动更新函数
  const updateScroll = useCallback((newScrollLeft: number) => {
    if (animationFrameRef.current) {
      cancelAnimationFrame(animationFrameRef.current);
    }
    
    animationFrameRef.current = requestAnimationFrame(() => {
      if (scrollRef.current) {
        scrollRef.current.scrollLeft = newScrollLeft;
      }
    });
  }, []);

  // 分页跳转函数
  const goToPage = useCallback((pageIndex: number) => {
    if (scrollRef.current && pageIndex >= 0) {
      const targetScrollLeft = pageIndex * PAGE_WIDTH;
      scrollRef.current.scrollTo({ left: targetScrollLeft, behavior: 'smooth' });
      setCurrentPage(pageIndex);
    }
  }, [PAGE_WIDTH]);

  // 监听滚动位置更新当前页码
  const handleScroll = useCallback(() => {
    if (scrollRef.current) {
      const scrollLeft = scrollRef.current.scrollLeft;
      const newPage = Math.round(scrollLeft / PAGE_WIDTH);
      setCurrentPage(newPage);
    }
  }, [PAGE_WIDTH]);



  // 添加滚动监听器
  useEffect(() => {
    const element = scrollRef.current;
    if (!element) return;

    element.addEventListener('scroll', handleScroll);
    
    return () => {
      element.removeEventListener('scroll', handleScroll);
    };
  }, [handleScroll]);

  // 组件卸载时清理
  useEffect(() => {
    return () => {
      // 强制恢复全局状态
      document.body.style.userSelect = '';
      document.body.style.pointerEvents = '';
      
      if (dragTimeoutRef.current) {
        clearTimeout(dragTimeoutRef.current);
      }
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
    };
  }, []);

  // 获取融合观看次数（DB累计 + Redis增量）
  const getViewCount = (video: VideoData): number => {
    const videoId = video.id.toString();
    const fusedData = fusedStats[videoId];
    
    // 优先使用融合统计数据，否则使用初始数据
    return fusedData?.views || video.view_count || video.views || 0;
  };

  // 批量获取融合统计数据
  const fetchFusedStats = async (videoList: VideoData[]) => {
    try {
      if (videoList.length === 0) return;

      const videoIds = videoList.map(video => video.id.toString());
      
      const response = await fetch(buildApiUrl('/api/video-stats/fused/batch'), {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ videoIds }),
      });

      if (!response.ok) {
        console.warn('融合统计API失败，使用初始数据');
        return;
      }

      const result = await response.json();
      
      if (result.success && result.data) {
        setFusedStats(result.data);

      }
    } catch (error) {
      console.warn('获取最新视频融合统计数据失败:', error);
    }
  };
  
  // 获取最新视频数据
  useEffect(() => {
    const fetchVideos = async () => {
      try {
        setIsLoading(true);
        const videos = await getRecentVideos(16); // 获取16个视频用于双排显示
        if (videos && videos.length > 0) {
          setVideos(videos);
          
          // 获取融合统计数据
          await fetchFusedStats(videos);
        } else {
          setError(t('common.failedToLoadVideos'));
        }
      } catch (err) {
        console.error('获取最新视频出错:', err);
        setError(t('common.errorLoadingVideos'));
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchVideos();
  }, [locale, t]);
  
  // 设置Socket.IO实时统计监听
  useEffect(() => {
    if (videos.length === 0) return;



    // 为每个视频设置统计更新监听
    const unsubscribeFunctions: (() => void)[] = [];

    videos.forEach(video => {
      const videoId = video.id.toString();
      
      const handleStatsUpdate = (updatedStats: VideoStats) => {

        
        // 更新融合统计数据
        setFusedStats(prevStats => {
          const existingFused = prevStats[videoId];
          if (existingFused) {
            return {
              ...prevStats,
              [videoId]: {
                ...existingFused,
                views: updatedStats.views,
                likes: updatedStats.likes,
                favorites: updatedStats.favorites,
                fusionTimestamp: Date.now()
              }
            };
          }
          // 如果没有融合数据，创建新的
          return {
            ...prevStats,
            [videoId]: {
              videoId,
              numericVideoId: parseInt(videoId),
              views: updatedStats.views,
              likes: updatedStats.likes,
              favorites: updatedStats.favorites,
              dbBase: { views: 0, likes: 0, favorites: 0 },
              redisIncrement: updatedStats,
              lastUpdated: Date.now(),
              fusionTimestamp: Date.now()
            }
          };
        });
      };

      // 订阅统计更新
      socketClient.current.subscribeToStats(videoId, handleStatsUpdate);
      
      // 记录取消订阅函数
      unsubscribeFunctions.push(() => {
        socketClient.current.unsubscribeFromStats(videoId);
      });
    });

    // 清理函数：取消所有订阅
    return () => {

      unsubscribeFunctions.forEach(unsubscribe => unsubscribe());
    };
  }, [videos]);

  // 定期刷新融合数据（确保数据一致性）
  useEffect(() => {
    if (videos.length === 0) return;

    const refreshInterval = setInterval(() => {
      fetchFusedStats(videos);
    }, 30000); // 每30秒刷新一次

    return () => clearInterval(refreshInterval);
  }, [videos]);
  
  // 高效的CSS动画，仅在需要时触发
  useEffect(() => {
    if (inView && titleRef.current) {
      titleRef.current.classList.add('animate-fade-in-up');
      
      // 动画完成后清理will-change
      setTimeout(() => {
        if (titleRef.current) {
          titleRef.current.classList.add('animation-complete');
        }
      }, 600);
      
      // 批量添加动画类，避免逐个操作DOM
      videoRefs.current.forEach((ref, index) => {
        if (ref) {
          setTimeout(() => {
            ref.classList.add('animate-fade-in-up');
            // 动画完成后清理will-change
            setTimeout(() => {
              if (ref) {
                ref.classList.add('animation-complete');
              }
            }, 600);
          }, index * 50); // 减少延时，提高响应速度
        }
      });
    }
  }, [inView, videos]);

  // 创建双排布局的分组数据 - 每组包含上下两个视频
  const createDoubleRowLayout = () => {
    const groups: { top: VideoData; bottom: VideoData | null }[] = [];
    
    for (let i = 0; i < Math.min(videos.length, 16); i += 8) {
      // 每8个视频创建一个双排组（上排4个，下排4个）
      const topRow = videos.slice(i, i + 4);
      const bottomRow = videos.slice(i + 4, i + 8);
      
      for (let j = 0; j < 4; j++) {
        // 只有当top视频存在时才创建组
        if (topRow[j]) {
          groups.push({
            top: topRow[j],
            bottom: bottomRow[j] || null
          });
        }
      }
    }
    
    return groups;
  };

  const doubleRowGroups = createDoubleRowLayout();
  
  // 计算总页数
  const totalPages = Math.ceil(doubleRowGroups.length / 4); // 每页4组双排
  
  // 本地化URL
  const localizedUrl = (path: string) => {
    return `/${locale}${path}`;
  };
  
  if (isLoading) {
    return (
      <div className="w-full">
        <div className="flex justify-between items-center mb-6">
          <div className="h-8 bg-gray-700 w-40 rounded animate-pulse"></div>
        </div>
        {/* 加载状态 - 完全匹配新视频页面的效果 */}
        <div className="flex justify-center items-center min-h-[400px]">
          <div className="text-center">
            <div className="relative">
              <div className="w-16 h-16 border-4 border-gray-700 border-t-white rounded-full animate-spin mx-auto mb-6"></div>
              <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-8 h-8 bg-white/20 rounded-full animate-pulse"></div>
            </div>
            <p className="text-gray-300 text-lg font-light">{t('common.loading') || '正在加载...'}</p>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="w-full">
        <h2 className="text-2xl md:text-3xl font-bold text-white mb-4">{t('nav.new')}</h2>
        <div className="p-4 bg-red-900/30 border border-red-800 rounded-lg">
          <p className="text-red-200">{error}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full">
      <div ref={inViewRef}>
        <SectionHeader
          title={t('nav.recent')}
          viewMoreHref={localizedUrl("/recent")}
          viewMoreText={t('common.viewMore')}
        />
      </div>
      
      {/* 双排水平滚动容器 */}
      <div className="relative">

        {/* 双排卡片滚动区域 - 增强物理滑动效果 */}
        <div
          ref={scrollRef}
          className={`flex space-x-4 overflow-x-auto scrollbar-hide px-4 pb-2 select-none transition-all duration-200 ${
            isDragging 
              ? 'cursor-grabbing scale-[0.98] brightness-95' 
              : 'cursor-grab scale-100 brightness-100'
          }`}
          style={{ 
            scrollbarWidth: 'none',
            msOverflowStyle: 'none',
            scrollBehavior: isDragging ? 'auto' : 'smooth'
          }}
          onMouseDown={handleMouseDown}
          onTouchStart={handleTouchStart}
          onDragStart={handleDragStart}
        >
          <style jsx>{`
            div::-webkit-scrollbar {
              display: none;
            }
          `}</style>
          
          {doubleRowGroups.map((group, groupIndex) => (
            <div
              key={groupIndex}
              className="flex-none flex flex-col space-y-4"
            >
              {/* 上排视频 - 使用362x192比例 */}
              {group.top && (
                <div
                  ref={(el) => setRef(el, groupIndex * 2)}
                  className="w-[362px] overflow-hidden"
                >
                  <VideoCard
                    video={group.top}
                    locale={locale}
                    size="small"
                    fusedStats={fusedStats[group.top.id.toString()]}
                    onCardClick={handleCardClick}
                    onDragStart={handleDragStart}
                  />
                </div>
              )}
              
              {/* 下排视频 - 使用362x192比例 */}
              {group.bottom && (
                <div
                  ref={(el) => setRef(el, groupIndex * 2 + 1)}
                  className="w-[362px] overflow-hidden"
                >
                  <VideoCard
                    video={group.bottom}
                    locale={locale}
                    size="small"
                    fusedStats={fusedStats[group.bottom.id.toString()]}
                    onCardClick={handleCardClick}
                    onDragStart={handleDragStart}
                  />
                </div>
              )}
            </div>
          ))}
        </div>
        
        {/* 底部分页指示器 */}
        {totalPages > 1 && (
          <div className="flex justify-center mt-6 space-x-2">
            {Array.from({ length: totalPages }, (_, index) => (
              <button
                key={index}
                onClick={() => goToPage(index)}
                className={`
                  transition-all duration-300 rounded-full cursor-pointer
                  ${currentPage === index 
                    ? 'w-8 h-3 bg-red-600 shadow-lg shadow-red-600/50' 
                    : 'w-3 h-3 bg-gray-600 hover:bg-red-500 hover:shadow-md hover:shadow-red-500/40'
                  }
                `}
                aria-label={`跳转到第${index + 1}页`}
              />
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default RecentVideos;