'use client';

import { useState, useRef, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import gsap from 'gsap';
import { <PERSON>Eye, FiHeart, FiShare2, FiBookmark, FiChevronDown, FiChevronUp, FiUser, FiHome, FiTag, FiClock, FiCalendar } from 'react-icons/fi';
import { useClientTranslations } from './TranslationsProvider';
import VideoStatsDisplay from './VideoStatsDisplay';

interface ActorInfo {
  name: string;
  avatar?: string;
}

interface VideoInfoProps {
  title: string;
  description: string;
  views: string;
  likes: string;
  uploadDate: string;
  duration: string;
  categories: string[];
  tags: string[];
  actorNames?: string[];
  actors?: ActorInfo[];
  studio?: string;
  hideMetadata?: boolean;
  locale?: string;
  videoId?: string; // 添加视频ID用于实时统计
  enableRealTimeStats?: boolean; // 是否启用实时统计
}

const VideoInfo = ({
  title,
  description,
  views,
  likes,
  uploadDate,
  duration,
  categories,
  tags,
  actorNames = [],
  actors = [],
  studio = '',
  hideMetadata = false,
  locale = 'zh',
  videoId,
  enableRealTimeStats = true
}: VideoInfoProps) => {
  const [isLiked, setIsLiked] = useState(false);
  const [isBookmarked, setIsBookmarked] = useState(false);
  const [showFullDescription, setShowFullDescription] = useState(false);
  
  const descriptionRef = useRef<HTMLDivElement>(null);
  const gradientRef = useRef<HTMLDivElement>(null);
  const { t } = useClientTranslations();
  
  // 处理描述展开/收起动画
  useEffect(() => {
    if (descriptionRef.current && gradientRef.current) {
      if (showFullDescription) {
        // 展开描述
        gsap.to(descriptionRef.current, {
          maxHeight: 'none',
          duration: 0.3
        });
        
        // 隐藏渐变遮罩
        gsap.to(gradientRef.current, {
          opacity: 0,
          duration: 0.2,
          onComplete: () => {
            if (gradientRef.current) {
              gradientRef.current.style.display = 'none';
            }
          }
        });
      } else {
        // 收起描述
        gsap.to(descriptionRef.current, {
          maxHeight: '96px', // 相当于max-h-24
          duration: 0.3
        });
        
        // 显示渐变遮罩
        gradientRef.current.style.display = 'block';
        gsap.to(gradientRef.current, {
          opacity: 1,
          duration: 0.2
        });
      }
    }
  }, [showFullDescription]);

  // 格式化上传日期
  const formattedDate = new Date(uploadDate).toLocaleDateString(locale, {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });

  const toggleLike = () => {
    setIsLiked(!isLiked);
  };

  const toggleBookmark = () => {
    setIsBookmarked(!isBookmarked);
  };

  const handleShare = () => {
    if (navigator.share) {
      navigator.share({
        title: title,
        url: window.location.href
      }).catch((error) => console.log('分享失败:', error));
    } else {
      // 复制链接到剪贴板
      navigator.clipboard.writeText(window.location.href)
        .then(() => alert('链接已复制到剪贴板'))
        .catch((err) => console.error('无法复制链接: ', err));
    }
  };

  // 使用演员对象数组或者演员名称
  const renderActors = () => {
    // 如果有演员对象数组则使用它
    if (actors && actors.length > 0) {
      return (
        <div className="flex flex-wrap gap-3 items-center">
          {actors.map((actor, index) => (
            <Link 
              key={index}
              href={`/${locale}/actress/${actor.name.toLowerCase().replace(/\s+/g, '-')}`}
              className="flex items-center gap-2 bg-gray-800/60 px-3 py-1.5 rounded-full hover:bg-gray-700/80 transition-all duration-300 group"
            >
              {actor.avatar ? (
                <div className="relative w-6 h-6 rounded-full overflow-hidden border border-gray-700/50 group-hover:border-blue-400/50 transition-all">
                  <Image 
                    src={actor.avatar} 
                    alt={actor.name} 
                    fill 
                    className="object-cover"
                  />
                </div>
              ) : (
                <FiUser className="text-blue-400" />
              )}
              <span className="text-gray-300 group-hover:text-blue-400 transition-colors">{actor.name}</span>
            </Link>
          ))}
        </div>
      );
    }
    
    // 否则使用演员名称数组
    if (actorNames && actorNames.length > 0) {
      return (
        <div className="flex flex-wrap gap-2 items-center">
          {actorNames.map((name, index) => (
            <Link 
              key={index}
              href={`/${locale}/actress/${name.toLowerCase().replace(/\s+/g, '-')}`}
              className="flex items-center gap-1.5 bg-gray-800/60 px-3 py-1.5 rounded-full hover:bg-gray-700/80 transition-all duration-300 group"
            >
              <FiUser className="text-blue-400" />
              <span className="text-gray-300 group-hover:text-blue-400 transition-colors">{name}</span>
            </Link>
          ))}
        </div>
      );
    }
    
    return null;
  };

  return (
    <div className="pb-4">
      {/* 视频数据信息显示 - 仅在未设置hideMetadata时显示 */}
      {!hideMetadata && (
        <>
          {/* 视频标题 */}
          <h1 className="text-2xl md:text-3xl font-bold text-white mb-3 leading-tight">{title}</h1>
          
          {/* 统计信息和操作按钮 */}
          <div className="flex flex-wrap items-center gap-4 text-sm text-gray-300 mb-6">
            {/* 如果有videoId且启用实时统计，使用实时组件 */}
            {videoId && enableRealTimeStats ? (
              <div className="flex items-center gap-4">
                <VideoStatsDisplay 
                  videoId={videoId}
                  initialStats={{
                    views: parseInt(views.replace(/[^\d]/g, '')) || 0,
                    likes: parseInt(likes.replace(/[^\d]/g, '')) || 0,
                    favorites: 0 // 如果有初始收藏数可以传入
                  }}
                  enableRealtime={true}
                  showActions={true}
                  size="medium"
                />
                <div className="flex items-center">
                  <FiClock className="mr-1.5 text-gray-400" />
                  <span>{duration}</span>
                </div>
                <div className="flex items-center">
                  <FiCalendar className="mr-1.5 text-gray-400" />
                  <span>{formattedDate}</span>
                </div>
              </div>
            ) : (
              // 备用静态显示
              <>
                <div className="flex items-center">
                  <FiEye className="mr-1.5 text-gray-400" />
                  <span>{views} {t('video.views')}</span>
                </div>
                <div className="flex items-center">
                  <FiClock className="mr-1.5 text-gray-400" />
                  <span>{duration}</span>
                </div>
                <div className="flex items-center">
                  <FiCalendar className="mr-1.5 text-gray-400" />
                  <span>{formattedDate}</span>
                </div>
              </>
            )}
          </div>
        </>
      )}
      
      <div className="space-y-6">
        {/* 分类信息 */}
        <div className="mb-5">
          {categories.length > 0 && (
            <div>
              <h3 className="text-white font-medium mb-2 flex items-center">
                <FiTag className="mr-1.5 text-red-500" />
                <span>{t('video.categoryLabel')}:</span>
              </h3>
              <div className="flex flex-wrap gap-2 mt-2">
                {categories.map((category, index) => (
                  <Link 
                    key={index}
                    href={`/${locale}/category/${category.toLowerCase()}`}
                    className="bg-gray-800/50 text-gray-300 hover:bg-red-600/90 hover:text-white px-2.5 py-1 rounded-full transition-all duration-300 text-sm"
                  >
                    {category}
                  </Link>
                ))}
              </div>
            </div>
          )}
        </div>
        
        {/* 演员和制作商信息 */}
        <div className="space-y-4">
          {/* 演员信息 */}
          {(actorNames.length > 0 || actors.length > 0) && (
            <div>
              <h3 className="text-white font-medium mb-2 flex items-center">
                <FiUser className="mr-1.5 text-blue-400" />
                <span>{t('video.actors')}:</span>
              </h3>
              {renderActors()}
            </div>
          )}
          
          {/* 制作商信息 */}
          {studio && (
            <div>
              <h3 className="text-white font-medium mb-2 flex items-center">
                <FiHome className="mr-1.5 text-purple-400" />
                <span>{t('video.studioLabel')}:</span>
              </h3>
              <Link 
                href={`/${locale}/studio/${studio.toLowerCase().replace(/\s+/g, '-')}`}
                className="bg-gray-800/50 text-gray-300 hover:bg-purple-600/90 hover:text-white px-2.5 py-1 rounded-full transition-all duration-300 text-sm"
              >
                {studio}
              </Link>
            </div>
          )}
        </div>
        
        {/* 视频描述 */}
        <div className="mt-6 relative">
          <div 
            ref={descriptionRef}
            className="text-gray-300 leading-relaxed text-sm max-h-24 overflow-hidden"
          >
            <p>{description}</p>
          </div>
          
          {/* 渐变遮罩效果 */}
          <div 
            ref={gradientRef}
            className="absolute bottom-0 left-0 right-0 h-12 bg-gradient-to-t from-black to-transparent"
          />
          
          {/* 展开/收起按钮 */}
          <button
            onClick={() => setShowFullDescription(!showFullDescription)}
            className="mt-2 text-red-500 hover:text-red-400 text-xs flex items-center transition-colors duration-200"
          >
            {showFullDescription ? (
              <>
                <FiChevronUp className="mr-1" />
                {t('common.viewMore')}
              </>
            ) : (
              <>
                <FiChevronDown className="mr-1" />
                {t('common.viewMore')}
              </>
            )}
          </button>
        </div>
        
        {/* 视频标签 */}
        <div className="pt-4 border-t border-gray-800/30">
          <h3 className="text-white font-medium mb-2 flex items-center">
            <FiTag className="mr-1.5 text-gray-400" />
            <span>{t('video.tags')}</span>
          </h3>
          <div className="flex flex-wrap gap-2 mt-2">
            {tags.map((tag, index) => (
              <Link 
                key={index}
                href={`/${locale}/tag/${tag.toLowerCase()}`}
                className="bg-gray-800/50 text-gray-300 hover:bg-blue-600/90 hover:text-white px-2.5 py-1 rounded-full transition-all duration-300 text-sm"
              >
                {tag}
              </Link>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default VideoInfo; 