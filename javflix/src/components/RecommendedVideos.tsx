'use client';

import React, { useState, useEffect, useRef } from 'react';
import Link from 'next/link';
import { <PERSON>H<PERSON><PERSON>, FiChevronLeft, FiChevronRight, FiRefreshCw } from 'react-icons/fi';
import { buildApiUrl } from '@/lib/api-config';
import { getSocketClient } from '@/lib/socket-client';
import VideoCard from '@/components/VideoCard';
import SectionHeader from '@/components/SectionHeader';
import { useClientTranslations } from '@/components/TranslationsProvider';

interface Video {
  id: string | number;
  movie_id: string;
  code: string;
  title: string;
  description?: string;
  duration: string;
  cover_image?: string;
  image_url?: string;
  release_date: string;
  views?: number;
  view_count?: number;
  timeAgo?: string;
  similarity_score?: number;
}

interface VideoStats {
  views: number;
  likes: number;
  favorites: number;
}

interface FusedVideoStats {
  videoId: string;
  numericVideoId: number;
  views: number;
  likes: number;
  favorites: number;
  dbBase: {
    views: number;
    likes: number;
    favorites: number;
  };
  redisIncrement: {
    views: number;
    likes: number;
    favorites: number;
  };
  lastUpdated: number;
  fusionTimestamp: number;
}

interface RecommendedVideosProps {
  locale: string;
}

const RecommendedVideos: React.FC<RecommendedVideosProps> = ({ locale }) => {
  const [videos, setVideos] = useState<Video[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [refreshing, setRefreshing] = useState(false); // 换一批的loading状态
  
  // 融合统计状态
  const [fusedStats, setFusedStats] = useState<Record<string, FusedVideoStats>>({});
  const socketClient = useRef(getSocketClient());

  // 检查用户是否登录 - 修复Hydration错误
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  
  const { t } = useClientTranslations();
  
  useEffect(() => {
    // 只在客户端检查登录状态
    const token = localStorage.getItem('token') || 
                 localStorage.getItem('auth_token') || 
                 localStorage.getItem('authToken');
    setIsLoggedIn(!!token);
  }, []);

  // 获取融合观看次数（DB累计 + Redis增量）
  const getViewCount = (video: Video): number => {
    const videoId = video.id.toString();
    const fusedData = fusedStats[videoId];
    
    const originalViews = video.view_count || video.views || 0;
    const fusedViews = fusedData?.views || originalViews;
    
    console.log(`💝 推荐视频 ${videoId} (${video.title?.substring(0, 30)}...):`, {
      originalViews,
      fusedViews,
      hasFusedData: !!fusedData,
      fusedDataKeys: Object.keys(fusedStats)
    });
    
    // 优先使用融合统计数据，否则使用初始数据
    return fusedViews;
  };

  // 批量获取融合统计数据
  const fetchFusedStats = async (videoList: Video[]) => {
    try {
      if (videoList.length === 0) return;

      const videoIds = videoList.map(video => video.id.toString());
      

      
      const response = await fetch(buildApiUrl('/api/video-stats/fused/batch'), {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ videoIds }),
      });



      if (!response.ok) {
        console.warn('推荐视频融合统计API失败，使用初始数据');
        return;
      }

      const result = await response.json();
      
      if (result.success && result.data) {
        setFusedStats(result.data);
      }
    } catch (error) {
      console.warn('获取推荐视频融合统计数据失败:', error);
    }
  };

  // 获取推荐视频数据
  const fetchRecommendedVideos = async (isRefresh = false) => {
    try {

      if (isRefresh) {
        setRefreshing(true);
      } else {
        setLoading(true);
      }
      setError(null);

      // 检查用户是否登录
      const token = localStorage.getItem('token') || 
                   localStorage.getItem('auth_token') || 
                   localStorage.getItem('authToken');
      
      let endpoint = '/api/recommended-videos';
      const headers: Record<string, string> = {};
      
      // 如果用户已登录，使用个性化推荐
      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }

      // 添加随机种子参数以获取不同批次的数据，换一批时使用时间戳作为种子
      const seedParam = isRefresh ? `&seed=${Date.now()}` : '';
      const apiUrl = buildApiUrl(`${endpoint}?per_page=8${seedParam}`);
      
      const response = await fetch(apiUrl, {
        headers,
        cache: 'no-store'
      });



      if (!response.ok) {
        throw new Error(`获取推荐视频失败: ${response.status}`);
      }

      const result = await response.json();
      
      if (result.success && Array.isArray(result.data)) {
        const videosData = result.data.slice(0, 8); // 限制8个视频
        setVideos(videosData);
        
        // 获取融合统计数据
        await fetchFusedStats(videosData);
      } else {
        setError('推荐视频数据格式错误');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : '获取推荐视频失败');
    } finally {
      if (isRefresh) {
        setRefreshing(false);
      } else {
        setLoading(false);
      }
    }
  };

  // 换一批功能
  const handleRefresh = async () => {
    if (refreshing) return; // 防止重复点击
    await fetchRecommendedVideos(true);
  };

  useEffect(() => {
    fetchRecommendedVideos();
  }, []);

  // 设置Socket.IO实时统计监听
  useEffect(() => {
    if (videos.length === 0) return;



    // 为每个视频设置统计更新监听
    const unsubscribeFunctions: (() => void)[] = [];

    videos.forEach(video => {
      const videoId = video.id.toString();
      
              const handleStatsUpdate = (updatedStats: VideoStats) => {
        
        // 更新融合统计数据
        setFusedStats(prevStats => {
          const existingFused = prevStats[videoId];
          if (existingFused) {
            return {
              ...prevStats,
              [videoId]: {
                ...existingFused,
                views: updatedStats.views,
                likes: updatedStats.likes,
                favorites: updatedStats.favorites,
                fusionTimestamp: Date.now()
              }
            };
          }
          // 如果没有融合数据，创建新的
          return {
            ...prevStats,
            [videoId]: {
              videoId,
              numericVideoId: parseInt(videoId),
              views: updatedStats.views,
              likes: updatedStats.likes,
              favorites: updatedStats.favorites,
              dbBase: { views: 0, likes: 0, favorites: 0 },
              redisIncrement: updatedStats,
              lastUpdated: Date.now(),
              fusionTimestamp: Date.now()
            }
          };
        });
      };

      // 订阅统计更新
      socketClient.current.subscribeToStats(videoId, handleStatsUpdate);
      
      // 记录取消订阅函数
      unsubscribeFunctions.push(() => {
        socketClient.current.unsubscribeFromStats(videoId);
      });
    });

    // 清理函数：取消所有订阅
    return () => {
      unsubscribeFunctions.forEach(unsubscribe => unsubscribe());
    };
  }, [videos]);

  // 定期刷新融合数据（确保数据一致性）
  useEffect(() => {
    if (videos.length === 0) return;

    const refreshInterval = setInterval(() => {
      fetchFusedStats(videos);
    }, 30000); // 每30秒刷新一次

    return () => clearInterval(refreshInterval);
  }, [videos]);



  if (loading) {
    return (
      <div className="w-full">
        <div className="mb-6 flex items-center justify-between">
          <h2 className="text-2xl font-bold text-white flex items-center">
            <div className="w-1 h-6 bg-red-600 rounded-full mr-3"></div>
            {isLoggedIn ? t('common.forYou') : t('common.recommendedVideos')}
          </h2>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {[...Array(8)].map((_, i) => (
            <div key={i} className="animate-pulse">
              <div className="h-48 md:h-52 bg-gray-800 rounded-xl mb-2"></div>
              <div className="h-4 bg-gray-800 rounded w-3/4 mb-2"></div>
              <div className="h-3 bg-gray-700 rounded w-1/2"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="w-full">
        <div className="mb-6 flex items-center justify-between">
          <h2 className="text-2xl font-bold text-white flex items-center">
            <div className="w-1 h-6 bg-red-600 rounded-full mr-3"></div>
            {isLoggedIn ? t('common.forYou') : t('common.recommendedVideos')}
          </h2>
        </div>
        <div className="p-4 text-center text-red-500">
          暂时无法加载推荐视频
        </div>
      </div>
    );
  }

  return (
    <div className="w-full">
      <div className="mb-6 flex items-center justify-between">
        <h2 className="text-2xl font-bold text-white flex items-center">
          <div className="w-1 h-6 bg-red-600 rounded-full mr-3"></div>
          {isLoggedIn ? t('common.forYou') : t('common.recommendedVideos')}
        </h2>
        
        <button 
          onClick={handleRefresh}
          disabled={refreshing}
          className={`group relative flex items-center text-sm rounded-lg px-3 py-2 transition-all duration-300 ease-out overflow-hidden border border-transparent ${
            refreshing 
              ? 'text-gray-500 cursor-not-allowed bg-gray-800/30 border-gray-700/50' 
              : 'text-gray-400 hover:text-white hover:bg-red-600/20 hover:border-red-600/30 hover:scale-105 hover:translate-x-1 hover:shadow-lg hover:shadow-red-600/25 active:scale-95 active:translate-x-0'
          }`}
        >
          {/* 背景光效 */}
          <div className={`absolute inset-0 bg-gradient-to-r from-red-600/0 via-red-600/20 to-red-600/0 transform transition-transform duration-700 ease-out ${
            refreshing ? 'translate-x-0' : '-translate-x-full group-hover:translate-x-full'
          }`}></div>
          
          <div className="relative flex items-center overflow-hidden z-10">
            {/* 图标容器 - 带旋转和缩放动画 */}
            <div className={`transition-all duration-500 ease-out transform ${
              refreshing 
                ? 'scale-110 rotate-180' 
                : 'scale-100 group-hover:rotate-180 group-hover:scale-110'
            }`}>
              <FiRefreshCw 
                className={`${refreshing ? 'animate-spin' : ''} transition-all duration-300 ease-out`} 
                size={14} 
              />
            </div>
            
            {/* 文字容器 - 带淡入淡出和位移动画 */}
            <div className="relative ml-2 min-w-[64px] h-5">
              {/* 正常状态文字 */}
              <span className={`absolute top-0 left-0 block transition-all duration-400 ease-[cubic-bezier(0.34,1.56,0.64,1)] transform ${
                refreshing 
                  ? 'opacity-0 -translate-x-6 -translate-y-1 scale-90 blur-sm' 
                  : 'opacity-100 translate-x-0 translate-y-0 scale-100 blur-0'
              }`}>
                换一批
              </span>
              
              {/* Loading状态文字 */}
              <span className={`absolute top-0 left-0 whitespace-nowrap block transition-all duration-400 ease-[cubic-bezier(0.34,1.56,0.64,1)] transform ${
                refreshing 
                  ? 'opacity-100 translate-x-0 translate-y-0 scale-100 blur-0' 
                  : 'opacity-0 translate-x-6 translate-y-1 scale-90 blur-sm'
              }`}>
                换一批中...
              </span>
            </div>
          </div>
        </button>
      </div>
      
      <div className={`grid grid-cols-2 md:grid-cols-4 gap-4 transition-opacity duration-300 ${
        refreshing ? 'opacity-50' : 'opacity-100'
      }`}>
        {videos.slice(0, 8).map((video) => (
          <div key={video.id} className="overflow-hidden">
            <VideoCard
              video={video}
              locale={locale}
              showSimilarity={isLoggedIn}
              size="small"
              fusedStats={fusedStats[video.id.toString()]}
            />
          </div>
        ))}
      </div>
    </div>
  );
};

export default RecommendedVideos; 