'use client';

import React, { useEffect, useRef } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { FiX, FiHeart, FiBookmark, FiMessageCircle, FiUser, FiStar, FiTrendingUp } from 'react-icons/fi';
import gsap from 'gsap';
import { useClientTranslations } from '@/components/TranslationsProvider';

interface LoginPromptModalProps {
  isOpen: boolean;
  onClose: () => void;
  actionType?: 'like' | 'favorite' | 'comment' | 'general';
}

const LoginPromptModal: React.FC<LoginPromptModalProps> = ({
  isOpen,
  onClose,
  actionType = 'general'
}) => {
  const { t } = useClientTranslations();
  const router = useRouter();
  const params = useParams();
  const locale = params?.locale?.toString() || 'zh-CN';
  
  const overlayRef = useRef<HTMLDivElement>(null);
  const modalRef = useRef<HTMLDivElement>(null);
  const contentRef = useRef<HTMLDivElement>(null);

  // 获取对应的消息和图标
  const getActionContent = () => {
    switch (actionType) {
      case 'like':
        return {
          icon: <FiHeart className="w-8 h-8 text-red-400" />,
          message: t('auth.login.loginRequired.likeMessage'),
          color: 'from-red-500/20 to-pink-500/20'
        };
      case 'favorite':
        return {
          icon: <FiBookmark className="w-8 h-8 text-yellow-400" />,
          message: t('auth.login.loginRequired.favoriteMessage'),
          color: 'from-yellow-500/20 to-orange-500/20'
        };
      case 'comment':
        return {
          icon: <FiMessageCircle className="w-8 h-8 text-blue-400" />,
          message: t('auth.login.loginRequired.commentMessage'),
          color: 'from-blue-500/20 to-purple-500/20'
        };
      default:
        return {
          icon: <FiUser className="w-8 h-8 text-green-400" />,
          message: t('auth.login.loginRequired.generalMessage'),
          color: 'from-green-500/20 to-blue-500/20'
        };
    }
  };

  const actionContent = getActionContent();

  // 处理登录
  const handleLogin = () => {
    onClose();
    router.push(`/${locale}/auth/login`);
  };

  // 处理注册
  const handleRegister = () => {
    onClose();
    router.push(`/${locale}/auth/register`);
  };

  // 动画效果
  useEffect(() => {
    if (typeof document === 'undefined') return;

    if (isOpen) {
      // 显示模态框
      document.body.style.overflow = 'hidden';
      
      if (overlayRef.current) {
        overlayRef.current.style.display = 'flex';
        gsap.fromTo(overlayRef.current, 
          { opacity: 0 },
          { opacity: 1, duration: 0.3 }
        );
      }

      if (modalRef.current) {
        gsap.fromTo(modalRef.current,
          { scale: 0.8, opacity: 0, y: 50 },
          { scale: 1, opacity: 1, y: 0, duration: 0.4, ease: 'back.out(1.7)' }
        );
      }

      if (contentRef.current) {
        gsap.fromTo(contentRef.current.children,
          { opacity: 0, y: 20 },
          { opacity: 1, y: 0, duration: 0.5, stagger: 0.1, delay: 0.2 }
        );
      }
    } else {
      // 隐藏模态框
      document.body.style.overflow = 'unset';
      
      if (overlayRef.current) {
        gsap.to(overlayRef.current, {
          opacity: 0,
          duration: 0.2,
          onComplete: () => {
            if (overlayRef.current) {
              overlayRef.current.style.display = 'none';
            }
          }
        });
      }
    }

    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  // ESC键关闭
  useEffect(() => {
    if (typeof document === 'undefined') return;

    const handleEsc = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEsc);
    }

    return () => {
      document.removeEventListener('keydown', handleEsc);
    };
  }, [isOpen, onClose]);

  if (!isOpen) return null;

  return (
    <div
      ref={overlayRef}
      className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4"
      style={{ display: 'none' }}
      onClick={(e) => {
        if (e.target === overlayRef.current) {
          onClose();
        }
      }}
    >
      <div
        ref={modalRef}
        className="bg-gray-900 rounded-2xl border border-gray-700/50 shadow-2xl max-w-md w-full overflow-hidden"
      >
        {/* 头部装饰 */}
        <div className={`h-2 bg-gradient-to-r ${actionContent.color}`} />
        
        {/* 关闭按钮 */}
        <div className="absolute top-4 right-4 z-10">
          <button
            onClick={onClose}
            className="p-2 text-gray-400 hover:text-white hover:bg-gray-800 rounded-full transition-all duration-200"
          >
            <FiX className="w-5 h-5" />
          </button>
        </div>

        {/* 内容区域 */}
        <div ref={contentRef} className="p-8 text-center">
          {/* 图标 */}
          <div className="flex justify-center mb-6">
            <div className={`p-4 rounded-full bg-gradient-to-r ${actionContent.color} border border-gray-600/30`}>
              {actionContent.icon}
            </div>
          </div>

          {/* 标题 */}
          <h2 className="text-2xl font-bold text-white mb-4">
            {t('auth.login.loginRequired.title')}
          </h2>

          {/* 消息 */}
          <p className="text-gray-300 text-lg mb-8 leading-relaxed">
            {actionContent.message}
          </p>

          {/* 功能列表 */}
          <div className="bg-gray-800/50 rounded-xl p-6 mb-8">
            <h3 className="text-white font-medium mb-4 text-left">登录后您可以：</h3>
            <div className="space-y-3">
              <div className="flex items-center text-gray-300 text-sm">
                <FiHeart className="w-4 h-4 text-red-400" />
                <span className="ml-3">{t('auth.login.loginRequired.benefits.like')}</span>
              </div>
              <div className="flex items-center text-gray-300 text-sm">
                <FiBookmark className="w-4 h-4 text-yellow-400" />
                <span className="ml-3">{t('auth.login.loginRequired.benefits.favorite')}</span>
              </div>
              <div className="flex items-center text-gray-300 text-sm">
                <FiMessageCircle className="w-4 h-4 text-blue-400" />
                <span className="ml-3">{t('auth.login.loginRequired.benefits.comment')}</span>
              </div>
              <div className="flex items-center text-gray-300 text-sm">
                <FiStar className="w-4 h-4 text-purple-400" />
                <span className="ml-3">{t('auth.login.loginRequired.benefits.history')}</span>
              </div>
              <div className="flex items-center text-gray-300 text-sm">
                <FiTrendingUp className="w-4 h-4 text-green-400" />
                <span className="ml-3">{t('auth.login.loginRequired.benefits.recommendation')}</span>
              </div>
            </div>
          </div>

          {/* 按钮组 */}
          <div className="space-y-3">
            <button
              onClick={handleLogin}
              className="w-full py-3 px-6 bg-gradient-to-r from-red-600 to-pink-600 hover:from-red-700 hover:to-pink-700 text-white font-medium rounded-xl transition-all duration-300 transform hover:scale-105 hover:shadow-lg hover:shadow-red-500/25"
            >
              {t('auth.login.loginRequired.loginNow')}
            </button>
            
            <button
              onClick={handleRegister}
              className="w-full py-3 px-6 bg-gray-700 hover:bg-gray-600 text-white font-medium rounded-xl transition-all duration-300 transform hover:scale-105"
            >
              {t('auth.login.loginRequired.registerNow')}
            </button>
            
            <button
              onClick={onClose}
              className="w-full py-2 px-6 text-gray-400 hover:text-white transition-colors duration-200"
            >
              {t('auth.login.loginRequired.later')}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LoginPromptModal;
