'use client';

interface VideoGridSkeletonProps {
  count?: number;
  gridCols?: string;
  cardHeight?: string;
  showTitle?: boolean;
  showMeta?: boolean;
}

const VideoGridSkeleton = ({ 
  count = 20, 
  gridCols = 'grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5',
  cardHeight = 'h-48',
  showTitle = true,
  showMeta = true
}: VideoGridSkeletonProps) => {
  return (
    <div className={`grid ${gridCols} gap-6`}>
      {[...Array(count)].map((_, i) => (
        <div key={i} className="bg-gray-800 rounded-lg p-4 animate-pulse">
          <div className={`bg-gray-700 rounded-lg mb-4 ${cardHeight}`}></div>
          {showTitle && (
            <div className="h-4 bg-gray-700 rounded mb-2"></div>
          )}
          {showMeta && (
            <div className="h-3 bg-gray-600 rounded w-3/4"></div>
          )}
        </div>
      ))}
    </div>
  );
};

export default VideoGridSkeleton;