'use client';

import { useClientTranslations } from '@/components/TranslationsProvider';

interface ErrorStateProps {
  error: string;
  onRetry?: () => void;
  title?: string;
  emoji?: string;
  retryText?: string;
}

const ErrorState = ({ 
  error, 
  onRetry, 
  title,
  emoji = '😞',
  retryText
}: ErrorStateProps) => {
  const { t } = useClientTranslations();

  return (
    <div className="text-center py-24">
      <div className="relative inline-block">
        <div className="text-8xl mb-6 filter drop-shadow-lg">{emoji}</div>
        <div className="absolute -top-2 -right-2 w-6 h-6 bg-red-500 rounded-full animate-ping"></div>
      </div>
      <h3 className="text-2xl font-medium mb-3 text-gray-200">
        {title || t('common.error') || '加载失败'}
      </h3>
      <p className="text-gray-400 text-lg mb-6">{error}</p>
      {onRetry && (
        <button 
          onClick={onRetry}
          className="px-6 py-3 bg-red-600 hover:bg-red-700 text-white rounded-xl transition-all duration-300 transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 focus:ring-offset-gray-900"
        >
          {retryText || t('common.retry') || '重试'}
        </button>
      )}
    </div>
  );
};

export default ErrorState;