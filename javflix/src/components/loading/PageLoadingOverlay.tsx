'use client';

import { useClientTranslations } from '@/components/TranslationsProvider';
import LoadingSpinner from './LoadingSpinner';

interface PageLoadingOverlayProps {
  text?: string;
  showBackgroundDecorations?: boolean;
  className?: string;
}

const PageLoadingOverlay = ({ 
  text, 
  showBackgroundDecorations = true,
  className = ''
}: PageLoadingOverlayProps) => {
  const { t } = useClientTranslations();

  return (
    <div className={`min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-800 text-white pt-20 pb-12 ${className}`}>
      {/* Background decorations */}
      {showBackgroundDecorations && (
        <div className="absolute inset-0 pointer-events-none">
          <div className="absolute top-1/4 left-1/4 w-64 h-64 bg-red-500/10 rounded-full filter blur-3xl"></div>
          <div className="absolute bottom-1/3 right-1/3 w-96 h-96 bg-blue-500/8 rounded-full filter blur-3xl"></div>
          <div className="absolute top-1/2 left-1/2 w-48 h-48 bg-purple-500/12 rounded-full filter blur-2xl"></div>
        </div>
      )}

      <div className="relative container mx-auto px-4 flex justify-center items-center min-h-[500px]">
        <LoadingSpinner 
          size="large"
          text={text || t('common.loading') || '正在加载精彩内容...'}
        />
      </div>
    </div>
  );
};

export default PageLoadingOverlay;