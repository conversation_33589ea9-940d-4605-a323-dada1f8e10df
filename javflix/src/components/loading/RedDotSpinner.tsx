'use client';

import { useClientTranslations } from '@/components/TranslationsProvider';

interface RedDotSpinnerProps {
  size?: 'small' | 'medium' | 'large';
  text?: string;
  showText?: boolean;
  className?: string;
}

const RedDotSpinner = ({ 
  size = 'large',
  text,
  showText = true,
  className = ''
}: RedDotSpinnerProps) => {
  const { t } = useClientTranslations();

  const sizeClasses = {
    small: 'w-8 h-8',
    medium: 'w-12 h-12',
    large: 'w-16 h-16'
  };

  const dotSizeClasses = {
    small: 'w-3 h-3',
    medium: 'w-6 h-6',
    large: 'w-8 h-8'
  };

  const textSizeClasses = {
    small: 'text-sm',
    medium: 'text-base',
    large: 'text-lg'
  };

  return (
    <div className={`flex justify-center items-center ${className}`}>
      <div className="text-center">
        <div className="relative">
          <div className={`border-4 border-gray-700 border-t-white rounded-full animate-spin mx-auto ${showText ? 'mb-6' : ''} ${sizeClasses[size]}`}></div>
          <div className={`absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white/20 rounded-full animate-pulse ${dotSizeClasses[size]}`}></div>
        </div>
        {showText && (
          <p className={`text-gray-300 font-light ${textSizeClasses[size]}`}>
            {text || t('common.loading') || '正在加载...'}
          </p>
        )}
      </div>
    </div>
  );
};

export default RedDotSpinner;