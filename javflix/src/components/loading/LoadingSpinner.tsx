'use client';

import { useClientTranslations } from '@/components/TranslationsProvider';

interface LoadingSpinnerProps {
  size?: 'small' | 'medium' | 'large';
  text?: string;
  showText?: boolean;
  variant?: 'default' | 'minimal';
}

const LoadingSpinner = ({ 
  size = 'medium', 
  text, 
  showText = true,
  variant = 'default'
}: LoadingSpinnerProps) => {
  const { t } = useClientTranslations();

  const sizeClasses = {
    small: 'w-8 h-8',
    medium: 'w-16 h-16',
    large: 'w-20 h-20'
  };

  const textSizeClasses = {
    small: 'text-sm',
    medium: 'text-lg',
    large: 'text-xl'
  };

  if (variant === 'minimal') {
    return (
      <div className="relative">
        <div className={`border-4 border-gray-700 border-t-white rounded-full animate-spin ${sizeClasses[size]}`}></div>
        <div className={`absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 ${size === 'small' ? 'w-4 h-4' : size === 'medium' ? 'w-8 h-8' : 'w-10 h-10'} bg-white/20 rounded-full animate-pulse`}></div>
      </div>
    );
  }

  return (
    <div className="flex justify-center items-center">
      <div className="text-center">
        <div className="relative">
          <div className={`border-4 border-gray-700 border-t-white rounded-full animate-spin mx-auto mb-6 ${sizeClasses[size]}`}></div>
          <div className={`absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 ${size === 'small' ? 'w-4 h-4' : size === 'medium' ? 'w-8 h-8' : 'w-10 h-10'} bg-white/20 rounded-full animate-pulse`}></div>
        </div>
        {showText && (
          <p className={`text-gray-300 font-light ${textSizeClasses[size]}`}>
            {text || t('common.loading') || '正在加载...'}
          </p>
        )}
      </div>
    </div>
  );
};

export default LoadingSpinner;