'use client';

import { useRef, useEffect, useState } from 'react';
import Link from 'next/link';
import gsap from 'gsap';
import { FiGrid, FiChevronRight } from 'react-icons/fi';
import { useClientTranslations } from '@/components/TranslationsProvider';

// 分类数据结构类型
interface Category {
  id: number | string;
  name: string;
  movie_count?: number;
  slug?: string;
  imageUrl?: string;
  color?: string;
  icon?: string;
}

interface CategoriesProps {
  locale?: string;
}

const Categories = ({ locale = 'zh' }: CategoriesProps) => {
  const { t } = useClientTranslations();
  const [categories, setCategories] = useState<Category[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  const titleRef = useRef<HTMLHeadingElement>(null);
  const categoryRefs = useRef<Array<HTMLDivElement | null>>([]);

  // 获取热门分类数据
  useEffect(() => {
    const fetchCategories = async () => {
      setIsLoading(true);
      setError(null);
      
      try {
        console.log('正在获取热门分类数据...');
        
        // 使用数据库API获取热门分类
        const response = await fetch('/api/db/genres/popular?limit=6');
        
        if (!response.ok) {
          throw new Error(`API请求失败: ${response.status} ${response.statusText}`);
        }
        
        const result = await response.json();
        console.log('分类API响应:', result);
        
        if (result.success && result.data && Array.isArray(result.data.items)) {
          // 处理数据并添加额外属性
          const processedCategories = result.data.items.map((category: any, index: number) => ({
            id: category.id,
            name: category.name,
            movie_count: category.movie_count || 0,
            slug: generateSlug(category.name),
            imageUrl: mapImageUrl(category, index),
            color: getColorForCategory(category.name, index),
            icon: getIconForCategory(category.name)
          }));
          
          setCategories(processedCategories);
          console.log('成功设置分类数据:', processedCategories);
        } else {
          console.error('数据格式错误:', result);
          throw new Error('数据格式不正确');
        }
      } catch (error) {
        console.error('获取分类数据失败:', error);
        setError(error instanceof Error ? error.message : '未知错误');
        
        // 错误时尝试获取普通分类列表作为备选
        try {
          console.log('尝试获取普通分类列表...');
          const fallbackResponse = await fetch('/api/db/genres?limit=6&sort=name');
          
          if (fallbackResponse.ok) {
            const fallbackResult = await fallbackResponse.json();
            
            if (fallbackResult.success && fallbackResult.data && Array.isArray(fallbackResult.data.items)) {
              const fallbackCategories = fallbackResult.data.items.map((category: any, index: number) => ({
                id: category.id,
                name: category.name,
                movie_count: category.movie_count || 0,
                slug: generateSlug(category.name),
                imageUrl: mapImageUrl(category, index),
                color: getColorForCategory(category.name, index),
                icon: getIconForCategory(category.name)
              }));
              
              setCategories(fallbackCategories);
              setError(null); // 清除错误状态
              console.log('使用备选数据成功:', fallbackCategories);
            } else {
              throw new Error('备选API数据格式错误');
            }
          } else {
            throw new Error('备选API请求失败');
          }
        } catch (fallbackError) {
          console.error('备选方案也失败:', fallbackError);
          // 最后使用硬编码的备用数据
          setCategories(getFallbackCategories());
        }
      } finally {
        setIsLoading(false);
      }
    };

    fetchCategories();
  }, []);
  
  // 添加动画效果
  useEffect(() => {
    if (isLoading || categories.length === 0) return;
    
    // 确保refs数组长度与categories匹配
    categoryRefs.current = categoryRefs.current.slice(0, categories.length);
    
    // 标题动画
    if (titleRef.current) {
      gsap.fromTo(
        titleRef.current,
        { x: -20, opacity: 0 },
        { x: 0, opacity: 1, duration: 0.5 }
      );
    }
    
    // 分类卡片动画
    categoryRefs.current.forEach((ref, index) => {
      if (ref) {
        gsap.fromTo(
          ref,
          { opacity: 0, scale: 0.95, y: 20 },
          { 
            opacity: 1, 
            scale: 1, 
            y: 0,
            duration: 0.4, 
            delay: index * 0.1,
            ease: "back.out(1.2)"
          }
        );
      }
    });
  }, [categories, isLoading]);
  
  const setRef = (el: HTMLDivElement | null, index: number) => {
    categoryRefs.current[index] = el;
  };

  // 为分类生成占位图片URL
  const generateImageUrl = (id: number): string => {
    // 使用现有的占位图片，循环使用
    const placeholderImages = [
      '/images/placeholder.jpg',
      '/images/placeholder.png',
      '/images/defaults/cover_default.jpg'
    ];
    
    // 根据分类ID取模选择占位图片
    const imageIndex = (id || 0) % placeholderImages.length;
    return placeholderImages[imageIndex];
  };

  // 生成分类路径的slug
  const generateSlug = (name: string): string => {
    return encodeURIComponent(name);
  };

  // 映射分类到图片URL
  const mapImageUrl = (category: any, index: number): string => {
    return generateImageUrl(category.id || index);
  };

  // 根据分类名称或索引获取颜色
  const getColorForCategory = (name: string, index: number): string => {
    const categoryColors: Record<string, string> = {
      '高畫質': 'from-blue-600 to-blue-800',
      '高清': 'from-blue-600 to-blue-800',
      '4K': 'from-purple-600 to-purple-800',
      '巨乳': 'from-pink-600 to-pink-800',
      '中出': 'from-red-600 to-red-800',
      '單體作品': 'from-green-600 to-green-800',
      '无码': 'from-gray-600 to-gray-800',
      'VR': 'from-cyan-600 to-cyan-800',
      '字幕': 'from-indigo-600 to-indigo-800',
      '新人': 'from-yellow-600 to-yellow-800',
      '经典': 'from-amber-600 to-amber-800'
    };

    const defaultColors = [
      'from-red-600 to-red-800',
      'from-blue-600 to-blue-800',
      'from-green-600 to-green-800',
      'from-purple-600 to-purple-800',
      'from-pink-600 to-pink-800',
      'from-indigo-600 to-indigo-800'
    ];

    return categoryColors[name] || defaultColors[index % defaultColors.length];
  };

  // 根据分类名称获取图标
  const getIconForCategory = (name: string): string => {
    const categoryIcons: Record<string, string> = {
      '高畫質': '🎬',
      '高清': '🎬',
      '4K': '📺',
      '巨乳': '🍈',
      '中出': '💦',
      '單體作品': '👤',
      '无码': '🌟',
      'VR': '🥽',
      '字幕': '📝',
      '新人': '🆕',
      '经典': '🏆'
    };

    return categoryIcons[name] || '🎭';
  };

  // 格式化数量显示
  const formatCount = (count: number): string => {
    if (count >= 10000) {
      return `${(count / 10000).toFixed(1)}${t('common.tenThousand')}`;
    } else if (count >= 1000) {
      return `${(count / 1000).toFixed(1)}${t('common.thousand')}`;
    }
    return count.toString();
  };

  // 如果有错误且没有数据，显示错误状态
  if (error && categories.length === 0) {
    return (
      <section className="my-8">
        <div className="container mx-auto px-4">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl font-bold text-white flex items-center">
              <FiGrid className="mr-2 text-red-600" />
              {t('category.popular')}
            </h2>
          </div>
          <div className="text-center py-8">
            <p className="text-gray-400 text-sm">{t('common.failedToLoadVideos')}</p>
            <p className="text-gray-500 text-xs mt-1">{error}</p>
          </div>
        </div>
      </section>
    );
  }

  return (
    <section className="my-8">
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between mb-6">
          <h2 
            ref={titleRef}
            className="text-xl md:text-2xl font-bold text-white flex items-center"
          >
            <FiGrid className="mr-2 text-red-600" />
            {t('category.popular')}
            {error && (
              <span className="ml-2 text-xs text-yellow-500" title={error}>
                ({t('common.loadingFailed')})
              </span>
            )}
          </h2>
          <Link href={`/${locale}/category`} className="text-sm text-gray-400 hover:text-white flex items-center">
            {t('common.viewMore')} <FiChevronRight className="ml-1" size={14} />
          </Link>
        </div>

        {isLoading ? (
          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4">
            {[...Array(6)].map((_, index) => (
              <div key={index} className="animate-pulse">
                <div className="bg-gray-800/30 rounded-lg overflow-hidden">
                  <div className="h-24 bg-gray-700/30"></div>
                  <div className="p-4">
                    <div className="h-4 bg-gray-700/30 w-3/4 rounded mb-2"></div>
                    <div className="h-3 bg-gray-700/30 w-1/2 rounded"></div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4">
            {categories.map((category, index) => (
              <div 
                key={category.id}
                ref={(el) => setRef(el, index)}
              >
                <Link href={`/${locale}/category/${category.slug}`}>
                  <div className={`bg-gradient-to-br ${category.color} rounded-lg overflow-hidden hover:shadow-lg hover:-translate-y-1 transition-all duration-300 group`}>
                    <div className="relative h-24 overflow-hidden">
                      <img
                        src={category.imageUrl}
                        alt={category.name}
                        className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110"
                        onError={(e) => {
                          e.currentTarget.src = generateImageUrl(category.id as number || index);
                        }}
                      />
                      <div className="absolute inset-0 bg-black/40 group-hover:bg-black/20 transition-all duration-300"></div>
                      
                      {/* 图标 */}
                      <div className="absolute top-2 left-2">
                        <span className="text-2xl filter drop-shadow-lg">
                          {category.icon}
                        </span>
                      </div>
                    </div>
                    
                    <div className="p-4 text-center">
                      <h3 className="text-white font-medium text-sm mb-1 group-hover:text-white/90 transition-colors truncate">
                        {category.name}
                      </h3>
                      <p className="text-white/70 text-xs">
                        {formatCount(category.movie_count || 0)}{t('common.videos')}
                      </p>
                    </div>
                  </div>
                </Link>
              </div>
            ))}
          </div>
        )}
      </div>
    </section>
  );
};

// 备用分类数据，用于API完全失败的情况
function getFallbackCategories(): Category[] {
  return [
    {
      id: 'fallback-1',
      name: '高清',
      movie_count: 2450,
      slug: 'hd',
      imageUrl: '/images/categories/hd.jpg',
      color: 'from-blue-600 to-blue-800',
      icon: '🎬'
    },
    {
      id: 'fallback-2',
      name: '巨乳',
      movie_count: 1680,
      slug: 'busty',
      imageUrl: '/images/categories/busty.jpg',
      color: 'from-pink-600 to-pink-800',
      icon: '🍈'
    },
    {
      id: 'fallback-3',
      name: '无码',
      movie_count: 1280,
      slug: 'uncensored',
      imageUrl: '/images/categories/uncensored.jpg',
      color: 'from-gray-600 to-gray-800',
      icon: '🌟'
    },
    {
      id: 'fallback-4',
      name: 'VR',
      movie_count: 580,
      slug: 'vr',
      imageUrl: '/images/categories/vr.jpg',
      color: 'from-cyan-600 to-cyan-800',
      icon: '🥽'
    },
    {
      id: 'fallback-5',
      name: '字幕',
      movie_count: 890,
      slug: 'subtitle',
      imageUrl: '/images/categories/subtitle.jpg',
      color: 'from-indigo-600 to-indigo-800',
      icon: '📝'
    },
    {
      id: 'fallback-6',
      name: '新人',
      movie_count: 320,
      slug: 'newcomer',
      imageUrl: '/images/categories/newcomer.jpg',
      color: 'from-yellow-600 to-yellow-800',
      icon: '🆕'
    }
  ];
}

export default Categories; 