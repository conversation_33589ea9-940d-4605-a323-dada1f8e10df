'use client';

import { useClientTranslations } from './TranslationsProvider';
import { useEffect, useState } from 'react';

export default function ClientTranslationExample() {
  const { t } = useClientTranslations();
  // 使用字符串格式初始化，避免水合不匹配错误
  const [currentDate, setCurrentDate] = useState<string>('');
  // 增加挂载状态标记，避免组件在挂载前渲染
  const [isMounted, setIsMounted] = useState(false);
  
  // 客户端加载后设置日期和挂载状态，避免水合错误
  useEffect(() => {
    // 首先标记组件已挂载
    setIsMounted(true);
    
    // 使用格式化工具或简单字符串处理，而非直接使用Date对象
    const now = new Date();
    const formattedDate = `${now.getFullYear()}-${(now.getMonth() + 1).toString().padStart(2, '0')}-${now.getDate().toString().padStart(2, '0')}`;
    setCurrentDate(formattedDate);
  }, []);
  
  // 如果组件尚未挂载，返回null或占位内容
  if (!isMounted) {
    return (
      <div className="my-8 p-4 bg-gray-800 rounded-lg">
        <h2 className="text-xl font-bold mb-4">客户端组件内翻译示例：</h2>
        <p>加载中...</p>
      </div>
    );
  }
  
  return (
    <div className="my-8 p-4 bg-gray-800 rounded-lg">
      <h2 className="text-xl font-bold mb-4">客户端组件内翻译示例：</h2>
      <p>{t('nav.home')} | {t('nav.popular')} | {t('nav.categories')}</p>
      {currentDate && <p className="mt-4">{t('common.published')} {currentDate}</p>}
    </div>
  );
} 