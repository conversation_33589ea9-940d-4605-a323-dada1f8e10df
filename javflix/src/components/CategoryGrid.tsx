'use client';

import Link from 'next/link';
import { FiGrid, FiChevronRight } from 'react-icons/fi';

interface Category {
  id: string;
  name: string;
  count: number;
  color: string;
  icon: string;
}

const CategoryGrid = () => {
  // 热门分类数据
  const categories: Category[] = [
    { id: 'uniform', name: '制服', count: 3450, color: 'from-blue-600 to-blue-800', icon: '👔' },
    { id: 'big-breast', name: '巨乳', count: 5280, color: 'from-pink-600 to-pink-800', icon: '🍈' },
    { id: 'ol', name: '办公室女郎', count: 2130, color: 'from-purple-600 to-purple-800', icon: '💼' },
    { id: 'schoolgirl', name: '女学生', count: 4720, color: 'from-yellow-600 to-yellow-800', icon: '🎒' },
    { id: 'teacher', name: '女教师', count: 1860, color: 'from-green-600 to-green-800', icon: '📚' },
    { id: 'beauty', name: '美少女', count: 6310, color: 'from-red-600 to-red-800', icon: '💖' },
    { id: 'milf', name: '人妻', count: 3890, color: 'from-indigo-600 to-indigo-800', icon: '👩‍👧' },
    { id: 'stockings', name: '丝袜', count: 4150, color: 'from-cyan-600 to-cyan-800', icon: '🧦' },
    { id: 'cosplay', name: '角色扮演', count: 1720, color: 'from-rose-600 to-rose-800', icon: '🎭' },
    { id: 'mature', name: '熟女', count: 2940, color: 'from-amber-600 to-amber-800', icon: '👩‍🦰' },
    { id: 'loli', name: '萝莉系', count: 3680, color: 'from-lime-600 to-lime-800', icon: '🎀' },
    { id: 'uncensored', name: '无码', count: 2350, color: 'from-gray-600 to-gray-800', icon: '🌟' },
  ];

  // 格式化视频数量
  const formatCount = (count: number) => {
    if (count >= 10000) {
      return `${(count / 10000).toFixed(1)}万`;
    } else if (count >= 1000) {
      return `${(count / 1000).toFixed(1)}千`;
    }
    return count.toString();
  };

  return (
    <section className="py-8 bg-gray-900/50">
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl md:text-2xl font-bold text-white flex items-center">
            <FiGrid className="mr-2 text-red-600" />
            热门分类
          </h2>
          <Link href="/categories" className="text-sm text-gray-400 hover:text-white transition flex items-center">
            全部分类 <FiChevronRight className="ml-1" />
          </Link>
        </div>

        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4">
          {categories.map((category) => (
            <Link
              key={category.id}
              href={`/category/${category.id}`}
              className="block"
            >
              <div className={`bg-gradient-to-br ${category.color} rounded-lg overflow-hidden hover:shadow-lg hover:-translate-y-1 transition-all duration-300`}>
                <div className="p-6 flex flex-col items-center">
                  <span className="text-3xl mb-3">{category.icon}</span>
                  <h3 className="text-white font-medium text-center">{category.name}</h3>
                  <p className="text-white/70 text-xs mt-2">{formatCount(category.count)}部视频</p>
                </div>
              </div>
            </Link>
          ))}
        </div>
      </div>
    </section>
  );
};

export default CategoryGrid;
