'use client';

import { useRouter, usePathname } from 'next/navigation';
import { useState, useEffect, useTransition, useRef } from 'react';
import { locales, getLanguageName, detectBrowserLanguage } from '@/i18n';
import { Locale } from '@/i18n/types';

export default function LanguageSwitcher() {
  const router = useRouter();
  const pathname = usePathname();
  const [open, setOpen] = useState(false);
  const [isPending, startTransition] = useTransition();
  const menuRef = useRef<HTMLDivElement>(null);
  
  // 从 URL 获取当前语言，避免在客户端和服务端之间的差异
  const [currentLocale, setCurrentLocale] = useState<Locale | null>(null);
  
  useEffect(() => {
    // 从 URL 解析当前语言
    const pathSegments = pathname.split('/');
    if (pathSegments.length > 1) {
      const possibleLocale = pathSegments[1] as Locale;
      if (locales.includes(possibleLocale)) {
        setCurrentLocale(possibleLocale);
        // 同步到 localStorage 以便记住用户选择
        localStorage.setItem('locale', possibleLocale);
        return;
      }
    }

    // 如果 URL 中没有有效的语言前缀，尝试从 localStorage 获取
    const savedLocale = localStorage.getItem('locale') as Locale;
    if (savedLocale && locales.includes(savedLocale)) {
      setCurrentLocale(savedLocale);
      return;
    }

    // 如果都没有，使用浏览器语言检测
    const detectedLocale = detectBrowserLanguage();
    setCurrentLocale(detectedLocale);
    localStorage.setItem('locale', detectedLocale);
  }, [pathname]);

  // 添加点击外部区域关闭下拉菜单的功能
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // 切换语言的函数
  const handleLocaleChange = (locale: Locale) => {
    if (!currentLocale) return;
    

    
    // 保存到 localStorage
    localStorage.setItem('locale', locale);
    
    // 设置当前状态
    setCurrentLocale(locale);
    
    // 关闭下拉菜单
    setOpen(false);
    
    // 导航到带有新语言的相同页面
    startTransition(() => {
      // 解析当前路径，替换语言部分
      let newPath = pathname;
      const segments = pathname.split('/');
      

      
      if (segments.length > 1 && locales.includes(segments[1] as Locale)) {
        // 替换现有的语言前缀
        segments[1] = locale;
        newPath = segments.join('/');
      } else {
        // 如果路径没有语言前缀，添加一个
        newPath = `/${locale}${pathname}`;
      }
      
      console.log('🚀 导航到:', newPath);
      
      // 使用 push 而不是 replace，这样用户可以使用后退按钮
      router.push(newPath);
    });
  };

  // 如果当前语言还未确定，显示加载状态
  if (!currentLocale) return null;

  return (
    <div className="relative inline-block text-left" ref={menuRef}>
      <div>
        <button
          type="button"
          className="inline-flex justify-center w-full rounded-md px-4 py-2 bg-gray-800 text-sm font-medium text-white hover:bg-gray-700 focus:outline-none"
          onClick={() => setOpen(!open)}
        >
          {getLanguageName(currentLocale)}
          <svg
            className="-mr-1 ml-2 h-5 w-5"
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 20 20"
            fill="currentColor"
            aria-hidden="true"
          >
            <path
              fillRule="evenodd"
              d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
              clipRule="evenodd"
            />
          </svg>
        </button>
      </div>

      {open && (
        <div className="origin-top-right absolute right-0 mt-2 w-40 rounded-md shadow-lg bg-gray-800 ring-1 ring-black ring-opacity-5 z-50">
          <div className="py-1" role="menu" aria-orientation="vertical">
            {locales.map((locale) => (
              <button
                key={locale}
                onClick={() => handleLocaleChange(locale)}
                className={`${
                  currentLocale === locale ? 'bg-gray-700' : ''
                } block w-full text-left px-4 py-2 text-sm text-white hover:bg-gray-700`}
                role="menuitem"
              >
                {getLanguageName(locale)}
              </button>
            ))}
          </div>
        </div>
      )}
    </div>
  );
} 