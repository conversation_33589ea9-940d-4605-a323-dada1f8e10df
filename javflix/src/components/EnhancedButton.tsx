'use client';

import React, { forwardRef, useRef, useImperativeHandle } from 'react';
import { useMicroInteractions, useAnimationPerformance } from '@/hooks/useEnhancedAnimations';

interface EnhancedButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'ghost' | 'danger';
  size?: 'sm' | 'md' | 'lg';
  loading?: boolean;
  icon?: React.ReactNode;
  iconPosition?: 'left' | 'right';
  ripple?: boolean;
  magnetic?: boolean;
  glow?: boolean;
  children: React.ReactNode;
}

const EnhancedButton = forwardRef<HTMLButtonElement, EnhancedButtonProps>(({
  variant = 'primary',
  size = 'md',
  loading = false,
  icon,
  iconPosition = 'left',
  ripple = true,
  magnetic = false,
  glow = false,
  className = '',
  children,
  onClick,
  disabled,
  ...props
}, ref) => {
  const internalRef = useRef<HTMLButtonElement>(null);
  const { elementRef: microRef, createRippleEffect, magneticEffect } = useMicroInteractions();
  const { prefersReducedMotion } = useAnimationPerformance();

  // 合并refs
  useImperativeHandle(ref, () => internalRef.current!);

  // 设置微交互效果
  React.useEffect(() => {
    if (internalRef.current && microRef) {
      microRef.current = internalRef.current;
      
      if (!prefersReducedMotion() && magnetic && !disabled) {
        const cleanup = magneticEffect(0.2);
        return cleanup;
      }
    }
  }, [microRef, magneticEffect, prefersReducedMotion, magnetic, disabled]);

  // 变体样式
  const variantStyles = {
    primary: `
      bg-gradient-to-r from-red-600 to-red-700 
      hover:from-red-500 hover:to-red-600 
      text-white border-transparent
      ${glow ? 'glow-subtle' : ''}
    `,
    secondary: `
      bg-gradient-to-r from-gray-700 to-gray-800 
      hover:from-gray-600 hover:to-gray-700 
      text-white border-transparent
      ${glow ? 'glow-blue' : ''}
    `,
    ghost: `
      bg-transparent hover:bg-white/10 
      text-white border-white/20 hover:border-white/40
    `,
    danger: `
      bg-gradient-to-r from-red-700 to-red-800 
      hover:from-red-600 hover:to-red-700 
      text-white border-transparent
      ${glow ? 'shadow-glow-red' : ''}
    `
  };

  // 尺寸样式
  const sizeStyles = {
    sm: 'px-3 py-1.5 text-sm',
    md: 'px-4 py-2 text-base',
    lg: 'px-6 py-3 text-lg'
  };

  // 处理点击事件
  const handleClick = (e: React.MouseEvent<HTMLButtonElement>) => {
    if (disabled || loading) return;

    // 添加涟漪效果
    if (ripple && !prefersReducedMotion()) {
      createRippleEffect(e);
    }

    // 调用原始点击处理器
    onClick?.(e);
  };

  // 组合所有样式
  const buttonClasses = `
    relative inline-flex items-center justify-center
    font-medium rounded-lg border
    transition-all duration-300 ease-out
    focus:outline-none focus:ring-2 focus:ring-red-500/50 focus:ring-offset-2 focus:ring-offset-gray-900
    disabled:opacity-50 disabled:cursor-not-allowed
    transform-gpu will-change-transform
    ${!prefersReducedMotion() ? 'btn-micro hover-lift' : ''}
    ${variantStyles[variant]}
    ${sizeStyles[size]}
    ${className}
  `.trim().replace(/\s+/g, ' ');

  return (
    <button
      ref={internalRef}
      className={buttonClasses}
      onClick={handleClick}
      disabled={disabled || loading}
      {...props}
    >
      {/* 加载状态 */}
      {loading && (
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="animate-spin rounded-full h-4 w-4 border-2 border-current border-t-transparent"></div>
        </div>
      )}

      {/* 内容 */}
      <div className={`flex items-center gap-2 ${loading ? 'opacity-0' : 'opacity-100'}`}>
        {icon && iconPosition === 'left' && (
          <span className="flex-shrink-0">{icon}</span>
        )}
        
        <span>{children}</span>
        
        {icon && iconPosition === 'right' && (
          <span className="flex-shrink-0">{icon}</span>
        )}
      </div>

      {/* 发光效果背景 */}
      {glow && !prefersReducedMotion() && (
        <div className="absolute inset-0 rounded-lg bg-gradient-to-r from-transparent via-white/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
      )}
    </button>
  );
});

EnhancedButton.displayName = 'EnhancedButton';

// 预设按钮组件
export const PrimaryButton = forwardRef<HTMLButtonElement, Omit<EnhancedButtonProps, 'variant'>>((props, ref) => (
  <EnhancedButton ref={ref} variant="primary" glow {...props} />
));

export const SecondaryButton = forwardRef<HTMLButtonElement, Omit<EnhancedButtonProps, 'variant'>>((props, ref) => (
  <EnhancedButton ref={ref} variant="secondary" {...props} />
));

export const GhostButton = forwardRef<HTMLButtonElement, Omit<EnhancedButtonProps, 'variant'>>((props, ref) => (
  <EnhancedButton ref={ref} variant="ghost" {...props} />
));

export const DangerButton = forwardRef<HTMLButtonElement, Omit<EnhancedButtonProps, 'variant'>>((props, ref) => (
  <EnhancedButton ref={ref} variant="danger" glow {...props} />
));

// 特殊效果按钮
export const MagneticButton = forwardRef<HTMLButtonElement, Omit<EnhancedButtonProps, 'magnetic'>>((props, ref) => (
  <EnhancedButton ref={ref} magnetic {...props} />
));

export const GlowButton = forwardRef<HTMLButtonElement, Omit<EnhancedButtonProps, 'glow'>>((props, ref) => (
  <EnhancedButton ref={ref} glow {...props} />
));

// 图标按钮
interface IconButtonProps extends Omit<EnhancedButtonProps, 'children'> {
  icon: React.ReactNode;
  'aria-label': string;
}

export const IconButton = forwardRef<HTMLButtonElement, IconButtonProps>(({
  icon,
  size = 'md',
  variant = 'ghost',
  className = '',
  ...props
}, ref) => {
  const iconSizes = {
    sm: 'w-8 h-8',
    md: 'w-10 h-10',
    lg: 'w-12 h-12'
  };

  return (
    <EnhancedButton
      ref={ref}
      variant={variant}
      size={size}
      className={`${iconSizes[size]} !p-0 rounded-full ${className}`}
      {...props}
    >
      {icon}
    </EnhancedButton>
  );
});

IconButton.displayName = 'IconButton';

// 浮动操作按钮
export const FloatingActionButton = forwardRef<HTMLButtonElement, Omit<EnhancedButtonProps, 'size' | 'variant'>>((props, ref) => (
  <EnhancedButton
    ref={ref}
    variant="primary"
    size="lg"
    className="!rounded-full !w-14 !h-14 !p-0 shadow-strong hover:shadow-glow-red fixed bottom-6 right-6 z-40"
    magnetic
    glow
    {...props}
  />
));

FloatingActionButton.displayName = 'FloatingActionButton';

export default EnhancedButton;
