'use client';

import Link from 'next/link';
import { FiChevronRight } from 'react-icons/fi';
import { ReactNode } from 'react';

interface SectionHeaderProps {
  title: string;
  icon?: ReactNode;
  viewMoreHref?: string;
  viewMoreText?: string;
  badge?: ReactNode;
  className?: string;
}

const SectionHeader = ({ 
  title, 
  icon, 
  viewMoreHref, 
  viewMoreText = '查看更多',
  badge,
  className = ''
}: SectionHeaderProps) => {
  return (
    <div className={`mb-6 flex items-center justify-between ${className}`}>
      <h2 className="text-2xl font-bold text-white flex items-center">
        <div className="w-1 h-6 bg-red-600 rounded-full mr-3"></div>
        {icon && <span className="mr-2 text-red-600">{icon}</span>}
        {title}
        {badge && <span className="ml-2">{badge}</span>}
      </h2>
      
      {viewMoreHref && (
        <Link 
          href={viewMoreHref}
          className="group flex items-center text-sm text-gray-400 hover:text-red-500 transition-colors duration-200"
        >
          {viewMoreText}
          <FiChevronRight className="ml-1 transform group-hover:translate-x-1 transition-transform duration-200" size={14} />
        </Link>
      )}
    </div>
  );
};

export default SectionHeader;