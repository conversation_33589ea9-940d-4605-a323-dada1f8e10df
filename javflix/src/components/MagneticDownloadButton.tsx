'use client';

import React, { useState, useCallback, useEffect, useMemo, useRef } from 'react';
import { 
  FiDownload, 
  FiLink, 
  FiLoader, 
  FiAlertCircle, 
  FiEye,
  FiCopy,
  FiCheck,
  FiX,
  FiChevronDown,
  FiStar,
  FiHardDrive,
  FiClock,
  FiShield,
  FiGlobe,
  FiExternalLink,
  FiVideo,
  FiActivity,
  FiFilter,
  FiList,
  FiClipboard,
  FiSettings,
  FiHash,
  FiTrendingUp,
  FiHeart,
  FiBarChart
} from 'react-icons/fi';
import { useClientTranslations } from './TranslationsProvider';

// 创建磁力链接缓存，用于存储已获取的数据
const magnetsCache = new Map<string, MagnetLink[]>();

interface TrendData {
  days7: number[];
  days30: number[];
}

interface HealthMetrics {
  score: number; // 0-100
  successRate: number; // 0-100
  avgSpeed: number; // KB/s
  status: 'excellent' | 'good' | 'fair' | 'poor';
}

interface MagnetLink {
  id: string;
  title: string;
  size: string;
  quality: string;
  format: string;
  hasSubtitle: boolean;
  shareDate: string;
  isHd: boolean;
  link: string;
  seeders?: number;
  leechers?: number;
  verified?: boolean;
  score?: number;
  isBestChoice?: boolean;
  codec?: string;
  audioInfo?: string;
  resolution?: string;
  trendData?: TrendData;
  healthMetrics?: HealthMetrics;
  totalDownloads?: number;
  isRealData?: boolean;
}

interface MagneticDownloadButtonProps {
  movieCode: string;
  className?: string;
  variant?: 'primary' | 'secondary' | 'compact';
  showPreview?: boolean;
}

// 定义筛选器类型
interface MagnetFilter {
  quality: string[];
  hasSubtitle: boolean | null;
  minSeeders: number | null;
  codec: string[];
  sort: 'score' | 'size' | 'seeders' | 'date';
}

const MagneticDownloadButton: React.FC<MagneticDownloadButtonProps> = ({
  movieCode,
  className = '',
  variant = 'primary',
  showPreview = true
}) => {
  const { t } = useClientTranslations();
  const [isOpen, setIsOpen] = useState(false);
  const [magnetLinks, setMagnetLinks] = useState<MagnetLink[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [copiedId, setCopiedId] = useState<string | null>(null);
  const [previewCount, setPreviewCount] = useState(3);
  const [selectedMagnets, setSelectedMagnets] = useState<string[]>([]);
  const [filter, setFilter] = useState<MagnetFilter>({
    quality: [],
    hasSubtitle: null,
    minSeeders: null,
    codec: [],
    sort: 'score'
  });
  const [showFilter, setShowFilter] = useState(false);
  const [showExportOptions, setShowExportOptions] = useState(false);
  const [openDropdownId, setOpenDropdownId] = useState<string | null>(null);

  // Fetch magnet links from database API
  const fetchMagnetLinks = useCallback(async () => {
    if (!movieCode) {
      console.warn('Movie code is required to fetch magnet links');
      return [];
    }
    
    // 先检查缓存中是否有数据
    const cacheKey = `magnet_${movieCode}`;
    if (magnetsCache.has(cacheKey)) {
      const cachedData = magnetsCache.get(cacheKey) || [];
      console.log(`Using cached data for ${movieCode}, ${cachedData.length} items`);
      setMagnetLinks(cachedData);
      return cachedData;
    }
    
    try {
      setLoading(true);
      setError(null);
      console.log(`Fetching magnet links for ${movieCode}...`);
      
      // 尝试不同的API端点获取真实数据
      const apiEndpoints = [
        `/api/db/movies/${encodeURIComponent(movieCode)}/magnets`, // 首先尝试直接数据库API
        `/api/magnets?code=${encodeURIComponent(movieCode)}`,
        `/api/v1/magnets/${encodeURIComponent(movieCode)}`,
        `/api/magnet-search?q=${encodeURIComponent(movieCode)}`
      ];
      
      let success = false;
      let response: Response | undefined = undefined;
      let data: any = null;
      
      // 依次尝试每个API端点
      for (const endpoint of apiEndpoints) {
        try {
          console.log(`Trying to fetch real magnet data from: ${endpoint}`);
          const endpointResponse = await fetch(endpoint, {
            method: 'GET',
            headers: {
              'Content-Type': 'application/json'
            },
            // 确保不使用缓存
            cache: 'no-store'
          });
          
          console.log(`Response from ${endpoint}:`, endpointResponse.status, endpointResponse.statusText);
          
          if (endpointResponse.ok) {
            try {
              const responseData = await endpointResponse.json();
              console.log(`Data from ${endpoint}:`, responseData);
              
              // 检查数据是否有效
              if (responseData && (
                responseData.success || 
                (responseData.items && Array.isArray(responseData.items)) || 
                (responseData.data && Array.isArray(responseData.data))
              )) {
                success = true;
                response = endpointResponse;
                data = responseData;
                console.log('Successfully fetched real magnet data');
                break; // 找到有效数据，跳出循环
              }
            } catch (parseError) {
              console.warn(`Failed to parse JSON from ${endpoint}:`, parseError);
            }
          }
        } catch (requestError) {
          console.warn(`Failed to fetch from ${endpoint}:`, requestError);
        }
      }
      
      // 如果API请求失败，返回空数据
      if (!success || !data) {
        console.warn('All API requests failed, returning empty data');
        setError(t('video.dataLoadError'));
        setMagnetLinks([]);
        setLoading(false);
        return [];
      }
      
      // 处理不同格式的API响应
      let items: any[] = [];
      if (data.success && data.data && Array.isArray(data.data.items)) {
        items = data.data.items;
      } else if (data.items && Array.isArray(data.items)) {
        items = data.items;
      } else if (data.results && Array.isArray(data.results)) {
        items = data.results;
      } else if (data.torrents && Array.isArray(data.torrents)) {
        items = data.torrents;
      } else if (data.data && Array.isArray(data.data)) {
        items = data.data;
      }
      
      if (items.length > 0) {
        // 标记这是真实数据
        console.log(`Processing ${items.length} real magnet links`);
        
        const formattedMagnets: MagnetLink[] = items.map((magnet: any) => {
          return {
            id: magnet.magnet_id || magnet.id || `magnet-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
            title: magnet.title || magnet.name || `${movieCode} - ${magnet.quality || 'Unknown'}`,
            size: magnet.size || magnet.filesize || 'Unknown',
            quality: magnet.quality || determineQuality(magnet.title || magnet.name || '') || (magnet.is_hd ? 'HD' : 'SD'),
            format: magnet.format || determineFormat(magnet.title || magnet.name || '') || 'MP4',
            hasSubtitle: magnet.has_subtitle || magnet.subtitle || false,
            shareDate: magnet.share_date || magnet.created_at || magnet.date || new Date().toISOString(),
            isHd: magnet.is_hd || (magnet.quality && ['HD', '720p', '1080p', '4K', '2160p'].includes(magnet.quality)),
            link: magnet.link || magnet.magnet || magnet.download || '',
            // 使用API返回的真实做种数和下载数，不添加默认值
            seeders: magnet.seeders || magnet.seeds || 0,
            leechers: magnet.leechers || magnet.peers || 0,
            verified: magnet.verified !== false
          };
        });
        
        // 检查关键字段
        const validMagnets = formattedMagnets.filter(m => m.link && m.title);
        
        if (validMagnets.length === 0) {
          console.warn('No valid magnet links found in API response');
          setError('未找到有效的磁力链接');
          setMagnetLinks([]);
          setLoading(false);
          return [];
        }
        
        // 处理技术信息
        const processedMagnets = validMagnets.map(magnet => {
          const withTechInfo = extractTechInfo(magnet);
          
          // 计算健康度指标
          const healthMetrics = generateHealthMetricsFromSeeders(withTechInfo.seeders || 0, withTechInfo.leechers || 0);
          
          // 简单趋势数据 - 仅包含基本数据点，不生成随机数据
          const trendData: TrendData = {
            days7: [0, 0, 0, 0, 0, 0, 0], // 默认为0
            days30: Array(30).fill(0)     // 默认为0
          };
          
          // 仅当API提供了趋势数据时才使用
          if (magnet.trendData) {
            trendData.days7 = magnet.trendData.days7;
            trendData.days30 = magnet.trendData.days30;
          }
          
          return {
            ...withTechInfo,
            trendData,
            healthMetrics,
            totalDownloads: magnet.totalDownloads || (withTechInfo.seeders || 0) * 10, // 估计值，仅当API没有提供时
            isRealData: true
          };
        });

        // 计算每个磁力链接的评分
        const scoredMagnets = processedMagnets.map(magnet => ({
          ...magnet,
          score: calculateMagnetScore(magnet)
        }));
        
        // 按评分排序，并标记最佳选择
        const sortedMagnets = [...scoredMagnets].sort((a, b) => {
          return (b.score || 0) - (a.score || 0);
        });
        
        if (sortedMagnets.length > 0) {
          sortedMagnets[0].isBestChoice = true;
        }
        
        // 将处理好的数据存入缓存
        magnetsCache.set(cacheKey, sortedMagnets);
        
        setMagnetLinks(sortedMagnets);
        return sortedMagnets;
      } else {
        // 如果API返回的数据无效，返回空数据
        console.warn('API response format invalid or empty');
        setError('API返回数据无效');
        setMagnetLinks([]);
        setLoading(false);
        return [];
      }
    } catch (err) {
      console.error('Error fetching magnet links:', err);
      setError('获取资源链接失败');
      setMagnetLinks([]);
      setLoading(false);
      return [];
    }
  }, [movieCode]);

  // 从标题中确定质量
  const determineQuality = (title: string): string => {
    if (!title) return 'SD';
    
    const title_lower = title.toLowerCase();
    if (title_lower.includes('2160p') || title_lower.includes('4k') || title_lower.includes('uhd')) {
      return '4K';
    } else if (title_lower.includes('1080p') || title_lower.includes('fhd')) {
      return '1080p';
    } else if (title_lower.includes('720p') || title_lower.includes('hd')) {
      return '720p';
    }
    return 'SD';
  };
  
  // 从标题中确定格式
  const determineFormat = (title: string): string => {
    if (!title) return 'MP4';
    
    const title_lower = title.toLowerCase();
    if (title_lower.includes('.mkv')) {
      return 'MKV';
    } else if (title_lower.includes('.avi')) {
      return 'AVI';
    } else if (title_lower.includes('.mp4')) {
      return 'MP4';
    } else if (title_lower.includes('.wmv')) {
      return 'WMV';
    }
    return 'MP4';
  };
  
  // 根据做种数生成健康度指标
  const generateHealthMetricsFromSeeders = (seeders: number, leechers: number): HealthMetrics => {
    // 计算做种比率
    const seedRatio = leechers > 0 ? seeders / leechers : seeders;
    
    // 基础健康分数
    let baseScore: number;
    if (seeders === 0) {
      baseScore = 0; // 没有做种者
    } else if (seeders < 5) {
      baseScore = 40 + seeders * 5; // 少量做种者
    } else if (seeders < 20) {
      baseScore = 60 + (seeders - 5) * 1.5; // 中等做种者
    } else if (seeders < 100) {
      baseScore = 80 + (seeders - 20) * 0.2; // 较多做种者
    } else {
      baseScore = 95; // 大量做种者
    }
    
    // 调整分数，考虑做种比
    let adjustedScore = baseScore;
    if (leechers > 0) {
      if (seedRatio < 0.5) {
        adjustedScore = Math.max(30, baseScore - 20); // 下载者远多于做种者，健康度较低
      } else if (seedRatio < 1) {
        adjustedScore = Math.max(50, baseScore - 10); // 下载者略多于做种者
      } else if (seedRatio > 5) {
        adjustedScore = Math.min(100, baseScore + 10); // 做种者远多于下载者，健康度高
      }
    }
    
    // 最终分数限制在0-100之间
    const finalScore = Math.min(100, Math.max(0, Math.round(adjustedScore)));
    
    // 成功率基于健康分数
    const successRate = Math.min(99, Math.max(50, finalScore));
    
    // 平均速度，基于健康分数和做种数
    // 假设健康分数高且做种者多的资源下载速度更快
    const speedBase = finalScore / 100 * Math.min(seeders, 100);
    const avgSpeed = Math.round(50 + speedBase * 10);
    
    // 确定状态
    let status: 'excellent' | 'good' | 'fair' | 'poor';
    if (finalScore >= 85) {
      status = 'excellent';
    } else if (finalScore >= 70) {
      status = 'good';
    } else if (finalScore >= 50) {
      status = 'fair';
    } else {
      status = 'poor';
    }
    
    return {
      score: finalScore,
      successRate,
      avgSpeed,
      status
    };
  };
  
  // 计算磁力链接评分
  const calculateMagnetScore = (magnet: MagnetLink): number => {
    let score = 0;
    
    // 质量评分 (最高40分)
    const qualityScore: Record<string, number> = {
      '4K': 40, 
      '2160p': 40,
      '1080p': 30, 
      'FHD': 30,
      '720p': 20, 
      'HD': 20,
      'SD': 10
    };
    
    score += qualityScore[magnet.quality] || 10;
    
    // 字幕加分 (10分)
    if (magnet.hasSubtitle) score += 10;
    
    // 做种者评分 (最高25分)
    if (magnet.seeders) {
      if (magnet.seeders > 100) score += 25;
      else if (magnet.seeders > 50) score += 20;
      else if (magnet.seeders > 20) score += 15;
      else if (magnet.seeders > 5) score += 10;
      else score += 5;
    }
    
    // 文件大小评分 (最高15分)
    // 解析文件大小
    try {
      const sizeMatch = magnet.size.match(/(\d+\.?\d*)\s*(GB|MB|KB|GiB|MiB|KiB)?/i);
      if (sizeMatch) {
        const value = parseFloat(sizeMatch[1]);
        const unit = (sizeMatch[2] || '').toUpperCase();
        
        if (unit.includes('G')) {
          if (value > 10) score += 5; // 过大可能有冗余
          else if (value > 5) score += 15; // 最佳范围
          else if (value > 2) score += 10;
          else score += 5;
        } else if (unit.includes('M')) {
          if (value > 800) score += 5;
          else score += 2;
        }
      }
    } catch (e) {
      // 解析失败时不加分
    }
    
    // 已验证加分 (10分)
    if (magnet.verified) score += 10;
    
    // 健康度评分 (最高15分)
    if (magnet.healthMetrics) {
      if (magnet.healthMetrics.status === 'excellent') score += 15;
      else if (magnet.healthMetrics.status === 'good') score += 10;
      else if (magnet.healthMetrics.status === 'fair') score += 5;
    }
    
    return score;
  };
  
  // 获取健康度颜色
  const getHealthColor = (status: string): string => {
    switch (status) {
      case 'excellent':
        return 'text-green-400';
      case 'good':
        return 'text-blue-400';
      case 'fair':
        return 'text-yellow-400';
      case 'poor':
        return 'text-red-400';
      default:
        return 'text-gray-400';
    }
  };

  // 格式化下载速度
  const formatSpeed = (speed: number): string => {
    if (speed >= 1024) {
      return `${(speed / 1024).toFixed(1)} MB/s`;
    }
    return `${speed} KB/s`;
  };

  // 健康度状态文字
  const getHealthStatusText = (status: string): string => {
    switch (status) {
      case 'excellent':
        return '极佳';
      case 'good':
        return '良好';
      case 'fair':
        return '一般';
      case 'poor':
        return '较差';
      default:
        return '未知';
    }
  };
  
  // 从标题中提取技术信息
  const extractTechInfo = (magnet: MagnetLink): MagnetLink => {
    const title = magnet.title || '';
    const updatedMagnet = { ...magnet };
    
    // 提取编码格式
    const codecMatch = title.match(/\b(x264|x265|HEVC|AVC|H\.?264|H\.?265)\b/i);
    if (codecMatch) {
      updatedMagnet.codec = codecMatch[0].toUpperCase();
    }
    
    // 提取音频信息
    const audioMatch = title.match(/\b(AAC|DTS|AC3|FLAC|MP3|TrueHD|Atmos)\b/i);
    if (audioMatch) {
      updatedMagnet.audioInfo = audioMatch[0].toUpperCase();
    }
    
    // 提取分辨率
    const resolutionMatch = title.match(/\b(\d{3,4}x\d{3,4})\b|\b(\d{3,4}[pP])\b/);
    if (resolutionMatch) {
      updatedMagnet.resolution = (resolutionMatch[1] || resolutionMatch[2]).toUpperCase();
    } else if (magnet.quality === '1080p' || magnet.quality === 'FHD') {
      updatedMagnet.resolution = '1920x1080';
    } else if (magnet.quality === '720p' || magnet.quality === 'HD') {
      updatedMagnet.resolution = '1280x720';
    } else if (magnet.quality === '4K' || magnet.quality === '2160p') {
      updatedMagnet.resolution = '3840x2160';
    }
    
    return updatedMagnet;
  };

  // Handle button click
  const handleClick = useCallback(async () => {
    if (!isOpen) {
      setIsOpen(true);
      await fetchMagnetLinks();
    } else {
      setIsOpen(false);
    }
  }, [isOpen, fetchMagnetLinks]);

  // 获取可用的筛选选项
  const getFilterOptions = useCallback(() => {
    if (!magnetLinks.length) return {
      qualities: [],
      codecs: []
    };
    
    const qualities = Array.from(new Set(magnetLinks.map(m => m.quality))).filter(Boolean);
    const codecs = Array.from(new Set(magnetLinks.map(m => m.codec))).filter(Boolean) as string[];
    
    return {
      qualities,
      codecs
    };
  }, [magnetLinks]);

  // Copy magnet link to clipboard
  const copyMagnetLink = useCallback(async (magnetLink: MagnetLink) => {
    try {
      await navigator.clipboard.writeText(magnetLink.link);
      setCopiedId(magnetLink.id);
      setTimeout(() => setCopiedId(null), 2000);
    } catch (err) {
      console.error('Failed to copy magnet link:', err);
    }
  }, []);
  
  // 复制多个磁力链接
  const copySelectedMagnets = useCallback(async () => {
    if (selectedMagnets.length === 0) return;
    
    try {
      // 找到选中的磁力链接
      const linksToExport = magnetLinks
        .filter(m => selectedMagnets.includes(m.id))
        .map(m => m.link)
        .join('\n\n');
      
      await navigator.clipboard.writeText(linksToExport);
      alert(`已复制 ${selectedMagnets.length} 个磁力链接`);
      // 复制后清空选择
      setSelectedMagnets([]);
    } catch (err) {
      console.error('Failed to copy selected magnet links:', err);
    }
  }, [selectedMagnets, magnetLinks]);
  
  // 复制哈希值
  const copyHash = useCallback(async (magnetLink: MagnetLink) => {
    try {
      const hash = magnetLink.link.match(/btih:([^&]+)/i);
      if (hash && hash[1]) {
        await navigator.clipboard.writeText(hash[1]);
        setCopiedId(`hash-${magnetLink.id}`);
        setTimeout(() => setCopiedId(null), 2000);
      }
    } catch (err) {
      console.error('Failed to copy hash:', err);
    }
  }, []);
  
  // 发送到下载客户端
  const sendToClient = useCallback((magnetLink: MagnetLink, client: 'qbittorrent' | 'transmission' | 'utorrent') => {
    const encodedLink = encodeURIComponent(magnetLink.link);
    let url = '';
    
    switch (client) {
      case 'qbittorrent':
        url = `http://localhost:8080/api/v2/torrents/add?urls=${encodedLink}`;
        break;
      case 'transmission':
        url = `transmission://open?uri=${encodedLink}`;
        break;
      case 'utorrent':
        url = `magnet:?xt=urn:btih:${encodedLink}`;
        break;
    }
    
    // 尝试打开URL或显示提示
    try {
      window.open(url, '_blank');
    } catch (err) {
      alert(`请确保您的 ${client} 已启动并配置允许远程连接`);
    }
  }, []);
  
  // 切换选择磁力链接
  const toggleSelectMagnet = useCallback((magnetId: string) => {
    setSelectedMagnets(prev => {
      if (prev.includes(magnetId)) {
        return prev.filter(id => id !== magnetId);
      } else {
        return [...prev, magnetId];
      }
    });
  }, []);

  // 切换下拉菜单的显示/隐藏
  const toggleDropdown = useCallback((magnetId: string) => {
    setOpenDropdownId(prev => prev === magnetId ? null : magnetId);
  }, []);

  // 关闭所有下拉菜单
  const closeAllDropdowns = useCallback(() => {
    setOpenDropdownId(null);
  }, []);

  // 筛选磁力链接
  const filteredMagnets = useMemo(() => {
    if (!magnetLinks.length) return [];
    
    return magnetLinks.filter(magnet => {
      // 质量筛选
      if (filter.quality.length > 0 && !filter.quality.includes(magnet.quality)) {
        return false;
      }
      
      // 字幕筛选
      if (filter.hasSubtitle !== null && magnet.hasSubtitle !== filter.hasSubtitle) {
        return false;
      }
      
      // 做种数筛选
      if (filter.minSeeders !== null && (magnet.seeders || 0) < filter.minSeeders) {
        return false;
      }
      
      // 编码筛选
      if (filter.codec.length > 0 && (!magnet.codec || !filter.codec.includes(magnet.codec))) {
        return false;
      }
      
      return true;
    }).sort((a, b) => {
      // 根据选择的排序方式排序
      switch (filter.sort) {
        case 'score':
          return (b.score || 0) - (a.score || 0);
        case 'size': {
          // 解析并比较大小
          const aSize = parseSizeToMB(a.size);
          const bSize = parseSizeToMB(b.size);
          return bSize - aSize;
        }
        case 'seeders':
          return (b.seeders || 0) - (a.seeders || 0);
        case 'date': {
          const aDate = new Date(a.shareDate || '').getTime();
          const bDate = new Date(b.shareDate || '').getTime();
          return bDate - aDate;
        }
        default:
          return (b.score || 0) - (a.score || 0);
      }
    });
  }, [magnetLinks, filter]);
  
  // 将大小解析为MB用于比较
  const parseSizeToMB = (sizeStr: string): number => {
    try {
      const sizeMatch = sizeStr.match(/(\d+\.?\d*)\s*(GB|MB|KB|GiB|MiB|KiB)?/i);
      if (!sizeMatch) return 0;
      
      const value = parseFloat(sizeMatch[1]);
      const unit = (sizeMatch[2] || '').toUpperCase();
      
      if (unit.includes('G')) {
        return value * 1024;
      } else if (unit.includes('K')) {
        return value / 1024;
      }
      return value;
    } catch (e) {
      return 0;
    }
  };

  // Format file size - 中文化文件大小
  const formatSize = (size: string): string => {
    if (!size || size === 'Unknown') return '未知大小';
    
    // Handle various size formats
    const sizeMatch = size.match(/(\d+\.?\d*)\s*(GB|MB|KB|GiB|MiB|KiB)?/i);
    if (sizeMatch) {
      const value = parseFloat(sizeMatch[1]);
      const unit = sizeMatch[2]?.toUpperCase() || 'MB';
      return `${value.toFixed(1)} ${unit}`;
    }
    
    return size;
  };

  // Format date - 中文化相对时间
  const formatDate = (dateStr: string): string => {
    if (!dateStr) return '未知时间';
    
    try {
      const date = new Date(dateStr);
      const now = new Date();
      const diffMs = now.getTime() - date.getTime();
      const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
      
      if (diffDays === 0) return '今天';
      if (diffDays === 1) return '昨天';
      if (diffDays < 7) return `${diffDays}天前`;
      if (diffDays < 30) return `${Math.floor(diffDays / 7)}周前`;
      if (diffDays < 365) return `${Math.floor(diffDays / 30)}个月前`;
      return `${Math.floor(diffDays / 365)}年前`;
    } catch {
      return '未知时间';
    }
  };

  // Get quality badge color
  const getQualityColor = (quality: string): string => {
    switch (quality.toLowerCase()) {
      case '4k':
      case '2160p':
        return 'bg-red-900/40 text-red-400 border-red-400/30';
      case '1080p':
      case 'fhd':
        return 'bg-blue-900/40 text-blue-400 border-blue-400/30';
      case '720p':
      case 'hd':
        return 'bg-green-900/40 text-green-400 border-green-400/30';
      default:
        return 'bg-gray-900/40 text-gray-400 border-gray-400/30';
    }
  };

  // Render button variants
  const renderButton = () => {
    const baseClasses = 'group flex items-center gap-2 transition-all duration-300 font-medium relative overflow-hidden';
    
    if (variant === 'compact') {
      return (
        <button
          onClick={handleClick}
          className={`${baseClasses} ${className} px-3 py-2 rounded-lg
            bg-gradient-to-r from-purple-600/20 to-red-600/20 hover:from-purple-600/30 hover:to-red-600/30
            border border-purple-500/30 hover:border-purple-400/50
            text-purple-200 hover:text-white hover:scale-105
            btn-micro hover-lift font-medium shadow-lg hover:shadow-purple-500/20
            backdrop-blur-sm transform transition-all duration-300
            focus:outline-none focus:ring-2 focus:ring-purple-500/50 focus:ring-offset-2 focus:ring-offset-gray-900`}
          aria-label="Magnetic Download"
        >
          {/* 微光效果 */}
          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500 transform -skew-x-12 group-hover:translate-x-full"></div>

          <FiLink className="relative z-10 text-lg group-hover:scale-110 transition-transform duration-300" />
          <span className="relative z-10 hidden sm:inline">{t('video.magnetLink')}</span>
        </button>
      );
    }
    
    if (variant === 'secondary') {
      return (
        <button
          onClick={handleClick}
          className={`${baseClasses} ${className} px-4 py-2 rounded-lg
            bg-gray-800/80 hover:bg-purple-600/90 border border-gray-600/50 hover:border-purple-500/50
            text-gray-300 hover:text-white hover:scale-105
            btn-micro hover-lift font-medium shadow-lg hover:shadow-purple-500/20
            backdrop-blur-sm transform transition-all duration-300
            focus:outline-none focus:ring-2 focus:ring-purple-500/50 focus:ring-offset-2 focus:ring-offset-gray-900`}
        >
          {/* 涟漪效果背景 */}
          <div className="absolute inset-0 bg-gradient-to-r from-purple-500/0 via-purple-500/5 to-purple-500/0 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

          {/* 微光效果 */}
          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500 transform -skew-x-12 group-hover:translate-x-full"></div>

          <FiLink className="relative z-10 text-lg group-hover:animate-pulse group-hover:scale-110 transition-transform duration-300" />
          <span className="relative z-10">{t('video.magnetDownload')}</span>
          {magnetLinks.length > 0 && (
            <span className="relative z-10 bg-red-500 text-white text-xs px-1.5 py-0.5 rounded-full ml-1 animate-pulse">
              {magnetLinks.length}
            </span>
          )}
        </button>
      );
    }
    
    // Primary variant
    return (
      <button
        onClick={handleClick}
        className={`${baseClasses} ${className} px-6 py-3 rounded-xl
          bg-gradient-to-r from-purple-600 to-red-600 hover:from-purple-700 hover:to-red-700
          text-white shadow-lg hover:shadow-purple-500/25 hover:scale-105 transform
          btn-micro hover-lift font-medium backdrop-blur-sm
          focus:outline-none focus:ring-2 focus:ring-purple-500/50 focus:ring-offset-2 focus:ring-offset-gray-900`}
      >
        {/* 微光效果 */}
        <div className="absolute inset-0 bg-gradient-to-r from-white/0 via-white/10 to-white/0 transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-700"></div>

        {/* 额外的悬停效果 */}
        <div className="absolute inset-0 bg-gradient-to-r from-purple-500/0 via-purple-500/10 to-red-500/0 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

        <FiLink className="text-xl group-hover:animate-pulse group-hover:scale-110 relative z-10 transition-transform duration-300" />
        <span className="relative z-10">{t('video.magnetDownload')}</span>
        {magnetLinks.length > 0 && (
          <span className="bg-white/20 text-white text-sm px-2 py-1 rounded-full ml-2 relative z-10 animate-pulse">
            {magnetLinks.length}
          </span>
        )}
      </button>
    );
  };

  // 在组件内添加useEffect实现点击外部关闭功能
  const dropdownRef = useRef<HTMLDivElement>(null);
  
  // 点击外部关闭下拉菜单
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node) && openDropdownId) {
        setOpenDropdownId(null);
      }
    };
    
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [openDropdownId]);

  return (
    <>
      <style jsx global>{`
        @keyframes fadeIn {
          from { opacity: 0; transform: translateY(-10px); }
          to { opacity: 1; transform: translateY(0); }
        }
        .animate-fadeIn {
          animation: fadeIn 0.2s ease-in-out forwards;
        }
      `}</style>
    <div className="relative">
      {renderButton()}
      
      {/* Magnetic Download Modal */}
      {isOpen && (
        <div className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4">
          <div className="bg-gray-900 rounded-2xl border border-gray-700/50 shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
            {/* Header */}
            <div className="flex items-center justify-between p-6 border-b border-gray-700/50">
              <div className="flex items-center gap-3">
                <div className="flex items-center justify-center w-12 h-12 bg-gradient-to-r from-purple-600 to-red-600 rounded-xl">
                  <FiLink className="text-white text-xl" />
                </div>
                <div>
                  <h2 className="text-xl font-bold text-white">{t('video.magnetDownload')}</h2>
                  <p className="text-gray-400 text-sm">{movieCode}</p>
                </div>
              </div>
              
              <button
                onClick={() => setIsOpen(false)}
                className="p-2 hover:bg-gray-800 rounded-lg transition-colors text-gray-400 hover:text-white"
              >
                <FiX size={20} />
              </button>
            </div>
            
            {/* Content */}
            <div className="p-6 max-h-[calc(90vh-200px)] overflow-y-auto">
              {loading && magnetLinks.length === 0 && (
                <div className="flex items-center justify-center py-12">
                  <div className="flex items-center gap-3 text-gray-300">
                    <FiLoader className="animate-spin text-xl" />
                    <span>{t('common.loading')}</span>
                  </div>
                </div>
              )}
              
              {error && (
                  <div className="mt-4 p-3 rounded-lg bg-red-900/30 border border-red-500/50 text-center">
                    <p className="text-red-200 flex items-center justify-center">
                      <FiAlertCircle className="mr-2 text-red-400" size={18} />
                      {error}
                    </p>
                </div>
              )}
              
              {!loading && !error && magnetLinks.length === 0 && (
                <div className="text-center py-12 text-gray-400">
                  <FiLink className="mx-auto mb-4 text-4xl opacity-50" />
                  <p>{t('video.noRelatedVideos')}</p>
                </div>
              )}
              
              {magnetLinks.length > 0 && (
                <>
                    {/* 筛选器和工具栏 */}
                    <div className="mb-4 flex flex-wrap items-center justify-between gap-2">
                      <div className="flex items-center gap-2">
                        <button 
                          onClick={() => setShowFilter(!showFilter)}
                          className="px-3 py-2 bg-gray-800 hover:bg-gray-700 text-white rounded-lg text-sm font-medium transition-colors flex items-center gap-2"
                        >
                          <FiFilter />
                          {t('search.filters')}
                          {(filter.quality.length > 0 || filter.hasSubtitle !== null || filter.minSeeders !== null || filter.codec.length > 0) && (
                            <span className="bg-blue-500 text-white text-xs px-1.5 py-0.5 rounded-full">
                              {filter.quality.length + (filter.hasSubtitle !== null ? 1 : 0) + (filter.minSeeders !== null ? 1 : 0) + filter.codec.length}
                            </span>
                          )}
                        </button>
                        
                        {selectedMagnets.length > 0 && (
                          <button
                            onClick={copySelectedMagnets}
                            className="px-3 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg text-sm font-medium transition-colors flex items-center gap-2"
                          >
                            <FiClipboard />
                            {t('video.copyMagnet')} ({selectedMagnets.length})
                          </button>
                        )}
                      </div>
                      
                      <div className="flex items-center gap-2">
                        <button
                          onClick={() => setShowExportOptions(!showExportOptions)}
                          className="px-3 py-2 bg-gray-800 hover:bg-gray-700 text-white rounded-lg text-sm font-medium transition-colors flex items-center gap-2"
                        >
                          <FiSettings />
                          {t('common.settings')}
                        </button>
                      </div>
                    </div>
                    
                    {/* 筛选器面板 */}
                    {showFilter && (
                      <div className="mb-6 p-4 bg-gray-800/50 border border-gray-700/50 rounded-lg">
                        <h4 className="text-lg font-medium text-white mb-3 flex items-center gap-2">
                          <FiFilter className="text-blue-400" />
                          筛选选项
                        </h4>
                        
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          {/* 质量筛选 */}
                          <div>
                            <label className="block text-sm font-medium text-gray-300 mb-2">视频质量</label>
                            <div className="flex flex-wrap gap-2">
                              {getFilterOptions().qualities.map(quality => (
                                <button
                                  key={quality}
                                  onClick={() => setFilter(prev => ({
                                    ...prev,
                                    quality: prev.quality.includes(quality)
                                      ? prev.quality.filter(q => q !== quality)
                                      : [...prev.quality, quality]
                                  }))}
                                  className={`px-3 py-1.5 rounded-md text-xs font-medium transition-colors ${
                                    filter.quality.includes(quality)
                                      ? 'bg-blue-600 text-white'
                                      : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                                  }`}
                                >
                                  {quality}
                                </button>
                              ))}
                            </div>
                          </div>
                          
                          {/* 编码筛选 */}
                          <div>
                            <label className="block text-sm font-medium text-gray-300 mb-2">编码格式</label>
                            <div className="flex flex-wrap gap-2">
                              {getFilterOptions().codecs.map(codec => (
                                <button
                                  key={codec}
                                  onClick={() => setFilter(prev => ({
                                    ...prev,
                                    codec: prev.codec.includes(codec)
                                      ? prev.codec.filter(c => c !== codec)
                                      : [...prev.codec, codec]
                                  }))}
                                  className={`px-3 py-1.5 rounded-md text-xs font-medium transition-colors ${
                                    filter.codec.includes(codec)
                                      ? 'bg-green-600 text-white'
                                      : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                                  }`}
                                >
                                  {codec}
                                </button>
                              ))}
                            </div>
                          </div>
                          
                          {/* 字幕筛选 */}
                          <div>
                            <label className="block text-sm font-medium text-gray-300 mb-2">字幕选项</label>
                            <div className="flex gap-2">
                              <button
                                onClick={() => setFilter(prev => ({
                                  ...prev,
                                  hasSubtitle: prev.hasSubtitle === true ? null : true
                                }))}
                                className={`px-3 py-1.5 rounded-md text-xs font-medium transition-colors ${
                                  filter.hasSubtitle === true
                                    ? 'bg-green-600 text-white'
                                    : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                                }`}
                              >
                                有字幕
                              </button>
                              <button
                                onClick={() => setFilter(prev => ({
                                  ...prev,
                                  hasSubtitle: prev.hasSubtitle === false ? null : false
                                }))}
                                className={`px-3 py-1.5 rounded-md text-xs font-medium transition-colors ${
                                  filter.hasSubtitle === false
                                    ? 'bg-red-600 text-white'
                                    : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                                }`}
                              >
                                无字幕
                              </button>
                            </div>
                          </div>
                          
                          {/* 做种数筛选 */}
                          <div>
                            <label className="block text-sm font-medium text-gray-300 mb-2">最低做种数</label>
                            <div className="flex gap-2">
                              {[5, 20, 50, 100].map(seeders => (
                                <button
                                  key={seeders}
                                  onClick={() => setFilter(prev => ({
                                    ...prev,
                                    minSeeders: prev.minSeeders === seeders ? null : seeders
                                  }))}
                                  className={`px-3 py-1.5 rounded-md text-xs font-medium transition-colors ${
                                    filter.minSeeders === seeders
                                      ? 'bg-blue-600 text-white'
                                      : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                                  }`}
                                >
                                  {seeders}+
                                </button>
                              ))}
                            </div>
                          </div>
                        </div>
                        
                        {/* 排序选项 */}
                        <div className="mt-4">
                          <label className="block text-sm font-medium text-gray-300 mb-2">排序方式</label>
                          <div className="flex flex-wrap gap-2">
                            <button
                              onClick={() => setFilter(prev => ({ ...prev, sort: 'score' }))}
                              className={`px-3 py-1.5 rounded-md text-xs font-medium transition-colors ${
                                filter.sort === 'score' ? 'bg-purple-600 text-white' : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                              }`}
                            >
                              综合评分
                            </button>
                            <button
                              onClick={() => setFilter(prev => ({ ...prev, sort: 'size' }))}
                              className={`px-3 py-1.5 rounded-md text-xs font-medium transition-colors ${
                                filter.sort === 'size' ? 'bg-purple-600 text-white' : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                              }`}
                            >
                              文件大小
                            </button>
                            <button
                              onClick={() => setFilter(prev => ({ ...prev, sort: 'seeders' }))}
                              className={`px-3 py-1.5 rounded-md text-xs font-medium transition-colors ${
                                filter.sort === 'seeders' ? 'bg-purple-600 text-white' : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                              }`}
                            >
                              做种数量
                            </button>
                            <button
                              onClick={() => setFilter(prev => ({ ...prev, sort: 'date' }))}
                              className={`px-3 py-1.5 rounded-md text-xs font-medium transition-colors ${
                                filter.sort === 'date' ? 'bg-purple-600 text-white' : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                              }`}
                            >
                              发布日期
                            </button>
                          </div>
                        </div>
                        
                        {/* 重置按钮 */}
                        <div className="mt-4 text-right">
                          <button
                            onClick={() => setFilter({
                              quality: [],
                              hasSubtitle: null,
                              minSeeders: null,
                              codec: [],
                              sort: 'score'
                            })}
                            className="px-3 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-lg text-sm transition-colors"
                          >
                            重置筛选器
                          </button>
                        </div>
                      </div>
                    )}
                    
                    {/* 导出选项面板 */}
                    {showExportOptions && (
                      <div className="mb-6 p-4 bg-gray-800/50 border border-gray-700/50 rounded-lg">
                        <h4 className="text-lg font-medium text-white mb-3 flex items-center gap-2">
                          <FiSettings className="text-yellow-400" />
                          导出选项
                        </h4>
                        
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div>
                            <h5 className="font-medium text-gray-300 mb-2">下载客户端</h5>
                            <p className="text-xs text-gray-400 mb-3">选择要发送到的下载客户端。请确保客户端已在运行。</p>
                            <div className="flex gap-2 flex-wrap">
                              <button className="px-3 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-lg text-sm transition-colors flex items-center gap-2">
                                <FiDownload />
                                qBittorrent
                              </button>
                              <button className="px-3 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-lg text-sm transition-colors flex items-center gap-2">
                                <FiDownload />
                                Transmission
                              </button>
                              <button className="px-3 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-lg text-sm transition-colors flex items-center gap-2">
                                <FiDownload />
                                µTorrent
                              </button>
                            </div>
                          </div>
                          
                          <div>
                            <h5 className="font-medium text-gray-300 mb-2">复制格式</h5>
                            <p className="text-xs text-gray-400 mb-3">选择要复制的磁力链接格式</p>
                            <div className="flex gap-2 flex-wrap">
                              <button className="px-3 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-lg text-sm transition-colors flex items-center gap-2">
                                <FiLink />
                                完整磁力链接
                              </button>
                              <button className="px-3 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-lg text-sm transition-colors flex items-center gap-2">
                                <FiHash />
                                仅Hash值
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>
                    )}
                    
                    {/* 预览模式提示 */}
                    {showPreview && filteredMagnets.length > previewCount && (
                    <div className="mb-4 p-4 bg-blue-900/20 border border-blue-500/30 rounded-lg">
                      <div className="flex items-center gap-2 text-blue-300 mb-2">
                        <FiEye />
                        <span className="font-medium">预览模式</span>
                      </div>
                      <p className="text-sm text-blue-200">
                        显示前 {previewCount} 个最优质的磁力链接
                        <button
                            onClick={() => setPreviewCount(filteredMagnets.length)}
                          className="ml-2 text-blue-400 hover:text-blue-300 underline"
                        >
                            显示全部 {filteredMagnets.length} 个
                        </button>
                      </p>
                    </div>
                  )}
                  
                    {/* 筛选结果统计 */}
                    {(filter.quality.length > 0 || filter.hasSubtitle !== null || filter.minSeeders !== null || filter.codec.length > 0) && (
                      <div className="mb-4 text-sm text-gray-400">
                        筛选结果: 找到 {filteredMagnets.length} 个匹配的磁力链接 (共 {magnetLinks.length} 个)
                      </div>
                    )}
                    
                    {/* 磁力链接网格 */}
                  <div className="space-y-3">
                      {filteredMagnets.slice(0, previewCount).map((magnet) => (
                      <div
                        key={magnet.id}
                          className={`group p-4 ${magnet.isBestChoice 
                            ? 'bg-gradient-to-r from-indigo-900/30 to-purple-900/40 hover:from-indigo-900/40 hover:to-purple-900/50' 
                            : 'bg-gray-800/30 hover:bg-gray-800/50'} 
                            border ${magnet.isBestChoice ? 'border-purple-500/40' : 'border-gray-700/30'} 
                            rounded-xl transition-all duration-300`}
                      >
                        <div className="flex items-start justify-between mb-3">
                            <div className="flex items-center mr-2">
                              <input
                                type="checkbox"
                                checked={selectedMagnets.includes(magnet.id)}
                                onChange={() => toggleSelectMagnet(magnet.id)}
                                className="w-4 h-4 rounded border-gray-600 text-purple-600 focus:ring-purple-500 focus:ring-offset-gray-800"
                              />
                            </div>
                          <div className="flex-1 min-w-0">
                              <div className="flex items-center gap-2 mb-2 flex-wrap">
                                {magnet.isBestChoice && (
                                  <span className="px-2 py-1 rounded-md text-xs font-medium bg-purple-900/70 text-purple-200 border border-purple-400/40 flex items-center gap-1">
                                    <FiStar className="text-yellow-400" />
                                    最佳选择
                                  </span>
                                )}
                              <span className={`px-2 py-1 rounded-md text-xs font-medium border ${getQualityColor(magnet.quality)}`}>
                                {magnet.quality}
                              </span>
                              {magnet.hasSubtitle && (
                                <span className="px-2 py-1 rounded-md text-xs font-medium bg-green-900/40 text-green-400 border border-green-400/30">
                                  字幕
                                </span>
                              )}
                              {magnet.verified && (
                                  <span className="px-2 py-1 rounded-md text-xs font-medium bg-blue-900/40 text-blue-300 border border-blue-400/30 flex items-center gap-1">
                                    <FiShield className="text-blue-400" />
                                    已验证
                                  </span>
                                )}
                                {magnet.score !== undefined && (
                                  <span className="px-2 py-1 rounded-md text-xs font-medium bg-gray-800/60 text-gray-300 border border-gray-500/30">
                                    评分: {magnet.score}
                                  </span>
                              )}
                            </div>
                            <h4 className="text-white font-medium text-sm line-clamp-2 mb-2">
                              {magnet.title}
                            </h4>
                              <div className="flex items-center flex-wrap gap-x-4 gap-y-2 text-xs text-gray-400 mt-3">
                              <span className="flex items-center gap-1">
                                <FiHardDrive />
                                {formatSize(magnet.size)}
                              </span>
                                {magnet.resolution && (
                                  <span className="flex items-center gap-1 text-blue-300">
                                    <FiEye />
                                    {magnet.resolution}
                                  </span>
                                )}
                                {magnet.codec && (
                                  <span className="flex items-center gap-1 text-green-300">
                                    <FiVideo />
                                    {magnet.codec}
                                  </span>
                                )}
                                {magnet.audioInfo && (
                                  <span className="flex items-center gap-1 text-yellow-300">
                                    <FiActivity />
                                    {magnet.audioInfo}
                                  </span>
                                )}
                              <span className="flex items-center gap-1">
                                <FiClock />
                                {formatDate(magnet.shareDate)}
                              </span>
                                <span className="flex items-center gap-1 text-green-400" title={`做种者: ${magnet.seeders || 0} | 下载者: ${magnet.leechers || 0}`}>
                                  <FiGlobe />
                                  {magnet.seeders || 0}做种/{magnet.leechers || 0}下载
                                </span>
                                
                                {/* 健康度指标 */}
                                {magnet.healthMetrics && (
                                  <span className={`flex items-center gap-1 ${getHealthColor(magnet.healthMetrics.status)}`} 
                                    title={`成功率: ${magnet.healthMetrics.successRate}% | 平均速度: ${formatSpeed(magnet.healthMetrics.avgSpeed)}`}>
                                    <FiHeart />
                                    健康度: {getHealthStatusText(magnet.healthMetrics.status)}
                                </span>
                              )}
                            </div>
                              
                              {/* 添加趋势图 */}
                              {magnet.trendData && (
                                <div className="mt-3 border-t border-gray-700/30 pt-3">
                                  <div className="flex items-center justify-between mb-2">
                                    <span className="text-xs text-gray-400 flex items-center gap-1">
                                      <FiTrendingUp className="text-purple-400" />
                                      下载趋势 (7天)
                                    </span>
                                    {magnet.totalDownloads && (
                                      <span className="text-xs text-gray-400 flex items-center gap-1">
                                        <FiBarChart className="text-blue-400" />
                                        总下载: {magnet.totalDownloads.toLocaleString()}
                                      </span>
                                    )}
                                  </div>                                  
                                </div>
                              )}
                          </div>
                          
                            <div className="flex flex-col gap-2 ml-4">
                          <button
                            onClick={() => copyMagnetLink(magnet)}
                                className="px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg text-sm font-medium transition-colors flex items-center gap-2 whitespace-nowrap"
                          >
                            {copiedId === magnet.id ? (
                              <>
                                <FiCheck />
                                已复制
                              </>
                            ) : (
                              <>
                                <FiCopy />
                                复制链接
                              </>
                            )}
                              </button>
                              
                              <div className="relative" ref={dropdownRef}>
                                <button
                                  onClick={() => toggleDropdown(magnet.id)}
                                  className="px-4 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-lg text-sm font-medium transition-colors flex items-center gap-2 w-full"
                                >
                                  <FiDownload />
                                  下载
                                  <FiChevronDown className={`ml-1 transition-transform duration-200 ${openDropdownId === magnet.id ? 'rotate-180' : ''}`} />
                                </button>
                                
                                {/* 下载选项下拉菜单 - 使用点击而不是悬停触发 */}
                                {openDropdownId === magnet.id && (
                                  <div className="absolute right-0 mt-1 w-48 bg-gray-800 border border-gray-700 rounded-lg shadow-xl z-10 animate-fadeIn">
                                    <div className="p-2 space-y-1">
                                      <button 
                                        onClick={() => {
                                          sendToClient(magnet, 'qbittorrent');
                                          closeAllDropdowns();
                                        }}
                                        className="flex items-center gap-2 px-3 py-2 rounded-lg hover:bg-gray-700 w-full text-left text-sm"
                                      >
                                        <FiDownload className="text-green-400" />
                                        发送到 qBittorrent
                                      </button>
                                      <button 
                                        onClick={() => {
                                          sendToClient(magnet, 'transmission');
                                          closeAllDropdowns();
                                        }}
                                        className="flex items-center gap-2 px-3 py-2 rounded-lg hover:bg-gray-700 w-full text-left text-sm"
                                      >
                                        <FiDownload className="text-blue-400" />
                                        发送到 Transmission
                                      </button>
                                      <button 
                                        onClick={() => {
                                          sendToClient(magnet, 'utorrent');
                                          closeAllDropdowns();
                                        }}
                                        className="flex items-center gap-2 px-3 py-2 rounded-lg hover:bg-gray-700 w-full text-left text-sm"
                                      >
                                        <FiDownload className="text-purple-400" />
                                        发送到 µTorrent
                                      </button>
                                      <hr className="border-gray-600 my-1" />
                                      <button 
                                        onClick={() => {
                                          copyHash(magnet);
                                          closeAllDropdowns();
                                        }}
                                        className="flex items-center gap-2 px-3 py-2 rounded-lg hover:bg-gray-700 w-full text-left text-sm"
                                      >
                                        <FiHash className="text-yellow-400" />
                                        {copiedId === `hash-${magnet.id}` ? '已复制哈希值' : '复制哈希值'}
                          </button>
                                    </div>
                                  </div>
                                )}
                              </div>
                            </div>
                        </div>
                      </div>
                    ))}
                  </div>
                  
                    {/* 显示更多按钮 */}
                    {previewCount < filteredMagnets.length && (
                    <div className="mt-6 text-center">
                      <button
                          onClick={() => setPreviewCount(filteredMagnets.length)}
                        className="px-6 py-3 bg-gray-800 hover:bg-gray-700 text-white rounded-lg font-medium transition-colors flex items-center gap-2 mx-auto"
                      >
                        <FiChevronDown />
                          显示剩余 {filteredMagnets.length - previewCount} 个链接
                      </button>
                    </div>
                  )}
                </>
              )}
            </div>
            
            {/* Footer */}
            <div className="p-4 border-t border-gray-700/50 bg-gray-800/30">
              <div className="flex items-center gap-2 text-xs text-gray-400">
                <FiAlertCircle />
                <span>请确保您有合法权限下载这些内容。磁力链接需要支持P2P的下载工具。</span>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
    </>
  );
};

export default MagneticDownloadButton; 