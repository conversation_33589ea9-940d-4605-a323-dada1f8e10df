'use client';

import { useState, useEffect, useRef, useCallback } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import gsap from 'gsap';
import { FiPlay, FiChevronRight, FiChevronLeft } from 'react-icons/fi';
import { useClientTranslations } from './TranslationsProvider';
import { usePathname } from 'next/navigation';
import { locales } from '@/i18n';
import { Locale } from '@/i18n/types';
import { getPopularVideos, VideoData } from '@/lib/video-client';
import { LoadingSpinner, VideoGridSkeleton } from '@/components/loading';
import { usePhysicsScroll } from '@/hooks/usePhysicsScroll';

const Hero = () => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isHovered, setIsHovered] = useState(false);
  const scrollRef = useRef<HTMLDivElement>(null);
  const [featuredVideos, setFeaturedVideos] = useState<VideoData[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  
  // 使用物理滚动Hook，配置更强的滑动效果
  const {
    isDragging,
    hasDragged,
    handleMouseDown,
    handleTouchStart,
    handleDragStart,
    handleCardClick
  } = usePhysicsScroll(scrollRef, {
    friction: 0.95,           // 适中摩擦力，保持流畅惯性
    velocityThreshold: 0.15,  // 降低速度阈值，更容易触发惯性
    maxVelocity: 45,          // 适中的最大速度
    bounceDamping: 0.35,      // 轻微的边界反弹
    enableBounce: true        // 启用边界反弹效果
  });
  
  const { t } = useClientTranslations();
  
  const pathname = usePathname();
  const getCurrentLocale = (): Locale => {
    const pathSegments = pathname.split('/');
    if (pathSegments.length > 1) {
      const possibleLocale = pathSegments[1] as Locale;
      if (locales.includes(possibleLocale)) {
        return possibleLocale;
      }
    }
    return 'zh';
  };
  
  const currentLocale = getCurrentLocale();
  
  const localizedUrl = (path: string): string => {
    if (locales.some(locale => path.startsWith(`/${locale}`))) {
      return path;
    }
    return path.startsWith('/') ? `/${currentLocale}${path}` : `/${currentLocale}/${path}`;
  };
  
  // 获取图片URL
  const getImageUrl = (video: VideoData): string => {
    if (video.cached_image_url) {
      return video.cached_image_url;
    }
    
    if (video.movie_id) {
      return `/api/image-proxy?type=cover&movie_id=${video.movie_id}`;
    }
    
    if (video.code) {
      return `/api/image-proxy?type=cover&movie_id=${video.code}`;
    }
    
    if (video.thumbnail || video.thumbnail_url) {
      return video.thumbnail || video.thumbnail_url || '';
    }
    
    return '/images/defaults/cover_default.jpg';
  };
  
  // 获取热门视频数据
  useEffect(() => {
    const fetchVideos = async () => {
      try {
        setIsLoading(true);
        const result = await getPopularVideos(8);

        // 处理返回的数据结构
        let videos = [];
        if (result && typeof result === 'object') {
          if (result.success && result.data) {
            videos = result.data;
          } else if (Array.isArray(result)) {
            videos = result;
          }
        }

        if (videos && videos.length > 0) {
          setFeaturedVideos(videos);
        }
      } catch (err) {
        console.error('获取Hero视频出错:', err);
      } finally {
        setIsLoading(false);
      }
    };

    fetchVideos();
  }, [currentLocale]);



  // 滚动按钮 - 使用useCallback优化
  const scrollLeftBtn = useCallback(() => {
    if (scrollRef.current) {
      scrollRef.current.scrollBy({ left: -400, behavior: 'smooth' });
    }
  }, []);

  const scrollRightBtn = useCallback(() => {
    if (scrollRef.current) {
      scrollRef.current.scrollBy({ left: 400, behavior: 'smooth' });
    }
  }, []);



  // 加载状态
  if (isLoading || featuredVideos.length === 0) {
    return (
      <div className="relative h-[280px] md:h-[320px] w-full overflow-hidden bg-gradient-to-br from-gray-900 via-black to-gray-800">
        {/* 背景装饰 */}
        <div className="absolute inset-0 pointer-events-none">
          <div className="absolute top-1/4 left-1/4 w-64 h-64 bg-red-500/10 rounded-full filter blur-3xl"></div>
          <div className="absolute bottom-1/3 right-1/3 w-96 h-96 bg-blue-500/8 rounded-full filter blur-3xl"></div>
          <div className="absolute top-1/2 left-1/2 w-48 h-48 bg-purple-500/12 rounded-full filter blur-2xl"></div>
        </div>
        
        {/* 骨架屏 */}
        <div className="container mx-auto px-4 h-full flex items-center">
          <div className="flex space-x-6 overflow-hidden">
            {[...Array(5)].map((_, index) => (
              <div key={index} className="flex-none w-[300px] md:w-[400px] space-y-4">
                <div className="w-full h-[202px] md:h-[269px] bg-gray-700 rounded-2xl animate-pulse"></div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="relative h-[280px] md:h-[320px] w-full overflow-hidden bg-gradient-to-br from-gray-900 via-black to-gray-800">
      {/* 背景装饰 */}
      <div className="absolute inset-0 pointer-events-none">
        <div className="absolute top-1/4 left-1/4 w-64 h-64 bg-red-500/10 rounded-full filter blur-3xl"></div>
        <div className="absolute bottom-1/3 right-1/3 w-96 h-96 bg-blue-500/8 rounded-full filter blur-3xl"></div>
        <div className="absolute top-1/2 left-1/2 w-48 h-48 bg-purple-500/12 rounded-full filter blur-2xl"></div>
      </div>

      {/* 卡片滚动区域 */}
      <div className="relative pt-4">
        {/* 左侧滚动按钮 */}
        <button
          onClick={scrollLeftBtn}
          className="absolute left-4 top-1/2 transform -translate-y-1/2 z-10 bg-black/50 hover:bg-black/70 backdrop-blur-md text-white p-3 rounded-full transition-all duration-300 hover:scale-110 border border-white/20"
          aria-label="向左滚动"
        >
          <FiChevronLeft size={24} />
        </button>

        {/* 右侧滚动按钮 */}
        <button
          onClick={scrollRightBtn}
          className="absolute right-4 top-1/2 transform -translate-y-1/2 z-10 bg-black/50 hover:bg-black/70 backdrop-blur-md text-white p-3 rounded-full transition-all duration-300 hover:scale-110 border border-white/20"
          aria-label="向右滚动"
        >
          <FiChevronRight size={24} />
        </button>

        {/* 卡片容器 */}
        <div
          ref={scrollRef}
          className={`flex space-x-6 overflow-x-auto scrollbar-hide px-4 pb-8 select-none transition-all duration-200 ${
            isDragging ? 'cursor-grabbing scale-[0.98] brightness-95' : 'cursor-grab scale-100 brightness-100'
          }`}
          style={{ 
            scrollbarWidth: 'none',
            msOverflowStyle: 'none',
            scrollBehavior: isDragging ? 'auto' : 'smooth'
          }}
          onMouseDown={handleMouseDown}
          onTouchStart={handleTouchStart}
          onDragStart={handleDragStart}
        >
          <style jsx>{`
            div::-webkit-scrollbar {
              display: none;
            }
          `}</style>
          
          {featuredVideos.map((video, index) => (
            <div
              key={video.id}
              className="flex-none w-[300px] md:w-[400px] group"
              onMouseEnter={() => setIsHovered(true)}
              onMouseLeave={() => setIsHovered(false)}
            >
              {/* 视频卡片 - 纯图片展示 */}
              <Link
                href={localizedUrl(`/video/${video.slug || video.code}`)}
                className="block relative rounded-2xl overflow-hidden transition-all duration-500 transform hover:scale-105"
                onClick={(e) => {
                  if (!handleCardClick(e)) return;
                  // 如果没有拖拽，则正常导航
                }}
                onDragStart={handleDragStart}
              >
                <div className="relative w-full h-[202px] md:h-[269px] overflow-hidden">
                  <Image
                    src={getImageUrl(video)}
                    alt={video.title}
                    fill
                    sizes="(max-width: 768px) 300px, 400px"
                    priority={index === 0}
                    className="object-cover transition-transform duration-700 group-hover:scale-110 select-none"
                    onError={(e) => {
                      e.currentTarget.src = '/images/defaults/cover_default.jpg';
                    }}
                    draggable={false}
                  />
                  
                  {/* 悬停时的播放按钮 */}
                  <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                    <div className="bg-white/20 backdrop-blur-sm text-white p-4 rounded-full transform scale-75 group-hover:scale-100 transition-transform duration-300">
                      <FiPlay size={32} />
                    </div>
                  </div>
                </div>
              </Link>
            </div>
          ))}
          
          {/* 查看更多卡片 */}
          <div className="flex-none w-[300px] md:w-[400px]">
            <Link
              href={localizedUrl('/popular')}
              className="block relative h-[202px] md:h-[269px] bg-gradient-to-br from-gray-800/30 to-gray-900/30 backdrop-blur-sm rounded-2xl transition-all duration-500 transform hover:scale-105 overflow-hidden"
              onDragStart={handleDragStart}
            >
              <div className="absolute inset-0 flex items-center justify-center opacity-60 hover:opacity-80 transition-opacity duration-300">
                <div className="w-16 h-16 bg-white/10 rounded-full flex items-center justify-center">
                  <FiChevronRight size={32} className="text-white" />
                </div>
              </div>
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Hero;