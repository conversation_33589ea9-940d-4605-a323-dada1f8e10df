'use client';

import React, { useState } from 'react';
import { FiPlay, FiHeart, FiBookmark, FiShare, FiDownload } from 'react-icons/fi';
import { 
  PrimaryButton, 
  SecondaryButton, 
  GhostButton, 
  DangerButton,
  MagneticButton,
  GlowButton,
  IconButton,
  FloatingActionButton
} from './EnhancedButton';
import { ContentSwitcher, LoadingTransition, StaggeredReveal } from './PageTransition';

const AnimationShowcase: React.FC = () => {
  const [activeTab, setActiveTab] = useState('buttons');
  const [isLoading, setIsLoading] = useState(false);

  const toggleLoading = () => {
    setIsLoading(true);
    setTimeout(() => setIsLoading(false), 2000);
  };

  const demoCards = [
    { id: 1, title: '卡片动画示例 1', description: '这是一个带有增强动画效果的卡片' },
    { id: 2, title: '卡片动画示例 2', description: '悬停时会有流畅的缩放和发光效果' },
    { id: 3, title: '卡片动画示例 3', description: '支持滚动触发的渐入动画' },
  ];

  return (
    <div className="min-h-screen bg-black text-white p-8">
      <div className="max-w-6xl mx-auto">
        {/* 标题 */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold mb-4 animate-fadeInUp">
            🎨 动画效果展示
          </h1>
          <p className="text-gray-400 animate-fadeInUp" style={{ animationDelay: '0.2s' }}>
            展示所有新增的UI动画和微交互效果
          </p>
        </div>

        {/* 标签页导航 */}
        <div className="flex justify-center mb-8">
          <div className="glass-effect rounded-lg p-1">
            {['buttons', 'cards', 'transitions', 'effects'].map((tab) => (
              <button
                key={tab}
                onClick={() => setActiveTab(tab)}
                className={`px-6 py-2 rounded-md transition-all duration-300 ${
                  activeTab === tab 
                    ? 'bg-red-600 text-white shadow-glow-red' 
                    : 'text-gray-400 hover:text-white hover:bg-white/10'
                }`}
              >
                {tab.charAt(0).toUpperCase() + tab.slice(1)}
              </button>
            ))}
          </div>
        </div>

        {/* 内容区域 */}
        <ContentSwitcher transitionKey={activeTab} animationType="slide">
          {activeTab === 'buttons' && (
            <div className="space-y-8">
              <h2 className="text-2xl font-semibold mb-6">按钮动画效果</h2>
              
              {/* 基础按钮 */}
              <div className="card-enhanced p-6">
                <h3 className="text-lg font-medium mb-4">基础按钮变体</h3>
                <div className="flex flex-wrap gap-4">
                  <PrimaryButton icon={<FiPlay />}>播放视频</PrimaryButton>
                  <SecondaryButton icon={<FiHeart />}>收藏</SecondaryButton>
                  <GhostButton icon={<FiBookmark />}>书签</GhostButton>
                  <DangerButton icon={<FiDownload />}>删除</DangerButton>
                </div>
              </div>

              {/* 特效按钮 */}
              <div className="card-enhanced p-6">
                <h3 className="text-lg font-medium mb-4">特殊效果按钮</h3>
                <div className="flex flex-wrap gap-4">
                  <MagneticButton variant="primary" icon={<FiPlay />}>
                    磁性效果
                  </MagneticButton>
                  <GlowButton variant="secondary" icon={<FiHeart />}>
                    发光效果
                  </GlowButton>
                  <PrimaryButton loading>加载中...</PrimaryButton>
                </div>
              </div>

              {/* 图标按钮 */}
              <div className="card-enhanced p-6">
                <h3 className="text-lg font-medium mb-4">图标按钮</h3>
                <div className="flex gap-4">
                  <IconButton icon={<FiPlay />} aria-label="播放" />
                  <IconButton icon={<FiHeart />} aria-label="喜欢" variant="primary" />
                  <IconButton icon={<FiShare />} aria-label="分享" variant="secondary" />
                  <IconButton icon={<FiBookmark />} aria-label="收藏" size="lg" />
                </div>
              </div>
            </div>
          )}

          {activeTab === 'cards' && (
            <div className="space-y-8">
              <h2 className="text-2xl font-semibold mb-6">卡片动画效果</h2>
              
              <StaggeredReveal staggerDelay={0.15}>
                {demoCards.map((card) => (
                  <div key={card.id} className="card-enhanced p-6 hover-lift">
                    <h3 className="text-lg font-medium mb-2">{card.title}</h3>
                    <p className="text-gray-400 mb-4">{card.description}</p>
                    <div className="flex gap-2">
                      <PrimaryButton size="sm">查看详情</PrimaryButton>
                      <SecondaryButton size="sm" icon={<FiHeart />}>
                        喜欢
                      </SecondaryButton>
                    </div>
                  </div>
                ))}
              </StaggeredReveal>
            </div>
          )}

          {activeTab === 'transitions' && (
            <div className="space-y-8">
              <h2 className="text-2xl font-semibold mb-6">过渡动画效果</h2>
              
              <div className="card-enhanced p-6">
                <h3 className="text-lg font-medium mb-4">加载状态过渡</h3>
                <div className="space-y-4">
                  <PrimaryButton onClick={toggleLoading}>
                    触发加载状态
                  </PrimaryButton>
                  
                  <LoadingTransition isLoading={isLoading}>
                    <div className="p-4 bg-gray-800 rounded-lg">
                      <p>这是加载完成后显示的内容</p>
                      <p className="text-sm text-gray-400 mt-2">
                        内容会在加载完成后以流畅的动画显示
                      </p>
                    </div>
                  </LoadingTransition>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'effects' && (
            <div className="space-y-8">
              <h2 className="text-2xl font-semibold mb-6">视觉效果</h2>
              
              {/* 毛玻璃效果 */}
              <div className="relative">
                <div className="absolute inset-0 bg-gradient-to-r from-red-500/20 to-blue-500/20 rounded-lg"></div>
                <div className="glass-effect p-6 relative">
                  <h3 className="text-lg font-medium mb-2">毛玻璃效果</h3>
                  <p className="text-gray-300">
                    背景模糊和饱和度增强的毛玻璃效果
                  </p>
                </div>
              </div>

              {/* 渐变背景 */}
              <div className="gradient-bg-primary p-6 rounded-lg">
                <h3 className="text-lg font-medium mb-2">渐变背景效果</h3>
                <p className="text-gray-300">
                  动态渐变背景，支持多种颜色组合
                </p>
              </div>

              {/* 阴影效果 */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="bg-gray-800 p-6 rounded-lg shadow-soft">
                  <h4 className="font-medium mb-2">柔和阴影</h4>
                  <p className="text-sm text-gray-400">shadow-soft</p>
                </div>
                <div className="bg-gray-800 p-6 rounded-lg shadow-medium">
                  <h4 className="font-medium mb-2">中等阴影</h4>
                  <p className="text-sm text-gray-400">shadow-medium</p>
                </div>
                <div className="bg-gray-800 p-6 rounded-lg shadow-strong">
                  <h4 className="font-medium mb-2">强烈阴影</h4>
                  <p className="text-sm text-gray-400">shadow-strong</p>
                </div>
              </div>
            </div>
          )}
        </ContentSwitcher>

        {/* 浮动操作按钮示例 */}
        <FloatingActionButton aria-label="主要操作">
          <FiPlay size={24} />
        </FloatingActionButton>
      </div>
    </div>
  );
};

export default AnimationShowcase;
