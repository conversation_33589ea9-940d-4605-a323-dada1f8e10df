'use client';

import { useState } from 'react';

interface FallbackImageProps {
  src: string;
  alt: string;
  fallbackSrc?: string;
  className?: string;
  width?: number | string;
  height?: number | string;
}

/**
 * 带有回退功能的图片组件
 * 如果主图片加载失败，会显示备用图片
 */
export default function FallbackImage({
  src,
  alt,
  fallbackSrc = '/images/placeholder.jpg',
  className = '',
  width,
  height,
}: FallbackImageProps) {
  const [imgSrc, setImgSrc] = useState(src);
  const [hasError, setHasError] = useState(false);

  // 处理图片加载错误
  const handleError = () => {
    if (!hasError) {
      setImgSrc(fallbackSrc);
      setHasError(true);
    }
  };

  return (
    <img
      src={imgSrc}
      alt={alt}
      className={className}
      onError={handleError}
      width={width}
      height={height}
    />
  );
} 