'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { FiVideo, FiStar, FiTrendingUp, FiChevronRight, FiHeart } from 'react-icons/fi';

interface Actress {
  id: string;
  name: string;
  japaneseName?: string;
  avatar: string;
  videoCount: number;
  rank: number;
  rating: number;
  tags: string[];
  isLiked?: boolean;
}

const PopularActresses = () => {
  const [actresses, setActresses] = useState<Actress[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // 模拟从API获取热门女优数据
    const fetchActresses = async () => {
      setIsLoading(true);
      try {
        // 模拟API调用延迟
        await new Promise(resolve => setTimeout(resolve, 600));
        
        // 模拟热门女优数据
        const mockActresses: Actress[] = [
          {
            id: 'act001',
            name: '三上悠亚',
            japaneseName: '三上悠亜',
            avatar: '/images/actresses/actress1.jpg',
            videoCount: 146,
            rank: 1,
            rating: 4.9,
            tags: ['巨乳', 'S级', '偶像'],
            isLiked: true
          },
          {
            id: 'act002',
            name: '桥本有菜',
            japaneseName: '橋本ありな',
            avatar: '/images/actresses/actress2.jpg',
            videoCount: 123,
            rank: 2,
            rating: 4.8,
            tags: ['美腿', '美少女', '清纯']
          },
          {
            id: 'act003',
            name: '深田咏美',
            japaneseName: '深田えいみ',
            avatar: '/images/actresses/actress3.jpg',
            videoCount: 198,
            rank: 3,
            rating: 4.8,
            tags: ['巨乳', '人气', '多P']
          },
          {
            id: 'act004',
            name: '天使萌',
            japaneseName: '天使もえ',
            avatar: '/images/actresses/actress4.jpg',
            videoCount: 135,
            rank: 4,
            rating: 4.7,
            tags: ['美少女', '童颜', '笑容']
          },
          {
            id: 'act005',
            name: '明日花绮罗',
            japaneseName: '明日花キララ',
            avatar: '/images/actresses/actress5.jpg',
            videoCount: 215,
            rank: 5,
            rating: 4.7,
            tags: ['金发', '巨乳', '女王']
          },
          {
            id: 'act006',
            name: '松下纱荣子',
            japaneseName: '松下紗栄子',
            avatar: '/images/actresses/actress6.jpg',
            videoCount: 112,
            rank: 6,
            rating: 4.6,
            tags: ['熟女', '知性美', 'OL']
          },
          {
            id: 'act007',
            name: '波多野结衣',
            japaneseName: '波多野結衣',
            avatar: '/images/actresses/actress7.jpg',
            videoCount: 345,
            rank: 7,
            rating: 4.6,
            tags: ['熟女', '传奇', '多P']
          },
          {
            id: 'act008',
            name: '河北彩花',
            japaneseName: '河北彩花',
            avatar: '/images/actresses/actress8.jpg',
            videoCount: 88,
            rank: 8,
            rating: 4.5,
            tags: ['美少女', '修长', '名器']
          }
        ];
        
        setActresses(mockActresses);
      } catch (error) {
        console.error('Failed to fetch popular actresses:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchActresses();
  }, []);

  return (
    <section className="py-8 bg-gray-900">
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl md:text-2xl font-bold text-white flex items-center">
            <FiTrendingUp className="mr-2 text-red-600" />
            热门女优
          </h2>
          <Link href="/actresses" className="text-sm text-gray-400 hover:text-white transition flex items-center">
            全部女优 <FiChevronRight className="ml-1" />
          </Link>
        </div>

        {isLoading ? (
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-8 gap-4">
            {[...Array(8)].map((_, index) => (
              <div key={index} className="bg-gray-800 rounded-lg overflow-hidden animate-pulse">
                <div className="aspect-square bg-gray-700"></div>
                <div className="p-3">
                  <div className="h-4 bg-gray-700 rounded mb-2"></div>
                  <div className="h-3 bg-gray-700 rounded w-2/3"></div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-8 gap-4">
            {actresses.map((actress) => (
              <div key={actress.id} className="bg-gray-800/40 hover:bg-gray-800 rounded-lg overflow-hidden transition group">
                <Link href={`/actress/${actress.id}`} className="block">
                  <div className="relative aspect-square overflow-hidden">
                    <Image
                      src={actress.avatar}
                      alt={actress.name}
                      fill
                      sizes="(max-width: 768px) 50vw, 25vw"
                      className="object-cover group-hover:scale-105 transition-transform duration-300"
                    />
                    <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-t from-black/70 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex flex-col justify-end p-3">
                      <div className="flex items-center text-xs text-gray-300 mb-1">
                        <FiVideo className="mr-1" size={12} />
                        <span>{actress.videoCount}部作品</span>
                      </div>
                      <div className="flex items-center text-xs text-gray-300">
                        <FiStar className="mr-1 text-yellow-500" size={12} />
                        <span>{actress.rating}</span>
                      </div>
                    </div>
                    {actress.rank <= 3 && (
                      <div className="absolute top-2 left-2 w-6 h-6 rounded-full bg-red-600 text-white text-xs flex items-center justify-center font-bold">
                        {actress.rank}
                      </div>
                    )}
                    {actress.isLiked && (
                      <div className="absolute top-2 right-2">
                        <FiHeart className="text-red-500" size={16} fill="currentColor" />
                      </div>
                    )}
                  </div>
                  <div className="p-3">
                    <h3 className="text-sm font-medium text-white text-center">
                      {actress.name}
                    </h3>
                    <p className="text-xs text-gray-500 text-center mt-1">
                      {actress.japaneseName}
                    </p>
                    <div className="mt-2 flex flex-wrap justify-center gap-1">
                      {actress.tags.slice(0, 2).map((tag, index) => (
                        <span key={index} className="text-xs bg-gray-700 text-gray-300 px-2 py-0.5 rounded">
                          {tag}
                        </span>
                      ))}
                    </div>
                  </div>
                </Link>
              </div>
            ))}
          </div>
        )}
      </div>
    </section>
  );
};

export default PopularActresses; 