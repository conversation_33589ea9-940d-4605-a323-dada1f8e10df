'use client';

import React, { useState } from 'react';
import { FiThumbsUp, FiMessageCircle, FiMoreHorizontal, FiTrash2, FiFlag } from 'react-icons/fi';
import CommentBox from './CommentBox';

export interface Comment {
  id: string;
  video_id: string;
  user_id: string;
  content: string;
  likes: number;
  parent_id?: string;
  created_at: string;
  updated_at: string;
  actual_likes: number;
  user_liked: boolean;
  reply_count: number;
  replies?: Comment[];
}

interface CommentItemProps {
  comment: Comment;
  currentUserId?: string;
  onLike: (commentId: string) => Promise<void>;
  onReply: (commentId: string, content: string) => Promise<void>;
  onDelete: (commentId: string) => Promise<void>;
  onReport: (commentId: string, reason: string) => Promise<void>;
  level?: number; // 嵌套层级，用于控制样式
}

const CommentItem: React.FC<CommentItemProps> = ({
  comment,
  currentUserId,
  onLike,
  onReply,
  onDelete,
  onReport,
  level = 0
}) => {
  const [showReplyBox, setShowReplyBox] = useState(false);
  const [showReplies, setShowReplies] = useState(false);
  const [showMenu, setShowMenu] = useState(false);
  const [isLiking, setIsLiking] = useState(false);
  const [localLikes, setLocalLikes] = useState(comment.actual_likes);
  const [localUserLiked, setLocalUserLiked] = useState(comment.user_liked);

  // 格式化时间
  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

    if (diffInSeconds < 60) return '刚刚';
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}分钟前`;
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}小时前`;
    if (diffInSeconds < 2592000) return `${Math.floor(diffInSeconds / 86400)}天前`;
    
    return date.toLocaleDateString('zh-CN');
  };

  // 处理点赞
  const handleLike = async () => {
    if (isLiking || !currentUserId) return;

    setIsLiking(true);
    
    // 乐观更新UI
    const newLiked = !localUserLiked;
    const newLikes = newLiked ? localLikes + 1 : localLikes - 1;
    
    setLocalUserLiked(newLiked);
    setLocalLikes(newLikes);

    try {
      await onLike(comment.id);
    } catch (error) {
      // 如果失败，回滚状态
      setLocalUserLiked(!newLiked);
      setLocalLikes(localLikes);
      console.error('点赞失败:', error);
    } finally {
      setIsLiking(false);
    }
  };

  // 处理回复
  const handleReply = async (content: string) => {
    try {
      await onReply(comment.id, content);
      setShowReplyBox(false);
      setShowReplies(true); // 回复后自动展开回复列表
    } catch (error) {
      console.error('回复失败:', error);
      throw error;
    }
  };

  // 处理删除
  const handleDelete = async () => {
    if (window.confirm('确定要删除这条评论吗？')) {
      try {
        await onDelete(comment.id);
      } catch (error) {
        console.error('删除失败:', error);
      }
    }
    setShowMenu(false);
  };

  // 处理举报
  const handleReport = async () => {
    const reason = window.prompt('请输入举报原因：');
    if (reason && reason.trim()) {
      try {
        await onReport(comment.id, reason.trim());
        alert('举报已提交，谢谢您的反馈');
      } catch (error) {
        console.error('举报失败:', error);
      }
    }
    setShowMenu(false);
  };

  const isOwner = currentUserId === comment.user_id;
  const canInteract = !!currentUserId;
  const maxNestingLevel = 2; // 最大嵌套层级

  return (
    <div className={`${level > 0 ? 'ml-8 pl-4 border-l border-gray-700' : ''}`}>
      <div className="group py-4">
        {/* 评论头部 */}
        <div className="flex items-start gap-3">
          {/* 用户头像 */}
          <div className="w-8 h-8 bg-gradient-to-br from-red-500 to-red-600 rounded-full flex items-center justify-center text-white text-sm font-medium">
            {comment.user_id.charAt(0).toUpperCase()}
          </div>

          <div className="flex-1 min-w-0">
            {/* 用户信息和时间 */}
            <div className="flex items-center gap-2 mb-2">
              <span className="text-sm font-medium text-white">
                用户{comment.user_id}
              </span>
              <span className="text-xs text-gray-500">
                {formatTimeAgo(comment.created_at)}
              </span>
            </div>

            {/* 评论内容 */}
            <div className="text-gray-200 leading-relaxed mb-3 whitespace-pre-wrap">
              {comment.content}
            </div>

            {/* 操作按钮 */}
            <div className="flex items-center gap-4">
              {/* 点赞 */}
              <button
                onClick={handleLike}
                disabled={!canInteract || isLiking}
                className={`
                  flex items-center gap-1.5 text-sm transition-colors
                  ${localUserLiked 
                    ? 'text-red-400' 
                    : canInteract 
                      ? 'text-gray-500 hover:text-red-400' 
                      : 'text-gray-600 cursor-not-allowed'
                  }
                  ${isLiking ? 'opacity-50' : ''}
                `}
              >
                <FiThumbsUp size={14} />
                <span>{localLikes > 0 ? localLikes : ''}</span>
              </button>

              {/* 回复 */}
              {canInteract && level < maxNestingLevel && (
                <button
                  onClick={() => setShowReplyBox(!showReplyBox)}
                  className="flex items-center gap-1.5 text-sm text-gray-500 hover:text-white transition-colors"
                >
                  <FiMessageCircle size={14} />
                  <span>回复</span>
                </button>
              )}

              {/* 查看回复 */}
              {comment.reply_count > 0 && (
                <button
                  onClick={() => setShowReplies(!showReplies)}
                  className="text-sm text-gray-500 hover:text-white transition-colors"
                >
                  {showReplies ? '收起' : `查看${comment.reply_count}条回复`}
                </button>
              )}

              {/* 更多操作 */}
              {canInteract && (
                <div className="relative">
                  <button
                    onClick={() => setShowMenu(!showMenu)}
                    className="text-gray-500 hover:text-white transition-colors opacity-0 group-hover:opacity-100"
                  >
                    <FiMoreHorizontal size={14} />
                  </button>

                  {showMenu && (
                    <div className="absolute top-6 right-0 bg-gray-800 border border-gray-700 rounded-lg shadow-lg z-10 py-1 min-w-[120px]">
                      {isOwner ? (
                        <button
                          onClick={handleDelete}
                          className="w-full px-3 py-2 text-sm text-left text-red-400 hover:bg-gray-700 flex items-center gap-2"
                        >
                          <FiTrash2 size={12} />
                          删除
                        </button>
                      ) : (
                        <button
                          onClick={handleReport}
                          className="w-full px-3 py-2 text-sm text-left text-gray-300 hover:bg-gray-700 flex items-center gap-2"
                        >
                          <FiFlag size={12} />
                          举报
                        </button>
                      )}
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>

        {/* 回复输入框 */}
        {showReplyBox && (
          <div className="mt-4 ml-11">
            <CommentBox
              onSubmit={handleReply}
              placeholder={`回复 用户${comment.user_id}...`}
              submitButtonText="回复"
              replyTo={{
                id: comment.id,
                username: `用户${comment.user_id}`
              }}
              onCancel={() => setShowReplyBox(false)}
              autoFocus
            />
          </div>
        )}

        {/* 回复列表 */}
        {showReplies && comment.replies && comment.replies.length > 0 && (
          <div className="mt-4">
            {comment.replies.map((reply) => (
              <CommentItem
                key={reply.id}
                comment={reply}
                currentUserId={currentUserId}
                onLike={onLike}
                onReply={onReply}
                onDelete={onDelete}
                onReport={onReport}
                level={level + 1}
              />
            ))}
          </div>
        )}
      </div>

      {/* 点击外部关闭菜单 */}
      {showMenu && (
        <div
          className="fixed inset-0 z-0"
          onClick={() => setShowMenu(false)}
        />
      )}
    </div>
  );
};

export default CommentItem; 