'use client';

import React, { useState, useEffect, useRef } from 'react';
import { 
  FiX, 
  FiMessageCircle, 
  FiLoader, 
  FiRefreshCw, 
  FiChevronUp,
  FiUsers,
  FiClock
} from 'react-icons/fi';
import CommentBox from './CommentBox';
import CommentItem, { Comment } from './CommentItem';
import LoginPromptModal from '@/components/LoginPromptModal';
import { useRouter, useParams } from 'next/navigation';

interface CommentsDrawerProps {
  isOpen: boolean;
  onClose: () => void;
  videoId: string;
  currentUserId?: string;
}

interface CommentsStats {
  totalComments: number;
  rootComments: number;
  replyComments: number;
}

interface CommentsResponse {
  success: boolean;
  data: {
    comments: Comment[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
    };
  };
}

const CommentsDrawer: React.FC<CommentsDrawerProps> = ({
  isOpen,
  onClose,
  videoId,
  currentUserId
}) => {
  const router = useRouter();
  const params = useParams();
  const locale = params?.locale?.toString() || 'zh-CN';

  const [comments, setComments] = useState<Comment[]>([]);
  const [stats, setStats] = useState<CommentsStats | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [sortBy, setSortBy] = useState<'created_at' | 'likes'>('created_at');
  const [sortOrder, setSortOrder] = useState<'DESC' | 'ASC'>('DESC');

  // 登录提示模态框状态
  const [showLoginPrompt, setShowLoginPrompt] = useState(false);
  const [loginPromptAction, setLoginPromptAction] = useState<'comment' | 'like' | 'general'>('comment');

  const drawerRef = useRef<HTMLDivElement>(null);
  const commentsContainerRef = useRef<HTMLDivElement>(null);

  // API配置 - 通过next.config.js rewrites配置代理
  const API_BASE = '';

  // 处理需要登录的操作
  const handleLoginRequired = (actionType: 'comment' | 'like' | 'general' = 'general') => {
    setLoginPromptAction(actionType);
    setShowLoginPrompt(true);
  };

  // 获取评论统计
  const fetchStats = async () => {
    try {
      const response = await fetch(`${API_BASE}/api/comments/${videoId}/stats`);
      const data = await response.json();
      if (data.success) {
        setStats(data.data);
      }
    } catch (error) {
      console.error('获取评论统计失败:', error);
    }
  };

  // 获取评论列表
  const fetchComments = async (pageNum = 1, resetList = false) => {
    try {
      setLoading(true);
      setError(null);

      // 使用字符串拼接而不是URL构造函数
      let urlString = `/api/comments/${videoId}?page=${pageNum}&limit=20&sort=${sortBy}&order=${sortOrder}`;
      

      const headers: HeadersInit = {
        'Content-Type': 'application/json',
      };

      // 如果用户已登录，添加认证头
      if (currentUserId) {
        const token = localStorage.getItem('token');
        if (token) {
          headers.Authorization = `Bearer ${token}`;
        }
      }

      const response = await fetch(urlString, { headers });
      const data: CommentsResponse = await response.json();

      if (data.success) {
        if (resetList) {
          setComments(data.data.comments);
        } else {
          setComments(prev => [...prev, ...data.data.comments]);
        }
        
        setHasMore(pageNum < data.data.pagination.totalPages);
        setPage(pageNum);
      } else {
        throw new Error('获取评论失败');
      }
    } catch (error) {
      setError(error instanceof Error ? error.message : '获取评论失败');
      console.error('获取评论失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 添加评论
  const handleAddComment = async (content: string) => {
    if (!currentUserId) {
      handleLoginRequired('comment');
      return;
    }

    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`${API_BASE}/api/comments`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({
          video_id: videoId,
          content
        }),
      });

      const data = await response.json();
      if (data.success) {
        // 添加到评论列表顶部
        setComments(prev => [data.data, ...prev]);
        // 更新统计
        if (stats) {
          setStats(prev => prev ? {
            ...prev,
            totalComments: prev.totalComments + 1,
            rootComments: prev.rootComments + 1
          } : null);
        }
      } else {
        throw new Error(data.message || '发布评论失败');
      }
    } catch (error) {
      console.error('发布评论失败:', error);
      throw error;
    }
  };

  // 回复评论
  const handleReply = async (commentId: string, content: string) => {
    if (!currentUserId) {
      handleLoginRequired('comment');
      return;
    }

    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`${API_BASE}/api/comments/${commentId}/reply`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({ content }),
      });

      const data = await response.json();
      if (data.success) {
        // 找到父评论并添加回复
        setComments(prev => prev.map(comment => {
          if (comment.id === commentId) {
            return {
              ...comment,
              reply_count: comment.reply_count + 1,
              replies: [...(comment.replies || []), data.data]
            };
          }
          return comment;
        }));
        
        // 更新统计
        if (stats) {
          setStats(prev => prev ? {
            ...prev,
            totalComments: prev.totalComments + 1,
            replyComments: prev.replyComments + 1
          } : null);
        }
      } else {
        throw new Error(data.message || '回复失败');
      }
    } catch (error) {
      console.error('回复失败:', error);
      throw error;
    }
  };

  // 点赞评论
  const handleLike = async (commentId: string) => {
    if (!currentUserId) {
      handleLoginRequired('like');
      return;
    }

    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`${API_BASE}/api/comments/${commentId}/like`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
      });

      const data = await response.json();
      if (data.success) {
        // 更新评论的点赞状态
        const updateCommentLikes = (comments: Comment[]): Comment[] => {
          return comments.map(comment => {
            if (comment.id === commentId) {
              return {
                ...comment,
                actual_likes: data.data.likesCount,
                user_liked: data.data.isLiked
              };
            }
            if (comment.replies) {
              return {
                ...comment,
                replies: updateCommentLikes(comment.replies)
              };
            }
            return comment;
          });
        };

        setComments(prev => updateCommentLikes(prev));
      } else {
        throw new Error(data.message || '操作失败');
      }
    } catch (error) {
      console.error('点赞失败:', error);
      throw error;
    }
  };

  // 删除评论
  const handleDelete = async (commentId: string) => {
    if (!currentUserId) {
      handleLoginRequired('general');
      return;
    }

    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`${API_BASE}/api/comments/${commentId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      const data = await response.json();
      if (data.success) {
        // 从列表中移除评论
        setComments(prev => prev.filter(comment => comment.id !== commentId));
        
        // 更新统计
        if (stats) {
          setStats(prev => prev ? {
            ...prev,
            totalComments: prev.totalComments - 1,
            rootComments: prev.rootComments - 1
          } : null);
        }
      } else {
        throw new Error(data.message || '删除失败');
      }
    } catch (error) {
      console.error('删除失败:', error);
      throw error;
    }
  };

  // 举报评论
  const handleReport = async (commentId: string, reason: string) => {
    if (!currentUserId) {
      handleLoginRequired('general');
      return;
    }

    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`${API_BASE}/api/comments/${commentId}/report`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({ reason }),
      });

      const data = await response.json();
      if (!data.success) {
        throw new Error(data.message || '举报失败');
      }
    } catch (error) {
      console.error('举报失败:', error);
      throw error;
    }
  };

  // 加载更多评论
  const loadMore = () => {
    if (hasMore && !loading) {
      fetchComments(page + 1, false);
    }
  };

  // 刷新评论
  const refresh = () => {
    setPage(1);
    setHasMore(true);
    fetchComments(1, true);
    fetchStats();
  };

  // 改变排序方式
  const changeSortBy = (newSortBy: 'created_at' | 'likes') => {
    setSortBy(newSortBy);
    setPage(1);
    setHasMore(true);
    // 延迟执行以确保状态更新
    setTimeout(() => {
      fetchComments(1, true);
    }, 0);
  };

  // 监听抽屉打开状态
  useEffect(() => {
    if (isOpen && videoId) {
      fetchComments(1, true);
      fetchStats();
    }
  }, [isOpen, videoId, sortBy, sortOrder]);

  // 监听ESC键关闭抽屉
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && isOpen) {
        onClose();
      }
    };

    document.addEventListener('keydown', handleEscape);
    return () => document.removeEventListener('keydown', handleEscape);
  }, [isOpen, onClose]);

  // 阻止背景滚动
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
      return () => {
        document.body.style.overflow = 'unset';
      };
    }
  }, [isOpen]);

  if (!isOpen) return null;

  return (
    <>
      {/* 背景遮罩 */}
      <div 
        className="fixed inset-0 bg-black/60 backdrop-blur-sm z-40 transition-opacity duration-300"
        onClick={onClose}
      />

      {/* 抽屉主体 */}
      <div 
        ref={drawerRef}
        className={`
          fixed right-0 top-0 bottom-0 w-full max-w-lg bg-gray-900 shadow-2xl z-50
          transform transition-transform duration-300 ease-out
          ${isOpen ? 'translate-x-0' : 'translate-x-full'}
        `}
      >
        {/* 抽屉头部 */}
        <div className="flex items-center justify-between p-4 border-b border-gray-700 bg-gray-800/50">
          <div className="flex items-center gap-3">
            <FiMessageCircle className="text-red-400" size={20} />
            <div>
              <h2 className="text-lg font-semibold text-white">评论</h2>
              {stats && (
                <p className="text-sm text-gray-400">
                  {stats.totalComments} 条评论
                </p>
              )}
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            <button
              onClick={refresh}
              disabled={loading}
              className="p-2 text-gray-400 hover:text-white transition-colors"
              title="刷新评论"
            >
              <FiRefreshCw className={loading ? 'animate-spin' : ''} size={18} />
            </button>
            
            <button
              onClick={onClose}
              className="p-2 text-gray-400 hover:text-white transition-colors"
            >
              <FiX size={20} />
            </button>
          </div>
        </div>

        {/* 排序选项 */}
        <div className="flex items-center gap-4 p-4 bg-gray-800/30 border-b border-gray-700">
          <span className="text-sm text-gray-400">排序:</span>
          <button
            onClick={() => changeSortBy('created_at')}
            className={`text-sm px-3 py-1 rounded-full transition-colors ${
              sortBy === 'created_at'
                ? 'bg-red-600 text-white'
                : 'text-gray-400 hover:text-white'
            }`}
          >
            <FiClock size={12} className="inline mr-1" />
            最新
          </button>
          <button
            onClick={() => changeSortBy('likes')}
            className={`text-sm px-3 py-1 rounded-full transition-colors ${
              sortBy === 'likes'
                ? 'bg-red-600 text-white'
                : 'text-gray-400 hover:text-white'
            }`}
          >
            <FiChevronUp size={12} className="inline mr-1" />
            热门
          </button>
        </div>

        {/* 评论输入 */}
        {currentUserId ? (
          <div className="p-4 border-b border-gray-700">
            <CommentBox
              onSubmit={handleAddComment}
              placeholder="分享你的想法..."
              submitButtonText="发布评论"
            />
          </div>
        ) : (
          <div className="p-4 border-b border-gray-700 text-center">
            <p className="text-gray-400 mb-3">登录后可以发表评论</p>
            <button
              onClick={() => handleLoginRequired('comment')}
              className="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-md text-sm transition-colors"
            >
              立即登录
            </button>
          </div>
        )}

        {/* 评论列表 */}
        <div 
          ref={commentsContainerRef}
          className="flex-1 overflow-y-auto"
        >
          {error ? (
            <div className="p-4 text-center">
              <p className="text-red-400 mb-3">{error}</p>
              <button
                onClick={refresh}
                className="px-4 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-md text-sm transition-colors"
              >
                重试
              </button>
            </div>
          ) : comments.length === 0 && !loading ? (
            <div className="p-8 text-center">
              <FiUsers className="mx-auto text-gray-600 mb-4" size={48} />
              <p className="text-gray-400 mb-2">还没有评论</p>
              <p className="text-gray-500 text-sm">成为第一个发表评论的人吧！</p>
            </div>
          ) : (
            <div className="divide-y divide-gray-700">
              {comments.map((comment) => (
                <CommentItem
                  key={comment.id}
                  comment={comment}
                  currentUserId={currentUserId}
                  onLike={handleLike}
                  onReply={handleReply}
                  onDelete={handleDelete}
                  onReport={handleReport}
                />
              ))}
              
              {/* 加载更多 */}
              {hasMore && (
                <div className="p-4 text-center">
                  <button
                    onClick={loadMore}
                    disabled={loading}
                    className="px-6 py-3 bg-gray-700 hover:bg-gray-600 text-white rounded-md text-sm transition-colors disabled:opacity-50"
                  >
                    {loading ? (
                      <>
                        <FiLoader className="animate-spin inline mr-2" size={16} />
                        加载中...
                      </>
                    ) : (
                      '加载更多'
                    )}
                  </button>
                </div>
              )}
            </div>
          )}
        </div>
      </div>

      {/* 登录提示模态框 */}
      <LoginPromptModal
        isOpen={showLoginPrompt}
        onClose={() => setShowLoginPrompt(false)}
        actionType={loginPromptAction}
      />
    </>
  );
};

export default CommentsDrawer; 