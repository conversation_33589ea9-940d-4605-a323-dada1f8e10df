'use client';

import React, { useState, useEffect } from 'react';
import { FiMessageCircle } from 'react-icons/fi';
import { useClientTranslations } from '@/components/TranslationsProvider';

interface CommentsButtonProps {
  videoId: string;
  onClick: () => void;
  className?: string;
}

interface CommentsStats {
  totalComments: number;
  rootComments: number;
  replyComments: number;
}

const CommentsButton: React.FC<CommentsButtonProps> = ({
  videoId,
  onClick,
  className = ''
}) => {
  const { t } = useClientTranslations();
  const [stats, setStats] = useState<CommentsStats | null>(null);
  const [loading, setLoading] = useState(false);

  // API基础URL - 通过next.config.js rewrites配置代理
  const API_BASE = '';

  // 获取评论统计
  const fetchStats = async () => {
    if (!videoId || loading) return;
    
    try {
      setLoading(true);
      const response = await fetch(`${API_BASE}/api/comments/${videoId}/stats`);
      const data = await response.json();
      
      if (data.success) {
        setStats(data.data);
      } else {
        console.warn('获取评论统计失败:', data.message);
      }
    } catch (error) {
      console.error('获取评论统计失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 格式化数字显示
  const formatNumber = (num: number) => {
    if (num >= 1000) {
      return `${(num / 1000).toFixed(1)}k`;
    }
    return num.toString();
  };

  useEffect(() => {
    if (videoId) {
      fetchStats();
    }
  }, [videoId]);

  return (
    <button
      onClick={onClick}
      className={`
        group relative inline-flex items-center gap-2 px-4 py-2
        bg-gray-800/80 hover:bg-red-600/90 border border-gray-700 hover:border-red-500/50
        text-gray-300 hover:text-white rounded-lg transition-all duration-300
        backdrop-blur-sm shadow-lg hover:shadow-red-500/20 transform hover:scale-105
        btn-micro hover-lift font-medium overflow-hidden
        focus:outline-none focus:ring-2 focus:ring-red-500/50 focus:ring-offset-2 focus:ring-offset-gray-900
        ${className}
      `}
    >
      {/* 涟漪效果背景 */}
      <div className="absolute inset-0 bg-gradient-to-r from-red-500/0 via-red-500/5 to-red-500/0 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

      {/* 微光效果 */}
      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500 transform -skew-x-12 group-hover:translate-x-full"></div>

      {/* 图标 */}
      <FiMessageCircle
        size={18}
        className="relative z-10 transition-transform duration-300 group-hover:scale-110 group-hover:rotate-12"
      />

      {/* 文本和数量 */}
      <span className="relative z-10 text-sm font-medium">
        {t('video.comments')}
        {stats && stats.totalComments > 0 && (
          <span className="ml-1.5 px-1.5 py-0.5 bg-red-600/80 text-white text-xs rounded-full animate-pulse">
            {formatNumber(stats.totalComments)}
          </span>
        )}
      </span>

      {/* 加载指示器 */}
      {loading && (
        <div className="relative z-10 w-3 h-3 border border-gray-500 border-t-transparent rounded-full animate-spin" />
      )}
    </button>
  );
};

export default CommentsButton; 