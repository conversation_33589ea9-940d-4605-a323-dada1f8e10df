'use client';

import React, { useState, useRef, useEffect } from 'react';
import { FiSend, FiX } from 'react-icons/fi';
import { useDynamicHeightField } from '@/hooks/useDynamicHeightField';

interface CommentBoxProps {
  onSubmit: (content: string) => Promise<void>;
  placeholder?: string;
  submitButtonText?: string;
  replyTo?: {
    id: string;
    username: string;
  };
  onCancel?: () => void;
  autoFocus?: boolean;
  maxLength?: number;
}

const INITIAL_HEIGHT = 46;

const CommentBox: React.FC<CommentBoxProps> = ({
  onSubmit,
  placeholder = "写下你的想法...",
  submitButtonText = "发布",
  replyTo,
  onCancel,
  autoFocus = false,
  maxLength = 1000
}) => {
  const [isExpanded, setIsExpanded] = useState(autoFocus);
  const [commentValue, setCommentValue] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const outerHeight = useRef(INITIAL_HEIGHT);
  const textRef = useRef<HTMLTextAreaElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // 动态调整文本框高度
  useDynamicHeightField(textRef, commentValue);

  // 展开评论框
  const onExpand = () => {
    if (!isExpanded) {
      if (containerRef.current) {
        outerHeight.current = containerRef.current.scrollHeight;
      }
      setIsExpanded(true);
      
      // 延迟聚焦，确保展开动画开始后再聚焦
      setTimeout(() => {
        textRef.current?.focus();
      }, 100);
    }
  };

  // 输入变化处理
  const onChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setCommentValue(e.target.value);
  };

  // 取消评论
  const onClose = () => {
    setCommentValue('');
    setIsExpanded(false);
    if (onCancel) {
      onCancel();
    }
  };

  // 提交评论
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!commentValue.trim() || isSubmitting) return;

    setIsSubmitting(true);
    try {
      await onSubmit(commentValue.trim());
      setCommentValue('');
      setIsExpanded(false);
    } catch (error) {
      console.error('提交评论失败:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  // 处理键盘快捷键
  const handleKeyDown = (e: React.KeyboardEvent) => {
    // Ctrl/Cmd + Enter 提交
    if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
      e.preventDefault();
      handleSubmit(e as any);
    }
    // Escape 取消
    if (e.key === 'Escape' && !replyTo) {
      e.preventDefault();
      onClose();
    }
  };

  // 自动聚焦
  useEffect(() => {
    if (autoFocus && textRef.current) {
      setIsExpanded(true);
      textRef.current.focus();
    }
  }, [autoFocus]);

  return (
    <div className="w-full">
      <form onSubmit={handleSubmit}>
        <div
          ref={containerRef}
          className={`
            comment-box relative overflow-hidden rounded-lg border transition-all duration-300 ease-out
            ${isExpanded 
              ? 'border-red-500/50 bg-gray-900/80 backdrop-blur-sm shadow-lg shadow-red-500/10' 
              : 'border-gray-700 bg-gray-800/50 hover:border-gray-600'
            }
          `}
          style={{
            height: isExpanded ? 'auto' : `${INITIAL_HEIGHT}px`,
          }}
        >
          {/* 回复提示 */}
          {replyTo && (
            <div className="flex items-center justify-between bg-gray-800/30 px-4 py-2 border-b border-gray-700">
              <span className="text-sm text-gray-400">
                回复 <span className="text-red-400">@{replyTo.username}</span>
              </span>
              {onCancel && (
                <button
                  type="button"
                  onClick={onCancel}
                  className="text-gray-500 hover:text-white transition-colors"
                >
                  <FiX size={16} />
                </button>
              )}
            </div>
          )}

          {/* 评论输入区 */}
          <div 
            className={`
              comment-field transition-transform duration-300 ease-out
              ${isExpanded ? 'translate-y-0' : 'translate-y-0'}
            `}
          >
            <textarea
              ref={textRef}
              value={commentValue}
              onChange={onChange}
              onFocus={onExpand}
              onKeyDown={handleKeyDown}
              placeholder={placeholder}
              maxLength={maxLength}
              className={`
                w-full resize-none border-none bg-transparent text-white placeholder-gray-500 
                focus:outline-none transition-all duration-300
                ${isExpanded ? 'min-h-[100px] px-4 py-3' : 'h-[46px] px-4 py-3'}
              `}
              style={{
                lineHeight: '1.5',
              }}
            />
          </div>

          {/* 底部操作栏 */}
          {isExpanded && (
            <div className="flex items-center justify-between px-4 py-3 bg-gray-800/20 border-t border-gray-700">
              <div className="flex items-center gap-3">
                <span className="text-xs text-gray-500">
                  {commentValue.length}/{maxLength}
                </span>
                <span className="text-xs text-gray-500">
                  Ctrl+Enter 发布
                </span>
              </div>
              
              <div className="flex items-center gap-2">
                {!replyTo && (
                  <button
                    type="button"
                    onClick={onClose}
                    className="px-4 py-1.5 text-sm text-gray-400 hover:text-white transition-colors"
                  >
                    取消
                  </button>
                )}
                
                <button
                  type="submit"
                  disabled={!commentValue.trim() || isSubmitting}
                  className={`
                    flex items-center gap-2 px-4 py-1.5 text-sm font-medium rounded-md
                    transition-all duration-200
                    ${commentValue.trim() && !isSubmitting
                      ? 'bg-red-600 hover:bg-red-700 text-white shadow-md'
                      : 'bg-gray-700 text-gray-500 cursor-not-allowed'
                    }
                  `}
                >
                  {isSubmitting ? (
                    <>
                      <div className="w-4 h-4 border-2 border-gray-300 border-t-transparent rounded-full animate-spin" />
                      发布中...
                    </>
                  ) : (
                    <>
                      <FiSend size={14} />
                      {submitButtonText}
                    </>
                  )}
                </button>
              </div>
            </div>
          )}
        </div>
      </form>

      <style jsx>{`
        .comment-box.expanded .comment-field {
          transform: translateY(0px);
        }
      `}</style>
    </div>
  );
};

export default CommentBox; 