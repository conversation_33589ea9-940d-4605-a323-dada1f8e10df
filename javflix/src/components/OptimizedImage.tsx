'use client';

import React, { useState, useCallback } from 'react';
import Image from 'next/image';

interface OptimizedImageProps {
  src: string;
  alt: string;
  width?: number;
  height?: number;
  className?: string;
  priority?: boolean;
  fallbackSrc?: string;
  lazy?: boolean;
  placeholder?: 'blur' | 'empty';
  quality?: number;
}

/**
 * 🚀 高性能优化图片组件
 * 基于 Pieces.app 和 Sematext 的图片优化最佳实践
 */
const OptimizedImage: React.FC<OptimizedImageProps> = ({
  src,
  alt,
  width = 400,
  height = 300,
  className = '',
  priority = false,
  fallbackSrc = '/images/placeholder.jpg',
  lazy = true,
  placeholder = 'empty',
  quality = 75
}) => {
  const [imageSrc, setImageSrc] = useState(src);
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);

  // 🚀 图片加载成功处理
  const handleLoad = useCallback(() => {
    setIsLoading(false);
    setHasError(false);
  }, []);

  // 🚀 图片加载失败处理 - 降级策略
  const handleError = useCallback(() => {
    setHasError(true);
    setIsLoading(false);
    if (imageSrc !== fallbackSrc) {
      setImageSrc(fallbackSrc);
    }
  }, [imageSrc, fallbackSrc]);

  // 🚀 生成响应式图片尺寸
  const generateSizes = (width: number): string => {
    return `(max-width: 768px) ${Math.round(width * 0.8)}px, (max-width: 1200px) ${Math.round(width * 0.9)}px, ${width}px`;
  };

  return (
    <div 
      className={`relative overflow-hidden ${className}`}
      style={{ aspectRatio: `${width}/${height}` }}
    >
      {/* 🚀 加载状态占位符 */}
      {isLoading && (
        <div className="absolute inset-0 bg-gray-800/20 animate-pulse flex items-center justify-center">
          <div className="w-8 h-8 border-2 border-gray-600 border-t-white rounded-full animate-spin"></div>
        </div>
      )}
      
      {/* 🚀 错误状态占位符 */}
      {hasError && imageSrc === fallbackSrc && (
        <div className="absolute inset-0 bg-gray-800/40 flex flex-col items-center justify-center text-gray-400">
          <svg 
            className="w-8 h-8 mb-2" 
            fill="none" 
            stroke="currentColor" 
            viewBox="0 0 24 24"
          >
            <path 
              strokeLinecap="round" 
              strokeLinejoin="round" 
              strokeWidth={2} 
              d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" 
            />
          </svg>
          <span className="text-xs">图片加载失败</span>
        </div>
      )}

      {/* 🚀 优化的 Next.js Image 组件 */}
      <Image
        src={imageSrc}
        alt={alt}
        width={width}
        height={height}
        className={`
          transition-opacity duration-300 
          ${isLoading ? 'opacity-0' : 'opacity-100'}
          ${hasError ? 'opacity-60' : ''}
          object-cover w-full h-full
        `}
        onLoad={handleLoad}
        onError={handleError}
        priority={priority}
        loading={lazy ? 'lazy' : 'eager'}
        placeholder={placeholder}
        quality={quality}
        sizes={generateSizes(width)}
        // 🚀 WebP 格式优化
        unoptimized={false}
        // 🚀 响应式图片优化
        style={{
          objectFit: 'cover',
          objectPosition: 'center',
        }}
      />
      
      {/* 🚀 渐进式增强 - 加载完成淡入效果 */}
      <style jsx>{`
        @keyframes fadeIn {
          from { opacity: 0; }
          to { opacity: 1; }
        }
        
        .fade-in {
          animation: fadeIn 0.3s ease-in-out;
        }
      `}</style>
    </div>
  );
};

export default OptimizedImage;