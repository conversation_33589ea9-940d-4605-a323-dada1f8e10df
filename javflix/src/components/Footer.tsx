'use client';

import Link from 'next/link';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, FiMail } from 'react-icons/fi';
import { useClientTranslations } from './TranslationsProvider';
import { usePathname } from 'next/navigation';
import { locales } from '@/i18n';
import { Locale } from '@/i18n/types';

const Footer = () => {
  const currentYear = new Date().getFullYear();
  const { t } = useClientTranslations();
  const pathname = usePathname();
  
  // 获取当前语言前缀
  const getCurrentLocale = (): Locale => {
    // 从路径中提取语言前缀
    const pathSegments = pathname.split('/');
    if (pathSegments.length > 1) {
      const possibleLocale = pathSegments[1] as Locale;
      if (locales.includes(possibleLocale)) {
        return possibleLocale;
      }
    }
    // 如果没有找到有效的语言前缀，返回默认语言
    return 'zh-Hans';
  };
  
  // 当前语言
  const currentLocale = getCurrentLocale();
  
  // 生成带有语言前缀的URL
  const localizedUrl = (path: string): string => {
    // 如果路径已经以语言代码开头，直接返回
    if (locales.some(locale => path.startsWith(`/${locale}`))) {
      return path;
    }
    
    // 否则添加当前语言前缀
    return path.startsWith('/') ? `/${currentLocale}${path}` : `/${currentLocale}/${path}`;
  };
  
  return (
    <footer className="bg-black text-gray-400 mt-auto">
      <div className="container mx-auto px-4 py-8">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          <div>
              <Link href={localizedUrl('/')} className="flex items-center">
                <span className="text-xl font-bold text-red-600">JAV<span className="text-white">FLIX</span></span>
              </Link>
              <p className="mt-4 text-sm">
                {t('site.description')}
              </p>
          </div>
          
          <div>
            <h3 className="text-white font-medium mb-4">{t('footer.quickLinks')}</h3>
            <ul className="space-y-2">
              <li><Link href={localizedUrl('/new')} className="hover:text-white transition">{t('nav.new')}</Link></li>
              <li><Link href={localizedUrl('/popular')} className="hover:text-white transition">{t('nav.popular')}</Link></li>
              <li><Link href={localizedUrl('/category')} className="hover:text-white transition">{t('nav.categories')}</Link></li>
              <li><Link href={localizedUrl('/profile/favorites')} className="hover:text-white transition">{t('user.favorites')}</Link></li>
            </ul>
          </div>
          
          <div>
            <h3 className="text-white font-medium mb-4">{t('footer.help')}</h3>
            <ul className="space-y-2">
              <li><Link href={localizedUrl('/about')} className="hover:text-white transition">{t('footer.aboutUs')}</Link></li>
              <li><Link href={localizedUrl('/faq')} className="hover:text-white transition">{t('footer.faq')}</Link></li>
              <li><Link href={localizedUrl('/privacy')} className="hover:text-white transition">{t('footer.privacyPolicy')}</Link></li>
              <li><Link href={localizedUrl('/terms')} className="hover:text-white transition">{t('footer.termsOfService')}</Link></li>
              <li><Link href={localizedUrl('/dmca')} className="hover:text-white transition">{t('footer.dmca')}</Link></li>
            </ul>
          </div>
          
          <div>
            <h3 className="text-white font-medium mb-4">{t('footer.contactUs')}</h3>
            <div className="flex space-x-4 mb-4">
              <a href="https://github.com" target="_blank" rel="noopener noreferrer" className="hover:text-white transition">
                <FiGithub size={20} />
              </a>
              <a href="https://twitter.com" target="_blank" rel="noopener noreferrer" className="hover:text-white transition">
                <FiTwitter size={20} />
              </a>
              <a href="mailto:<EMAIL>" className="hover:text-white transition">
                <FiMail size={20} />
              </a>
            </div>
            <p className="text-sm">
              {t('footer.email')}: <EMAIL>
            </p>
          </div>
        </div>
        
        <div className="border-t border-gray-800 mt-8 pt-6 space-y-4">
          {/* DMCA and Legal Links */}
          <div className="flex flex-wrap justify-center gap-4 text-xs text-gray-500">
            <Link href="#" className="hover:text-white transition">
              {t('footer.dmcaNotice')}
            </Link>
            <span>|</span>
            <Link href={localizedUrl('/2257')} className="hover:text-white transition">
              {t('footer.section2257')} - {t('footer.section2257Desc')}
            </Link>
            <span>|</span>
            <span className="text-yellow-500">
              {t('footer.ageVerification')} - {t('footer.ageVerificationDesc')}
            </span>
          </div>
          
          {/* Copyright */}
          <div className="text-center text-sm">
            <p>{t('footer.copyright', { year: currentYear })}</p>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer; 