'use client';

import React, { useState, useRef, useEffect } from 'react';
import Link from 'next/link';
import { FiP<PERSON>, FiClock, FiEye, FiHeart, FiBookmark } from 'react-icons/fi';
import { formatDuration } from '@/lib/duration-format';
import { useMicroInteractions, useScrollAnimations, useAnimationPerformance } from '@/hooks/useEnhancedAnimations';

interface VideoCardProps {
  video: {
    id: string | number;
    title: string;
    code?: string;
    movie_id?: string;
    slug?: string;
    duration?: string;
    cover_image?: string;
    image_url?: string;
    cached_image_url?: string;
    thumbnail?: string;
    thumbnail_url?: string;
    views?: number;
    view_count?: number;
    timeAgo?: string;
    similarity_score?: number;
  };
  locale: string;
  showSimilarity?: boolean;
  size?: 'small' | 'medium' | 'large';
  className?: string;
  fusedStats?: {
    views: number;
    likes: number;
    favorites: number;
  };
  onCardClick?: (e: React.MouseEvent) => boolean;
  onDragStart?: (e: React.DragEvent) => void;
}

const VideoCard: React.FC<VideoCardProps> = ({
  video,
  locale,
  showSimilarity = false,
  size = 'medium',
  className = '',
  fusedStats,
  onCardClick,
  onDragStart
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [imageLoaded, setImageLoaded] = useState(false);
  const cardRef = useRef<HTMLDivElement>(null);

  // 使用增强动画hooks
  const { elementRef: microRef, createRippleEffect, hoverScale } = useMicroInteractions();
  const { observeElement } = useScrollAnimations();
  const { getOptimizedConfig, prefersReducedMotion } = useAnimationPerformance();

  // 懒加载和滚动动画逻辑
  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
          observer.disconnect();
        }
      },
      {
        threshold: 0.1,
        rootMargin: '50px' // 提前50px开始加载
      }
    );

    if (cardRef.current) {
      observer.observe(cardRef.current);

      // 添加滚动触发动画（如果不是减少动画模式）
      if (!prefersReducedMotion()) {
        const cleanup = observeElement(cardRef.current, 'fadeInUp', 0.1);
        return () => {
          observer.disconnect();
          cleanup?.();
        };
      }
    }

    return () => observer.disconnect();
  }, [observeElement, prefersReducedMotion]);

  // 设置微交互效果
  useEffect(() => {
    if (cardRef.current && microRef) {
      microRef.current = cardRef.current;

      if (!prefersReducedMotion()) {
        const cleanup = hoverScale(1.03);
        return cleanup;
      }
    }
  }, [microRef, hoverScale, prefersReducedMotion]);
  // 获取图片URL
  const getImageUrl = (): string => {
    if (video.cached_image_url) return video.cached_image_url;
    if (video.cover_image) return video.cover_image;
    if (video.image_url) return video.image_url;
    if (video.thumbnail) return video.thumbnail;
    if (video.thumbnail_url) return video.thumbnail_url;
    
    if (video.movie_id) {
      return `/api/image-proxy?type=cover&movie_id=${video.movie_id}`;
    }
    if (video.code) {
      return `/api/image-proxy?type=cover&movie_id=${video.code}`;
    }
    
    return '/images/defaults/cover_default.jpg';
  };

  // 时长格式化函数现在从lib/duration-format.ts导入

  // 构建视频链接
  const buildVideoLink = (): string => {
    const slug = video.slug || video.code || video.movie_id || video.id;
    return `/${locale}/video/${slug}`;
  };

  // 获取观看次数
  const getViewCount = (): number => {
    return fusedStats?.views || video.view_count || video.views || 0;
  };

  // 尺寸样式 - small使用362x192比例
  const sizeStyles = {
    small: {
      container: 'w-full',
      image: 'h-48' // 对应362x192比例，192px高度
    },
    medium: {
      container: 'w-48 md:w-56',
      image: 'h-40 md:h-48'
    },
    large: {
      container: 'w-56 md:w-64',
      image: 'h-44 md:h-52'
    }
  };

  const currentSize = sizeStyles[size];

  return (
    <div
      ref={cardRef}
      className={`flex-none ${currentSize.container} relative group ${className} gpu-accelerated`}
      onClick={!prefersReducedMotion() ? createRippleEffect : undefined}
    >
      <div className="flex flex-col space-y-2">
        <Link
          href={buildVideoLink()}
          className="block"
          onClick={onCardClick}
          onDragStart={onDragStart}
        >
          <div className="card-enhanced glass-effect hover:glass-effect-strong transition-all duration-500 group-hover:shadow-glow-red">
            <div className={`relative ${currentSize.image} overflow-hidden`}>
              {isVisible ? (
                <img
                  src={getImageUrl()}
                  alt={video.title}
                  className={`w-full h-full object-cover transition-all duration-500 group-hover:scale-110 ${
                    imageLoaded ? 'opacity-100' : 'opacity-0'
                  }`}
                  onLoad={() => setImageLoaded(true)}
                  onError={(e) => {
                    e.currentTarget.src = '/images/defaults/cover_default.jpg';
                    setImageLoaded(true);
                  }}
                  loading="lazy"
                />
              ) : (
                <div className="w-full h-full bg-gray-800 animate-pulse" />
              )}
              
              {/* 渐变遮罩 */}
              <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/20 to-transparent opacity-70 group-hover:opacity-50 transition-opacity duration-300"></div>
              
              {/* 时长标签 */}
              {video.duration && (
                <div className="absolute bottom-3 right-3 bg-black/80 backdrop-blur-sm text-white text-xs px-3 py-1.5 rounded-full border border-white/20">
                  <div className="flex items-center gap-1">
                    <FiClock size={10} />
                    {formatDuration(video.duration)}
                  </div>
                </div>
              )}
              
              {/* 播放按钮 */}
              <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-300">
                <div className="bg-white/20 backdrop-blur-sm text-white p-4 rounded-full transform scale-75 group-hover:scale-100 transition-transform duration-300">
                  <FiPlay size={24} className="ml-0.5" />
                </div>
              </div>

              {/* 相似度标签 (仅推荐视频显示) */}
              {showSimilarity && video.similarity_score && video.similarity_score > 0.7 && (
                <div className="absolute top-3 left-3 bg-gradient-to-r from-blue-500 to-purple-600 text-white px-2.5 py-1 rounded-full text-xs font-bold shadow-lg">
                  {Math.round(video.similarity_score * 100)}%匹配
                </div>
              )}


            </div>
          </div>
          
          {/* 视频信息 */}
          <div className="mt-3 px-1">
            <h3 className="font-normal line-clamp-1 text-gray-100 group-hover:text-white transition-colors duration-200 text-sm leading-relaxed">
              {video.title}
            </h3>
            
            <div className="flex items-center justify-between mt-2 text-xs text-gray-400 min-w-0">
              <div className="flex items-center gap-2 flex-shrink min-w-0">
                {video.timeAgo && (
                  <span className="flex-shrink-0">{video.timeAgo}</span>
                )}
                {video.timeAgo && getViewCount() > 0 && (
                  <span className="flex-shrink-0">•</span>
                )}
                {getViewCount() > 0 && (
                  <span className="flex items-center gap-1 realtime-stats flex-shrink-0">
                    <FiEye size={10} />
                    {getViewCount().toLocaleString()}
                  </span>
                )}
              </div>
              
              {/* 统计信息 */}
              {fusedStats && (fusedStats.likes > 0 || fusedStats.favorites > 0) && (
                <div className="flex items-center gap-1.5 flex-shrink-0 ml-2">
                  {fusedStats.likes > 0 && (
                    <span className="flex items-center gap-0.5 text-red-400">
                      <FiHeart size={10} />
                      {fusedStats.likes}
                    </span>
                  )}
                  {fusedStats.favorites > 0 && (
                    <span className="flex items-center gap-0.5 text-yellow-400">
                      <FiBookmark size={10} />
                      {fusedStats.favorites}
                    </span>
                  )}
                </div>
              )}
            </div>
          </div>
        </Link>
      </div>
    </div>
  );
};

export default VideoCard; 