'use client';

import React from 'react';
import { FiEye, FiHeart, FiBookmark, FiRefreshCw } from 'react-icons/fi';
import { useVideoStats } from '../hooks/useVideoStats';
import { VideoStats } from '../lib/socket-client';
import LoginPromptModal from '@/components/LoginPromptModal';

interface VideoStatsDisplayProps {
  videoId: string;
  initialStats?: VideoStats;
  enableRealtime?: boolean;
  showActions?: boolean;
  size?: 'small' | 'medium' | 'large';
  className?: string;
  autoIncrementViews?: boolean; // 新增：是否自动增加观看次数
}

const VideoStatsDisplay: React.FC<VideoStatsDisplayProps> = ({
  videoId,
  initialStats,
  enableRealtime = true,
  showActions = true,
  size = 'medium',
  className = '',
  autoIncrementViews = false,
}) => {
  const { stats, loading, error, actions, isConnected, userStatus } = useVideoStats({
    videoId,
    initialStats,
    enableRealtime,
  });

  // 格式化数字显示
  const formatNumber = (num: number): string => {
    if (num >= 1000000) {
      return `${(num / 1000000).toFixed(1)}M`;
    }
    if (num >= 1000) {
      return `${(num / 1000).toFixed(1)}K`;
    }
    return num.toString();
  };

  // 根据尺寸设置样式
  const getSizeClasses = () => {
    switch (size) {
      case 'small':
        return {
          container: 'text-xs gap-2',
          icon: 'w-3 h-3',
          button: 'px-2 py-1 text-xs',
        };
      case 'large':
        return {
          container: 'text-lg gap-4',
          icon: 'w-5 h-5',
          button: 'px-4 py-2 text-base',
        };
      default:
        return {
          container: 'text-sm gap-3',
          icon: 'w-4 h-4',
          button: 'px-3 py-1.5 text-sm',
        };
    }
  };

  const sizeClasses = getSizeClasses();

  // 检查用户是否登录
  const isLoggedIn = () => {
    if (typeof window === 'undefined') return false;
    const token = localStorage.getItem('token') || 
                 localStorage.getItem('auth_token') || 
                 localStorage.getItem('authToken');
    return !!token;
  };

  // 防止多次点击的状态
  const [actionLoading, setActionLoading] = React.useState<{[key: string]: boolean}>({});

  // 登录提示模态框状态
  const [showLoginPrompt, setShowLoginPrompt] = React.useState(false);
  const [loginPromptAction, setLoginPromptAction] = React.useState<'like' | 'favorite' | 'general'>('general');

  // 自动增加观看次数（仅一次）
  const viewsIncremented = React.useRef(false);
  React.useEffect(() => {
    if (typeof window === 'undefined') return;

    if (autoIncrementViews && !viewsIncremented.current) {
      viewsIncremented.current = true;

      // 检查本地存储，防止短时间内重复计数
      const lastViewKey = `lastView_${videoId}`;
      const lastViewTime = localStorage.getItem(lastViewKey);
      const now = Date.now();
      const fiveMinutes = 5 * 60 * 1000;

      if (!lastViewTime || (now - parseInt(lastViewTime)) > fiveMinutes) {
        // 延迟1秒确保组件完全加载
        setTimeout(() => {
          actions.incrementViews().then(() => {
            localStorage.setItem(lastViewKey, now.toString());
          }).catch((err) => {
            // 忽略自动增加观看次数错误
          });
        }, 1000);
      } else {
        // 5分钟内已观看过此视频，跳过自动观看次数增加
      }
    }
  }, [autoIncrementViews, videoId, actions]);

  // 🔥 处理Toggle操作（点赞/收藏）
  const handleToggleAction = async (actionType: 'like' | 'favorite') => {
    // 防止重复点击
    if (userStatus.isLikeLoading || userStatus.isFavoriteLoading) {
      return;
    }

    // 检查登录状态
    if (!isLoggedIn()) {
      setLoginPromptAction(actionType);
      setShowLoginPrompt(true);
      return;
    }

    try {
      switch (actionType) {
        case 'like':
          await actions.toggleLike();
          break;
        case 'favorite':
          await actions.toggleFavorite();
          break;
        default:
          return;
      }
    } catch (err: any) {
      if (err?.message?.includes('未登录') || err?.message?.includes('请先登录')) {
        setLoginPromptAction(actionType);
        setShowLoginPrompt(true);
      } else {
        // 保留其他错误的 alert，因为这些是技术错误
        alert(`操作失败: ${err?.message || '请稍后再试'}`);
      }
    }
  };

  return (
    <div className={`flex items-center ${sizeClasses.container} ${className}`}>
      {/* 观看数 - 只显示，不可点击 */}
      <div className="flex items-center text-gray-300" title="观看次数（自动统计）">
        <FiEye className={`${sizeClasses.icon} mr-1.5 text-gray-400`} />
        <span className={loading ? 'animate-pulse' : ''}>
          {formatNumber(stats.views)}
        </span>
      </div>

      {/* 点赞数 */}
      <div className="flex items-center">
        {showActions ? (
          <button
            onClick={() => handleToggleAction('like')}
            className={`flex items-center transition-colors duration-200 ${sizeClasses.button} rounded-full hover:bg-gray-800/50 ${
              userStatus.isLiked ? 'text-red-400' : 'text-gray-300 hover:text-red-400'
            } ${!isLoggedIn() ? 'opacity-60 cursor-not-allowed' : ''}`}
            disabled={loading || userStatus.isLikeLoading}
            title={!isLoggedIn() ? '登录后可点赞' : userStatus.isLikeLoading ? '处理中...' : userStatus.isLiked ? '取消点赞' : '点赞'}
          >
            <FiHeart className={`${sizeClasses.icon} mr-1.5 ${userStatus.isLiked ? 'fill-current' : ''}`} />
            <span className={loading ? 'animate-pulse' : ''}>
              {formatNumber(stats.likes)}
            </span>
          </button>
        ) : (
          <div className="flex items-center text-gray-300">
            <FiHeart className={`${sizeClasses.icon} mr-1.5 text-red-400`} />
            <span className={loading ? 'animate-pulse' : ''}>
              {formatNumber(stats.likes)}
            </span>
          </div>
        )}
      </div>

      {/* 收藏数 */}
      <div className="flex items-center">
        {showActions ? (
          <button
            onClick={() => handleToggleAction('favorite')}
            className={`flex items-center transition-colors duration-200 ${sizeClasses.button} rounded-full hover:bg-gray-800/50 ${
              userStatus.isFavorited ? 'text-yellow-400' : 'text-gray-300 hover:text-yellow-400'
            } ${!isLoggedIn() ? 'opacity-60 cursor-not-allowed' : ''}`}
            disabled={loading || userStatus.isFavoriteLoading}
            title={!isLoggedIn() ? '登录后可收藏' : userStatus.isFavoriteLoading ? '处理中...' : userStatus.isFavorited ? '取消收藏' : '收藏'}
          >
            <FiBookmark className={`${sizeClasses.icon} mr-1.5 ${userStatus.isFavorited ? 'fill-current' : ''}`} />
            <span className={loading ? 'animate-pulse' : ''}>
              {formatNumber(stats.favorites)}
            </span>
          </button>
        ) : (
          <div className="flex items-center text-gray-300">
            <FiBookmark className={`${sizeClasses.icon} mr-1.5 text-yellow-400`} />
            <span className={loading ? 'animate-pulse' : ''}>
              {formatNumber(stats.favorites)}
            </span>
          </div>
        )}
      </div>

      {/* 实时连接状态指示器 - 已移除，用户不需要看到技术细节 */}

      {/* 错误提示 */}
      {error && (
        <div className="ml-2 text-red-400 text-xs" title={error}>
          ⚠️
        </div>
      )}

      {/* 登录提示模态框 */}
      <LoginPromptModal
        isOpen={showLoginPrompt}
        onClose={() => setShowLoginPrompt(false)}
        actionType={loginPromptAction}
      />
    </div>
  );
};

export default VideoStatsDisplay; 