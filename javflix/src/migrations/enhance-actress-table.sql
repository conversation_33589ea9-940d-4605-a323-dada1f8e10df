-- 增强女优表字段，使其与javbus-api返回的数据一致
-- 2024-12-19: 添加女优详细信息字段

-- 添加缺失的字段
ALTER TABLE stars 
ADD COLUMN IF NOT EXISTS birthday VARCHAR(20),  -- 生日，格式：1990-08-14
ADD COLUMN IF NOT EXISTS age INTEGER,           -- 年龄
ADD COLUMN IF NOT EXISTS birthplace TEXT,       -- 出生地
ADD COLUMN IF NOT EXISTS hobby TEXT,            -- 爱好
ADD COLUMN IF NOT EXISTS javbus_id VARCHAR(50), -- javbus的女优ID
ADD COLUMN IF NOT EXISTS bust_size VARCHAR(10), -- 胸围，格式：88cm
ADD COLUMN IF NOT EXISTS waist_size VARCHAR(10), -- 腰围，格式：58cm  
ADD COLUMN IF NOT EXISTS hip_size VARCHAR(10);  -- 臀围，格式：86cm

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_stars_javbus_id ON stars(javbus_id);
CREATE INDEX IF NOT EXISTS idx_stars_name ON stars(name);
CREATE INDEX IF NOT EXISTS idx_stars_birthday ON stars(birthday);

-- 添加注释
COMMENT ON COLUMN stars.birthday IS '生日，格式：YYYY-MM-DD';
COMMENT ON COLUMN stars.age IS '年龄';
COMMENT ON COLUMN stars.birthplace IS '出生地';
COMMENT ON COLUMN stars.hobby IS '爱好';
COMMENT ON COLUMN stars.javbus_id IS 'javbus网站的女优ID';
COMMENT ON COLUMN stars.bust_size IS '胸围，格式：88cm';
COMMENT ON COLUMN stars.waist_size IS '腰围，格式：58cm';
COMMENT ON COLUMN stars.hip_size IS '臀围，格式：86cm';