-- 创建用户收藏表
CREATE TABLE IF NOT EXISTS user_favorites (
  id SERIAL PRIMARY KEY,
  user_id VARCHAR(255) NOT NULL,
  video_id VARCHAR(255) NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  UNIQUE(user_id, video_id)
);

-- 为收藏表添加索引
CREATE INDEX IF NOT EXISTS idx_user_favorites_user_id ON user_favorites(user_id);
CREATE INDEX IF NOT EXISTS idx_user_favorites_video_id ON user_favorites(video_id);

-- 创建用户观看历史表
CREATE TABLE IF NOT EXISTS user_history (
  id SERIAL PRIMARY KEY,
  user_id VARCHAR(255) NOT NULL,
  video_id VARCHAR(255) NOT NULL,
  progress INTEGER NOT NULL DEFAULT 0,
  is_completed BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  UNIQUE(user_id, video_id)
);

-- 为历史表添加索引
CREATE INDEX IF NOT EXISTS idx_user_history_user_id ON user_history(user_id);
CREATE INDEX IF NOT EXISTS idx_user_history_video_id ON user_history(video_id);

-- 创建关注女优表
CREATE TABLE IF NOT EXISTS user_followed_actresses (
  id SERIAL PRIMARY KEY,
  user_id VARCHAR(255) NOT NULL,
  actress_id INTEGER NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  UNIQUE(user_id, actress_id)
);

-- 为关注女优表添加索引
CREATE INDEX IF NOT EXISTS idx_user_followed_actresses_user_id ON user_followed_actresses(user_id);
CREATE INDEX IF NOT EXISTS idx_user_followed_actresses_actress_id ON user_followed_actresses(actress_id); 