// 检查用户收藏数据
const { Pool } = require('pg');

// 创建数据库连接池
const pool = new Pool({
  host: 'localhost',
  port: 5432,
  user: 'longgedemacminim4',
  password: '',
  database: 'javflix'
});

async function checkFavorites() {
  const client = await pool.connect();
  
  try {
    // 测试用户ID
    const userId = 'test-user-123';
    
    console.log(`检查用户 ${userId} 的收藏...`);
    
    // 查询用户收藏
    const favoritesResult = await client.query(
      'SELECT * FROM user_favorites WHERE user_id = $1',
      [userId]
    );
    
    console.log(`找到 ${favoritesResult.rowCount} 条收藏记录:`);
    console.log(JSON.stringify(favoritesResult.rows, null, 2));
    
    // 查询电影表
    console.log('\n检查movies表...');
    const moviesResult = await client.query(
      'SELECT * FROM movies LIMIT 5'
    );
    
    console.log(`找到 ${moviesResult.rowCount} 条电影记录:`);
    console.log(JSON.stringify(moviesResult.rows, null, 2));
    
    // 测试JOIN查询
    console.log('\n测试JOIN查询...');
    const joinResult = await client.query(`
      SELECT f.*, m.title, m.movie_id as code
      FROM user_favorites f
      JOIN movies m ON f.video_id = m.movie_id
      WHERE f.user_id = $1
      LIMIT 5
    `, [userId]);
    
    console.log(`JOIN查询结果 (${joinResult.rowCount} 条):`);
    console.log(JSON.stringify(joinResult.rows, null, 2));
    
    console.log('\n检查完成！');
  } catch (error) {
    console.error('查询数据库出错:', error);
  } finally {
    client.release();
    await pool.end();
  }
}

// 运行函数
checkFavorites(); 