// 为movies表添加cached_image_url字段
const { Pool } = require('pg');

async function main() {
  try {
    // 使用与项目相同的数据库配置创建连接
    const pool = new Pool({
      host: process.env.PGHOST || 'localhost',
      port: parseInt(process.env.PGPORT || '5432'),
      database: process.env.PGDATABASE || 'javflix',
      user: process.env.PGUSER || 'longgedemacminim4',
      password: process.env.PGPASSWORD || '',
    });
    
    console.log('连接到数据库...');
    
    // 执行ALTER TABLE语句
    await pool.query(`
      ALTER TABLE movies ADD COLUMN IF NOT EXISTS cached_image_url TEXT;
      CREATE INDEX IF NOT EXISTS idx_movies_cached_image_url ON movies(cached_image_url);
    `);
    
    console.log('电影表迁移成功完成！');
    
    // 关闭连接
    await pool.end();
  } catch (error) {
    console.error('数据库迁移失败:', error);
    process.exit(1);
  }
}

main(); 