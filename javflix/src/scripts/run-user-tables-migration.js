// 运行用户表数据库迁移脚本
const fs = require('fs');
const path = require('path');
const { Pool } = require('pg');

// 创建数据库连接池，指定连接参数
const pool = new Pool({
  host: 'localhost',
  port: 5432,
  user: 'longgedemacminim4',
  password: '',
  database: 'javflix'
});

// 迁移脚本路径
const migrationPath = path.join(__dirname, '../migrations/create-user-tables.sql');

// 运行迁移
async function runMigration() {
  let client;
  
  try {
    // 检查迁移文件是否存在
    if (!fs.existsSync(migrationPath)) {
      throw new Error(`迁移文件不存在: ${migrationPath}`);
    }
    
    // 读取SQL文件
    const sql = fs.readFileSync(migrationPath, 'utf8');
    
    console.log('正在运行用户表迁移...');
    console.log('使用的数据库连接参数:', {
      host: 'localhost',
      port: 5432,
      database: 'javflix'
    });
    
    // 获取连接
    client = await pool.connect();
    
    // 开始事务
    await client.query('BEGIN');
    
    // 执行SQL
    await client.query(sql);
    
    // 提交事务
    await client.query('COMMIT');
    
    console.log('用户表迁移成功完成！');
  } catch (error) {
    // 发生错误时回滚
    if (client) {
      await client.query('ROLLBACK');
    }
    console.error('迁移失败:', error);
  } finally {
    // 释放连接
    if (client) {
      client.release();
    }
    
    // 关闭连接池
    await pool.end();
  }
}

// 运行迁移
runMigration(); 