// 运行数据库迁移脚本
const fs = require('fs');
const path = require('path');
const { Pool } = require('pg');

// 创建数据库连接池
const pool = new Pool();

// 迁移脚本路径
const migrationPath = path.join(__dirname, '../migrations/add-cached-image-url.sql');

// 运行迁移
async function runMigration() {
  let client;
  
  try {
    // 读取SQL文件
    const sql = fs.readFileSync(migrationPath, 'utf8');
    
    // 获取连接
    client = await pool.connect();
    
    // 开始事务
    await client.query('BEGIN');
    
    // 执行SQL
    await client.query(sql);
    
    // 提交事务
    await client.query('COMMIT');
    
    console.log('迁移成功完成！');
  } catch (error) {
    // 发生错误时回滚
    if (client) {
      await client.query('ROLLBACK');
    }
    console.error('迁移失败:', error);
  } finally {
    // 释放连接
    if (client) {
      client.release();
    }
    
    // 关闭连接池
    await pool.end();
  }
}

// 运行迁移
runMigration(); 