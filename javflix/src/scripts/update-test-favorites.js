// 更新测试收藏数据，使用数据库中存在的movie_id
const { Pool } = require('pg');

// 创建数据库连接池
const pool = new Pool({
  host: 'localhost',
  port: 5432,
  user: 'longgedemacminim4',
  password: '',
  database: 'javflix'
});

async function updateTestFavorites() {
  const client = await pool.connect();
  
  try {
    // 开始事务
    await client.query('BEGIN');
    
    // 测试用户ID
    const userId = 'test-user-123';
    
    // 先获取数据库中存在的电影ID
    const moviesResult = await client.query(
      'SELECT movie_id FROM movies LIMIT 5'
    );
    
    if (moviesResult.rowCount === 0) {
      console.log('数据库中没有电影数据，无法添加收藏。');
      await client.query('ROLLBACK');
      return;
    }
    
    // 使用数据库中实际存在的电影ID
    const movieIds = moviesResult.rows.map(row => row.movie_id);
    console.log('找到的电影ID:', movieIds);
    
    // 清除当前测试用户的收藏
    await client.query('DELETE FROM user_favorites WHERE user_id = $1', [userId]);
    
    // 添加收藏
    console.log('添加测试收藏数据...');
    for (const movieId of movieIds) {
      await client.query(`
        INSERT INTO user_favorites (user_id, video_id, created_at)
        VALUES ($1, $2, NOW())
      `, [userId, movieId]);
      console.log(`添加收藏: ${movieId}`);
    }
    
    // 提交事务
    await client.query('COMMIT');
    
    console.log('测试数据更新成功！');
  } catch (error) {
    // 出错时回滚
    await client.query('ROLLBACK');
    console.error('更新测试数据失败:', error);
  } finally {
    // 释放客户端
    client.release();
    // 关闭连接池
    await pool.end();
  }
}

// 运行函数
updateTestFavorites(); 