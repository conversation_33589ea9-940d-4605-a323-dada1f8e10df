#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { Pool } = require('pg');
const readline = require('readline');

// 创建数据库连接池
const pool = new Pool({
  host: process.env.PGHOST || 'localhost',
  port: parseInt(process.env.PGPORT || '5432'),
  database: process.env.PGDATABASE || 'javflix',
  user: process.env.PGUSER || 'longgedemacminim4',
  password: process.env.PGPASSWORD || '',
});

// 图片缓存目录
const CACHE_DIR = path.join(process.cwd(), 'public', 'images', 'javbus');

// 创建readline接口用于用户确认
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// 清空数据库表
async function cleanDatabase() {
  const client = await pool.connect();
  
  try {
    // 开始事务
    await client.query('BEGIN');
    
    console.log('开始清空数据库表...');
    
    // 删除依赖关系的表数据
    await client.query('DELETE FROM movie_stars');
    console.log('- 已清空 movie_stars 表');
    
    await client.query('DELETE FROM movie_genres');
    console.log('- 已清空 movie_genres 表');
    
    await client.query('DELETE FROM movie_directors');
    console.log('- 已清空 movie_directors 表');
    
    await client.query('DELETE FROM magnets');
    console.log('- 已清空 magnets 表');
    
    await client.query('DELETE FROM samples');
    console.log('- 已清空 samples 表');
    
    await client.query('DELETE FROM similar_movies');
    console.log('- 已清空 similar_movies 表');
    
    // 清空主要表
    await client.query('DELETE FROM movies');
    console.log('- 已清空 movies 表');
    
    // 提交事务
    await client.query('COMMIT');
    console.log('数据库清空成功!');
  } catch (error) {
    // 回滚事务
    await client.query('ROLLBACK');
    console.error('清空数据库失败:', error);
    throw error;
  } finally {
    // 释放客户端
    client.release();
  }
}

// 清空图片缓存
function cleanImageCache() {
  console.log('开始清空图片缓存...');
  
  // 检查缓存目录是否存在
  if (!fs.existsSync(CACHE_DIR)) {
    console.log('缓存目录不存在:', CACHE_DIR);
    return;
  }
  
  // 清空封面图片
  const coverDir = path.join(CACHE_DIR, 'cover');
  if (fs.existsSync(coverDir)) {
    const files = fs.readdirSync(coverDir);
    console.log(`发现 ${files.length} 个封面缓存图片`);
    
    files.forEach(file => {
      fs.unlinkSync(path.join(coverDir, file));
    });
    console.log(`- 已删除所有封面缓存图片`);
  }
  
  // 清空女优图片
  const actressDir = path.join(CACHE_DIR, 'actress');
  if (fs.existsSync(actressDir)) {
    const files = fs.readdirSync(actressDir);
    console.log(`发现 ${files.length} 个女优缓存图片`);
    
    files.forEach(file => {
      fs.unlinkSync(path.join(actressDir, file));
    });
    console.log(`- 已删除所有女优缓存图片`);
  }
  
  // 清空样本图片
  const sampleDir = path.join(CACHE_DIR, 'sample');
  if (fs.existsSync(sampleDir)) {
    const files = fs.readdirSync(sampleDir);
    console.log(`发现 ${files.length} 个样本缓存图片`);
    
    files.forEach(file => {
      fs.unlinkSync(path.join(sampleDir, file));
    });
    console.log(`- 已删除所有样本缓存图片`);
  }
  
  console.log('图片缓存清空成功!');
}

// 主函数
async function main() {
  console.log('=== 数据库和图片缓存清理工具 ===');
  console.log('警告: 此操作将删除所有影片数据和缓存图片!');
  
  rl.question('确定要继续吗? (y/n): ', async (answer) => {
    if (answer.toLowerCase() === 'y') {
      try {
        await cleanDatabase();
        cleanImageCache();
        console.log('=== 清理完成 ===');
      } catch (error) {
        console.error('清理过程中出错:', error);
      } finally {
        // 关闭数据库连接池
        await pool.end();
        rl.close();
      }
    } else {
      console.log('操作已取消');
      rl.close();
      await pool.end();
    }
  });
}

// 执行主函数
main().catch(console.error); 