// 添加测试数据到收藏表
const { Pool } = require('pg');

// 创建数据库连接池
const pool = new Pool({
  host: 'localhost',
  port: 5432,
  user: 'longgedemacminim4',
  password: '',
  database: 'javflix'
});

async function addTestData() {
  const client = await pool.connect();
  
  try {
    // 开始事务
    await client.query('BEGIN');
    
    // 测试用户ID
    const userId = 'test-user-123';
    
    // 添加几个测试收藏
    const testFavorites = [
      { videoId: 'ipx-789', title: 'IPX-789 激情四射的完美演出' },
      { videoId: 'ssis-123', title: 'SSIS-123 绝美女优的激情表演' },
      { videoId: 'mide-456', title: 'MIDE-456 性感女神的极致魅力' }
    ];
    
    // 查询是否有测试电影
    const moviesExist = await client.query('SELECT COUNT(*) FROM movies LIMIT 1');
    const hasMovies = parseInt(moviesExist.rows[0].count) > 0;
    
    // 如果没有电影数据，先添加测试电影
    if (!hasMovies) {
      console.log('添加测试电影数据...');
      
      for (const fav of testFavorites) {
        await client.query(`
          INSERT INTO movies (movie_id, title, image_url, duration, created_at, updated_at)
          VALUES ($1, $2, $3, $4, NOW(), NOW())
          ON CONFLICT (movie_id) DO NOTHING
        `, [fav.videoId, fav.title, '/images/thumbnails/' + fav.videoId + '.jpg', '02:15:30']);
      }
    }
    
    // 清除当前测试用户的收藏
    await client.query('DELETE FROM user_favorites WHERE user_id = $1', [userId]);
    
    // 添加收藏
    console.log('添加测试收藏数据...');
    for (const fav of testFavorites) {
      await client.query(`
        INSERT INTO user_favorites (user_id, video_id, created_at)
        VALUES ($1, $2, NOW())
      `, [userId, fav.videoId]);
    }
    
    // 提交事务
    await client.query('COMMIT');
    
    console.log('测试数据添加成功！');
  } catch (error) {
    // 出错时回滚
    await client.query('ROLLBACK');
    console.error('添加测试数据失败:', error);
  } finally {
    // 释放客户端
    client.release();
    // 关闭连接池
    await pool.end();
  }
}

// 运行函数
addTestData(); 