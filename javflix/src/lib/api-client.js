import axios from 'axios';

/**
 * API客户端
 * 用于与后端API通信，集中处理请求配置和错误处理
 */

// API基础URL配置
const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:4000/api';

// 创建axios实例
const apiClient = axios.create({
  baseURL: API_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器 - 添加认证令牌
apiClient.interceptors.request.use(
  (config) => {
    // 如果在浏览器环境，尝试从本地存储获取令牌
    if (typeof window !== 'undefined') {
      const token = localStorage.getItem('auth_token');
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
    }
    return config;
  },
  (error) => Promise.reject(error)
);

// 响应拦截器 - 处理常见错误
apiClient.interceptors.response.use(
  (response) => {
    // 直接返回API的data部分
    return response.data;
  },
  (error) => {
    // 统一处理错误
    const errorResponse = {
      success: false,
      message: '请求失败',
      code: 500,
    };
    
    if (error.response) {
      // 服务器返回了错误状态码
      errorResponse.message = error.response.data.message || '服务器错误';
      errorResponse.code = error.response.status;
      
      // 处理401未授权错误，可能需要重新登录
      if (error.response.status === 401) {
        // 在浏览器端清除失效的认证信息
        if (typeof window !== 'undefined') {
          localStorage.removeItem('auth_token');
          localStorage.removeItem('user');
          // 可以添加重定向到登录页
          // window.location.href = '/auth/login';
        }
      }
    } else if (error.request) {
      // 请求已发送但未收到响应
      errorResponse.message = '无法连接到服务器，请检查您的网络连接';
    }
    
    return Promise.reject(errorResponse);
  }
);

// API调用函数
const api = {
  // 视频相关（已发布影片）
  videos: {
    // 获取已发布视频列表
    getAll: (page = 1, limit = 20) => apiClient.get(`/published-videos?page=${page}&limit=${limit}`),

    // 获取热门已发布视频
    getPopular: (limit = 10) => apiClient.get(`/published-videos/popular?limit=${limit}`),

    // 获取最新已发布视频
    getRecent: (limit = 10) => apiClient.get(`/published-videos/recent?limit=${limit}`),

    // 获取已发布视频详情(通过ID)
    getById: (id) => apiClient.get(`/published-videos/${id}`),

    // 获取已发布视频详情(通过Slug)
    getBySlug: (slug) => apiClient.get(`/published-videos/slug/${slug}`),

    // 搜索已发布视频
    search: (query, page = 1, limit = 20) => apiClient.get(`/published-videos/search?q=${encodeURIComponent(query)}&page=${page}&limit=${limit}`),
  },
  
  // 用户相关
  users: {
    // 用户注册
    register: (userData) => apiClient.post('/users/register', userData),
    
    // 用户登录
    login: (credentials) => apiClient.post('/users/login', credentials),
    
    // 获取当前用户资料
    getProfile: () => apiClient.get('/users/profile'),
    
    // 更新用户资料
    updateProfile: (userData) => apiClient.put('/users/profile', userData),
    
    // 获取收藏列表
    getFavorites: (page = 1, limit = 20) => apiClient.get(`/users/favorites?page=${page}&limit=${limit}`),
    
    // 添加收藏
    addFavorite: (videoId) => apiClient.post(`/users/favorites/${videoId}`),
    
    // 取消收藏
    removeFavorite: (videoId) => apiClient.delete(`/users/favorites/${videoId}`),
  },
};

export default api; 