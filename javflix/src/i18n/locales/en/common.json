{"site": {"title": "JAVFLIX.TV - Japanese Adult Videos Online", "description": "JAVFLIX.TV offers high-quality Japanese adult videos online, ad-free, HD streaming"}, "pages": {"new": {"title": "New Releases", "description": "Browse JAVFLIX's latest Japanese adult videos, updated daily, offering subtitled, uncensored, and censored HD content"}, "popular": {"title": "Popular Videos", "description": "JAVFLIX's most popular Japanese adult videos collection, high definition quality, stable viewing experience, featuring top-rated content", "weekly": "Weekly Popular", "monthly": "Monthly Popular", "yearly": "Yearly Popular", "hdPopular": "HD Popular", "4kPopular": "4K Popular", "newcomers": "Newcomers Popular"}, "recent": {"title": "Recently Updated", "description": "Check out JAVFLIX's recently updated Japanese adult videos, get the latest resources, high quality videos, and smooth playback experience"}, "category": {"title": "Browse Categories", "description": "JAVFLIX video category navigation, offering a rich variety of Japanese adult videos, organized by plot, scene, costume, series and more"}, "actress": {"title": "Actress Directory", "description": "JAVFLIX actress database featuring comprehensive profiles of Japanese adult video actresses, including detailed bios, filmography, and stats"}, "profile": {"title": "User Profile", "description": "Manage your JAVFLIX profile, favorites, watch history and account settings"}, "about": {"title": "About Us", "description": "Learn about JAVFLIX.TV's mission, vision and services, providing you with the best adult video experience"}, "faq": {"title": "FAQ", "description": "JAVFLIX.TV frequently asked questions to help you better use our platform services"}, "privacy": {"title": "Privacy Policy", "description": "JAVFLIX.TV privacy policy details to protect your personal information and user experience"}, "terms": {"title": "Terms of Service", "description": "JAVFLIX.TV terms of service and user agreement to ensure fair and safe service environment for all users"}}, "nav": {"home": "Home", "new": "New Videos", "popular": "Popular", "recent": "Recent", "categories": "Categories", "actresses": "Actresses", "search": "Search", "profile": "Profile", "login": "<PERSON><PERSON>", "signup": "Sign Up", "logout": "Logout"}, "common": {"viewMore": "View More", "relatedVideos": "Related Videos", "moreVideos": "More Videos", "recommendations": "Recommendations", "recommendedVideos": "Recommended For You", "forYou": "For You", "discoveredContent": "Discover popular content", "discoveredActresses": "Discovered {{count}} actresses", "discoveredVideos": "Discovered {{count}} popular videos", "searchActress": "Search actresses...", "searchButton": "Search", "sortBy": "Sort by", "sortByViews": "Sort by views", "sortByLikes": "Sort by likes", "sortByDate": "Sort by date", "videosCount": " videos", "tenThousand": "w", "thousand": "k", "failedToLoadVideos": "Failed to load videos", "errorLoadingVideos": "Error loading videos", "noResults": "No results found", "loading": "Loading...", "watchNow": "Watch Now", "addToFavorites": "Add to Favorites", "removeFromFavorites": "Remove from Favorites", "published": "Published on", "weekly": "Weekly", "monthly": "Monthly", "yearly": "Yearly", "newcomers": "Newcomers", "videos": "Videos", "popular": "Popular", "new": "New", "recent": "Recent", "nextVideo": "Next Video", "previousVideo": "Previous Video", "switchToVideo": "Switch to Video", "moreRecommendations": "More Recommendations", "recommendedVideo": "Recommended Video", "recommendedPopular": "Recommended Popular", "scrollLeft": "<PERSON><PERSON> Left", "scrollRight": "Scroll Right", "pageInfo": "Page {{current}} of {{total}}", "loadingFailed": "Loading failed", "retry": "Retry", "noNewVideos": "No new videos available", "comingSoon": "Exciting content coming soon", "noDescription": "No description available", "unknown": "Unknown", "backToHome": "Back to Home", "language": "Language", "ageUnit": "years old", "cancel": "Cancel", "confirm": "Confirm", "loadMore": "Load More", "loadingMore": "Loading more...", "endOfList": "End of List", "pageOf": "Page {{current}} of {{total}}", "or": "or", "player": {"play": "Play", "pause": "Pause", "mute": "Mute", "unmute": "Unmute", "enterFullscreen": "Enter Fullscreen", "exitFullscreen": "Exit Fullscreen", "settings": "Settings", "speed": "Speed", "normal": "Normal", "quality": "Quality", "browserNotSupported": "Your browser does not support the video tag.", "initializingPlayer": "Initializing player...", "loadingVideo": "Loading video...", "ready": "Ready!", "loadingFailed": "Loading failed, please refresh to retry"}, "videoLoading": "Loading video...", "browserNotSupported": "Your browser does not support HTML5 video. Please upgrade your browser.", "failedToLoadVideo": "Failed to load video data", "loadVideoError": "Error loading video", "dataLoadError": "Failed to load data", "moreActors": "more actors"}, "video": {"releaseDate": "Release Date", "duration": "Duration", "studio": "Studio", "series": "Series", "code": "Code", "addToFavorites": "Add to Favorites", "removeFromFavorites": "Remove from Favorites", "share": "Share", "quality": {"label": "Quality", "auto": "Auto", "hd": "HD", "sd": "SD", "original": "Original"}, "subtitles": "Subtitles", "views": "Views", "likes": "<PERSON>s", "comments": "Comments", "similarVideos": "Similar Videos", "download": {"label": "Download", "title": "Download Video", "preparing": "Preparing download...", "failed": "Download failed", "complete": "Download complete", "start": "Start download", "cancel": "Cancel download"}, "featuredVideos": "Featured Videos", "category": "Category", "categories": "Categories", "producer": "Producer", "bookmark": "Bookmark", "magnetDownload": "Magnet Download", "watchCount": "views", "actors": "Actors", "moreActors": "more actors", "studioLabel": "Studio", "categoryLabel": "Categories", "magnetLink": "Magnet <PERSON>", "copyMagnet": "Copy Magnet Link", "tags": "Tags", "uncensored": "Uncensored", "description": "Description", "relatedVideos": "Related Videos", "loading": "Loading Video", "loadingDetails": "Getting video details...", "loadError": "Unable to load video", "notFound": "Video not found or has been removed", "backToHome": "Back to Home", "durationLabel": "Duration", "releaseDateLabel": "Release", "uploadDateLabel": "Upload", "studioInfo": "Studio", "director": "Director", "publisher": "Publisher", "linkCopied": "Link copied to clipboard", "noRelatedVideos": "No related recommendations", "findingContent": "System is finding related content for you", "searchingRelated": "System is finding related content for you", "uploadDate": "Upload", "detailedInfo": "Detailed Information", "previewImages": "Preview Images", "previewImage": "Preview image", "dataLoadError": "Failed to load data", "playback": {"error": "Playback error", "retry": "Retry", "buffering": "Buffering...", "notAvailable": "Video not available"}, "relatedMovies": "Related Videos"}, "actress": {"videos": "Videos", "followers": "Followers", "age": "Age", "birthdate": "Birthdate", "height": "Height", "measurements": "Measurements", "bloodType": "Blood Type", "popular": "Popular Actresses", "newcomer": "New Actresses", "ranking": "Actress Ranking", "byAge": "By Age", "byBody": "By Body", "byAlphabet": "By Alphabet", "actresses": "actresses", "searchPlaceholder": "Search actress names...", "sortByVideosDesc": "Video Count ↓", "sortByVideosAsc": "Video Count ↑", "sortByNameAsc": "Name A-Z", "sortByNameDesc": "Name Z-A", "sortByNewest": "Newest Added", "sortByOldest": "Oldest Added", "noActressesFound": "No actresses found", "tryAdjustSearch": "Try adjusting your search criteria", "loadMore": "<PERSON><PERSON>es", "loadError": "Failed to load actress information", "backToList": "Back to Actress List", "birthplace": "Birthplace", "debutDate": "Debut Date", "bust": "Bust", "waist": "Waist", "hip": "Hip", "cupSize": "Cup Size", "hobby": "Hobbies", "relatedVideos": "Related Videos", "noVideos": "No videos found", "sortByPopularity": "Popularity ↓"}, "category": {"allCategories": "All Categories", "allTags": "All Tags", "popular": "Popular Categories", "videosCount": "Video Count", "byStory": "By Story", "byScene": "By Scene", "byCostume": "By Costume", "bySeries": "By Series", "byProducer": "By Producer", "byDate": "By Release Date"}, "search": {"placeholder": "Search videos, actresses or categories...", "noResults": "No results found", "trending": "Trending", "recentSearches": "Recent Searches", "search": "Search", "advancedSearch": "Advanced Search", "filters": "Filters", "clearFilters": "Clear Filters", "searching": "Searching", "noSuggestions": "No suggestions found", "tryAdjustSearch": "Try adjusting search criteria", "sortBy": "Sort by", "relevance": "Relevance", "views": "Views", "recent": "Recently Published", "oldest": "Oldest First", "searchResults": "Search results for \"{{query}}\"", "foundResults": "Found {{count}} results", "category": "Category", "duration": "Duration", "uploadTime": "Upload Time", "quality": "Quality", "actress": "Actress", "time": "Time", "clearAll": "Clear All Filters", "advanced": {"title": "Advanced Search", "duration": "Duration", "quality": "Quality", "category": "Category", "actress": "Actress", "studio": "Studio", "releaseDate": "Release Date", "apply": "Apply Filters", "reset": "Reset"}, "history": {"title": "Search History", "clear": "Clear History", "empty": "No search history", "remove": "Remove"}, "sort": {"title": "Sort By", "relevance": "Relevance", "newest": "Newest First", "oldest": "Oldest First", "popular": "Most Popular", "duration": "Duration"}, "noResultsDesc": "Try using different keywords or browse our categories", "browseCategories": "Browse All Categories", "results": "results", "allCategories": "All Categories", "drama": "Drama", "uniform": "Uniform", "schoolgirl": "Student", "mature": "Mature", "idol": "Idol", "cosplay": "Cosplay", "outdoor": "Outdoor", "allDurations": "All Durations", "short": "Short (< 30 min)", "medium": "Medium (30-60 min)", "long": "Long (60-120 min)", "movie": "Movie (> 120 min)", "allTimes": "All Times", "today": "Today", "week": "This Week", "month": "This Month", "year": "This Year", "allQualities": "All Qualities", "hd": "HD 720P", "fullhd": "Full HD 1080P", "4k": "Ultra HD 4K"}, "tags": {"categories": {"quality": "Video Quality", "genre": "Content Type", "actress": "Actress Type", "bodytype": "Body Features", "actions": "Content Actions", "multiplayer": "Multiple Performers", "props": "Props & Toys", "scenes": "Scene Settings", "others": "Other Categories"}, "searchPlaceholder": "Search tags...", "trending": "Trending Tags", "searchResults": "Search Results", "noResults": "No matching tags found", "clothing": "Clothing", "body": "Body", "action": "Action", "story": "Story", "role": "Role", "location": "Location", "misc": "Miscellaneous", "selection": "Selection", "allTags": "All Tags", "popularTags": "Popular Tags", "relatedTags": "Related Tags", "searchTags": "Search Tags", "noTags": "No tags found"}, "user": {"profile": "Profile", "favorites": "Favorites", "history": "Watch History", "settings": "Settings", "membership": "Membership", "changePassword": "Change Password", "deleteAccount": "Delete Account"}, "profile": {"tabs": {"favorites": "My Favorites", "history": "Watch History", "following": "Following", "recommendations": "Recommendations", "settings": "Settings"}, "following": "Following", "recommendations": "Recommendations", "settings": {"title": "Settings", "subtitle": "Customize your experience", "appearance": "Appearance", "theme": "Theme", "light": "Light", "dark": "Dark", "language": "Language", "playback": "Playback", "autoplay": "Autoplay", "subtitles": "Default Subtitles", "quality": "Default Video Quality", "notifications": "Notifications", "enable_notifications": "Enable Notifications"}, "time": {"justNow": "Just now", "minutesAgo": "{{minutes}} minutes ago", "hoursAgo": "{{hours}} hours ago", "daysAgo": "{{days}} days ago", "monthsAgo": "{{months}} months ago", "yearsAgo": "{{years}} years ago"}, "myFavorites": "My Favorites", "watchHistory": "Watch History", "accountSettings": "Account <PERSON><PERSON>", "noFavorites": "You haven't added any videos to favorites yet", "noWatchHistory": "You haven't watched any videos yet", "browseVideos": "Browse Videos", "followedActresses": "Followed Actresses", "noFollowedActresses": "You haven't followed any actresses yet", "browseActresses": "Browse Actresses", "followMore": "Follow More", "clearHistory": "Clear History", "watched": "Watched", "completed": "Completed", "continueWatching": "Continue Watching", "basicInfo": "Basic Information", "editProfile": "Edit Profile", "securitySettings": "Security Settings", "password": "Password", "lastUpdated": "Last Updated", "monthsAgo": "{{months}} months ago", "changePassword": "Change Password", "twoFactor": "Two-Factor Authentication", "enhanceSecurity": "Enhance account security", "enable": "Enable", "notificationSettings": "Notification Settings", "webUpdates": "Website Updates", "webUpdatesDesc": "Receive notifications about new features, updates and maintenance", "actressUpdates": "Actress Updates", "actressUpdatesDesc": "Get notified when followed actresses have new releases", "marketingEmails": "Marketing Emails", "marketingEmailsDesc": "Receive promotional and special offer information", "loggingOut": "Logging out...", "goToLogin": "Go to Login", "joinedOn": "Joined on", "watchedCount": "Watched {{count}} videos", "favoriteCount": "Favorited {{count}} videos", "unfollow": "Unfollow", "unfollowConfirm": "Are you sure you want to unfollow this actress?", "unfollowSuccess": "Unfollowed successfully", "personalizedRecommendations": "Personalized Recommendations", "noRecommendations": "No recommendations available", "refreshRecommendations": "Refresh Recommendations", "like": "Like", "dislike": "Dislike", "feedbackSuccess": "<PERSON><PERSON><PERSON> submitted", "loadingRecommendations": "Loading recommendations...", "preferences": "Preferences", "theme": "Theme", "language": "Language", "autoplay": "Autoplay", "autoplayDesc": "Automatically play next video when current ends", "subtitles": "Subtitles", "subtitlesDesc": "Show subtitles by default", "videoQuality": "Video Quality", "videoQualityDesc": "Select default video quality", "notifications": "Notifications", "notificationsDesc": "Receive system notifications", "dark": "Dark", "light": "Light", "system": "System", "auto": "Auto", "high": "High", "medium": "Medium", "low": "Low", "saveSettings": "Save Settings", "settingsSaved": "Setting<PERSON> saved"}, "footer": {"termsOfService": "Terms of Service", "privacyPolicy": "Privacy Policy", "contactUs": "Contact Us", "dmca": "DMCA", "faq": "FAQ", "aboutUs": "About Us", "copyright": "© {{year}} JAVFLIX.TV All Rights Reserved", "quickLinks": "Quick Links", "help": "Help & Support", "email": "Contact Email", "section2257": "18 U.S.C. § 2257", "section2257Desc": "Record-Keeping Compliance Statement", "dmcaNotice": "DMCA Notice", "ageVerification": "Age Verification", "ageVerificationDesc": "This website is restricted to users 18 years and older"}, "download": {"options": "Download Options", "magnetLinkHelp": "Magnet links can be used with BitTorrent clients, click to copy", "disclaimer": "Downloaded files are for personal research use only, please delete within 24 hours. Please respect copyright, support official releases. Some video files may require specific video players to play normally.", "downloadButton": "Download"}, "auth": {"login": {"title": "Login to Your Account", "subtitle": "Sign in to access more features", "username": "Username", "email": "Email Address", "password": "Password", "rememberMe": "Remember Me", "forgotPassword": "Forgot Password?", "loginButton": "<PERSON><PERSON>", "noAccount": "Don't have an account?", "createAccount": "Create New Account", "loginError": "<PERSON><PERSON> failed, please check your credentials", "loginSuccess": "Login Successful", "loginRequired": {"title": "<PERSON><PERSON> Required", "likeMessage": "Dear user, please login to like your favorite videos ✨", "favoriteMessage": "Login to save amazing videos and revisit them anytime ✨", "commentMessage": "Want to join the discussion? Login to share your thoughts ✨", "generalMessage": "Login to unlock more amazing features and join our community ✨", "loginNow": "Login Now", "registerNow": "Sign Up", "later": "Maybe Later", "benefits": {"like": "Like your favorite content", "favorite": "Save amazing videos", "comment": "Join community discussions", "history": "Track viewing history", "recommendation": "Get personalized recommendations"}}}, "register": {"title": "Create New Account", "subtitle": "Join us to access more content", "username": "Username", "email": "Email Address", "password": "Password", "confirmPassword": "Confirm Password", "usernameError": "Username must be at least 3 characters", "emailError": "Please enter a valid email address", "passwordError": "Password must be at least 8 characters", "confirmPasswordError": "Passwords do not match", "termsError": "You must agree to the Terms of Service and Privacy Policy", "agreeToTerms": "I agree to the", "termsOfService": "Terms of Service", "and": "and", "privacyPolicy": "Privacy Policy", "registerButton": "Create Account", "haveAccount": "Already have an account?", "loginHere": "Login here", "registerError": "Registration failed, please try again", "registerSuccess": "Registration Successful", "welcome": "Welcome, "}, "oauth": {"continueWith": "Continue with", "google": "Google", "github": "GitHub", "twitter": "Twitter", "or": "or"}}, "about": {"title": "About Us", "heroTitle": "About JAVFLIX.TV", "heroSubtitle": "Creating the ultimate adult entertainment platform", "mission": {"title": "Our Mission", "content": "JAVFLIX.TV is dedicated to providing users worldwide with high-quality, high-definition Japanese adult video streaming services. We are committed to user experience, continuously improving platform features to ensure every user enjoys a safe, convenient, and smooth viewing experience."}, "vision": {"title": "Our Vision", "content": "To become the world's leading adult entertainment platform through technological innovation and content optimization, creating an unparalleled viewing experience for users. We believe that quality content combined with advanced technology can bring truly valuable entertainment experiences to users."}, "features": {"title": "Platform Features", "feature1": {"title": "High Definition Videos", "description": "All videos are provided in high definition to ensure the best viewing experience"}, "feature2": {"title": "Multi-language Support", "description": "Support for Traditional Chinese, Simplified Chinese, English, Japanese and more"}, "feature3": {"title": "Fast Loading", "description": "Advanced CDN technology ensures fast video loading and smooth playback"}, "feature4": {"title": "Personalized Recommendations", "description": "Personalized content recommendations based on user preferences"}}, "contact": {"title": "Contact Us", "content": "If you have any questions or suggestions, please feel free to contact us:", "email": "Email: <EMAIL>", "response": "We will respond to your message within 24 hours"}}, "faq": {"title": "Frequently Asked Questions", "subtitle": "Here are the most frequently asked questions from users. If you can't find your answer, please contact us", "general": {"title": "General Questions", "q1": {"question": "What is JAVFLIX.TV?", "answer": "JAVFLIX.TV is a professional Japanese adult video streaming platform that provides high-definition, smooth viewing experience with multi-language and device support."}, "q2": {"question": "How do I register an account?", "answer": "Click the 'Sign Up' button in the top right corner of the page and fill in the required information to register quickly. Registration is free and your personal information will be strictly protected."}, "q3": {"question": "Do I need to download any software?", "answer": "No. JAVFLIX.TV is a web-based platform, you only need a modern browser to watch all content."}}, "technical": {"title": "Technical Issues", "q1": {"question": "Which browsers are supported?", "answer": "We support all mainstream browsers including Chrome, Firefox, Safari, Edge, etc. We recommend using the latest version for the best experience."}, "q2": {"question": "What if videos load slowly?", "answer": "Please check your internet connection or try switching to a lower video quality. If the problem persists, please contact our technical support."}, "q3": {"question": "Are mobile devices supported?", "answer": "Yes, our platform fully supports mobile phones and tablets with responsive design to fit different screen sizes."}}, "account": {"title": "Account Related", "q1": {"question": "How do I reset my password?", "answer": "Click 'Forgot Password' on the login page, enter your email address, and we will send you a reset link."}, "q2": {"question": "Can I change my username?", "answer": "Username changes are currently not supported, but you can modify other personal information in your personal settings."}, "q3": {"question": "How do I delete my account?", "answer": "Please contact our customer service team and we will assist you in deleting your account. Please note that deleted data cannot be recovered."}}}, "privacy": {"title": "Privacy Policy", "lastUpdated": "Last Updated: December 2024", "intro": {"title": "Introduction", "content": "JAVFLIX.TV (referred to as 'we') takes your privacy very seriously. This privacy policy explains how we collect, use and protect your personal information. By using our services, you agree to the terms of this policy."}, "collection": {"title": "Information Collection", "subtitle": "We may collect the following types of information:", "personal": {"title": "Personal Information", "content": "Including but not limited to: username, email address, IP address, browser type and version."}, "usage": {"title": "Usage Information", "content": "Your activity on our website, including viewing history, search queries, preference settings, etc."}, "technical": {"title": "Technical Information", "content": "Device information, operating system, browser information and other technical data."}}, "usage": {"title": "Information Usage", "subtitle": "We use collected information for:", "item1": "Providing and maintaining our services", "item2": "Improving user experience and website functionality", "item3": "Providing personalized content recommendations", "item4": "Processing user requests and customer service", "item5": "Ensuring website security and preventing abuse"}, "sharing": {"title": "Information Sharing", "content": "We do not sell, trade or transfer your personally identifiable information to third parties. We may share information in the following situations:", "item1": "With your explicit consent", "item2": "Legal requirements or court orders", "item3": "Protecting our rights and security"}, "security": {"title": "Information Security", "content": "We employ various security measures to protect your personal information, including encrypted transmission, secure storage and access control. However, please note that no method of internet transmission or electronic storage is 100% secure."}, "cookies": {"title": "<PERSON><PERSON>", "content": "We use cookies to improve your browsing experience, analyze website traffic and provide personalized services. You can manage cookie preferences through your browser settings."}, "rights": {"title": "Your Rights", "content": "You have the right to:", "item1": "Access and update your personal information", "item2": "Request deletion of your account and data", "item3": "Opt out of certain data collection", "item4": "Request data portability"}, "contact": {"title": "Contact Us", "content": "If you have any questions about this privacy policy, please contact us: <EMAIL>"}}, "terms": {"title": "Terms of Service", "lastUpdated": "Last Updated: December 2024", "intro": {"title": "Terms of Service", "content": "Welcome to JAVFLIX.TV. By using our services, you agree to comply with the following terms and conditions. Please read these terms carefully."}, "acceptance": {"title": "Acceptance of Terms", "content": "By accessing and using this website, you acknowledge that you have read, understood and agree to be bound by these terms of service. If you do not agree to these terms, please do not use our services."}, "eligibility": {"title": "Eligibility", "content": "You must be at least 18 years old to use this service. By using this website, you represent and warrant that you have reached the legal age and have the right to accept these terms."}, "useGuidelines": {"title": "Usage Guidelines", "subtitle": "When using our services, you agree to:", "item1": "Not engage in any illegal activities", "item2": "Not upload or share harmful content", "item3": "Not infringe on others' rights", "item4": "Not interfere with the normal operation of the website", "item5": "Comply with all applicable laws and regulations"}, "content": {"title": "Content Policy", "content": "All content on this website is protected by copyright law. You may not copy, distribute, modify or otherwise use any content without explicit authorization. We reserve the right to modify or remove any content without notice."}, "account": {"title": "Account Responsibility", "content": "You are responsible for maintaining the confidentiality and accuracy of your account information. You agree to accept responsibility for all activities that occur under your account. If you suspect unauthorized use of your account, please notify us immediately."}, "termination": {"title": "Service Termination", "content": "We reserve the right to suspend or terminate your account and access privileges at any time, especially if you violate these terms. After termination, your right to use the service will immediately cease."}, "disclaimer": {"title": "Disclaimer", "content": "This service is provided 'as is' and we provide no express or implied warranties. We do not guarantee that the service will be uninterrupted, error-free or virus-free."}, "limitation": {"title": "Limitation of Liability", "content": "To the maximum extent permitted by law, we are not liable for any indirect, incidental, special or consequential damages."}, "governing": {"title": "Governing Law", "content": "These terms are governed by the laws of the relevant jurisdiction. Any disputes will be resolved through appropriate legal procedures."}, "changes": {"title": "Changes to Terms", "content": "We reserve the right to modify these terms at any time. Significant changes will be notified to users via website notice or email. Continued use of the service constitutes acceptance of the modified terms."}, "contact": {"title": "Contact Information", "content": "If you have any questions about these terms, please contact us: <EMAIL>"}}, "errors": {"notFound": "Content not found", "loadFailed": "Loading failed", "retryButton": "Retry", "networkError": "Network error", "serverError": "Server error", "timeout": "Request timeout", "unauthorized": "Unauthorized access", "forbidden": "Access forbidden", "pageNotFound": "Page not found", "somethingWentWrong": "Something went wrong", "goBack": "Go back", "goHome": "Go home"}, "pagination": {"prev": "Previous", "next": "Next", "page": "Page", "of": "of", "perPage": "Per page"}, "loading": {"loadingData": "Loading data...", "processingRequest": "Processing request...", "preparingContent": "Preparing content..."}}