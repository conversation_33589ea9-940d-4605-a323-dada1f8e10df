'use client';

import { createContext, useContext, useRef, useEffect, useState, ReactNode, useCallback } from 'react';
import { getSocketClient } from '@/lib/socket-client';

interface VideoStats {
  views: number;
  likes: number;
  favorites: number;
}

interface RealtimeStatsContextType {
  videoStats: Record<string, VideoStats>;
  subscribeToVideo: (videoId: string, movieId?: string, initialStats?: VideoStats) => void;
  unsubscribeFromVideo: (videoId: string, movieId?: string) => void;
  getViewCount: (videoId: string, movieId?: string, fallbackCount?: number) => number;
}

const RealtimeStatsContext = createContext<RealtimeStatsContextType | null>(null);

interface RealtimeStatsProviderProps {
  children: ReactNode;
}

// 统一的视频标识符生成函数
const getUnifiedVideoKey = (videoId: string, movieId?: string): string => {
  // 优先使用 movie_id 作为主要标识符，如果没有则使用 videoId
  if (movieId && movieId.trim()) {
    return `movie_${movieId}`;
  }
  return `video_${videoId}`;
};

export const RealtimeStatsProvider = ({ children }: RealtimeStatsProviderProps) => {
  const [videoStats, setVideoStats] = useState<Record<string, VideoStats>>({});
  const socketClient = useRef(getSocketClient());
  const subscriptions = useRef<Record<string, () => void>>({});

  const subscribeToVideo = (videoId: string, movieId?: string, initialStats?: VideoStats) => {
    const unifiedKey = getUnifiedVideoKey(videoId, movieId);
    
    // 如果已经订阅过，直接返回
    if (subscriptions.current[unifiedKey]) {
      return;
    }

    // 设置初始统计数据
    if (initialStats) {
      setVideoStats(prev => ({
        ...prev,
        [unifiedKey]: initialStats
      }));
    }

    // 订阅Socket.IO更新 - 使用 movie_id 如果存在，否则使用 videoId
    const targetId = movieId || videoId;
    
    const handleStatsUpdate = (updatedStats: VideoStats) => {
      setVideoStats(prev => ({
        ...prev,
        [unifiedKey]: {
          ...prev[unifiedKey],
          ...updatedStats
        }
      }));
    };

    // 订阅统计更新
    socketClient.current.subscribeToStats(targetId, handleStatsUpdate);
    
    // 记录取消订阅函数
    subscriptions.current[unifiedKey] = () => {
      socketClient.current.unsubscribeFromStats(targetId);
      delete subscriptions.current[unifiedKey];
    };
  };

  const unsubscribeFromVideo = (videoId: string, movieId?: string) => {
    const unifiedKey = getUnifiedVideoKey(videoId, movieId);
    
    if (subscriptions.current[unifiedKey]) {
      subscriptions.current[unifiedKey]();
    }
  };

  const getViewCount = (videoId: string, movieId?: string, fallbackCount?: number): number => {
    const unifiedKey = getUnifiedVideoKey(videoId, movieId);
    const stats = videoStats[unifiedKey];
    
    // 优先使用实时统计数据，否则使用fallback
    const viewCount = stats?.views ?? fallbackCount ?? 0;
    
    return viewCount;
  };

  // 清理函数：组件卸载时取消所有订阅
  useEffect(() => {
    return () => {
      Object.values(subscriptions.current).forEach(unsubscribe => unsubscribe());
      subscriptions.current = {};
    };
  }, []);

  return (
    <RealtimeStatsContext.Provider value={{
      videoStats,
      subscribeToVideo,
      unsubscribeFromVideo,
      getViewCount
    }}>
      {children}
    </RealtimeStatsContext.Provider>
  );
};

export const useRealtimeStats = () => {
  const context = useContext(RealtimeStatsContext);
  if (!context) {
    throw new Error('useRealtimeStats must be used within a RealtimeStatsProvider');
  }
  return context;
}; 