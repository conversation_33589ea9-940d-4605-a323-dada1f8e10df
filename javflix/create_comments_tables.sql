-- 创建评论表
CREATE TABLE IF NOT EXISTS comments (
    id SERIAL PRIMARY KEY,
    video_id VARCHAR(50) NOT NULL,
    user_id VARCHAR(255) NOT NULL,
    content TEXT NOT NULL,
    likes INTEGER DEFAULT 0,
    parent_id INTEGER REFERENCES comments(id),
    is_deleted BO<PERSON>EAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建评论点赞表
CREATE TABLE IF NOT EXISTS comment_likes (
    id SERIAL PRIMARY KEY,
    comment_id INTEGER NOT NULL REFERENCES comments(id) ON DELETE CASCADE,
    user_id VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(comment_id, user_id)
);

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_comments_video_id ON comments(video_id);
CREATE INDEX IF NOT EXISTS idx_comments_user_id ON comments(user_id);
CREATE INDEX IF NOT EXISTS idx_comments_parent_id ON comments(parent_id);
CREATE INDEX IF NOT EXISTS idx_comments_created_at ON comments(created_at);
CREATE INDEX IF NOT EXISTS idx_comments_likes ON comments(likes);
CREATE INDEX IF NOT EXISTS idx_comment_likes_comment_id ON comment_likes(comment_id);
CREATE INDEX IF NOT EXISTS idx_comment_likes_user_id ON comment_likes(user_id);

-- 更新时间触发器
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_comments_updated_at 
    BEFORE UPDATE ON comments 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- 插入一些示例数据（可选）
INSERT INTO comments (video_id, user_id, content, likes, created_at) VALUES
('89', '1', '这部作品质量很不错，画面清晰，值得收藏！', 28, NOW() - INTERVAL '2 hours'),
('89', '2', '演员表现很自然，制作用心，推荐大家观看。', 19, NOW() - INTERVAL '2 hours'),
('89', '3', '感谢分享，一直在找这个资源！终于找到了高清版本。', 9, NOW() - INTERVAL '3 hours');

-- 为示例评论添加回复
INSERT INTO comments (video_id, user_id, content, parent_id, likes, created_at) VALUES
('89', '4', '同意，这个系列一直质量稳定。', 1, 7, NOW() - INTERVAL '1 hour');

-- 插入一些点赞记录
INSERT INTO comment_likes (comment_id, user_id) VALUES
(1, '5'), (1, '6'), (1, '7'),
(2, '8'), (2, '9'),
(3, '10');

COMMIT;