# JAVFLIX AI Agent 开发标准

## 项目概述

### 技术栈
- **框架**: Next.js 14 with App Router
- **语言**: TypeScript + React
- **样式**: Tailwind CSS
- **动画**: GSAP
- **状态管理**: React Context + Custom Hooks
- **国际化**: 自定义i18n系统 (zh-CN, en-US, ja-JP)
- **认证**: JWT + Cookies

### 核心架构
- **前端**: `/javflix/src/` - Next.js应用
- **API路由**: `/javflix/src/app/api/` - 后端API代理
- **组件**: `/javflix/src/components/` - 可复用组件
- **页面**: `/javflix/src/app/[locale]/` - 国际化页面路由
- **工具**: `/javflix/src/lib/` - 工具函数和客户端库

## 国际化处理标准

### 强制要求
- **修改任何包含文本的组件时，必须同时更新三个语言文件**:
  - `/javflix/src/i18n/locales/zh-CN.json`
  - `/javflix/src/i18n/locales/en-US.json`
  - `/javflix/src/i18n/locales/ja-JP.json`

### 文本处理规则
- **必须使用**: `{t('key.path')}` 或 `{t('key.path') || '默认文本'}`
- **禁止使用**: 硬编码中文、英文或日文文本
- **新增翻译键时**: 必须在所有三个语言文件中添加对应键值

### 路由处理
- **所有页面路径必须支持语言前缀**: `/zh-CN/path`, `/en-US/path`, `/ja-JP/path`
- **使用**: `localizedUrl('/path')` 生成国际化链接
- **获取当前语言**: 从 `useParams()` 或 `pathname` 解析

### 示例
```typescript
// ✅ 正确
<h1>{t('nav.home') || '首页'}</h1>
<Link href={localizedUrl('/about')}>{t('nav.about')}</Link>

// ❌ 错误
<h1>首页</h1>
<Link href="/about">关于我们</Link>
```

## 认证系统修改标准

### 核心文件协调
- **修改认证功能时，必须同时考虑**:
  - `/javflix/src/context/AuthContext.tsx` - 状态管理
  - `/javflix/src/lib/auth-client.ts` - API客户端
  - `/javflix/src/components/Navbar.tsx` - UI显示

### 状态更新规则
- **登录/注册成功后**: 必须使用 `setUserImmediate(userData)` 立即更新状态
- **登出时**: 必须使用 `setUserImmediate(null)` 立即清除状态
- **禁止**: 使用多次延迟触发的 `auth-state-changed` 事件

### 事件处理标准
- **触发认证状态变更**: 使用 `triggerAuthStateChange(userData)` 
- **事件必须携带用户数据**: `CustomEvent({ detail: { user } })`
- **避免不必要的API调用**: 优先使用事件中的用户数据

### 示例
```typescript
// ✅ 正确
const result = await login(email, password);
if (result.success) {
  // AuthContext会自动调用setUserImmediate
  playSuccessAnimation();
}

// ❌ 错误
const result = await login(email, password);
if (result.success) {
  setTimeout(() => triggerAuthChanged(), 500);
  setTimeout(() => triggerAuthChanged(), 1000);
  setTimeout(() => triggerAuthChanged(), 2000);
}
```

## 组件开发规范

### 状态管理
- **使用**: `const { user, isLoading, logout } = useAuth()` 获取认证状态
- **禁止**: 在组件中独立管理用户状态或重复获取用户信息

### 国际化支持
- **所有新组件必须支持国际化**
- **使用**: `const { t } = useClientTranslations()`
- **文本内容**: 必须通过翻译键获取

### 动画处理
- **使用**: GSAP库进行动画
- **引入**: `import gsap from 'gsap'`
- **常用模式**: `gsap.fromTo()` 用于入场动画

### 样式规范
- **使用**: Tailwind CSS类名
- **响应式**: 使用 `md:`, `lg:` 等前缀
- **主题色**: `bg-red-500`, `text-red-400` (品牌色)

## API开发规范

### 响应格式标准
- **所有API必须返回统一格式**:
```typescript
{
  success: boolean;
  data?: any;
  error?: string;
  message?: string;
}
```

### 认证API特殊要求
- **必须设置cookies**: `auth_token` (httpOnly) 和 `auth_state` (非httpOnly)
- **用户数据格式**: `{ success: true, user: userData, token?: string }`
- **错误处理**: 返回具体错误信息而不是通用消息

### 示例
```typescript
// ✅ 正确的API响应
return NextResponse.json({
  success: true,
  user: userData,
  token: jwtToken
});

// ❌ 错误的API响应
return NextResponse.json(userData);
```

## 文件协调要求

### 国际化文件协调
- **修改组件文本时**: 必须同时更新 `zh-CN.json`, `en-US.json`, `ja-JP.json`
- **新增翻译键**: 在所有语言文件中保持键结构一致

### 认证系统文件协调
- **修改登录逻辑**: 同时检查 `AuthContext.tsx`, `auth-client.ts`, 登录页面
- **修改用户状态**: 确保 `Navbar.tsx` 使用 `useAuth` hook

### 路由文件协调
- **新增页面**: 必须在 `[locale]` 目录下创建，支持所有语言
- **修改导航**: 同时更新 `Navbar.tsx` 和相关翻译文件

## AI决策标准

### 优先级判断
1. **国际化完整性** > 功能实现
2. **认证状态一致性** > UI美观
3. **多文件协调** > 单文件优化
4. **用户体验** > 代码简洁

### 冲突解决
- **翻译缺失时**: 提供默认文本并在所有语言文件中补充
- **认证状态不一致时**: 优先使用AuthContext的状态
- **样式冲突时**: 优先使用Tailwind CSS类名

### 错误处理决策树
```
错误类型判断
├── 国际化相关 → 检查所有语言文件
├── 认证相关 → 检查AuthContext和auth-client
├── 路由相关 → 检查locale支持
└── 其他 → 按标准错误处理流程
```

## 禁止操作

### 严格禁止
- **硬编码任何语言的文本内容**
- **在组件中独立管理用户认证状态**
- **使用多次延迟触发的认证事件**
- **忽略国际化要求直接修改文本**
- **破坏现有的文件协调关系**

### 代码禁忌
```typescript
// ❌ 禁止硬编码文本
<button>登录</button>
<button>Login</button>

// ❌ 禁止独立用户状态
const [user, setUser] = useState(null);

// ❌ 禁止多次事件触发
setTimeout(() => triggerAuth(), 500);
setTimeout(() => triggerAuth(), 1000);

// ❌ 禁止不一致的API格式
return { user: userData }; // 缺少success字段
```

### 修改限制
- **不得修改**: 核心i18n配置文件
- **不得删除**: 现有的语言支持
- **不得破坏**: AuthContext的状态管理逻辑
- **不得忽略**: 多文件协调要求

## 特殊注意事项

### 测试文件处理
- **测试文件**: `test-*.html` 文件仅用于开发测试
- **不得在生产代码中引用测试文件**
- **测试文件可以包含硬编码文本**

### 性能考虑
- **避免重复API调用**: 优先使用缓存和事件传递数据
- **减少不必要的状态更新**: 使用 `setUserImmediate` 而不是多次触发
- **优化动画性能**: 使用GSAP的硬件加速特性

### 向后兼容
- **保持现有API接口不变**
- **维护现有的数据格式**
- **确保错误处理机制完整**
- **提供降级方案** 