const fetch = require('node-fetch');

async function testAuth() {
  console.log('🔍 调试用户认证和API调用问题...\n');
  
  // 1. 测试登录
  console.log('1. 测试用户登录...');
  try {
    const loginResponse = await fetch('http://localhost:4000/api/users/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: '123456'
      })
    });
    
    const loginData = await loginResponse.json();
    if (loginData.success) {
      console.log('✅ 登录成功');
      console.log(`   Token: ${loginData.data.token.substring(0, 50)}...`);
      
      const token = loginData.data.token;
      
      // 2. 测试收藏API
      console.log('\n2. 测试收藏API...');
      const favoritesResponse = await fetch('http://localhost:4000/api/users/favorites?page=1&limit=5', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        }
      });
      
      const favoritesData = await favoritesResponse.json();
      if (favoritesData.success) {
        console.log(`✅ 收藏API工作正常，找到 ${favoritesData.data.videos.length} 个收藏`);
        console.log('   收藏列表:');
        favoritesData.data.videos.slice(0, 3).forEach((video, index) => {
          console.log(`   ${index + 1}. ${video.title.substring(0, 50)}...`);
        });
      } else {
        console.log('❌ 收藏API调用失败:', favoritesData.message);
      }
      
      // 3. 测试点赞API
      console.log('\n3. 测试点赞API...');
      const likesResponse = await fetch('http://localhost:4000/api/users/likes?page=1&limit=5', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        }
      });
      
      const likesData = await likesResponse.json();
      if (likesData.success) {
        console.log(`✅ 点赞API工作正常，找到 ${likesData.data.data.length} 个点赞`);
        console.log('   点赞列表:');
        likesData.data.data.slice(0, 3).forEach((video, index) => {
          console.log(`   ${index + 1}. ${video.title.substring(0, 50)}...`);
        });
      } else {
        console.log('❌ 点赞API调用失败:', likesData.message);
      }
      
      // 4. 测试前端代理API
      console.log('\n4. 测试前端代理API...');
      
      // 模拟前端cookie
      const cookieString = `auth_token=${token}`;
      
      try {
        const proxyFavoritesResponse = await fetch('http://localhost:3001/api/proxy/users/favorites?page=1&limit=5', {
          method: 'GET',
          headers: {
            'Cookie': cookieString,
            'Content-Type': 'application/json',
          }
        });
        
        if (proxyFavoritesResponse.ok) {
          const proxyFavoritesData = await proxyFavoritesResponse.json();
          console.log('✅ 前端收藏代理API工作正常');
          console.log(`   返回数据类型: ${typeof proxyFavoritesData}`);
          console.log(`   数据结构:`, Object.keys(proxyFavoritesData));
        } else {
          console.log('❌ 前端收藏代理API调用失败:', proxyFavoritesResponse.status);
        }
      } catch (error) {
        console.log('❌ 前端代理API连接失败:', error.message);
        console.log('   可能原因: 前端服务器未运行');
      }
      
    } else {
      console.log('❌ 登录失败:', loginData.message);
    }
  } catch (error) {
    console.log('❌ 登录请求失败:', error.message);
  }
  
  console.log('\n📋 调试总结:');
  console.log('1. 检查用户是否已登录（浏览器中的auth_token cookie）');
  console.log('2. 检查前端服务器是否运行在 http://localhost:3001');
  console.log('3. 检查浏览器控制台是否有API调用错误');
  console.log('4. 验证前端代理API是否正确转发认证信息');
}

testAuth().catch(console.error); 