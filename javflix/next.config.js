/** @type {import('next').NextConfig} */
const nextConfig = {
  /* Enhanced config for A+ performance based on https://nextjs.org/docs/app/deep-dive/caching */
  serverExternalPackages: ["pg", "pg-native"],
  
  // Enable compression
  compress: true,
  
  // Enable experimental features for performance (compatible with current Next.js version)
  experimental: {
    // Optimize server components
    optimizeServerReact: true,
    // Enable optimized package imports
    optimizePackageImports: ['react-icons', 'lodash'],
  },
  
  // Turbopack configuration (moved from experimental)
  turbopack: {
    rules: {
      '*.svg': {
        loaders: ['@svgr/webpack'],
        as: '*.js',
      },
    },
  },
  
  // Performance optimizations
  productionBrowserSourceMaps: false,
  
  // Cache configuration for better performance
  onDemandEntries: {
    // Period (in ms) where the server will keep pages in the buffer
    maxInactiveAge: 25 * 1000,
    // Number of pages that should be kept simultaneously without being disposed
    pagesBufferLength: 5,
  },
  // 添加CSP配置以允许外部样式表
  async headers() {
    const isDev = process.env.NODE_ENV === 'development';
    
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'Content-Security-Policy',
            value: isDev 
              ? "default-src 'self'; img-src 'self' https: http: data:; media-src 'self' https: http: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://www.google-analytics.com https://www.googletagmanager.com; style-src 'self' 'unsafe-inline' https://cdn.plyr.io; style-src-elem 'self' 'unsafe-inline' https://cdn.plyr.io; font-src 'self' data:; connect-src 'self' https: http://localhost:* ws://localhost:* blob:; worker-src 'self' blob:;"
              : "default-src 'self'; img-src 'self' https: data:; media-src 'self' https: blob:; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://www.google-analytics.com https://www.googletagmanager.com; style-src 'self' 'unsafe-inline' https://cdn.plyr.io; style-src-elem 'self' 'unsafe-inline' https://cdn.plyr.io; font-src 'self' data:; connect-src 'self' https: blob:; worker-src 'self' blob:;"
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff'
          },
          {
            key: 'X-Frame-Options',
            value: 'DENY'
          },
          {
            key: 'X-XSS-Protection',
            value: '1; mode=block'
          }
        ]
      }
    ];
  },
  eslint: {
    // 生产构建时将ESLint错误降级为警告
    ignoreDuringBuilds: false,
  },
  images: {
    // Optimize images for performance
    formats: ['image/webp', 'image/avif'],
    deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],
    imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
    minimumCacheTTL: 60 * 60 * 24 * 365, // 1 year cache
    dangerouslyAllowSVG: true,
    contentSecurityPolicy: "default-src 'self'; script-src 'none'; sandbox;",
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'picsum.photos',
        pathname: '**',
      },
      {
        protocol: 'https',
        hostname: '*.javflix.tv',
      },
      {
        protocol: 'https',
        hostname: 'www.javbus.com',
        pathname: '**',
      },
      {
        protocol: 'https',
        hostname: 'javbus.com',
        pathname: '**',
      },
      {
        protocol: 'https',
        hostname: '*.javbus.com',
        pathname: '**',
      },
      {
        protocol: 'http',
        hostname: 'www.javbus.com',
        pathname: '**',
      },
      {
        protocol: 'http',
        hostname: 'javbus.com',
        pathname: '**',
      },
    ],
  },
  webpack: (config, { isServer }) => {
    if (!isServer) {
      // 在客户端构建中将Node.js模块替换为空对象
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
        net: false,
        tls: false,
        pg: false,
        "pg-native": false,
        dns: false,
      };
    }
    return config;
  },
  // 定义重定向规则
  async redirects() {
    return [
      // 测试中的重定向规则 - 确保这些能被测试脚本检测到
      {
        source: '/videos',
        destination: '/',
        permanent: true,
      },
      {
        source: '/actress/:slug',
        destination: '/actors/:slug',
        permanent: true,
      },
      {
        source: '/category/:slug',
        destination: '/tags/:slug',
        permanent: true,
      },
      {
        source: '/film/:slug',
        destination: '/video/:slug',
        permanent: true,
      },
      {
        source: '/movie/:slug',
        destination: '/video/:slug',
        permanent: true,
      },
      {
        source: '/hot',
        destination: '/popular',
        permanent: true,
      },
      {
        source: '/new',
        destination: '/recent',
        permanent: true,
      },
      {
        source: '/find',
        destination: '/search',
        permanent: true,
      },
      // 旧域名重定向（如果有的话）
      // {
      //   source: '/:path*',
      //   has: [
      //     {
      //       type: 'host',
      //       value: 'old-domain.com',
      //     },
      //   ],
      //   destination: 'https://new-domain.com/:path*',
      //   permanent: true,
      // },
    ];
  },
  async rewrites() {
    return [
      {
        source: '/video-sitemap.xml',
        destination: '/api/video-sitemap',
      },
      // 兼容性API重定向 - 热门视频API已移除
      {
        source: '/api/videos/recent',
        destination: '/api/recent-videos',
      },
      // 美化URL的重写规则
      {
        source: '/latest',
        destination: '/new',
      },
      {
        source: '/trending',
        destination: '/popular',
      },
      // 重写语言路径下的manifest.json请求到根目录的manifest.json
      {
        source: '/:locale(zh-Hans|zh|en|ja)/manifest.json',
        destination: '/manifest.json',
      },
      // 确保API请求正确处理
      {
        source: '/api/:path*',
        destination: '/api/:path*',
      },
      // 为映像资源添加回退
      {
        source: '/images/defaults/:imageName',
        destination: '/images/placeholders/:imageName',
      },
      // 评论API代理配置
      {
        source: '/api/comments/:path*',
        destination: 'http://localhost:4000/api/comments/:path*',
      },
      // 视频统计API代理配置
      {
        source: '/api/video-stats/:path*',
        destination: 'http://localhost:4000/api/video-stats/:path*',
      }
    ];
  },
  // 支持静态导出 - 完整配置standalone模式
  output: 'standalone',
  
  // 确保必要的文件被包含在tracing中
  outputFileTracingIncludes: {
    '/**/*': ['./public/**/*'],
  },
};

module.exports = nextConfig; 