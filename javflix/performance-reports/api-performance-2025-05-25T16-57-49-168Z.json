[{"name": "API-首页数据", "url": "http://localhost:3000/api/popular-videos?limit=20", "score": 32, "responseTime": 23, "status": 404, "contentLength": 35, "cacheControl": "none", "serverTime": "not provided", "dataValid": false, "success": false, "timestamp": "2025-05-25T16:57:47.154Z", "performanceGrade": "D"}, {"name": "API-搜索数据", "url": "http://localhost:3000/api/search?q=test&limit=20", "score": 32, "responseTime": 2, "status": 404, "contentLength": 35, "cacheControl": "none", "serverTime": "not provided", "dataValid": false, "success": false, "timestamp": "2025-05-25T16:57:47.660Z", "performanceGrade": "D"}, {"name": "API-分类数据", "url": "http://localhost:3000/api/db/genres?limit=50", "score": 32, "responseTime": 1, "status": 404, "contentLength": 35, "cacheControl": "none", "serverTime": "not provided", "dataValid": false, "success": false, "timestamp": "2025-05-25T16:57:48.162Z", "performanceGrade": "D"}, {"name": "API-视频详情", "url": "http://localhost:3000/api/video/details?id=SSIS-001", "score": 32, "responseTime": 4, "status": 404, "contentLength": 35, "cacheControl": "none", "serverTime": "not provided", "dataValid": false, "success": false, "timestamp": "2025-05-25T16:57:48.666Z", "performanceGrade": "D"}]