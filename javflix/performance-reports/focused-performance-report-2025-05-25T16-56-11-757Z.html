
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JAVFLIX.TV 专项性能测试报告</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        .container { 
            max-width: 1400px; 
            margin: 0 auto; 
            background: white; 
            border-radius: 15px; 
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header { 
            background: linear-gradient(135deg, #ff6b6b, #ee5a52);
            color: white; 
            padding: 30px; 
            text-align: center;
        }
        .header h1 { font-size: 2.5em; margin-bottom: 10px; }
        .stats { 
            display: grid; 
            grid-template-columns: repeat(auto-fit, minmax(180px, 1fr)); 
            gap: 20px; 
            padding: 30px;
            background: #f8f9fa;
        }
        .stat-card { 
            background: white; 
            padding: 20px; 
            border-radius: 10px; 
            text-align: center; 
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .stat-number { font-size: 2em; font-weight: bold; margin-bottom: 5px; }
        .stat-label { color: #666; font-size: 0.9em; }
        .grade-excellent { color: #28a745; }
        .grade-good { color: #17a2b8; }
        .grade-warning { color: #ffc107; }
        .grade-poor { color: #dc3545; }
        .results { padding: 20px; }
        .category-section { margin-bottom: 40px; }
        .category-title { 
            font-size: 1.5em; 
            color: #333; 
            margin-bottom: 20px; 
            padding-bottom: 10px; 
            border-bottom: 3px solid #ff6b6b;
        }
        .result-grid { 
            display: grid; 
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr)); 
            gap: 20px; 
        }
        .result-card { 
            background: white; 
            border-radius: 10px; 
            padding: 20px; 
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            border-left: 5px solid #ddd;
        }
        .result-card.excellent { border-left-color: #28a745; }
        .result-card.good { border-left-color: #17a2b8; }
        .result-card.warning { border-left-color: #ffc107; }
        .result-card.poor { border-left-color: #dc3545; }
        .result-title { font-weight: bold; margin-bottom: 10px; font-size: 1.1em; }
        .result-score { font-size: 2em; font-weight: bold; margin: 10px 0; }
        .result-details { font-size: 0.9em; color: #666; }
        .result-metrics { 
            display: grid; 
            grid-template-columns: 1fr 1fr; 
            gap: 10px; 
            margin-top: 15px; 
        }
        .metric { 
            background: #f8f9fa; 
            padding: 8px; 
            border-radius: 5px; 
            text-align: center; 
        }
        .metric-label { font-size: 0.8em; color: #666; }
        .metric-value { font-weight: bold; color: #333; }
        .footer { 
            text-align: center; 
            padding: 20px; 
            color: #666; 
            background: #f8f9fa; 
        }
        .critical-badge { 
            background: #dc3545; 
            color: white; 
            padding: 2px 8px; 
            border-radius: 10px; 
            font-size: 0.8em; 
            margin-left: 10px; 
        }
        .api-badge { 
            background: #6f42c1; 
            color: white; 
            padding: 2px 8px; 
            border-radius: 10px; 
            font-size: 0.8em; 
            margin-left: 5px; 
        }
        .grade-badge {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 15px;
            font-weight: bold;
            font-size: 0.9em;
            margin-left: 10px;
        }
        .grade-A-plus { background: #28a745; color: white; }
        .grade-A { background: #20c997; color: white; }
        .grade-B-plus { background: #17a2b8; color: white; }
        .grade-B { background: #007bff; color: white; }
        .grade-C { background: #ffc107; color: black; }
        .grade-D { background: #fd7e14; color: white; }
        .grade-F { background: #dc3545; color: white; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 JAVFLIX.TV 专项性能测试报告</h1>
            <p>基于 Medium 和 freeCodeCamp 的 Next.js 优化最佳实践</p>
            <p>测试时间: 2025/5/26 00:56:11</p>
            <p>专注测试：视频页面、搜索功能、API接口等核心功能</p>
        </div>
        
        <div class="stats">
            <div class="stat-card">
                <div class="stat-number grade-poor">55</div>
                <div class="stat-label">平均分数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number grade-excellent">0</div>
                <div class="stat-label">通过测试</div>
            </div>
            <div class="stat-card">
                <div class="stat-number grade-poor">22</div>
                <div class="stat-label">测试失败</div>
            </div>
            <div class="stat-card">
                <div class="stat-number grade-poor">7</div>
                <div class="stat-label">关键问题</div>
            </div>
            <div class="stat-card">
                <div class="stat-number grade-good">4ms</div>
                <div class="stat-label">平均响应时间</div>
            </div>
            <div class="stat-card">
                <div class="stat-number grade-good">0</div>
                <div class="stat-label">A级页面数</div>
            </div>
        </div>
        
        <div class="results">
            
                <div class="category-section">
                    <h2 class="category-title">📊 核心页面 (1个测试)</h2>
                    <div class="result-grid">
                        
                            <div class="result-card warning">
                                <div class="result-title">
                                    首页
                                    <span class="critical-badge">关键</span>
                                    
                                    <span class="grade-badge grade-C">C</span>
                                </div>
                                <div class="result-score grade-warning">60</div>
                                <div class="result-details">
                                    <strong>URL:</strong> http://localhost:3000/zh-CN<br>
                                    
                                </div>
                                <div class="result-metrics">
                                    <div class="metric">
                                        <div class="metric-label">响应时间</div>
                                        <div class="metric-value">84ms</div>
                                    </div>
                                    
                                        <div class="metric">
                                            <div class="metric-label">状态码</div>
                                            <div class="metric-value">404</div>
                                        </div>
                                    
                                    
                                        <div class="metric">
                                            <div class="metric-label">内容大小</div>
                                            <div class="metric-value">35</div>
                                        </div>
                                    
                                    
                                </div>
                            </div>
                          
                    </div>
                </div>
            
                <div class="category-section">
                    <h2 class="category-title">📊 视频页面 (4个测试)</h2>
                    <div class="result-grid">
                        
                            <div class="result-card warning">
                                <div class="result-title">
                                    播放页-热门1
                                    <span class="critical-badge">关键</span>
                                    
                                    <span class="grade-badge grade-C">C</span>
                                </div>
                                <div class="result-score grade-warning">60</div>
                                <div class="result-details">
                                    <strong>URL:</strong> http://localhost:3000/zh-CN/video/SSIS-001<br>
                                    
                                </div>
                                <div class="result-metrics">
                                    <div class="metric">
                                        <div class="metric-label">响应时间</div>
                                        <div class="metric-value">1ms</div>
                                    </div>
                                    
                                        <div class="metric">
                                            <div class="metric-label">状态码</div>
                                            <div class="metric-value">404</div>
                                        </div>
                                    
                                    
                                        <div class="metric">
                                            <div class="metric-label">内容大小</div>
                                            <div class="metric-value">35</div>
                                        </div>
                                    
                                    
                                </div>
                            </div>
                          
                            <div class="result-card warning">
                                <div class="result-title">
                                    播放页-热门2
                                    <span class="critical-badge">关键</span>
                                    
                                    <span class="grade-badge grade-C">C</span>
                                </div>
                                <div class="result-score grade-warning">60</div>
                                <div class="result-details">
                                    <strong>URL:</strong> http://localhost:3000/zh-CN/video/SSNI-999<br>
                                    
                                </div>
                                <div class="result-metrics">
                                    <div class="metric">
                                        <div class="metric-label">响应时间</div>
                                        <div class="metric-value">2ms</div>
                                    </div>
                                    
                                        <div class="metric">
                                            <div class="metric-label">状态码</div>
                                            <div class="metric-value">404</div>
                                        </div>
                                    
                                    
                                        <div class="metric">
                                            <div class="metric-label">内容大小</div>
                                            <div class="metric-value">35</div>
                                        </div>
                                    
                                    
                                </div>
                            </div>
                          
                            <div class="result-card warning">
                                <div class="result-title">
                                    播放页-高清
                                    
                                    
                                    <span class="grade-badge grade-C">C</span>
                                </div>
                                <div class="result-score grade-warning">60</div>
                                <div class="result-details">
                                    <strong>URL:</strong> http://localhost:3000/zh-CN/video/PRED-123<br>
                                    
                                </div>
                                <div class="result-metrics">
                                    <div class="metric">
                                        <div class="metric-label">响应时间</div>
                                        <div class="metric-value">3ms</div>
                                    </div>
                                    
                                        <div class="metric">
                                            <div class="metric-label">状态码</div>
                                            <div class="metric-value">404</div>
                                        </div>
                                    
                                    
                                        <div class="metric">
                                            <div class="metric-label">内容大小</div>
                                            <div class="metric-value">35</div>
                                        </div>
                                    
                                    
                                </div>
                            </div>
                          
                            <div class="result-card warning">
                                <div class="result-title">
                                    播放页-VR
                                    
                                    
                                    <span class="grade-badge grade-C">C</span>
                                </div>
                                <div class="result-score grade-warning">60</div>
                                <div class="result-details">
                                    <strong>URL:</strong> http://localhost:3000/zh-CN/video/VR-001<br>
                                    
                                </div>
                                <div class="result-metrics">
                                    <div class="metric">
                                        <div class="metric-label">响应时间</div>
                                        <div class="metric-value">2ms</div>
                                    </div>
                                    
                                        <div class="metric">
                                            <div class="metric-label">状态码</div>
                                            <div class="metric-value">404</div>
                                        </div>
                                    
                                    
                                        <div class="metric">
                                            <div class="metric-label">内容大小</div>
                                            <div class="metric-value">35</div>
                                        </div>
                                    
                                    
                                </div>
                            </div>
                          
                    </div>
                </div>
            
                <div class="category-section">
                    <h2 class="category-title">📊 搜索功能 (3个测试)</h2>
                    <div class="result-grid">
                        
                            <div class="result-card warning">
                                <div class="result-title">
                                    搜索页面
                                    <span class="critical-badge">关键</span>
                                    
                                    <span class="grade-badge grade-C">C</span>
                                </div>
                                <div class="result-score grade-warning">60</div>
                                <div class="result-details">
                                    <strong>URL:</strong> http://localhost:3000/zh-CN/search?q=高清<br>
                                    
                                </div>
                                <div class="result-metrics">
                                    <div class="metric">
                                        <div class="metric-label">响应时间</div>
                                        <div class="metric-value">2ms</div>
                                    </div>
                                    
                                        <div class="metric">
                                            <div class="metric-label">状态码</div>
                                            <div class="metric-value">404</div>
                                        </div>
                                    
                                    
                                        <div class="metric">
                                            <div class="metric-label">内容大小</div>
                                            <div class="metric-value">35</div>
                                        </div>
                                    
                                    
                                </div>
                            </div>
                          
                            <div class="result-card warning">
                                <div class="result-title">
                                    搜索结果-巨乳
                                    
                                    
                                    <span class="grade-badge grade-C">C</span>
                                </div>
                                <div class="result-score grade-warning">60</div>
                                <div class="result-details">
                                    <strong>URL:</strong> http://localhost:3000/zh-CN/search?q=巨乳<br>
                                    
                                </div>
                                <div class="result-metrics">
                                    <div class="metric">
                                        <div class="metric-label">响应时间</div>
                                        <div class="metric-value">3ms</div>
                                    </div>
                                    
                                        <div class="metric">
                                            <div class="metric-label">状态码</div>
                                            <div class="metric-value">404</div>
                                        </div>
                                    
                                    
                                        <div class="metric">
                                            <div class="metric-label">内容大小</div>
                                            <div class="metric-value">35</div>
                                        </div>
                                    
                                    
                                </div>
                            </div>
                          
                            <div class="result-card warning">
                                <div class="result-title">
                                    搜索结果-无码
                                    
                                    
                                    <span class="grade-badge grade-C">C</span>
                                </div>
                                <div class="result-score grade-warning">60</div>
                                <div class="result-details">
                                    <strong>URL:</strong> http://localhost:3000/zh-CN/search?q=无码<br>
                                    
                                </div>
                                <div class="result-metrics">
                                    <div class="metric">
                                        <div class="metric-label">响应时间</div>
                                        <div class="metric-value">3ms</div>
                                    </div>
                                    
                                        <div class="metric">
                                            <div class="metric-label">状态码</div>
                                            <div class="metric-value">404</div>
                                        </div>
                                    
                                    
                                        <div class="metric">
                                            <div class="metric-label">内容大小</div>
                                            <div class="metric-value">35</div>
                                        </div>
                                    
                                    
                                </div>
                            </div>
                          
                    </div>
                </div>
            
                <div class="category-section">
                    <h2 class="category-title">📊 API接口 (4个测试)</h2>
                    <div class="result-grid">
                        
                            <div class="result-card poor">
                                <div class="result-title">
                                    API-首页数据
                                    <span class="critical-badge">关键</span>
                                    <span class="api-badge">API</span>
                                    <span class="grade-badge grade-D">D</span>
                                </div>
                                <div class="result-score grade-poor">32</div>
                                <div class="result-details">
                                    <strong>URL:</strong> http://localhost:3000/api/popular-videos?limit=20<br>
                                    
                                </div>
                                <div class="result-metrics">
                                    <div class="metric">
                                        <div class="metric-label">响应时间</div>
                                        <div class="metric-value">7ms</div>
                                    </div>
                                    
                                        <div class="metric">
                                            <div class="metric-label">状态码</div>
                                            <div class="metric-value">404</div>
                                        </div>
                                    
                                    
                                        <div class="metric">
                                            <div class="metric-label">内容大小</div>
                                            <div class="metric-value">35</div>
                                        </div>
                                    
                                    
                                </div>
                            </div>
                          
                            <div class="result-card poor">
                                <div class="result-title">
                                    API-搜索数据
                                    <span class="critical-badge">关键</span>
                                    <span class="api-badge">API</span>
                                    <span class="grade-badge grade-D">D</span>
                                </div>
                                <div class="result-score grade-poor">32</div>
                                <div class="result-details">
                                    <strong>URL:</strong> http://localhost:3000/api/search?q=高清&limit=20<br>
                                    
                                </div>
                                <div class="result-metrics">
                                    <div class="metric">
                                        <div class="metric-label">响应时间</div>
                                        <div class="metric-value">1ms</div>
                                    </div>
                                    
                                        <div class="metric">
                                            <div class="metric-label">状态码</div>
                                            <div class="metric-value">404</div>
                                        </div>
                                    
                                    
                                        <div class="metric">
                                            <div class="metric-label">内容大小</div>
                                            <div class="metric-value">35</div>
                                        </div>
                                    
                                    
                                </div>
                            </div>
                          
                            <div class="result-card poor">
                                <div class="result-title">
                                    API-视频详情
                                    <span class="critical-badge">关键</span>
                                    <span class="api-badge">API</span>
                                    <span class="grade-badge grade-D">D</span>
                                </div>
                                <div class="result-score grade-poor">32</div>
                                <div class="result-details">
                                    <strong>URL:</strong> http://localhost:3000/api/video/details?id=SSIS-001<br>
                                    
                                </div>
                                <div class="result-metrics">
                                    <div class="metric">
                                        <div class="metric-label">响应时间</div>
                                        <div class="metric-value">3ms</div>
                                    </div>
                                    
                                        <div class="metric">
                                            <div class="metric-label">状态码</div>
                                            <div class="metric-value">404</div>
                                        </div>
                                    
                                    
                                        <div class="metric">
                                            <div class="metric-label">内容大小</div>
                                            <div class="metric-value">35</div>
                                        </div>
                                    
                                    
                                </div>
                            </div>
                          
                            <div class="result-card poor">
                                <div class="result-title">
                                    API-推荐视频
                                    
                                    <span class="api-badge">API</span>
                                    <span class="grade-badge grade-D">D</span>
                                </div>
                                <div class="result-score grade-poor">32</div>
                                <div class="result-details">
                                    <strong>URL:</strong> http://localhost:3000/api/recommendations?id=SSIS-001&limit=12<br>
                                    
                                </div>
                                <div class="result-metrics">
                                    <div class="metric">
                                        <div class="metric-label">响应时间</div>
                                        <div class="metric-value">3ms</div>
                                    </div>
                                    
                                        <div class="metric">
                                            <div class="metric-label">状态码</div>
                                            <div class="metric-value">404</div>
                                        </div>
                                    
                                    
                                        <div class="metric">
                                            <div class="metric-label">内容大小</div>
                                            <div class="metric-value">35</div>
                                        </div>
                                    
                                    
                                </div>
                            </div>
                          
                    </div>
                </div>
            
                <div class="category-section">
                    <h2 class="category-title">📊 用户页面 (3个测试)</h2>
                    <div class="result-grid">
                        
                            <div class="result-card warning">
                                <div class="result-title">
                                    用户中心
                                    
                                    
                                    <span class="grade-badge grade-C">C</span>
                                </div>
                                <div class="result-score grade-warning">60</div>
                                <div class="result-details">
                                    <strong>URL:</strong> http://localhost:3000/zh-CN/user/profile<br>
                                    
                                </div>
                                <div class="result-metrics">
                                    <div class="metric">
                                        <div class="metric-label">响应时间</div>
                                        <div class="metric-value">2ms</div>
                                    </div>
                                    
                                        <div class="metric">
                                            <div class="metric-label">状态码</div>
                                            <div class="metric-value">404</div>
                                        </div>
                                    
                                    
                                        <div class="metric">
                                            <div class="metric-label">内容大小</div>
                                            <div class="metric-value">35</div>
                                        </div>
                                    
                                    
                                </div>
                            </div>
                          
                            <div class="result-card warning">
                                <div class="result-title">
                                    收藏页面
                                    
                                    
                                    <span class="grade-badge grade-C">C</span>
                                </div>
                                <div class="result-score grade-warning">60</div>
                                <div class="result-details">
                                    <strong>URL:</strong> http://localhost:3000/zh-CN/user/favorites<br>
                                    
                                </div>
                                <div class="result-metrics">
                                    <div class="metric">
                                        <div class="metric-label">响应时间</div>
                                        <div class="metric-value">3ms</div>
                                    </div>
                                    
                                        <div class="metric">
                                            <div class="metric-label">状态码</div>
                                            <div class="metric-value">404</div>
                                        </div>
                                    
                                    
                                        <div class="metric">
                                            <div class="metric-label">内容大小</div>
                                            <div class="metric-value">35</div>
                                        </div>
                                    
                                    
                                </div>
                            </div>
                          
                            <div class="result-card warning">
                                <div class="result-title">
                                    观看历史
                                    
                                    
                                    <span class="grade-badge grade-C">C</span>
                                </div>
                                <div class="result-score grade-warning">60</div>
                                <div class="result-details">
                                    <strong>URL:</strong> http://localhost:3000/zh-CN/user/history<br>
                                    
                                </div>
                                <div class="result-metrics">
                                    <div class="metric">
                                        <div class="metric-label">响应时间</div>
                                        <div class="metric-value">2ms</div>
                                    </div>
                                    
                                        <div class="metric">
                                            <div class="metric-label">状态码</div>
                                            <div class="metric-value">404</div>
                                        </div>
                                    
                                    
                                        <div class="metric">
                                            <div class="metric-label">内容大小</div>
                                            <div class="metric-value">35</div>
                                        </div>
                                    
                                    
                                </div>
                            </div>
                          
                    </div>
                </div>
            
                <div class="category-section">
                    <h2 class="category-title">📊 静态页面 (3个测试)</h2>
                    <div class="result-grid">
                        
                            <div class="result-card warning">
                                <div class="result-title">
                                    关于页面
                                    
                                    
                                    <span class="grade-badge grade-C">C</span>
                                </div>
                                <div class="result-score grade-warning">60</div>
                                <div class="result-details">
                                    <strong>URL:</strong> http://localhost:3000/zh-CN/about<br>
                                    
                                </div>
                                <div class="result-metrics">
                                    <div class="metric">
                                        <div class="metric-label">响应时间</div>
                                        <div class="metric-value">1ms</div>
                                    </div>
                                    
                                        <div class="metric">
                                            <div class="metric-label">状态码</div>
                                            <div class="metric-value">404</div>
                                        </div>
                                    
                                    
                                        <div class="metric">
                                            <div class="metric-label">内容大小</div>
                                            <div class="metric-value">35</div>
                                        </div>
                                    
                                    
                                </div>
                            </div>
                          
                            <div class="result-card warning">
                                <div class="result-title">
                                    隐私政策
                                    
                                    
                                    <span class="grade-badge grade-C">C</span>
                                </div>
                                <div class="result-score grade-warning">60</div>
                                <div class="result-details">
                                    <strong>URL:</strong> http://localhost:3000/zh-CN/privacy<br>
                                    
                                </div>
                                <div class="result-metrics">
                                    <div class="metric">
                                        <div class="metric-label">响应时间</div>
                                        <div class="metric-value">3ms</div>
                                    </div>
                                    
                                        <div class="metric">
                                            <div class="metric-label">状态码</div>
                                            <div class="metric-value">404</div>
                                        </div>
                                    
                                    
                                        <div class="metric">
                                            <div class="metric-label">内容大小</div>
                                            <div class="metric-value">35</div>
                                        </div>
                                    
                                    
                                </div>
                            </div>
                          
                            <div class="result-card warning">
                                <div class="result-title">
                                    使用条款
                                    
                                    
                                    <span class="grade-badge grade-C">C</span>
                                </div>
                                <div class="result-score grade-warning">60</div>
                                <div class="result-details">
                                    <strong>URL:</strong> http://localhost:3000/zh-CN/terms<br>
                                    
                                </div>
                                <div class="result-metrics">
                                    <div class="metric">
                                        <div class="metric-label">响应时间</div>
                                        <div class="metric-value">3ms</div>
                                    </div>
                                    
                                        <div class="metric">
                                            <div class="metric-label">状态码</div>
                                            <div class="metric-value">404</div>
                                        </div>
                                    
                                    
                                        <div class="metric">
                                            <div class="metric-label">内容大小</div>
                                            <div class="metric-value">35</div>
                                        </div>
                                    
                                    
                                </div>
                            </div>
                          
                    </div>
                </div>
            
                <div class="category-section">
                    <h2 class="category-title">📊 标签页面 (2个测试)</h2>
                    <div class="result-grid">
                        
                            <div class="result-card warning">
                                <div class="result-title">
                                    标签页-高清
                                    
                                    
                                    <span class="grade-badge grade-C">C</span>
                                </div>
                                <div class="result-score grade-warning">60</div>
                                <div class="result-details">
                                    <strong>URL:</strong> http://localhost:3000/zh-CN/tag/hd<br>
                                    
                                </div>
                                <div class="result-metrics">
                                    <div class="metric">
                                        <div class="metric-label">响应时间</div>
                                        <div class="metric-value">2ms</div>
                                    </div>
                                    
                                        <div class="metric">
                                            <div class="metric-label">状态码</div>
                                            <div class="metric-value">404</div>
                                        </div>
                                    
                                    
                                        <div class="metric">
                                            <div class="metric-label">内容大小</div>
                                            <div class="metric-value">35</div>
                                        </div>
                                    
                                    
                                </div>
                            </div>
                          
                            <div class="result-card warning">
                                <div class="result-title">
                                    标签页-巨乳
                                    
                                    
                                    <span class="grade-badge grade-C">C</span>
                                </div>
                                <div class="result-score grade-warning">60</div>
                                <div class="result-details">
                                    <strong>URL:</strong> http://localhost:3000/zh-CN/tag/big-breasts<br>
                                    
                                </div>
                                <div class="result-metrics">
                                    <div class="metric">
                                        <div class="metric-label">响应时间</div>
                                        <div class="metric-value">3ms</div>
                                    </div>
                                    
                                        <div class="metric">
                                            <div class="metric-label">状态码</div>
                                            <div class="metric-value">404</div>
                                        </div>
                                    
                                    
                                        <div class="metric">
                                            <div class="metric-label">内容大小</div>
                                            <div class="metric-value">35</div>
                                        </div>
                                    
                                    
                                </div>
                            </div>
                          
                    </div>
                </div>
            
                <div class="category-section">
                    <h2 class="category-title">📊 数据页面 (2个测试)</h2>
                    <div class="result-grid">
                        
                            <div class="result-card warning">
                                <div class="result-title">
                                    排行榜
                                    
                                    
                                    <span class="grade-badge grade-C">C</span>
                                </div>
                                <div class="result-score grade-warning">60</div>
                                <div class="result-details">
                                    <strong>URL:</strong> http://localhost:3000/zh-CN/ranking<br>
                                    
                                </div>
                                <div class="result-metrics">
                                    <div class="metric">
                                        <div class="metric-label">响应时间</div>
                                        <div class="metric-value">2ms</div>
                                    </div>
                                    
                                        <div class="metric">
                                            <div class="metric-label">状态码</div>
                                            <div class="metric-value">404</div>
                                        </div>
                                    
                                    
                                        <div class="metric">
                                            <div class="metric-label">内容大小</div>
                                            <div class="metric-value">35</div>
                                        </div>
                                    
                                    
                                </div>
                            </div>
                          
                            <div class="result-card warning">
                                <div class="result-title">
                                    最新更新
                                    
                                    
                                    <span class="grade-badge grade-C">C</span>
                                </div>
                                <div class="result-score grade-warning">60</div>
                                <div class="result-details">
                                    <strong>URL:</strong> http://localhost:3000/zh-CN/latest<br>
                                    
                                </div>
                                <div class="result-metrics">
                                    <div class="metric">
                                        <div class="metric-label">响应时间</div>
                                        <div class="metric-value">4ms</div>
                                    </div>
                                    
                                        <div class="metric">
                                            <div class="metric-label">状态码</div>
                                            <div class="metric-value">404</div>
                                        </div>
                                    
                                    
                                        <div class="metric">
                                            <div class="metric-label">内容大小</div>
                                            <div class="metric-value">35</div>
                                        </div>
                                    
                                    
                                </div>
                            </div>
                          
                    </div>
                </div>
            
        </div>
        
        <div class="footer">
            <p>🎯 由 JAVFLIX.TV 性能优化团队生成 | 基于 Medium 和 freeCodeCamp 最佳实践</p>
            <p>💡 专注测试核心功能页面，排除分类页面的性能表现</p>
        </div>
    </div>
    
    <script>
        function getCategoryName(category) {
            const names = {
                'core': '核心页面',
                'video': '视频页面', 
                'search': '搜索功能',
                'api': 'API接口',
                'user': '用户页面',
                'static': '静态页面',
                'tag': '标签页面',
                'data': '数据页面',
                'other': '其他页面'
            };
            return names[category] || category;
        }
    </script>
</body>
</html>