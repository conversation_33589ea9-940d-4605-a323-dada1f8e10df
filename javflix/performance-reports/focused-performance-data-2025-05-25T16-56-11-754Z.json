[{"name": "首页", "url": "http://localhost:3000/zh-CN", "score": 60, "responseTime": 84, "status": 404, "contentLength": "35", "contentType": "application/json; charset=utf-8", "success": false, "performanceGrade": "C", "timestamp": "2025-05-25T16:56:07.262Z"}, {"name": "播放页-热门1", "url": "http://localhost:3000/zh-CN/video/SSIS-001", "score": 60, "responseTime": 1, "status": 404, "contentLength": "35", "contentType": "application/json; charset=utf-8", "success": false, "performanceGrade": "C", "timestamp": "2025-05-25T16:56:07.465Z"}, {"name": "播放页-热门2", "url": "http://localhost:3000/zh-CN/video/SSNI-999", "score": 60, "responseTime": 2, "status": 404, "contentLength": "35", "contentType": "application/json; charset=utf-8", "success": false, "performanceGrade": "C", "timestamp": "2025-05-25T16:56:07.669Z"}, {"name": "播放页-高清", "url": "http://localhost:3000/zh-CN/video/PRED-123", "score": 60, "responseTime": 3, "status": 404, "contentLength": "35", "contentType": "application/json; charset=utf-8", "success": false, "performanceGrade": "C", "timestamp": "2025-05-25T16:56:07.873Z"}, {"name": "播放页-VR", "url": "http://localhost:3000/zh-CN/video/VR-001", "score": 60, "responseTime": 2, "status": 404, "contentLength": "35", "contentType": "application/json; charset=utf-8", "success": false, "performanceGrade": "C", "timestamp": "2025-05-25T16:56:08.077Z"}, {"name": "搜索页面", "url": "http://localhost:3000/zh-CN/search?q=高清", "score": 60, "responseTime": 2, "status": 404, "contentLength": "35", "contentType": "application/json; charset=utf-8", "success": false, "performanceGrade": "C", "timestamp": "2025-05-25T16:56:08.280Z"}, {"name": "搜索结果-巨乳", "url": "http://localhost:3000/zh-CN/search?q=巨乳", "score": 60, "responseTime": 3, "status": 404, "contentLength": "35", "contentType": "application/json; charset=utf-8", "success": false, "performanceGrade": "C", "timestamp": "2025-05-25T16:56:08.485Z"}, {"name": "搜索结果-无码", "url": "http://localhost:3000/zh-CN/search?q=无码", "score": 60, "responseTime": 3, "status": 404, "contentLength": "35", "contentType": "application/json; charset=utf-8", "success": false, "performanceGrade": "C", "timestamp": "2025-05-25T16:56:08.689Z"}, {"name": "API-首页数据", "url": "http://localhost:3000/api/popular-videos?limit=20", "score": 32, "responseTime": 7, "status": 404, "contentLength": 35, "cacheControl": "none", "serverTime": "not provided", "dataValid": false, "success": false, "timestamp": "2025-05-25T16:56:08.899Z", "performanceGrade": "D"}, {"name": "API-搜索数据", "url": "http://localhost:3000/api/search?q=高清&limit=20", "score": 32, "responseTime": 1, "status": 404, "contentLength": 35, "cacheControl": "none", "serverTime": "not provided", "dataValid": false, "success": false, "timestamp": "2025-05-25T16:56:09.103Z", "performanceGrade": "D"}, {"name": "API-视频详情", "url": "http://localhost:3000/api/video/details?id=SSIS-001", "score": 32, "responseTime": 3, "status": 404, "contentLength": 35, "cacheControl": "none", "serverTime": "not provided", "dataValid": false, "success": false, "timestamp": "2025-05-25T16:56:09.307Z", "performanceGrade": "D"}, {"name": "API-推荐视频", "url": "http://localhost:3000/api/recommendations?id=SSIS-001&limit=12", "score": 32, "responseTime": 3, "status": 404, "contentLength": 35, "cacheControl": "none", "serverTime": "not provided", "dataValid": false, "success": false, "timestamp": "2025-05-25T16:56:09.512Z", "performanceGrade": "D"}, {"name": "用户中心", "url": "http://localhost:3000/zh-CN/user/profile", "score": 60, "responseTime": 2, "status": 404, "contentLength": "35", "contentType": "application/json; charset=utf-8", "success": false, "performanceGrade": "C", "timestamp": "2025-05-25T16:56:09.718Z"}, {"name": "收藏页面", "url": "http://localhost:3000/zh-CN/user/favorites", "score": 60, "responseTime": 3, "status": 404, "contentLength": "35", "contentType": "application/json; charset=utf-8", "success": false, "performanceGrade": "C", "timestamp": "2025-05-25T16:56:09.922Z"}, {"name": "观看历史", "url": "http://localhost:3000/zh-CN/user/history", "score": 60, "responseTime": 2, "status": 404, "contentLength": "35", "contentType": "application/json; charset=utf-8", "success": false, "performanceGrade": "C", "timestamp": "2025-05-25T16:56:10.126Z"}, {"name": "关于页面", "url": "http://localhost:3000/zh-CN/about", "score": 60, "responseTime": 1, "status": 404, "contentLength": "35", "contentType": "application/json; charset=utf-8", "success": false, "performanceGrade": "C", "timestamp": "2025-05-25T16:56:10.329Z"}, {"name": "隐私政策", "url": "http://localhost:3000/zh-CN/privacy", "score": 60, "responseTime": 3, "status": 404, "contentLength": "35", "contentType": "application/json; charset=utf-8", "success": false, "performanceGrade": "C", "timestamp": "2025-05-25T16:56:10.533Z"}, {"name": "使用条款", "url": "http://localhost:3000/zh-CN/terms", "score": 60, "responseTime": 3, "status": 404, "contentLength": "35", "contentType": "application/json; charset=utf-8", "success": false, "performanceGrade": "C", "timestamp": "2025-05-25T16:56:10.736Z"}, {"name": "标签页-高清", "url": "http://localhost:3000/zh-CN/tag/hd", "score": 60, "responseTime": 2, "status": 404, "contentLength": "35", "contentType": "application/json; charset=utf-8", "success": false, "performanceGrade": "C", "timestamp": "2025-05-25T16:56:10.939Z"}, {"name": "标签页-巨乳", "url": "http://localhost:3000/zh-CN/tag/big-breasts", "score": 60, "responseTime": 3, "status": 404, "contentLength": "35", "contentType": "application/json; charset=utf-8", "success": false, "performanceGrade": "C", "timestamp": "2025-05-25T16:56:11.144Z"}, {"name": "排行榜", "url": "http://localhost:3000/zh-CN/ranking", "score": 60, "responseTime": 2, "status": 404, "contentLength": "35", "contentType": "application/json; charset=utf-8", "success": false, "performanceGrade": "C", "timestamp": "2025-05-25T16:56:11.347Z"}, {"name": "最新更新", "url": "http://localhost:3000/zh-CN/latest", "score": 60, "responseTime": 4, "status": 404, "contentLength": "35", "contentType": "application/json; charset=utf-8", "success": false, "performanceGrade": "C", "timestamp": "2025-05-25T16:56:11.553Z"}]