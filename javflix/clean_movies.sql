-- 开始事务
BEGIN;

-- 清空所有与电影相关的数据
-- 由于有外键约束，我们需要按照正确的顺序删除

-- 1. 首先删除依赖于movies表的关联表
DELETE FROM video_processing_tasks;  -- 视频处理任务表
DELETE FROM magnets;
DELETE FROM movie_directors;
DELETE FROM movie_genres;
DELETE FROM movie_stars;
DELETE FROM similar_movies;

-- 2. 清空用户相关数据（收藏、观看历史等）- 只删除存在的表
DO $$
BEGIN
    -- 清空用户收藏表（如果存在）
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'user_favorites') THEN
        DELETE FROM user_favorites;
        RAISE NOTICE '已清空 user_favorites 表';
    ELSE
        RAISE NOTICE 'user_favorites 表不存在，跳过';
    END IF;

    -- 清空用户历史表（如果存在）
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'user_history') THEN
        DELETE FROM user_history;
        RAISE NOTICE '已清空 user_history 表';
    ELSE
        RAISE NOTICE 'user_history 表不存在，跳过';
    END IF;

    -- 清空收藏分类表（如果存在）
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'favorite_categories') THEN
        DELETE FROM favorite_categories;
        RAISE NOTICE '已清空 favorite_categories 表';
    ELSE
        RAISE NOTICE 'favorite_categories 表不存在，跳过';
    END IF;

    -- 清空收藏分类关联表（如果存在）
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'favorite_category_items') THEN
        DELETE FROM favorite_category_items;
        RAISE NOTICE '已清空 favorite_category_items 表';
    ELSE
        RAISE NOTICE 'favorite_category_items 表不存在，跳过';
    END IF;
END $$;

-- 3. 然后删除movies表中的所有数据
DELETE FROM movies;

-- 4. 重置ID序列
DO $$
BEGIN
    -- 重置主要表的序列
    IF EXISTS (SELECT 1 FROM pg_sequences WHERE sequencename = 'movies_id_seq') THEN
        ALTER SEQUENCE movies_id_seq RESTART WITH 1;
        RAISE NOTICE '已重置 movies_id_seq 序列';
    END IF;

    IF EXISTS (SELECT 1 FROM pg_sequences WHERE sequencename = 'magnets_id_seq') THEN
        ALTER SEQUENCE magnets_id_seq RESTART WITH 1;
        RAISE NOTICE '已重置 magnets_id_seq 序列';
    END IF;

    IF EXISTS (SELECT 1 FROM pg_sequences WHERE sequencename = 'video_processing_tasks_id_seq') THEN
        ALTER SEQUENCE video_processing_tasks_id_seq RESTART WITH 1;
        RAISE NOTICE '已重置 video_processing_tasks_id_seq 序列';
    END IF;

    -- 重置用户相关表的序列（如果存在）
    IF EXISTS (SELECT 1 FROM pg_sequences WHERE sequencename = 'user_favorites_id_seq') THEN
        ALTER SEQUENCE user_favorites_id_seq RESTART WITH 1;
        RAISE NOTICE '已重置 user_favorites_id_seq 序列';
    END IF;

    IF EXISTS (SELECT 1 FROM pg_sequences WHERE sequencename = 'user_history_id_seq') THEN
        ALTER SEQUENCE user_history_id_seq RESTART WITH 1;
        RAISE NOTICE '已重置 user_history_id_seq 序列';
    END IF;

    IF EXISTS (SELECT 1 FROM pg_sequences WHERE sequencename = 'favorite_categories_id_seq') THEN
        ALTER SEQUENCE favorite_categories_id_seq RESTART WITH 1;
        RAISE NOTICE '已重置 favorite_categories_id_seq 序列';
    END IF;

    IF EXISTS (SELECT 1 FROM pg_sequences WHERE sequencename = 'favorite_category_items_id_seq') THEN
        ALTER SEQUENCE favorite_category_items_id_seq RESTART WITH 1;
        RAISE NOTICE '已重置 favorite_category_items_id_seq 序列';
    END IF;
END $$;

-- 提交事务
COMMIT;
