// 在浏览器控制台运行这个脚本来调试点赞API调用

async function debugProfileLikes() {
  console.log('🔍 开始调试个人页面点赞功能...');
  
  // 1. 检查认证状态
  const authToken = localStorage.getItem('auth_token');
  const authState = document.cookie.includes('auth_state=true');
  
  console.log('认证状态:');
  console.log('- localStorage token:', authToken ? '存在' : '不存在');
  console.log('- auth_state cookie:', authState ? '存在' : '不存在');
  
  if (!authToken && !authState) {
    console.error('❌ 用户未登录，无法继续调试');
    return;
  }
  
  // 2. 测试点赞API
  console.log('\n测试点赞API...');
  
  try {
    const response = await fetch('/api/proxy/users/likes?page=1&limit=8', {
      method: 'GET',
      credentials: 'include',
      headers: {
        'Authorization': authToken ? `Bearer ${authToken}` : undefined
      }
    });
    
    console.log('API响应状态:', response.status);
    
    if (response.ok) {
      const data = await response.json();
      console.log('✅ 点赞API调用成功');
      console.log('响应数据结构:', Object.keys(data));
      
      if (data.success) {
        console.log('数据格式正确:', data.success);
        
        if (data.data && data.data.data) {
          console.log(`找到 ${data.data.data.length} 个点赞`);
          console.log('点赞列表:');
          data.data.data.slice(0, 3).forEach((video, index) => {
            console.log(`${index + 1}. ${video.title.substring(0, 50)}...`);
          });
          
          // 检查数据结构
          const firstVideo = data.data.data[0];
          if (firstVideo) {
            console.log('\n第一个视频的完整数据结构:');
            console.log('- id:', firstVideo.id);
            console.log('- title:', firstVideo.title);
            console.log('- movie_id:', firstVideo.movie_id);
            console.log('- image_url:', firstVideo.image_url);
            console.log('- duration:', firstVideo.duration);
            console.log('- liked_at:', firstVideo.liked_at);
          }
        } else {
          console.log('❌ 数据结构异常: data.data 不存在或为空');
          console.log('完整响应:', data);
        }
      } else {
        console.log('❌ API返回失败状态:', data.message);
      }
    } else {
      console.log('❌ API请求失败:', response.status);
      const errorData = await response.json();
      console.log('错误详情:', errorData);
    }
  } catch (error) {
    console.error('❌ API调用异常:', error);
  }
  
  // 3. 检查前端组件状态
  console.log('\n检查前端组件状态...');
  
  // 检查是否在点赞标签页
  const currentTab = window.location.hash || document.querySelector('[data-tab="likes"]');
  console.log('当前标签页:', currentTab);
  
  // 检查页面上的点赞列表元素
  const likeElements = document.querySelectorAll('[data-testid="liked-video"], .liked-video');
  console.log('页面上的点赞元素数量:', likeElements.length);
  
  // 检查是否显示"noLikes"消息
  const noLikesMessage = document.querySelector('[data-testid="no-likes"]') || 
                        document.textContent?.includes('noLikes') ||
                        document.textContent?.includes('去浏览视频');
  console.log('是否显示noLikes消息:', !!noLikesMessage);
  
  console.log('\n🔧 调试建议:');
  console.log('1. 如果API返回了数据但页面显示空，可能是前端数据映射问题');
  console.log('2. 如果API调用失败，检查认证token是否正确');
  console.log('3. 检查浏览器网络面板中的实际API调用');
  console.log('4. 确认点击了正确的"likes"标签页');
}

// 自动运行调试
debugProfileLikes();

// 也可以手动在控制台调用: debugProfileLikes()
console.log('调试脚本已加载。可以调用 debugProfileLikes() 来重新运行调试。'); 