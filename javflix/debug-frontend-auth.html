<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JAVFLIX 认证调试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #1a1a1a; color: white; }
        .container { max-width: 800px; margin: 0 auto; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #333; border-radius: 5px; background: #2a2a2a; }
        .status { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .success { background: #155724; border: 1px solid #4caf50; }
        .error { background: #721c24; border: 1px solid #f44336; }
        .warning { background: #856404; border: 1px solid #ffc107; }
        button { padding: 10px 20px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; }
        button:hover { background: #0056b3; }
        textarea { width: 100%; height: 200px; background: #333; color: white; border: 1px solid #555; padding: 10px; }
        .token { word-break: break-all; font-family: monospace; font-size: 12px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 JAVFLIX 认证状态调试</h1>
        
        <div class="section">
            <h2>📋 当前认证状态</h2>
            <div id="authStatus"></div>
        </div>

        <div class="section">
            <h2>🍪 Cookies</h2>
            <div id="cookieStatus"></div>
        </div>

        <div class="section">
            <h2>💾 LocalStorage</h2>
            <div id="localStorageStatus"></div>
        </div>

        <div class="section">
            <h2>🔑 API 测试</h2>
            <button onclick="testLogin()">测试登录</button>
            <button onclick="testCurrentUser()">获取当前用户</button>
            <button onclick="testFavorites()">测试收藏API</button>
            <button onclick="testLikes()">测试点赞API</button>
            <div id="apiResults"></div>
        </div>

        <div class="section">
            <h2>📄 完整日志</h2>
            <textarea id="logOutput" readonly></textarea>
            <button onclick="clearLog()">清空日志</button>
        </div>
    </div>

    <script>
        let logOutput = [];

        function log(message) {
            const timestamp = new Date().toISOString().substr(11, 8);
            logOutput.push(`[${timestamp}] ${message}`);
            document.getElementById('logOutput').value = logOutput.join('\n');
            console.log(message);
        }

        function clearLog() {
            logOutput = [];
            document.getElementById('logOutput').value = '';
        }

        function checkAuthStatus() {
            log('开始检查认证状态...');
            
            // 检查cookies
            const cookies = document.cookie.split(';');
            let authToken = null;
            let authState = null;
            
            for (const cookie of cookies) {
                const [name, value] = cookie.trim().split('=');
                if (name === 'auth_token') authToken = value;
                if (name === 'auth_state') authState = value;
            }

            // 检查localStorage
            const localToken = localStorage.getItem('auth_token');
            const localUser = localStorage.getItem('current_user');
            const authTimestamp = localStorage.getItem('auth_timestamp');

            // 显示状态
            let statusHtml = '';
            
            if (authState === 'true' || localToken) {
                statusHtml += '<div class="status success">✅ 用户已登录</div>';
            } else {
                statusHtml += '<div class="status error">❌ 用户未登录</div>';
            }

            document.getElementById('authStatus').innerHTML = statusHtml;

            // 显示cookies
            let cookieHtml = '';
            if (authToken) {
                cookieHtml += `<div class="status success">✅ auth_token cookie: ${authToken.substring(0, 20)}...</div>`;
            } else {
                cookieHtml += '<div class="status error">❌ 没有 auth_token cookie</div>';
            }
            
            if (authState) {
                cookieHtml += `<div class="status success">✅ auth_state cookie: ${authState}</div>`;
            } else {
                cookieHtml += '<div class="status error">❌ 没有 auth_state cookie</div>';
            }

            document.getElementById('cookieStatus').innerHTML = cookieHtml;

            // 显示localStorage
            let localStorageHtml = '';
            if (localToken) {
                localStorageHtml += `<div class="status success">✅ localStorage token: ${localToken.substring(0, 20)}...</div>`;
            } else {
                localStorageHtml += '<div class="status error">❌ 没有 localStorage token</div>';
            }

            if (localUser) {
                try {
                    const user = JSON.parse(localUser);
                    localStorageHtml += `<div class="status success">✅ 缓存用户: ${user.username} (${user.email})</div>`;
                } catch (e) {
                    localStorageHtml += '<div class="status error">❌ 用户数据格式错误</div>';
                }
            } else {
                localStorageHtml += '<div class="status error">❌ 没有缓存用户数据</div>';
            }

            if (authTimestamp) {
                const time = new Date(parseInt(authTimestamp));
                localStorageHtml += `<div class="status success">✅ 认证时间: ${time.toLocaleString()}</div>`;
            }

            document.getElementById('localStorageStatus').innerHTML = localStorageHtml;

            log(`Cookie auth_token: ${authToken ? '存在' : '不存在'}`);
            log(`Cookie auth_state: ${authState || '不存在'}`);
            log(`LocalStorage token: ${localToken ? '存在' : '不存在'}`);
            log(`LocalStorage user: ${localUser ? '存在' : '不存在'}`);
        }

        async function testLogin() {
            log('测试登录 <EMAIL>...');
            try {
                const response = await fetch('/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    credentials: 'include',
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: '123456'
                    })
                });

                const data = await response.json();
                
                if (data.success) {
                    log('✅ 登录成功！');
                    log(`用户: ${data.user.username}`);
                    if (data.token) {
                        localStorage.setItem('auth_token', data.token);
                        log('✅ Token已保存到localStorage');
                    }
                    checkAuthStatus();
                } else {
                    log(`❌ 登录失败: ${data.error}`);
                }
            } catch (error) {
                log(`❌ 登录请求失败: ${error.message}`);
            }
        }

        async function testCurrentUser() {
            log('测试获取当前用户...');
            try {
                const response = await fetch('/api/auth/user', {
                    method: 'GET',
                    credentials: 'include',
                });

                if (response.ok) {
                    const data = await response.json();
                    log(`✅ 获取用户成功: ${data.username} (${data.email})`);
                } else {
                    log(`❌ 获取用户失败: ${response.status}`);
                }
            } catch (error) {
                log(`❌ 获取用户请求失败: ${error.message}`);
            }
        }

        async function testFavorites() {
            log('测试获取收藏列表...');
            try {
                const response = await fetch('/api/proxy/users/favorites?page=1&limit=5', {
                    method: 'GET',
                    credentials: 'include',
                });

                if (response.ok) {
                    const data = await response.json();
                    if (data.success && data.data.videos) {
                        log(`✅ 获取收藏成功: ${data.data.videos.length} 个收藏`);
                        document.getElementById('apiResults').innerHTML = 
                            `<div class="status success">收藏数量: ${data.data.videos.length}</div>`;
                    } else {
                        log(`❌ 收藏数据格式错误: ${JSON.stringify(data)}`);
                    }
                } else {
                    log(`❌ 获取收藏失败: ${response.status}`);
                    const errorData = await response.json();
                    log(`错误详情: ${JSON.stringify(errorData)}`);
                }
            } catch (error) {
                log(`❌ 收藏请求失败: ${error.message}`);
            }
        }

        async function testLikes() {
            log('测试获取点赞列表...');
            try {
                const response = await fetch('/api/proxy/users/likes?page=1&limit=5', {
                    method: 'GET',
                    credentials: 'include',
                });

                if (response.ok) {
                    const data = await response.json();
                    if (data.success && data.data.data) {
                        log(`✅ 获取点赞成功: ${data.data.data.length} 个点赞`);
                        document.getElementById('apiResults').innerHTML += 
                            `<div class="status success">点赞数量: ${data.data.data.length}</div>`;
                    } else {
                        log(`❌ 点赞数据格式错误: ${JSON.stringify(data)}`);
                    }
                } else {
                    log(`❌ 获取点赞失败: ${response.status}`);
                }
            } catch (error) {
                log(`❌ 点赞请求失败: ${error.message}`);
            }
        }

        // 页面加载时检查状态
        window.onload = function() {
            log('页面加载完成，开始调试...');
            checkAuthStatus();
        };
    </script>
</body>
</html> 