<!DOCTYPE html>
<html>
<head>
    <title>设置测试Token</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; background: #111; color: white; }
        .container { max-width: 600px; margin: 0 auto; }
        .success { color: #4CAF50; }
        .error { color: #f44336; }
        button { background: #ff6b6b; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; }
        input { width: 100%; padding: 10px; margin: 10px 0; background: #222; color: white; border: 1px solid #555; }
    </style>
</head>
<body>
    <div class="container">
        <h1>JAVFLIX 测试Token设置</h1>
        
        <h3>当前认证状态</h3>
        <div id="status"></div>
        
        <h3>设置测试Token</h3>
        <input type="text" id="tokenInput" placeholder="输入测试token" 
               value="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6MTQsImlhdCI6MTc0ODM0MTIxOSwiZXhwIjoxNzQ4OTQ2MDE5fQ.gad_80DRk7RJv8IoaAMyRlBdQBWxUZmsPmyOYVE082Y">
        <br>
        <button onclick="setToken()">设置Token</button>
        <button onclick="clearToken()">清除Token</button>
        <button onclick="testLike()">测试点赞API</button>
        <button onclick="testFavorite()">测试收藏API</button>
        
        <h3>测试结果</h3>
        <div id="result"></div>
        
        <h3>调试信息</h3>
        <div id="debug"></div>
    </div>
    
    <script>
        function updateStatus() {
            const token = localStorage.getItem('auth_token');
            const status = document.getElementById('status');
            
            if (token) {
                status.innerHTML = `<div class="success">✅ Token已设置: ${token.substring(0, 20)}...</div>`;
            } else {
                status.innerHTML = '<div class="error">❌ 未找到Token</div>';
            }
        }
        
        function setToken() {
            const token = document.getElementById('tokenInput').value;
            if (token) {
                localStorage.setItem('auth_token', token);
                localStorage.setItem('auth_timestamp', Date.now().toString());
                
                // 设置cookie
                document.cookie = `auth_token=${token}; path=/; max-age=${7 * 24 * 60 * 60}`;
                document.cookie = `auth_state=true; path=/; max-age=${7 * 24 * 60 * 60}`;
                
                updateStatus();
                document.getElementById('result').innerHTML = '<div class="success">Token已设置成功</div>';
            }
        }
        
        function clearToken() {
            localStorage.removeItem('auth_token');
            localStorage.removeItem('auth_timestamp');
            localStorage.removeItem('current_user');
            
            // 清除cookie
            document.cookie = 'auth_token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT';
            document.cookie = 'auth_state=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT';
            
            updateStatus();
            document.getElementById('result').innerHTML = '<div class="success">Token已清除</div>';
        }
        
        async function testLike() {
            const result = document.getElementById('result');
            const debug = document.getElementById('debug');
            result.innerHTML = '<div>正在测试点赞...</div>';
            
            try {
                const response = await fetch('/api/proxy/users/likes/58', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
                    }
                });
                
                const data = await response.json();
                debug.innerHTML = `<pre>${JSON.stringify(data, null, 2)}</pre>`;
                
                if (response.ok && data.success) {
                    result.innerHTML = '<div class="success">✅ 点赞API测试成功</div>';
                } else {
                    result.innerHTML = `<div class="error">❌ 点赞API失败: ${data.message || response.statusText}</div>`;
                }
            } catch (error) {
                result.innerHTML = `<div class="error">❌ 网络错误: ${error.message}</div>`;
                debug.innerHTML = `<pre>${error.stack}</pre>`;
            }
        }
        
        async function testFavorite() {
            const result = document.getElementById('result');
            const debug = document.getElementById('debug');
            result.innerHTML = '<div>正在测试收藏...</div>';
            
            try {
                const response = await fetch('/api/proxy/users/favorites/58', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
                    }
                });
                
                const data = await response.json();
                debug.innerHTML = `<pre>${JSON.stringify(data, null, 2)}</pre>`;
                
                if (response.ok && data.success) {
                    result.innerHTML = '<div class="success">✅ 收藏API测试成功</div>';
                } else {
                    result.innerHTML = `<div class="error">❌ 收藏API失败: ${data.message || response.statusText}</div>`;
                }
            } catch (error) {
                result.innerHTML = `<div class="error">❌ 网络错误: ${error.message}</div>`;
                debug.innerHTML = `<pre>${error.stack}</pre>`;
            }
        }
        
        // 页面加载时更新状态
        updateStatus();
    </script>
</body>
</html>