<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>跨页面同步测试 - JAVFLIX 统计系统</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            max-width: 1400px; 
            margin: 20px auto; 
            padding: 20px; 
            background: #f5f5f5;
        }
        .test-container { 
            display: grid; 
            grid-template-columns: 1fr 1fr; 
            gap: 20px; 
            margin-bottom: 20px;
        }
        .page-simulator { 
            background: white;
            padding: 20px; 
            border: 2px solid #ddd; 
            border-radius: 8px; 
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .video-player { border-color: #007bff; }
        .profile-page { border-color: #28a745; }
        
        .stats-display {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            font-family: monospace;
        }
        
        .btn {
            display: inline-block;
            padding: 8px 16px;
            margin: 5px;
            background: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            border: none;
            cursor: pointer;
            font-size: 14px;
        }
        
        .btn:hover { background: #0056b3; }
        .btn-success { background: #28a745; }
        .btn-warning { background: #ffc107; color: #212529; }
        .btn-danger { background: #dc3545; }
        
        .event-log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            max-height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            margin-top: 10px;
        }
        
        .status-indicator {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-connected { background: #28a745; }
        .status-disconnected { background: #dc3545; }
        .status-syncing { background: #ffc107; }
        
        .video-list {
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 10px;
        }
        
        .video-item {
            padding: 8px;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .video-item:last-child { border-bottom: none; }
        
        .sync-status {
            background: #e9ecef;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        
        h1 { text-align: center; color: #333; }
        h2 { color: #007bff; margin-top: 0; }
        h3 { color: #666; }
    </style>
</head>
<body>
    <h1>🔄 JAVFLIX 跨页面同步测试系统</h1>
    
    <!-- 同步状态总览 -->
    <div class="sync-status">
        <h3>🎯 系统状态总览</h3>
        <div style="display: grid; grid-template-columns: repeat(4, 1fr); gap: 15px; text-align: center;">
            <div>
                <div><span id="socketStatus" class="status-indicator status-disconnected"></span>Socket.IO</div>
                <div id="socketStatusText">检查中...</div>
            </div>
            <div>
                <div><span id="syncManagerStatus" class="status-indicator status-disconnected"></span>同步管理器</div>
                <div id="syncManagerStatusText">检查中...</div>
            </div>
            <div>
                <div><span id="localStorageStatus" class="status-indicator status-disconnected"></span>本地存储</div>
                <div id="localStorageStatusText">检查中...</div>
            </div>
            <div>
                <div><span id="eventSystemStatus" class="status-indicator status-disconnected"></span>事件系统</div>
                <div id="eventSystemStatusText">检查中...</div>
            </div>
        </div>
    </div>

    <!-- 页面模拟器 -->
    <div class="test-container">
        <!-- 播放页面模拟器 -->
        <div class="page-simulator video-player">
            <h2>🎬 播放页面模拟器</h2>
            <p><strong>技术栈:</strong> useVideoStats + Socket.IO + 融合API</p>
            
            <div class="stats-display">
                <h4>当前视频统计 (视频ID: <span id="currentVideoId">123</span>)</h4>
                <div>👀 观看次数: <span id="playerViews">0</span></div>
                <div>👍 点赞数量: <span id="playerLikes">0</span></div>
                <div>❤️ 收藏数量: <span id="playerFavorites">0</span></div>
                <div>🔗 Socket连接: <span id="playerSocketStatus">❌</span></div>
                <div>📊 数据来源: <span id="playerDataSource">融合API</span></div>
            </div>
            
            <div>
                <h4>用户操作</h4>
                <button class="btn" onclick="simulatePlayerView()">📺 观看视频</button>
                <button class="btn btn-success" onclick="simulatePlayerLike()">👍 点赞</button>
                <button class="btn btn-warning" onclick="simulatePlayerFavorite()">❤️ 收藏</button>
                <button class="btn" onclick="refreshPlayerStats()">🔄 刷新统计</button>
            </div>
            
            <div>
                <h4>实时事件日志</h4>
                <div id="playerEventLog" class="event-log">等待事件...</div>
            </div>
        </div>

        <!-- 个人页面模拟器 -->
        <div class="page-simulator profile-page">
            <h2>📱 个人资料页面模拟器</h2>
            <p><strong>技术栈:</strong> sync-manager + 事件监听 + 本地缓存</p>
            
            <div class="stats-display">
                <h4>用户统计概览</h4>
                <div>📊 总点赞: <span id="profileTotalLikes">0</span></div>
                <div>📊 总收藏: <span id="profileTotalFavorites">0</span></div>
                <div>🔄 同步状态: <span id="profileSyncStatus">待检查</span></div>
                <div>💾 本地缓存: <span id="profileCacheStatus">待检查</span></div>
            </div>
            
            <div>
                <h4>我的点赞列表</h4>
                <div id="profileLikedVideos" class="video-list">
                    <div class="video-item">
                        <span>视频123 - 测试视频</span>
                        <button class="btn btn-danger" onclick="removeFromProfile(123, 'like')">取消点赞</button>
                    </div>
                </div>
            </div>
            
            <div>
                <h4>我的收藏列表</h4>
                <div id="profileFavoriteVideos" class="video-list">
                    <div class="video-item">
                        <span>视频123 - 测试视频</span>
                        <button class="btn btn-danger" onclick="removeFromProfile(123, 'favorite')">取消收藏</button>
                    </div>
                </div>
            </div>
            
            <div>
                <h4>同步事件日志</h4>
                <div id="profileEventLog" class="event-log">等待事件...</div>
            </div>
        </div>
    </div>

    <!-- 测试控制面板 -->
    <div style="background: white; padding: 20px; border-radius: 8px; border: 2px solid #6c757d;">
        <h2>🧪 测试控制面板</h2>
        
        <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 15px;">
            <div>
                <h4>基础功能测试</h4>
                <button class="btn" onclick="testBasicSync()">测试基础同步</button>
                <button class="btn" onclick="testCrossPageEvents()">测试跨页面事件</button>
                <button class="btn" onclick="testLocalStorage()">测试本地存储</button>
            </div>
            
            <div>
                <h4>高级功能测试</h4>
                <button class="btn btn-warning" onclick="testOfflineMode()">测试离线模式</button>
                <button class="btn btn-warning" onclick="testConflictResolution()">测试冲突解决</button>
                <button class="btn btn-warning" onclick="testBatchOperations()">测试批量操作</button>
            </div>
            
            <div>
                <h4>系统管理</h4>
                <button class="btn btn-success" onclick="initializeSystem()">初始化系统</button>
                <button class="btn btn-danger" onclick="clearAllData()">清空所有数据</button>
                <button class="btn" onclick="exportTestReport()">导出测试报告</button>
            </div>
        </div>
        
        <div style="margin-top: 15px;">
            <h4>测试结果</h4>
            <div id="testResults" class="event-log" style="max-height: 150px;">
                测试结果将显示在这里...
            </div>
        </div>
    </div>

    <script>
        // 全局状态管理
        let testState = {
            currentVideoId: 123,
            playerStats: { views: 0, likes: 0, favorites: 0 },
            profileStats: { totalLikes: 0, totalFavorites: 0 },
            likedVideos: [123],
            favoriteVideos: [123],
            socketConnected: false,
            syncManagerActive: false,
            eventListeners: [],
            testResults: []
        };

        // 初始化系统
        function initializeSystem() {
            logTestResult('🚀 初始化JAVFLIX跨页面同步系统...');
            
            // 检查系统状态
            checkSystemStatus();
            
            // 设置事件监听器
            setupEventListeners();
            
            // 模拟初始数据
            loadInitialData();
            
            logTestResult('✅ 系统初始化完成');
        }

        // 检查系统状态
        function checkSystemStatus() {
            // 检查Socket.IO状态
            const socketStatus = typeof io !== 'undefined';
            updateStatusIndicator('socketStatus', socketStatus);
            document.getElementById('socketStatusText').textContent = socketStatus ? '已连接' : '未连接';
            
            // 检查同步管理器
            const syncManagerStatus = typeof window.syncManager !== 'undefined' || localStorage.getItem('sync_manager_active');
            updateStatusIndicator('syncManagerStatus', syncManagerStatus);
            document.getElementById('syncManagerStatusText').textContent = syncManagerStatus ? '活跃' : '未激活';
            
            // 检查本地存储
            const localStorageStatus = typeof localStorage !== 'undefined';
            updateStatusIndicator('localStorageStatus', localStorageStatus);
            document.getElementById('localStorageStatusText').textContent = localStorageStatus ? '可用' : '不可用';
            
            // 检查事件系统
            const eventSystemStatus = typeof window.addEventListener !== 'undefined';
            updateStatusIndicator('eventSystemStatus', eventSystemStatus);
            document.getElementById('eventSystemStatusText').textContent = eventSystemStatus ? '正常' : '异常';
        }

        // 更新状态指示器
        function updateStatusIndicator(elementId, isActive) {
            const element = document.getElementById(elementId);
            element.className = `status-indicator ${isActive ? 'status-connected' : 'status-disconnected'}`;
        }

        // 设置事件监听器
        function setupEventListeners() {
            // 监听点赞状态变化
            window.addEventListener('likeStatusChanged', function(event) {
                const { videoId, isLiked } = event.detail;
                logPlayerEvent(`点赞状态变化: 视频${videoId} ${isLiked ? '已点赞' : '取消点赞'}`);
                logProfileEvent(`收到点赞同步: 视频${videoId} ${isLiked ? '添加到列表' : '从列表移除'}`);
                
                // 更新播放页面统计
                if (videoId == testState.currentVideoId) {
                    if (isLiked) {
                        testState.playerStats.likes++;
                    } else {
                        testState.playerStats.likes = Math.max(0, testState.playerStats.likes - 1);
                    }
                    updatePlayerDisplay();
                }
                
                // 更新个人页面列表
                updateProfileLikedVideos(videoId, isLiked);
            });

            // 监听收藏状态变化
            window.addEventListener('favoriteStatusChanged', function(event) {
                const { videoId, isFavorited } = event.detail;
                logPlayerEvent(`收藏状态变化: 视频${videoId} ${isFavorited ? '已收藏' : '取消收藏'}`);
                logProfileEvent(`收到收藏同步: 视频${videoId} ${isFavorited ? '添加到列表' : '从列表移除'}`);
                
                // 更新播放页面统计
                if (videoId == testState.currentVideoId) {
                    if (isFavorited) {
                        testState.playerStats.favorites++;
                    } else {
                        testState.playerStats.favorites = Math.max(0, testState.playerStats.favorites - 1);
                    }
                    updatePlayerDisplay();
                }
                
                // 更新个人页面列表
                updateProfileFavoriteVideos(videoId, isFavorited);
            });

            logTestResult('📡 事件监听器设置完成');
        }

        // 加载初始数据
        function loadInitialData() {
            // 从localStorage加载数据
            const savedLikes = JSON.parse(localStorage.getItem('test_liked_videos') || '[]');
            const savedFavorites = JSON.parse(localStorage.getItem('test_favorite_videos') || '[]');
            
            testState.likedVideos = savedLikes;
            testState.favoriteVideos = savedFavorites;
            testState.profileStats.totalLikes = savedLikes.length;
            testState.profileStats.totalFavorites = savedFavorites.length;
            
            // 如果当前视频在列表中，更新播放页面统计
            if (savedLikes.includes(testState.currentVideoId)) {
                testState.playerStats.likes = 1;
            }
            if (savedFavorites.includes(testState.currentVideoId)) {
                testState.playerStats.favorites = 1;
            }
            
            updatePlayerDisplay();
            updateProfileDisplay();
            
            logTestResult('📊 初始数据加载完成');
        }

        // 播放页面操作模拟
        function simulatePlayerView() {
            testState.playerStats.views++;
            updatePlayerDisplay();
            logPlayerEvent(`观看次数增加: ${testState.playerStats.views}`);
            logTestResult('✅ 播放页面观看操作完成');
        }

        function simulatePlayerLike() {
            const videoId = testState.currentVideoId;
            const isCurrentlyLiked = testState.likedVideos.includes(videoId);
            const newLikedStatus = !isCurrentlyLiked;
            
            // 更新本地状态
            localStorage.setItem(`like_${videoId}`, JSON.stringify(newLikedStatus));
            
            if (newLikedStatus) {
                testState.likedVideos.push(videoId);
                testState.playerStats.likes++;
                testState.profileStats.totalLikes++;
            } else {
                testState.likedVideos = testState.likedVideos.filter(id => id !== videoId);
                testState.playerStats.likes = Math.max(0, testState.playerStats.likes - 1);
                testState.profileStats.totalLikes = Math.max(0, testState.profileStats.totalLikes - 1);
            }
            
            // 保存到localStorage
            localStorage.setItem('test_liked_videos', JSON.stringify(testState.likedVideos));
            
            // 触发跨页面事件
            window.dispatchEvent(new CustomEvent('likeStatusChanged', {
                detail: { videoId, isLiked: newLikedStatus }
            }));
            
            updatePlayerDisplay();
            updateProfileDisplay();
            
            logTestResult(`✅ 播放页面点赞操作: ${newLikedStatus ? '点赞' : '取消点赞'}`);
        }

        function simulatePlayerFavorite() {
            const videoId = testState.currentVideoId;
            const isCurrentlyFavorited = testState.favoriteVideos.includes(videoId);
            const newFavoritedStatus = !isCurrentlyFavorited;
            
            // 更新本地状态
            localStorage.setItem(`favorite_${videoId}`, JSON.stringify(newFavoritedStatus));
            
            if (newFavoritedStatus) {
                testState.favoriteVideos.push(videoId);
                testState.playerStats.favorites++;
                testState.profileStats.totalFavorites++;
            } else {
                testState.favoriteVideos = testState.favoriteVideos.filter(id => id !== videoId);
                testState.playerStats.favorites = Math.max(0, testState.playerStats.favorites - 1);
                testState.profileStats.totalFavorites = Math.max(0, testState.profileStats.totalFavorites - 1);
            }
            
            // 保存到localStorage
            localStorage.setItem('test_favorite_videos', JSON.stringify(testState.favoriteVideos));
            
            // 触发跨页面事件
            window.dispatchEvent(new CustomEvent('favoriteStatusChanged', {
                detail: { videoId, isFavorited: newFavoritedStatus }
            }));
            
            updatePlayerDisplay();
            updateProfileDisplay();
            
            logTestResult(`✅ 播放页面收藏操作: ${newFavoritedStatus ? '收藏' : '取消收藏'}`);
        }

        function refreshPlayerStats() {
            logPlayerEvent('刷新统计数据...');
            // 模拟从融合API获取数据
            setTimeout(() => {
                logPlayerEvent('统计数据已刷新');
                logTestResult('✅ 播放页面统计刷新完成');
            }, 500);
        }

        // 个人页面操作模拟
        function removeFromProfile(videoId, type) {
            if (type === 'like') {
                testState.likedVideos = testState.likedVideos.filter(id => id !== videoId);
                testState.profileStats.totalLikes = Math.max(0, testState.profileStats.totalLikes - 1);
                if (videoId == testState.currentVideoId) {
                    testState.playerStats.likes = 0;
                }
                localStorage.setItem('test_liked_videos', JSON.stringify(testState.likedVideos));
                localStorage.setItem(`like_${videoId}`, JSON.stringify(false));
                
                window.dispatchEvent(new CustomEvent('likeStatusChanged', {
                    detail: { videoId, isLiked: false }
                }));
            } else if (type === 'favorite') {
                testState.favoriteVideos = testState.favoriteVideos.filter(id => id !== videoId);
                testState.profileStats.totalFavorites = Math.max(0, testState.profileStats.totalFavorites - 1);
                if (videoId == testState.currentVideoId) {
                    testState.playerStats.favorites = 0;
                }
                localStorage.setItem('test_favorite_videos', JSON.stringify(testState.favoriteVideos));
                localStorage.setItem(`favorite_${videoId}`, JSON.stringify(false));
                
                window.dispatchEvent(new CustomEvent('favoriteStatusChanged', {
                    detail: { videoId, isFavorited: false }
                }));
            }
            
            updatePlayerDisplay();
            updateProfileDisplay();
            
            logTestResult(`✅ 个人页面移除操作: ${type} 视频${videoId}`);
        }

        // 更新显示
        function updatePlayerDisplay() {
            document.getElementById('playerViews').textContent = testState.playerStats.views;
            document.getElementById('playerLikes').textContent = testState.playerStats.likes;
            document.getElementById('playerFavorites').textContent = testState.playerStats.favorites;
            document.getElementById('playerSocketStatus').textContent = testState.socketConnected ? '✅' : '❌';
        }

        function updateProfileDisplay() {
            document.getElementById('profileTotalLikes').textContent = testState.profileStats.totalLikes;
            document.getElementById('profileTotalFavorites').textContent = testState.profileStats.totalFavorites;
            document.getElementById('profileSyncStatus').textContent = '正常';
            document.getElementById('profileCacheStatus').textContent = '已缓存';
            
            updateProfileLikedVideos();
            updateProfileFavoriteVideos();
        }

        function updateProfileLikedVideos(videoId = null, isLiked = null) {
            const container = document.getElementById('profileLikedVideos');
            container.innerHTML = '';
            
            testState.likedVideos.forEach(id => {
                const item = document.createElement('div');
                item.className = 'video-item';
                item.innerHTML = `
                    <span>视频${id} - 测试视频</span>
                    <button class="btn btn-danger" onclick="removeFromProfile(${id}, 'like')">取消点赞</button>
                `;
                container.appendChild(item);
            });
            
            if (testState.likedVideos.length === 0) {
                container.innerHTML = '<div style="text-align: center; color: #666; padding: 20px;">暂无点赞视频</div>';
            }
        }

        function updateProfileFavoriteVideos(videoId = null, isFavorited = null) {
            const container = document.getElementById('profileFavoriteVideos');
            container.innerHTML = '';
            
            testState.favoriteVideos.forEach(id => {
                const item = document.createElement('div');
                item.className = 'video-item';
                item.innerHTML = `
                    <span>视频${id} - 测试视频</span>
                    <button class="btn btn-danger" onclick="removeFromProfile(${id}, 'favorite')">取消收藏</button>
                `;
                container.appendChild(item);
            });
            
            if (testState.favoriteVideos.length === 0) {
                container.innerHTML = '<div style="text-align: center; color: #666; padding: 20px;">暂无收藏视频</div>';
            }
        }

        // 日志记录
        function logPlayerEvent(message) {
            const log = document.getElementById('playerEventLog');
            const timestamp = new Date().toLocaleTimeString();
            log.innerHTML += `[${timestamp}] ${message}\n`;
            log.scrollTop = log.scrollHeight;
        }

        function logProfileEvent(message) {
            const log = document.getElementById('profileEventLog');
            const timestamp = new Date().toLocaleTimeString();
            log.innerHTML += `[${timestamp}] ${message}\n`;
            log.scrollTop = log.scrollHeight;
        }

        function logTestResult(message) {
            const log = document.getElementById('testResults');
            const timestamp = new Date().toLocaleTimeString();
            log.innerHTML += `[${timestamp}] ${message}\n`;
            log.scrollTop = log.scrollHeight;
            
            testState.testResults.push({ timestamp, message });
        }

        // 测试功能
        function testBasicSync() {
            logTestResult('🧪 开始基础同步测试...');
            
            // 测试点赞同步
            simulatePlayerLike();
            setTimeout(() => {
                // 验证个人页面是否同步
                const isInProfile = testState.likedVideos.includes(testState.currentVideoId);
                logTestResult(isInProfile ? '✅ 点赞同步测试通过' : '❌ 点赞同步测试失败');
                
                // 测试收藏同步
                simulatePlayerFavorite();
                setTimeout(() => {
                    const isInFavorites = testState.favoriteVideos.includes(testState.currentVideoId);
                    logTestResult(isInFavorites ? '✅ 收藏同步测试通过' : '❌ 收藏同步测试失败');
                    logTestResult('🎉 基础同步测试完成');
                }, 500);
            }, 500);
        }

        function testCrossPageEvents() {
            logTestResult('🧪 开始跨页面事件测试...');
            
            let eventCount = 0;
            const testListener = () => eventCount++;
            
            window.addEventListener('likeStatusChanged', testListener);
            window.addEventListener('favoriteStatusChanged', testListener);
            
            // 触发多个事件
            simulatePlayerLike();
            setTimeout(() => simulatePlayerFavorite(), 100);
            setTimeout(() => removeFromProfile(testState.currentVideoId, 'like'), 200);
            
            setTimeout(() => {
                window.removeEventListener('likeStatusChanged', testListener);
                window.removeEventListener('favoriteStatusChanged', testListener);
                logTestResult(`✅ 跨页面事件测试完成，触发了 ${eventCount} 个事件`);
            }, 1000);
        }

        function testLocalStorage() {
            logTestResult('🧪 开始本地存储测试...');
            
            const testKey = 'test_sync_data';
            const testValue = { timestamp: Date.now(), test: true };
            
            try {
                localStorage.setItem(testKey, JSON.stringify(testValue));
                const retrieved = JSON.parse(localStorage.getItem(testKey));
                
                if (retrieved && retrieved.test === true) {
                    logTestResult('✅ 本地存储读写测试通过');
                } else {
                    logTestResult('❌ 本地存储读写测试失败');
                }
                
                localStorage.removeItem(testKey);
                logTestResult('✅ 本地存储清理完成');
            } catch (error) {
                logTestResult(`❌ 本地存储测试失败: ${error.message}`);
            }
        }

        function testOfflineMode() {
            logTestResult('🧪 开始离线模式测试...');
            logTestResult('⚠️ 离线模式测试需要断开网络连接');
            
            // 模拟离线操作
            testState.socketConnected = false;
            updatePlayerDisplay();
            
            // 执行离线操作
            simulatePlayerLike();
            simulatePlayerFavorite();
            
            setTimeout(() => {
                testState.socketConnected = true;
                updatePlayerDisplay();
                logTestResult('✅ 离线模式测试完成，数据已缓存到本地');
            }, 2000);
        }

        function clearAllData() {
            // 清空测试状态
            testState = {
                currentVideoId: 123,
                playerStats: { views: 0, likes: 0, favorites: 0 },
                profileStats: { totalLikes: 0, totalFavorites: 0 },
                likedVideos: [],
                favoriteVideos: [],
                socketConnected: false,
                syncManagerActive: false,
                eventListeners: [],
                testResults: []
            };
            
            // 清空localStorage
            const keysToRemove = [];
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                if (key && (key.startsWith('test_') || key.startsWith('like_') || key.startsWith('favorite_'))) {
                    keysToRemove.push(key);
                }
            }
            keysToRemove.forEach(key => localStorage.removeItem(key));
            
            // 清空日志
            document.getElementById('playerEventLog').innerHTML = '等待事件...';
            document.getElementById('profileEventLog').innerHTML = '等待事件...';
            document.getElementById('testResults').innerHTML = '测试结果将显示在这里...';
            
            // 更新显示
            updatePlayerDisplay();
            updateProfileDisplay();
            
            logTestResult('🧹 所有数据已清空');
        }

        function exportTestReport() {
            const report = {
                timestamp: new Date().toISOString(),
                testResults: testState.testResults,
                finalState: testState,
                systemStatus: {
                    socketConnected: testState.socketConnected,
                    syncManagerActive: testState.syncManagerActive,
                    localStorageAvailable: typeof localStorage !== 'undefined',
                    eventSystemActive: typeof window.addEventListener !== 'undefined'
                }
            };
            
            const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `javflix-sync-test-report-${Date.now()}.json`;
            a.click();
            URL.revokeObjectURL(url);
            
            logTestResult('📄 测试报告已导出');
        }

        // 页面加载时自动初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🔄 JAVFLIX跨页面同步测试系统加载完成');
            setTimeout(initializeSystem, 1000);
        });
    </script>
</body>
</html> 