<!DOCTYPE html>
<html>
<head>
    <title>统计功能调试</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; background: #111; color: white; }
        .container { max-width: 800px; margin: 0 auto; }
        .success { color: #4CAF50; }
        .error { color: #f44336; }
        .warning { color: #ff9800; }
        button { background: #ff6b6b; color: white; padding: 8px 16px; border: none; border-radius: 4px; cursor: pointer; margin: 5px; }
        .section { border: 1px solid #444; padding: 15px; margin: 10px 0; border-radius: 5px; }
        pre { background: #222; padding: 10px; border-radius: 4px; overflow-x: auto; }
    </style>
</head>
<body>
    <div class="container">
        <h1>JAVFLIX 统计功能调试</h1>
        
        <div class="section">
            <h3>认证状态</h3>
            <div id="authStatus"></div>
            <button onclick="setTestToken()">设置测试Token</button>
            <button onclick="clearAuth()">清除认证</button>
        </div>
        
        <div class="section">
            <h3>API测试</h3>
            <button onclick="testStats()">获取统计数据</button>
            <button onclick="testUserStatus()">获取用户状态</button>
            <button onclick="testLike()">点赞切换</button>
            <button onclick="testFavorite()">收藏切换</button>
            <div id="apiResults"></div>
        </div>
        
        <div class="section">
            <h3>调试信息</h3>
            <div id="debugInfo"></div>
        </div>
    </div>
    
    <script>
        const TEST_TOKEN = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6MTQsImlhdCI6MTc0ODM0MTIxOSwiZXhwIjoxNzQ4OTQ2MDE5fQ.gad_80DRk7RJv8IoaAMyRlBdQBWxUZmsPmyOYVE082Y";
        const VIDEO_ID = "58"; // FCH-103
        
        function updateAuthStatus() {
            const token = localStorage.getItem('auth_token');
            const authStatus = document.getElementById('authStatus');
            
            if (token) {
                authStatus.innerHTML = `<div class="success">✅ Token: ${token.substring(0, 30)}...</div>`;
            } else {
                authStatus.innerHTML = '<div class="error">❌ 未找到认证Token</div>';
            }
        }
        
        function setTestToken() {
            localStorage.setItem('auth_token', TEST_TOKEN);
            localStorage.setItem('auth_timestamp', Date.now().toString());
            
            // 设置cookie
            document.cookie = `auth_token=${TEST_TOKEN}; path=/; max-age=${7 * 24 * 60 * 60}`;
            document.cookie = `auth_state=true; path=/; max-age=${7 * 24 * 60 * 60}`;
            
            updateAuthStatus();
            log('测试Token已设置');
        }
        
        function clearAuth() {
            localStorage.removeItem('auth_token');
            localStorage.removeItem('auth_timestamp');
            localStorage.removeItem('current_user');
            
            document.cookie = 'auth_token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT';
            document.cookie = 'auth_state=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT';
            
            updateAuthStatus();
            log('认证已清除');
        }
        
        async function testStats() {
            log('获取视频统计数据...');
            try {
                const response = await fetch(`/api/video-stats/fused/${VIDEO_ID}`);
                const data = await response.json();
                logResult('统计数据', response.status, data);
            } catch (error) {
                logError('统计数据获取失败', error);
            }
        }
        
        async function testUserStatus() {
            log('获取用户状态...');
            const token = localStorage.getItem('auth_token');
            if (!token) {
                logError('用户状态', new Error('需要先设置Token'));
                return;
            }
            
            try {
                const response = await fetch(`/api/proxy/users/likes/${VIDEO_ID}`, {
                    headers: { 'Authorization': `Bearer ${token}` }
                });
                const data = await response.json();
                logResult('用户点赞状态', response.status, data);
                
                const response2 = await fetch(`/api/proxy/users/favorites/${VIDEO_ID}`, {
                    headers: { 'Authorization': `Bearer ${token}` }
                });
                const data2 = await response2.json();
                logResult('用户收藏状态', response2.status, data2);
            } catch (error) {
                logError('用户状态获取失败', error);
            }
        }
        
        async function testLike() {
            log('测试点赞切换...');
            const token = localStorage.getItem('auth_token');
            if (!token) {
                logError('点赞', new Error('需要先设置Token'));
                return;
            }
            
            try {
                const response = await fetch(`/api/proxy/users/likes/${VIDEO_ID}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    }
                });
                const data = await response.json();
                logResult('点赞操作', response.status, data);
            } catch (error) {
                logError('点赞操作失败', error);
            }
        }
        
        async function testFavorite() {
            log('测试收藏切换...');
            const token = localStorage.getItem('auth_token');
            if (!token) {
                logError('收藏', new Error('需要先设置Token'));
                return;
            }
            
            try {
                const response = await fetch(`/api/proxy/users/favorites/${VIDEO_ID}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    }
                });
                const data = await response.json();
                logResult('收藏操作', response.status, data);
            } catch (error) {
                logError('收藏操作失败', error);
            }
        }
        
        function log(message) {
            const now = new Date().toLocaleTimeString();
            const debug = document.getElementById('debugInfo');
            debug.innerHTML += `<div>[${now}] ${message}</div>`;
        }
        
        function logResult(operation, status, data) {
            const now = new Date().toLocaleTimeString();
            const debug = document.getElementById('debugInfo');
            const statusClass = status >= 200 && status < 300 ? 'success' : 'error';
            debug.innerHTML += `<div class="${statusClass}">[${now}] ${operation} (${status}):</div>`;
            debug.innerHTML += `<pre>${JSON.stringify(data, null, 2)}</pre>`;
        }
        
        function logError(operation, error) {
            const now = new Date().toLocaleTimeString();
            const debug = document.getElementById('debugInfo');
            debug.innerHTML += `<div class="error">[${now}] ${operation} 错误: ${error.message}</div>`;
        }
        
        // 页面加载时更新状态
        updateAuthStatus();
    </script>
</body>
</html>