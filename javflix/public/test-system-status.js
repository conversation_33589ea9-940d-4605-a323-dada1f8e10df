// JAVFLIX 系统状态检查脚本
// 在浏览器控制台中运行此脚本来检查系统状态

console.log('🔄 JAVFLIX 系统状态检查开始...');

// 1. 检查基础环境
console.log('\n📋 基础环境检查:');
console.log('- localStorage 可用:', typeof localStorage !== 'undefined');
console.log('- sessionStorage 可用:', typeof sessionStorage !== 'undefined');
console.log('- CustomEvent 支持:', typeof CustomEvent !== 'undefined');
console.log('- fetch API 可用:', typeof fetch !== 'undefined');

// 2. 检查认证状态
console.log('\n🔐 认证状态检查:');
const authTokens = [
  localStorage.getItem('auth_token'),
  localStorage.getItem('auth-token'),
  sessionStorage.getItem('auth_token'),
  sessionStorage.getItem('auth-token')
].filter(Boolean);

console.log('- 找到认证token:', authTokens.length > 0);
if (authTokens.length > 0) {
  console.log('- Token 数量:', authTokens.length);
  console.log('- Token 来源:', authTokens.map((token, index) => {
    const sources = ['localStorage.auth_token', 'localStorage.auth-token', 'sessionStorage.auth_token', 'sessionStorage.auth-token'];
    return sources[index];
  }).filter((_, index) => authTokens[index]));
}

// 3. 检查同步管理器状态
console.log('\n🔄 同步管理器检查:');
const syncManagerKeys = Object.keys(localStorage).filter(key => 
  key.startsWith('like_') || 
  key.startsWith('favorite_') || 
  key.includes('sync')
);
console.log('- 同步相关缓存键:', syncManagerKeys.length);
console.log('- 缓存键列表:', syncManagerKeys);

// 4. 检查API连通性
console.log('\n🌐 API连通性检查:');

async function checkApiEndpoints() {
  const endpoints = [
    '/api/users/profile',
    '/api/video-stats/fused/123',
    '/api/proxy/users/likes/123'
  ];
  
  for (const endpoint of endpoints) {
    try {
      const response = await fetch(endpoint, {
        headers: authTokens.length > 0 ? { 'Authorization': `Bearer ${authTokens[0]}` } : {}
      });
      console.log(`- ${endpoint}: ${response.status} ${response.statusText}`);
    } catch (error) {
      console.log(`- ${endpoint}: ❌ ${error.message}`);
    }
  }
}

// 5. 检查Socket.IO连接
console.log('\n📡 Socket.IO检查:');
if (typeof io !== 'undefined') {
  console.log('- Socket.IO 库已加载: ✅');
  try {
    const socket = io('http://localhost:4000');
    socket.on('connect', () => {
      console.log('- Socket.IO 连接成功: ✅');
      socket.disconnect();
    });
    socket.on('connect_error', (error) => {
      console.log('- Socket.IO 连接失败: ❌', error.message);
    });
  } catch (error) {
    console.log('- Socket.IO 连接测试失败:', error.message);
  }
} else {
  console.log('- Socket.IO 库未加载: ❌');
}

// 6. 测试跨页面事件系统
console.log('\n📢 事件系统测试:');
let eventTestPassed = false;

const testEventListener = (event) => {
  console.log('- 事件系统测试: ✅ 收到测试事件', event.detail);
  eventTestPassed = true;
};

window.addEventListener('testSyncEvent', testEventListener);

// 触发测试事件
window.dispatchEvent(new CustomEvent('testSyncEvent', {
  detail: { test: true, timestamp: Date.now() }
}));

setTimeout(() => {
  window.removeEventListener('testSyncEvent', testEventListener);
  console.log('- 事件系统状态:', eventTestPassed ? '✅ 正常' : '❌ 异常');
}, 100);

// 7. 运行API检查
checkApiEndpoints().then(() => {
  console.log('\n✅ 系统状态检查完成!');
  console.log('\n📊 测试建议:');
  console.log('1. 打开 http://localhost:3001/test-cross-page-sync.html 进行完整测试');
  console.log('2. 打开 http://localhost:3001/zh-CN/profile 查看个人页面');
  console.log('3. 打开 http://localhost:3001/zh-CN/video/123 查看播放页面');
  console.log('4. 在不同页面间操作，观察同步效果');
});

// 8. 导出检查函数供手动调用
window.javflixSystemCheck = {
  checkAuth: () => {
    console.log('认证token:', authTokens);
    return authTokens.length > 0;
  },
  
  checkSync: () => {
    console.log('同步缓存:', syncManagerKeys);
    return syncManagerKeys.length;
  },
  
  testEvent: (eventName, data) => {
    window.dispatchEvent(new CustomEvent(eventName, { detail: data }));
    console.log(`已触发事件: ${eventName}`, data);
  },
  
  clearCache: () => {
    const keysToRemove = Object.keys(localStorage).filter(key => 
      key.startsWith('like_') || 
      key.startsWith('favorite_') || 
      key.startsWith('test_')
    );
    keysToRemove.forEach(key => localStorage.removeItem(key));
    console.log(`已清理 ${keysToRemove.length} 个缓存键`);
  }
};

console.log('\n🛠️ 可用的调试函数:');
console.log('- javflixSystemCheck.checkAuth() - 检查认证状态');
console.log('- javflixSystemCheck.checkSync() - 检查同步缓存');
console.log('- javflixSystemCheck.testEvent(name, data) - 测试事件');
console.log('- javflixSystemCheck.clearCache() - 清理缓存'); 