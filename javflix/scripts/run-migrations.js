// 数据库迁移执行脚本
const { execSync } = require('child_process');
const path = require('path');
const fs = require('fs');

// 获取迁移文件夹的路径
const migrationsDir = path.join(__dirname, '../src/migrations');

// 数据库连接字符串
// 从环境变量或.env文件获取
const dbUrl = process.env.DATABASE_URL || 'postgresql://postgres:postgres@localhost:5432/javflix';

console.log('开始执行数据库迁移...');

// 读取迁移文件夹中的所有SQL文件
const migrationFiles = fs.readdirSync(migrationsDir)
  .filter(file => file.endsWith('.sql'))
  .sort(); // 按文件名排序

if (migrationFiles.length === 0) {
  console.log('没有迁移文件需要执行');
  process.exit(0);
}

// 逐个执行迁移文件
migrationFiles.forEach(file => {
  const filePath = path.join(migrationsDir, file);
  console.log(`执行迁移: ${file}`);
  
  try {
    // 使用psql执行SQL文件
    execSync(`psql "${dbUrl}" -f "${filePath}"`, { stdio: 'inherit' });
    console.log(`完成迁移: ${file}`);
  } catch (error) {
    console.error(`迁移失败: ${file}`, error);
    process.exit(1);
  }
});

console.log('所有迁移已完成'); 