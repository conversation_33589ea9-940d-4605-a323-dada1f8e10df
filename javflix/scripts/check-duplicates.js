const { Pool } = require('pg');
const pool = new Pool({
  host: process.env.PGHOST || 'localhost',
  port: parseInt(process.env.PGPORT || '5432'),
  database: process.env.PGDATABASE || 'javflix',
  user: process.env.PGUSER || 'longgedemacminim4',
  password: process.env.PGPASSWORD || '',
});

async function main() {
  try {
    console.log('查询使用相同缓存图片的电影...');
    
    // 找出所有重复的缓存图片URL
    const duplicatesResult = await pool.query(`
      SELECT cached_image_url, COUNT(*) as count
      FROM movies
      WHERE cached_image_url IS NOT NULL
      GROUP BY cached_image_url
      HAVING COUNT(*) > 1
      ORDER BY count DESC
    `);
    
    if (duplicatesResult.rows.length === 0) {
      console.log('没有发现重复的缓存图片URL - 成功！');
      return;
    }
    
    console.log(`发现 ${duplicatesResult.rows.length} 个重复的缓存图片URL\n`);
    
    // 处理每个重复的URL
    for (const duplicate of duplicatesResult.rows) {
      const url = duplicate.cached_image_url;
      const count = duplicate.count;
      
      console.log(`\n缓存图片 "${url}" 被 ${count} 个电影使用`);
      
      // 获取使用此缓存URL的电影列表
      const movies = await pool.query(`
        SELECT movie_id, title
        FROM movies
        WHERE cached_image_url = $1
        ORDER BY movie_id
      `, [url]);
      
      console.log('使用相同缓存图片的电影:');
      console.table(movies.rows);
    }
    
    await pool.end();
  } catch (err) {
    console.error('查询出错:', err);
    process.exit(1);
  }
}

main(); 