const crypto = require('crypto');
const path = require('path');

// 复制自项目中的getHashedFileName函数
function getHashedFileName(url) {
  const hash = crypto.createHash('md5').update(url).digest('hex');
  const ext = path.extname(url) || '.jpg'; // 默认为jpg
  return `${hash}${ext}`;
}

// 测试电影封面URL
const testUrls = [
  'https://www.javbus.com/pics/cover/miab-480_b.jpg',
  'https://www.javbus.com/pics/cover/miab-420_b.jpg',
  'https://www.javbus.com/pics/cover/mih-016_b.jpg',
  'https://www.javbus.com/pics/cover/mngs-001_b.jpg',
  'https://www.javbus.com/pics/cover/maan-1073_b.jpg',
];

// 计算并显示哈希值
console.log('=========== JAVBUS 电影封面 URL 哈希测试 ===========');
testUrls.forEach(url => {
  const hashedFileName = getHashedFileName(url);
  console.log(`URL: ${url}`);
  console.log(`哈希文件名: ${hashedFileName}`);
  console.log('-----------------------------------');
});

// 测试标准URL格式（使用在image-proxy中的格式）
console.log('\n=========== 标准URL格式 哈希测试 ===========');
const movieIds = ['MIAB-480', 'MIAB-420', 'MIH-016', 'MNGS-001', 'MAAN-1073'];
movieIds.forEach(id => {
  const standardUrl = `https://www.javbus.com/pics/cover/${id.toLowerCase()}_b.jpg`;
  const hashedFileName = getHashedFileName(standardUrl);
  console.log(`电影ID: ${id}`);
  console.log(`标准URL: ${standardUrl}`);
  console.log(`哈希文件名: ${hashedFileName}`);
  console.log('-----------------------------------');
}); 