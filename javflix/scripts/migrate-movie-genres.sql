-- 将movie_genres表的数据迁移到video_categories表
-- 先删除已有数据
TRUNCATE video_categories;

-- 使用JOIN将genres和categories关联起来并迁移数据
INSERT INTO video_categories (video_id, category_id)
SELECT 
  mg.movie_id,  -- 电影ID
  c.id          -- 分类ID
FROM movie_genres mg
JOIN genres g ON mg.genre_id = g.id
JOIN categories c ON LOWER(c.name) = LOWER(g.name) OR 
                     c.slug = LOWER(REPLACE(REPLACE(g.name, ' ', '-'), '/', '-'));

-- 更新分类的计数
UPDATE categories c SET count = (
  SELECT COUNT(*) FROM video_categories vc WHERE vc.category_id = c.id
);

-- 查看迁移结果
SELECT 
  c.name AS category_name, 
  c.slug, 
  c.count, 
  c.is_featured
FROM categories c
ORDER BY c.count DESC
LIMIT 20; 