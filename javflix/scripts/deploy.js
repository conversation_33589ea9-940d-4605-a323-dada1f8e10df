#!/usr/bin/env node
/**
 * 自动化部署脚本
 * 根据部署环境自动配置API设置，无需手动修改代码
 * 
 * 使用方法：
 * node scripts/deploy.js --env production --mode single-domain
 * node scripts/deploy.js --env production --mode separate-domain --api-url https://api.javflix.tv
 * node scripts/deploy.js --env production --mode docker --api-url http://backend:4000
 */

const fs = require('fs');
const path = require('path');

// 解析命令行参数
function parseArgs() {
  const args = process.argv.slice(2);
  const options = {};
  
  for (let i = 0; i < args.length; i += 2) {
    const key = args[i].replace('--', '');
    const value = args[i + 1];
    options[key] = value;
  }
  
  return options;
}

// 生成环境配置
function generateEnvConfig(env, mode, apiUrl = '') {
  const configs = {
    development: {
      NODE_ENV: 'development',
      NEXT_PUBLIC_USE_PROXY: 'true',
      NEXT_PUBLIC_API_BASE_URL: ''
    },
    
    production: {
      'single-domain': {
        NODE_ENV: 'production',
        NEXT_PUBLIC_USE_PROXY: 'true',
        NEXT_PUBLIC_API_BASE_URL: ''
      },
      'separate-domain': {
        NODE_ENV: 'production',
        NEXT_PUBLIC_USE_PROXY: 'false',
        NEXT_PUBLIC_API_BASE_URL: apiUrl
      },
      'docker': {
        NODE_ENV: 'production',
        NEXT_PUBLIC_USE_PROXY: 'false',
        NEXT_PUBLIC_API_BASE_URL: apiUrl
      }
    },
    
    test: {
      NODE_ENV: 'test',
      NEXT_PUBLIC_USE_PROXY: 'false',
      NEXT_PUBLIC_API_BASE_URL: 'http://localhost:4000',
      NEXT_PUBLIC_USE_MOCK_DATA: 'true'
    }
  };
  
  if (env === 'production') {
    return configs.production[mode] || configs.production['single-domain'];
  }
  
  return configs[env] || configs.development;
}

// 写入环境配置文件
function writeEnvFile(config, filename = '.env.local') {
  const envContent = Object.entries(config)
    .map(([key, value]) => `${key}=${value}`)
    .join('\n');
  
  const envPath = path.join(process.cwd(), filename);
  fs.writeFileSync(envPath, envContent);
  
  console.log(`✅ 环境配置已写入: ${filename}`);
  console.log(`📄 配置内容:\n${envContent}`);
}

// 生成部署说明
function generateDeploymentGuide(env, mode, config) {
  console.log('\n📋 部署指南:');
  console.log('='.repeat(50));
  
  if (env === 'production' && mode === 'single-domain') {
    console.log('🚀 单域名部署模式');
    console.log('1. 确保前端运行在端口3001');
    console.log('2. 确保后端运行在端口4000');
    console.log('3. 配置Nginx反向代理:');
    console.log(`
server {
    listen 80;
    server_name your-domain.com;
    
    location / {
        proxy_pass http://localhost:3001;
    }
    
    location /api/ {
        proxy_pass http://localhost:4000/api/;
    }
}`);
  } else if (env === 'production' && mode === 'separate-domain') {
    console.log('🌐 分离域名部署模式');
    console.log(`1. 前端部署到任意服务器`);
    console.log(`2. 后端部署到: ${config.NEXT_PUBLIC_API_BASE_URL}`);
    console.log('3. 确保后端配置CORS允许前端域名');
  } else if (env === 'production' && mode === 'docker') {
    console.log('🐳 容器化部署模式');
    console.log('1. 构建镜像: docker build -t javflix .');
    console.log('2. 运行容器时设置环境变量');
    console.log(`3. API地址: ${config.NEXT_PUBLIC_API_BASE_URL}`);
  }
  
  console.log('\n📝 下一步:');
  console.log('1. npm run build');
  console.log('2. npm start');
  console.log('3. 测试API连接: node test-proxy-api.js');
}

// 主函数
function main() {
  const options = parseArgs();
  const env = options.env || 'development';
  const mode = options.mode || 'single-domain';
  const apiUrl = options['api-url'] || '';
  
  console.log('🔧 自动化部署配置');
  console.log(`📍 环境: ${env}`);
  console.log(`🎯 模式: ${mode}`);
  if (apiUrl) console.log(`🌐 API地址: ${apiUrl}`);
  
  // 验证参数
  if (env === 'production' && mode === 'separate-domain' && !apiUrl) {
    console.error('❌ 错误: 分离域名模式需要指定API地址');
    console.log('💡 示例: node scripts/deploy.js --env production --mode separate-domain --api-url https://api.javflix.tv');
    process.exit(1);
  }
  
  if (env === 'production' && mode === 'docker' && !apiUrl) {
    console.error('❌ 错误: Docker模式需要指定API地址');
    console.log('💡 示例: node scripts/deploy.js --env production --mode docker --api-url http://backend:4000');
    process.exit(1);
  }
  
  // 生成配置
  const config = generateEnvConfig(env, mode, apiUrl);
  
  // 写入文件
  const filename = env === 'production' ? '.env.production' : '.env.local';
  writeEnvFile(config, filename);
  
  // 生成部署指南
  generateDeploymentGuide(env, mode, config);
  
  console.log('\n🎉 配置完成! 代码无需任何修改即可在目标环境运行。');
}

// 显示帮助
if (process.argv.includes('--help') || process.argv.includes('-h')) {
  console.log(`
🚀 JAVFLIX 自动化部署配置工具

用法:
  node scripts/deploy.js [选项]

选项:
  --env <环境>          目标环境 (development|production|test)
  --mode <模式>         部署模式 (single-domain|separate-domain|docker)
  --api-url <URL>       API服务地址 (分离域名和Docker模式必需)
  --help, -h            显示帮助信息

示例:
  # 开发环境 (默认)
  node scripts/deploy.js
  
  # 生产环境 - 单域名部署 (推荐)
  node scripts/deploy.js --env production --mode single-domain
  
  # 生产环境 - 分离域名部署
  node scripts/deploy.js --env production --mode separate-domain --api-url https://api.javflix.tv
  
  # 生产环境 - Docker部署
  node scripts/deploy.js --env production --mode docker --api-url http://backend:4000
  
  # 测试环境
  node scripts/deploy.js --env test
`);
  process.exit(0);
}

// 运行
main(); 