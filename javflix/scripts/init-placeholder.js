const fs = require('fs');
const path = require('path');

// 创建需要的目录
const publicDir = path.join(process.cwd(), 'public');
const imagesDir = path.join(publicDir, 'images');
const javbusDir = path.join(imagesDir, 'javbus');
const actressDir = path.join(javbusDir, 'actress');
const coverDir = path.join(javbusDir, 'cover');
const sampleDir = path.join(javbusDir, 'sample');

// 确保目录存在
[publicDir, imagesDir, javbusDir, actressDir, coverDir, sampleDir].forEach(dir => {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
    console.log(`创建目录: ${dir}`);
  }
});

// 占位图Base64数据 - 简单的灰色图像
const placeholderImageData = 'data:image/jpeg;base64,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';

// 解码Base64数据
const data = placeholderImageData.replace(/^data:image\/jpeg;base64,/, '');
const buffer = Buffer.from(data, 'base64');

// 写入占位图文件
const placeholderPath = path.join(imagesDir, 'placeholder.jpg');
fs.writeFileSync(placeholderPath, buffer);
console.log(`创建占位图: ${placeholderPath}`);

// 写入女优占位图
const actressPlaceholderPath = path.join(imagesDir, 'placeholder-actress.svg');
const actressPlaceholderSVG = `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100" width="100" height="100">
  <circle cx="50" cy="40" r="30" fill="#444" />
  <circle cx="50" cy="120" r="60" fill="#444" />
</svg>`;
fs.writeFileSync(actressPlaceholderPath, actressPlaceholderSVG);
console.log(`创建女优占位图: ${actressPlaceholderPath}`);

console.log('占位图初始化完成！'); 