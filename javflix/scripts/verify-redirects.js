/**
 * 重定向验证脚本
 * 用于测试网站的重定向规则是否按预期工作
 * 
 * 使用方法: node scripts/verify-redirects.js
 */

const https = require('https');
const http = require('http');
const { URL } = require('url');

// 配置
const BASE_URL = 'http://localhost:3000'; // 本地开发环境
// const BASE_URL = 'https://javflix.tv';  // 生产环境

// 要测试的重定向规则
const redirectsToTest = [
  // 基本重定向
  { from: '/videos', expectedTo: '/video', expectedStatus: 308 },
  { from: '/stars', expectedTo: '/actress', expectedStatus: 308 },
  { from: '/home', expectedTo: '/', expectedStatus: 308 },
  
  // URL规范化
  { from: '/Video/sample', expectedTo: '/video/sample', expectedStatus: 308 },
  { from: '/category/uncensored/', expectedTo: '/category/uncensored', expectedStatus: 308 },
  
  // 添加其他测试案例...
];

/**
 * 测试单个重定向
 */
async function testRedirect(fromUrl, expectedToUrl, expectedStatus) {
  return new Promise((resolve, reject) => {
    const fullFromUrl = new URL(fromUrl, BASE_URL);
    const client = fullFromUrl.protocol === 'https:' ? https : http;
    
    const req = client.request(
      {
        method: 'HEAD', // 只获取头信息，不需要内容
        hostname: fullFromUrl.hostname,
        port: fullFromUrl.port || (fullFromUrl.protocol === 'https:' ? 443 : 80),
        path: fullFromUrl.pathname + fullFromUrl.search,
        headers: {
          'User-Agent': 'RedirectVerifier/1.0'
        },
        // 不自动跟随重定向
        followRedirect: false
      },
      (res) => {
        // 检查是否是重定向状态码
        const isRedirect = res.statusCode >= 300 && res.statusCode < 400;
        
        if (isRedirect) {
          const location = res.headers.location;
          const fullExpectedUrl = new URL(expectedToUrl, BASE_URL).href;
          
          // 验证重定向目标
          const locationUrl = new URL(location, BASE_URL).href;
          const isLocationCorrect = locationUrl === fullExpectedUrl;
          
          // 验证状态码
          const isStatusCorrect = res.statusCode === expectedStatus;
          
          resolve({
            from: fromUrl,
            to: location,
            actualStatus: res.statusCode,
            expectedStatus,
            expectedTo: expectedToUrl,
            success: isLocationCorrect && isStatusCorrect,
            issues: !isLocationCorrect ? 'Wrong redirect target' : 
                   !isStatusCorrect ? 'Wrong status code' : null
          });
        } else {
          resolve({
            from: fromUrl,
            actualStatus: res.statusCode,
            expectedStatus,
            success: false,
            issues: `Not a redirect (status: ${res.statusCode})`
          });
        }
      }
    );
    
    req.on('error', (error) => {
      resolve({
        from: fromUrl,
        success: false,
        issues: `Request error: ${error.message}`
      });
    });
    
    req.end();
  });
}

/**
 * 运行所有重定向测试
 */
async function runTests() {
  console.log(`\n测试重定向 - 基准URL: ${BASE_URL}\n`);
  console.log('开始测试...\n');
  
  const results = [];
  
  for (const test of redirectsToTest) {
    const result = await testRedirect(test.from, test.expectedTo, test.expectedStatus);
    results.push(result);
    
    // 打印单个测试结果
    if (result.success) {
      console.log(`✅ ${test.from} -> ${test.expectedTo} (${test.expectedStatus})`);
    } else {
      console.log(`❌ ${test.from} -> 预期: ${test.expectedTo} (${test.expectedStatus})`);
      console.log(`   实际: ${result.to || '无重定向'} (${result.actualStatus || 'N/A'})`);
      console.log(`   问题: ${result.issues}`);
    }
  }
  
  // 打印摘要
  const successCount = results.filter(r => r.success).length;
  console.log(`\n测试完成: ${successCount}/${results.length} 通过`);
  
  if (successCount < results.length) {
    console.log('\n失败的测试:');
    results.filter(r => !r.success).forEach(r => {
      console.log(`- ${r.from} (${r.issues})`);
    });
  }
}

// 执行测试
runTests().catch(console.error); 