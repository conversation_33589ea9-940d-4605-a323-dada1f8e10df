#!/bin/bash

# JAVFLIX 生产环境启动脚本
# 智能选择最佳启动模式

echo "🚀 JAVFLIX 生产环境启动脚本"

# 设置环境变量
export NODE_ENV=production
export PORT=3001

# 函数：启动standalone模式
start_standalone() {
    echo "🌟 使用Standalone模式启动..."
    
    # 检查standalone构建是否存在且完整
    if [ ! -d ".next/standalone" ]; then
        echo "📦 Standalone构建不存在，开始构建..."
        npm run build:standalone
    elif [ ! -f ".next/standalone/start.js" ]; then
        echo "🔧 Standalone配置不完整，重新配置..."
        node scripts/post-build-standalone.js
    fi
    
    if [ -f ".next/standalone/start.js" ]; then
        echo "✅ 启动Standalone服务器..."
        cd .next/standalone
        node start.js
    else
        echo "❌ Standalone模式启动失败，回退到标准模式"
        start_standard
    fi
}

# 函数：启动标准模式
start_standard() {
    echo "🎯 使用标准模式启动..."
    
    # 检查构建文件
    if [ ! -d ".next" ]; then
        echo "📦 未找到构建文件，开始构建..."
        npm run build
    fi
    
    echo "✅ 启动标准服务器..."
    npm run start
}

# 检查命令行参数
if [ "$1" = "--standard" ]; then
    start_standard
elif [ "$1" = "--standalone" ]; then
    start_standalone
else
    # 默认尝试standalone模式
    echo "💡 自动选择启动模式..."
    
    # 检查是否配置了standalone
    if grep -q 'output.*standalone' next.config.js 2>/dev/null; then
        start_standalone
    else
        echo "⚠️  未启用standalone模式，使用标准模式"
        start_standard
    fi
fi