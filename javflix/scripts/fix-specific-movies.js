const { Pool } = require('pg');
const fs = require('fs');
const path = require('path');
const crypto = require('crypto');
const http = require('http');
const https = require('https');

// 设置数据库连接
const pool = new Pool({
  host: process.env.PGHOST || 'localhost',
  port: parseInt(process.env.PGPORT || '5432'),
  database: process.env.PGDATABASE || 'javflix',
  user: process.env.PGUSER || 'longgedemacminim4',
  password: process.env.PGPASSWORD || '',
});

// 设置基本缓存目录
const CACHE_DIR = path.join(process.cwd(), 'public', 'images', 'javbus');
const COVER_DIR = path.join(CACHE_DIR, 'cover');

// 要修复的电影列表
const moviesToFix = [
  'MIAB-420',
  'MIAB-480',
  'MIH-016',
  'MNGS-001'
];

// 确保缓存目录存在
function ensureDirExists(dirPath) {
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true });
  }
}

// 获取哈希文件名
function getHashedFileName(url) {
  const hash = crypto.createHash('md5').update(url).digest('hex');
  const ext = path.extname(url) || '.jpg'; // 默认为jpg
  return `${hash}${ext}`;
}

// 检查缓存是否存在
function isCached(filePath) {
  return fs.existsSync(filePath);
}

// 下载图片并保存到缓存
function downloadAndSaveImage(url, filePath) {
  return new Promise((resolve, reject) => {
    // 忽略无效URL
    if (!url || !url.startsWith('http')) {
      reject(new Error(`无效URL: ${url}`));
      return;
    }
    
    console.log(`尝试下载图片: ${url} 到 ${filePath}`);
    
    // 确保目录存在
    const fileDir = path.dirname(filePath);
    if (!fs.existsSync(fileDir)) {
      fs.mkdirSync(fileDir, { recursive: true });
    }
    
    const file = fs.createWriteStream(filePath);
    
    // 使用https或http模块，根据URL协议
    const client = url.startsWith('https') ? https : http;
    
    client.get(url, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Referer': 'https://www.javbus.com/'
      },
      timeout: 10000 // 10秒超时
    }, (response) => {
      if (response.statusCode !== 200) {
        file.close();
        fs.unlink(filePath, () => {}); // 删除可能部分下载的文件
        reject(new Error(`下载失败, 状态码: ${response.statusCode}`));
        return;
      }
      
      response.pipe(file);
      
      file.on('finish', () => {
        file.close();
        console.log(`图片下载成功: ${url}`);
        resolve();
      });
      
      file.on('error', (err) => {
        file.close();
        fs.unlink(filePath, () => {});
        console.error(`文件写入错误: ${err.message}`);
        reject(err);
      });
    }).on('error', (err) => {
      file.close();
      fs.unlink(filePath, () => {});
      console.error(`HTTP请求错误: ${err.message}`);
      reject(err);
    });
  });
}

// 从JAVBUS API获取电影信息
async function getMovieInfoFromJavbus(movieId) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 3000,
      path: `/api/movies/${movieId}`,
      method: 'GET',
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
      }
    };

    const req = http.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          if (res.statusCode === 200) {
            const movie = JSON.parse(data);
            resolve(movie);
          } else {
            reject(new Error(`获取电影信息失败, 状态码: ${res.statusCode}`));
          }
        } catch (e) {
          reject(new Error(`解析电影信息失败: ${e.message}`));
        }
      });
    });
    
    req.on('error', (e) => {
      reject(new Error(`请求出错: ${e.message}`));
    });
    
    req.end();
  });
}

// 直接从javbus.com获取封面图片URL
async function getDirectCoverUrl(movieId) {
  const lowerCode = movieId.toLowerCase();
  return `https://www.javbus.com/pics/cover/${lowerCode}_b.jpg`;
}

// 更新电影缓存图片
async function updateMovieCachedImage(movieId, cachedUrl) {
  try {
    await pool.query(
      'UPDATE movies SET cached_image_url = $1, updated_at = NOW() WHERE movie_id = $2',
      [cachedUrl, movieId]
    );
    console.log(`已更新电影(ID: ${movieId})的缓存图片URL: ${cachedUrl}`);
    return true;
  } catch (error) {
    console.error(`更新电影(ID: ${movieId})缓存图片失败:`, error);
    return false;
  }
}

// 处理单个电影的封面图片
async function processMovieCover(movieId) {
  try {
    console.log(`正在处理电影 ${movieId} 的封面图片...`);
    
    // 先尝试从JAVBUS API获取电影信息
    let coverUrl = null;
    try {
      const movie = await getMovieInfoFromJavbus(movieId);
      if (movie && movie.img) {
        coverUrl = movie.img;
      }
    } catch (error) {
      console.log(`通过API获取电影${movieId}信息失败: ${error.message}`);
    }
    
    // 如果API获取失败，使用直接构造的URL作为备选
    if (!coverUrl) {
      coverUrl = await getDirectCoverUrl(movieId);
      console.log(`使用直接构造的URL: ${coverUrl}`);
    }
    
    console.log(`电影 ${movieId} 的封面图片URL: ${coverUrl}`);
    
    // 根据URL生成缓存文件路径
    const hashedFileName = getHashedFileName(coverUrl);
    const cacheFilePath = path.join(COVER_DIR, hashedFileName);
    const publicPath = `/images/javbus/cover/${hashedFileName}`;
    
    // 确保缓存目录存在
    ensureDirExists(COVER_DIR);
    
    // 检查缓存是否存在
    if (!isCached(cacheFilePath)) {
      // 下载并保存图片
      await downloadAndSaveImage(coverUrl, cacheFilePath);
    } else {
      console.log(`电影 ${movieId} 的封面图片已缓存: ${publicPath}`);
    }
    
    // 更新数据库中的缓存路径
    await updateMovieCachedImage(movieId, publicPath);
    
    return true;
  } catch (error) {
    console.error(`处理电影 ${movieId} 的封面图片失败:`, error);
    return false;
  }
}

// 主函数
async function main() {
  try {
    console.log(`开始修复 ${moviesToFix.length} 部电影的缓存图片...`);
    
    let successCount = 0;
    let failCount = 0;
    
    // 处理每个电影
    for (const movieId of moviesToFix) {
      console.log(`\n处理电影 ${movieId}...`);
      
      try {
        const success = await processMovieCover(movieId);
        if (success) {
          successCount++;
        } else {
          failCount++;
        }
      } catch (error) {
        console.error(`处理电影 ${movieId} 时出错:`, error);
        failCount++;
      }
      
      // 暂停一下，避免请求过快
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    console.log(`\n处理完成! 成功: ${successCount}, 失败: ${failCount}`);
    
  } catch (error) {
    console.error('执行脚本时出错:', error);
  } finally {
    await pool.end();
  }
}

// 执行主函数
main().catch(err => console.error('脚本执行失败:', err));
