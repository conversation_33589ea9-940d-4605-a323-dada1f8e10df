#!/usr/bin/env node

/**
 * 构建后处理脚本 - 自动配置standalone模式
 * 解决静态资源和MIME类型问题
 */

const fs = require('fs');
const path = require('path');

function copyRecursive(src, dest) {
  const exists = fs.existsSync(src);
  const stats = exists && fs.statSync(src);
  const isDirectory = exists && stats.isDirectory();
  
  if (isDirectory) {
    if (!fs.existsSync(dest)) {
      fs.mkdirSync(dest, { recursive: true });
    }
    fs.readdirSync(src).forEach(childItemName => {
      copyRecursive(
        path.join(src, childItemName),
        path.join(dest, childItemName)
      );
    });
  } else {
    fs.copyFileSync(src, dest);
  }
}

async function postBuildStandalone() {
  console.log('🔧 开始配置standalone模式...');
  
  const standaloneDir = '.next/standalone';
  
  // 检查standalone目录是否存在
  if (!fs.existsSync(standaloneDir)) {
    console.log('❌ Standalone目录不存在，请确保next.config.js中设置了 output: "standalone"');
    process.exit(1);
  }
  
  try {
    // 1. 复制静态资源
    console.log('📂 复制静态资源...');
    
    // 复制 .next/static 到 standalone/.next/static
    const staticSrc = '.next/static';
    const staticDest = path.join(standaloneDir, '.next/static');    
    if (fs.existsSync(staticSrc)) {
      copyRecursive(staticSrc, staticDest);
      console.log('✅ 静态文件复制完成');
    }
    
    // 复制 public 到 standalone/public
    const publicSrc = 'public';
    const publicDest = path.join(standaloneDir, 'public');
    
    if (fs.existsSync(publicSrc)) {
      copyRecursive(publicSrc, publicDest);
      console.log('✅ 公共资源复制完成');
    }
    
    // 2. 创建优化的启动脚本
    console.log('📝 创建启动脚本...');
    
    const startScript = `#!/usr/bin/env node

// JAVFLIX Standalone Server
// 自动配置环境和端口

const { createServer } = require('http');
const { parse } = require('url');
const next = require('next');

// 设置环境变量
process.env.NODE_ENV = process.env.NODE_ENV || 'production';
const port = parseInt(process.env.PORT, 10) || 3001;
const hostname = process.env.HOSTNAME || '0.0.0.0';

console.log(\`🚀 启动JAVFLIX服务器...\`);
console.log(\`📍 地址: http://\${hostname}:\${port}\`);
console.log(\`🌟 环境: \${process.env.NODE_ENV}\`);

// 启动原始server.js
require('./server.js');
`;
    
    fs.writeFileSync(path.join(standaloneDir, 'start.js'), startScript);
    
    // 3. 创建package.json（如果需要）
    const packageJsonPath = path.join(standaloneDir, 'package.json');
    if (!fs.existsSync(packageJsonPath)) {
      const minimalPackage = {
        name: 'javflix-standalone',
        version: '1.0.0',
        private: true,
        scripts: {
          start: 'node start.js'
        },
        dependencies: {
          next: require('../package.json').dependencies.next
        }
      };
      
      fs.writeFileSync(packageJsonPath, JSON.stringify(minimalPackage, null, 2));
      console.log('✅ 创建standalone package.json');
    }    
    // 4. 设置正确的权限
    if (process.platform !== 'win32') {
      fs.chmodSync(path.join(standaloneDir, 'start.js'), '755');
      fs.chmodSync(path.join(standaloneDir, 'server.js'), '755');
    }
    
    console.log('🎉 Standalone模式配置完成！');
    console.log('');
    console.log('启动方式：');
    console.log('  开发模式: npm run start');
    console.log('  生产模式: npm run start:standalone');
    console.log('  或直接:   node .next/standalone/start.js');
    console.log('');
    
  } catch (error) {
    console.error('❌ 配置standalone模式时出错:', error);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  postBuildStandalone();
}

module.exports = postBuildStandalone;