const { Pool } = require('pg');
const pool = new Pool({
  host: process.env.PGHOST || 'localhost',
  port: parseInt(process.env.PGPORT || '5432'),
  database: process.env.PGDATABASE || 'javflix',
  user: process.env.PGUSER || 'longgedemacminim4',
  password: process.env.PGPASSWORD || '',
});

async function main() {
  try {
    // 检查多个电影是否使用相同的缓存图片
    const duplicateResult = await pool.query(`
      SELECT cached_image_url, COUNT(*) as count
      FROM movies
      WHERE cached_image_url IS NOT NULL
      GROUP BY cached_image_url
      HAVING COUNT(*) > 1
      ORDER BY count DESC
      LIMIT 10
    `);
    
    console.log('重复的缓存图片URL:');
    if (duplicateResult.rows.length === 0) {
      console.log('没有发现重复的缓存图片URL - 成功！');
    } else {
      console.table(duplicateResult.rows);
    }
    
    // 获取前15个电影的图片缓存信息
    const movieResult = await pool.query(`
      SELECT movie_id, title, cached_image_url
      FROM movies
      WHERE cached_image_url IS NOT NULL
      ORDER BY movie_id
      LIMIT 15
    `);
    
    console.log('\n电影缓存URL信息:');
    console.table(movieResult.rows);
    
    await pool.end();
  } catch (err) {
    console.error('查询出错:', err);
    process.exit(1);
  }
}

main();
