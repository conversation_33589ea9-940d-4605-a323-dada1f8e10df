const fetch = require('node-fetch');
const { Pool } = require('pg');

// 创建数据库连接
const pool = new Pool({
  host: process.env.PGHOST || 'localhost',
  port: parseInt(process.env.PGPORT || '5432'),
  database: process.env.PGDATABASE || 'javflix',
  user: process.env.PGUSER || 'longgedemacminim4',
  password: process.env.PGPASSWORD || '',
});

// 清空所有电影的缓存URL
async function clearAllCacheUrls() {
  console.log('清空所有电影的缓存URL...');
  
  try {
    const result = await pool.query(`
      UPDATE movies
      SET cached_image_url = NULL
      WHERE cached_image_url IS NOT NULL
      RETURNING id, title, movie_id
    `);
    
    console.log(`成功清空 ${result.rowCount} 部电影的缓存URL`);
    return result.rows;
  } catch (error) {
    console.error('清空缓存URL失败:', error);
    throw error;
  }
}

// 采集封面图片
async function scrapeCoverImages() {
  console.log('开始采集封面图片...');
  
  try {
    const response = await fetch('http://localhost:3001/api/image-scraper?type=cover');
    const data = await response.json();
    
    if (data.status === 'success') {
      console.log('封面图片采集成功!');
      const processed = data.results.processed;
      console.log(`总共: ${processed.total}, 成功: ${processed.successful}, 失败: ${processed.failed}`);
      return processed.results;
    } else {
      console.error('封面图片采集失败:', data);
      return null;
    }
  } catch (error) {
    console.error('调用采集API失败:', error);
    return null;
  }
}

// 更新缓存URL
async function updateCacheUrls() {
  console.log('开始更新缓存URL...');
  
  try {
    const response = await fetch('http://localhost:3001/api/cache-update?type=cover');
    const data = await response.json();
    
    if (data.status === 'success') {
      console.log('缓存URL更新成功!');
      const results = data.results.covers;
      console.log(`总共: ${results.total}, 已更新: ${results.updated}, 已缓存: ${results.alreadyCached}, 错误: ${results.errors}`);
      return results;
    } else {
      console.error('更新缓存URL失败:', data);
      return null;
    }
  } catch (error) {
    console.error('调用更新API失败:', error);
    return null;
  }
}

// 获取所有电影及其缓存URL
async function getMoviesWithCacheUrls() {
  console.log('获取所有电影及其缓存URL...');
  
  try {
    const result = await pool.query(`
      SELECT id, title, movie_id, cached_image_url
      FROM movies
      ORDER BY id
    `);
    
    console.log(`成功获取 ${result.rowCount} 部电影信息`);
    
    // 统计有缓存URL的电影数量
    const withCache = result.rows.filter(movie => movie.cached_image_url).length;
    console.log(`有缓存URL的电影: ${withCache}/${result.rowCount}`);
    
    return result.rows;
  } catch (error) {
    console.error('获取电影信息失败:', error);
    throw error;
  }
}

// 手动更新每部电影的缓存URL
async function updateMovieCacheUrl(movieId, cachedImageUrl) {
  console.log(`手动更新电影ID=${movieId}的缓存URL...`);
  
  try {
    const response = await fetch('http://localhost:3001/api/update-movie-cache', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ movieId, cachedImageUrl })
    });
    
    const data = await response.json();
    
    if (data.status === 'success') {
      console.log(`电影ID=${movieId}的缓存URL更新成功!`);
      return data.data;
    } else {
      console.error(`更新电影ID=${movieId}的缓存URL失败:`, data);
      return null;
    }
  } catch (error) {
    console.error(`调用更新API失败:`, error);
    return null;
  }
}

// 主函数
async function main() {
  try {
    // 1. 清空所有缓存URL
    const clearedMovies = await clearAllCacheUrls();
    
    // 2. 采集封面图片
    const scrapedImages = await scrapeCoverImages();
    
    // 3. 更新缓存URL
    const updatedUrls = await updateCacheUrls();
    
    // 4. 检查结果
    const finalMovies = await getMoviesWithCacheUrls();
    
    // 5. 如果有电影没有缓存URL，手动更新
    const moviesWithoutCache = finalMovies.filter(movie => !movie.cached_image_url);
    if (moviesWithoutCache.length > 0) {
      console.log(`有 ${moviesWithoutCache.length} 部电影没有缓存URL，尝试手动更新...`);
      
      // 获取所有缓存图片的路径
      const cachedImages = scrapedImages.map(img => img.cached);
      
      // 为每部电影分配一个缓存图片
      for (let i = 0; i < moviesWithoutCache.length && i < cachedImages.length; i++) {
        await updateMovieCacheUrl(moviesWithoutCache[i].id, cachedImages[i]);
      }
      
      // 再次检查结果
      await getMoviesWithCacheUrls();
    }
    
    console.log('封面缓存测试完成!');
  } catch (error) {
    console.error('测试过程中出错:', error);
  } finally {
    // 关闭数据库连接
    await pool.end();
  }
}

// 执行主函数
main(); 