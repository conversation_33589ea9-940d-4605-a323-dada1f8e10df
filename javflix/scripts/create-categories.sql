-- 创建categories表
CREATE TABLE IF NOT EXISTS categories (
  id SERIAL PRIMARY KEY,
  name TEXT NOT NULL,
  slug TEXT NOT NULL UNIQUE,
  image_url TEXT,
  count INTEGER DEFAULT 0,
  description TEXT,
  color TEXT,
  icon TEXT,
  is_featured BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 从genres导入基础数据
INSERT INTO categories (name, slug, is_featured, count)
SELECT 
  name,
  LOWER(REPLACE(REPLACE(name, ' ', '-'), '/', '-')), -- 简单的slug生成
  CASE 
    WHEN name IN ('无码', 'VR', '高清', '字幕', '新人', '经典') THEN TRUE 
    ELSE FALSE 
  END,
  (SELECT COUNT(*) FROM movie_genres WHERE genre_id = genres.id)
FROM genres;

-- 更新特定分类的颜色和图标
UPDATE categories SET color = '#dc2626', icon = 'film' WHERE name = '无码' OR slug = 'uncensored';
UPDATE categories SET color = '#0284c7', icon = 'vr' WHERE name = 'VR' OR slug = 'vr';
UPDATE categories SET color = '#059669', icon = 'hd' WHERE name = '高清' OR slug = 'hd';
UPDATE categories SET color = '#7c3aed', icon = 'subtitle' WHERE name = '字幕' OR slug = 'subtitle';
UPDATE categories SET color = '#db2777', icon = 'new' WHERE name = '新人' OR slug = 'new-face';
UPDATE categories SET color = '#b45309', icon = 'classic' WHERE name = '经典' OR slug = 'classic';

-- 手动添加热门分类（如果在genres中不存在）
INSERT INTO categories (name, slug, is_featured, color, icon, count)
VALUES 
  ('无码', 'uncensored', TRUE, '#dc2626', 'film', 1280),
  ('VR', 'vr', TRUE, '#0284c7', 'vr', 580),
  ('高清', 'hd', TRUE, '#059669', 'hd', 2450),
  ('字幕', 'subtitle', TRUE, '#7c3aed', 'subtitle', 890),
  ('新人', 'new-face', TRUE, '#db2777', 'new', 320),
  ('经典', 'classic', TRUE, '#b45309', 'classic', 750)
ON CONFLICT (slug) DO UPDATE SET 
  is_featured = TRUE,
  color = EXCLUDED.color,
  icon = EXCLUDED.icon;

-- 创建一个video_categories表来关联视频和分类
CREATE TABLE IF NOT EXISTS video_categories (
  video_id INTEGER NOT NULL,
  category_id INTEGER NOT NULL,
  PRIMARY KEY (video_id, category_id)
);

-- 关联视频和分类
-- 注意：这个操作可能会很复杂，因为需要将movie_genres中的数据转换为video_categories
-- 这里只是一个简化的示例
-- INSERT INTO video_categories (video_id, category_id)
-- SELECT mg.movie_id, c.id
-- FROM movie_genres mg
-- JOIN genres g ON mg.genre_id = g.id
-- JOIN categories c ON c.name = g.name; 