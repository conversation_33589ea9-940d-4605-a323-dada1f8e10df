#!/usr/bin/env node

/**
 * 🎯 JAVFLIX.TV 简化API性能测试
 * 基于 freeCodeCamp 的 Next.js 优化最佳实践
 */

const fs = require('fs').promises;

// 🎯 测试配置
const BASE_URL = 'http://localhost:3000';

// 🔧 API测试端点
const API_TESTS = [
  { 
    name: 'API-首页数据', 
    url: `${BASE_URL}/api/popular-videos?limit=20`,
    expectedScore: 85,
    critical: true
  },
  { 
    name: 'API-搜索数据', 
    url: `${BASE_URL}/api/search?q=test&limit=20`,
    expectedScore: 85,
    critical: true
  },
  { 
    name: 'API-分类数据', 
    url: `${BASE_URL}/api/db/genres?limit=50`,
    expectedScore: 80,
    critical: false
  },
  { 
    name: 'API-视频详情', 
    url: `${BASE_URL}/api/video/details?id=SSIS-001`,
    expectedScore: 80,
    critical: false
  }
];

// 🚀 API性能测试函数
async function testAPIPerformance(url, name) {
  const startTime = Date.now();
  
  try {
    const response = await fetch(url, {
      headers: {
        'User-Agent': 'JAVFLIX-Performance-Test/1.0',
        'Accept': 'application/json'
      }
    });
    
    const responseTime = Date.now() - startTime;
    
    let data = null;
    let contentLength = 0;
    
    try {
      const text = await response.text();
      contentLength = text.length;
      data = JSON.parse(text);
    } catch (e) {
      // 非JSON响应
    }
    
    // 🎯 API性能评分算法
    let score = 100;
    
    // 响应时间评分 (权重50%)
    if (responseTime > 1000) score -= 40;
    else if (responseTime > 500) score -= 30;
    else if (responseTime > 300) score -= 20;
    else if (responseTime > 200) score -= 10;
    else if (responseTime > 100) score -= 5;
    
    // 状态码评分 (权重25%)
    if (response.status !== 200) score -= 30;
    
    // 数据质量评分 (权重15%)
    if (data && !data.success) score -= 15;
    if (!data || !data.data) score -= 10;
    
    // 缓存和性能头评分 (权重10%)
    const cacheControl = response.headers.get('cache-control');
    const responseTimeHeader = response.headers.get('x-response-time');
    if (!cacheControl || !cacheControl.includes('max-age')) score -= 8;
    if (!responseTimeHeader) score -= 5;
    
    return {
      name,
      url,
      score: Math.max(0, Math.round(score)),
      responseTime,
      status: response.status,
      contentLength,
      cacheControl: cacheControl || 'none',
      serverTime: responseTimeHeader || 'not provided',
      dataValid: data ? (data.success || false) : false,
      success: response.ok,
      timestamp: new Date().toISOString(),
      
      // 📊 性能等级
      performanceGrade: score >= 90 ? 'A+' : score >= 85 ? 'A' : score >= 80 ? 'B+' : score >= 70 ? 'B' : score >= 60 ? 'C' : 'D'
    };
    
  } catch (error) {
    return {
      name,
      url,
      score: 0,
      responseTime: Date.now() - startTime,
      error: error.message,
      success: false,
      performanceGrade: 'F',
      timestamp: new Date().toISOString()
    };
  }
}

// 🚀 主测试函数
async function runSimpleAPITest() {
  console.log('🎯 启动JAVFLIX.TV API性能测试...\n');
  
  const results = [];
  let totalResponseTime = 0;
  let successCount = 0;
  
  for (let i = 0; i < API_TESTS.length; i++) {
    const test = API_TESTS[i];
    const progress = `(${i + 1}/${API_TESTS.length})`;
    
    console.log(`🔍 ${progress} 测试: ${test.name}${test.critical ? ' [关键]' : ''}`);
    
    const result = await testAPIPerformance(test.url, test.name);
    results.push(result);
    
    if (result.success) {
      totalResponseTime += result.responseTime;
      successCount++;
    }
    
    // 🎯 显示结果
    if (result.error) {
      console.log(`   ❌ 测试失败: ${result.error}`);
    } else {
      const emoji = result.score >= 85 ? '🟢' : result.score >= 70 ? '🟡' : '🔴';
      const expectedScore = test.expectedScore || 70;
      const status = result.score >= expectedScore ? '✅ 达标' : '❌ 未达标';
      
      console.log(`   ${emoji} 分数: ${result.score}/100 (${result.performanceGrade}) ${status}`);
      console.log(`   ⏱️  响应时间: ${result.responseTime}ms`);
      console.log(`   📊 状态码: ${result.status}`);
      console.log(`   📦 内容大小: ${result.contentLength} bytes`);
      console.log(`   🗄️ 缓存策略: ${result.cacheControl}`);
      
      if (result.dataValid) {
        console.log(`   ✅ 数据有效`);
      } else {
        console.log(`   ⚠️  数据可能无效`);
      }
      
      if (test.critical && result.score < expectedScore) {
        console.log(`   🚨 关键API性能不达标!`);
      }
    }
    
    console.log('');
    
    // 🔄 避免过于频繁的请求
    await new Promise(resolve => setTimeout(resolve, 500));
  }
  
  // 📈 显示总结
  const totalScore = Math.round(results.reduce((sum, r) => sum + (r.score || 0), 0) / results.length);
  const passedTests = results.filter(r => {
    const test = API_TESTS.find(t => t.name === r.name);
    return r.score >= (test?.expectedScore || 70);
  }).length;
  const criticalIssues = results.filter(r => {
    const test = API_TESTS.find(t => t.name === r.name);
    return test?.critical && r.score < (test.expectedScore || 70);
  }).length;
  
  const avgResponseTime = successCount > 0 ? Math.round(totalResponseTime / successCount) : 0;
  
  // 📊 性能等级统计
  const gradeStats = results.reduce((acc, r) => {
    acc[r.performanceGrade] = (acc[r.performanceGrade] || 0) + 1;
    return acc;
  }, {});
  
  console.log('=' .repeat(60));
  console.log('📊 API性能测试总结');
  console.log('='.repeat(60));
  console.log(`🎯 平均分数: ${totalScore}/100`);
  console.log(`✅ 通过测试: ${passedTests}/${results.length}`);
  console.log(`🚨 关键问题: ${criticalIssues}个`);
  console.log(`⚡ 平均响应时间: ${avgResponseTime}ms`);
  console.log(`📡 成功率: ${Math.round((successCount / results.length) * 100)}%`);
  console.log('');
  console.log('📈 性能等级分布:');
  Object.entries(gradeStats).forEach(([grade, count]) => {
    console.log(`   ${grade}: ${count}个API`);
  });
  
  // 🎉 最终评估
  if (totalScore >= 85 && criticalIssues === 0) {
    console.log('\n🎉 优秀! API性能表现出色!');
  } else if (totalScore >= 75 && criticalIssues <= 1) {
    console.log('\n👍 良好! API性能基本达标，有少量改进空间。');
  } else {
    console.log('\n⚠️  警告! API性能需要优化，建议检查数据库查询和缓存策略。');
  }
  
  // 📋 详细建议
  console.log('\n💡 优化建议:');
  results.forEach(result => {
    if (result.score < 80) {
      console.log(`   • ${result.name}: 响应时间${result.responseTime}ms，建议优化数据库查询`);
    }
    if (result.cacheControl === 'none') {
      console.log(`   • ${result.name}: 缺少缓存策略，建议添加Cache-Control头`);
    }
  });
  
  // 💾 保存结果
  try {
    await fs.mkdir('./performance-reports', { recursive: true });
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const reportPath = `./performance-reports/api-performance-${timestamp}.json`;
    await fs.writeFile(reportPath, JSON.stringify(results, null, 2));
    console.log(`\n💾 详细数据已保存: ${reportPath}`);
  } catch (error) {
    console.log(`\n❌ 保存报告失败: ${error.message}`);
  }
  
  return {
    averageScore: totalScore,
    passedTests,
    totalTests: results.length,
    criticalIssues,
    averageResponseTime: avgResponseTime,
    successRate: Math.round((successCount / results.length) * 100),
    gradeStats
  };
}

// 🚀 启动测试
if (require.main === module) {
  runSimpleAPITest().catch(error => {
    console.error('❌ 测试执行失败:', error);
    process.exit(1);
  });
}

module.exports = { runSimpleAPITest };