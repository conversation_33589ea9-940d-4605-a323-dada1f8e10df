# 环境配置示例

## 概述

通过环境变量配置，无需为不同部署环境修改代码。

## 开发环境 (.env.local)

```bash
# 🔧 开发环境 - 使用前端代理
NODE_ENV=development
NEXT_PUBLIC_USE_PROXY=true
NEXT_PUBLIC_API_BASE_URL=
```

**工作原理**: 前端请求 → 前端代理(3001) → 后端API(4000)

## 生产环境配置方案

### 方案一：单域名部署 (.env.production)

```bash
# 🚀 推荐：前后端使用同一域名
NODE_ENV=production
NEXT_PUBLIC_USE_PROXY=true
NEXT_PUBLIC_API_BASE_URL=
```

**Nginx配置示例**:
```nginx
server {
    listen 80;
    server_name javflix.tv;
    
    # 前端页面
    location / {
        proxy_pass http://localhost:3001;
    }
    
    # API请求直接转发到后端
    location /api/ {
        proxy_pass http://localhost:4000/api/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

**优点**:
- ✅ 无跨域问题
- ✅ 代码无需修改
- ✅ 前端代理依然有效

### 方案二：分离域名部署 (.env.production)

```bash
# 🌐 前后端使用不同域名
NODE_ENV=production
NEXT_PUBLIC_USE_PROXY=false
NEXT_PUBLIC_API_BASE_URL=https://api.javflix.tv
```

**适用场景**:
- API服务部署在不同服务器
- 使用CDN加速前端资源
- 微服务架构

**优点**:
- ✅ 前后端完全解耦
- ✅ 可独立扩展
- ✅ CDN友好

### 方案三：容器化部署 (.env.production)

```bash
# 🐳 Docker/Kubernetes环境
NODE_ENV=production
NEXT_PUBLIC_USE_PROXY=false
NEXT_PUBLIC_API_BASE_URL=http://javflix-api-service:4000
```

**Docker Compose示例**:
```yaml
version: '3.8'
services:
  frontend:
    build: ./javflix
    ports:
      - "80:3000"
    environment:
      - NODE_ENV=production
      - NEXT_PUBLIC_USE_PROXY=false
      - NEXT_PUBLIC_API_BASE_URL=http://backend:4000
    depends_on:
      - backend
      
  backend:
    build: ./javflix-api
    ports:
      - "4000:4000"
    environment:
      - NODE_ENV=production
```

## 测试环境 (.env.test)

```bash
# 🧪 测试环境
NODE_ENV=test
NEXT_PUBLIC_USE_PROXY=false
NEXT_PUBLIC_API_BASE_URL=http://localhost:4000
NEXT_PUBLIC_USE_MOCK_DATA=true
```

## 环境配置对比

| 配置项 | 开发环境 | 生产方案一 | 生产方案二 | 容器环境 |
|--------|----------|------------|------------|----------|
| USE_PROXY | true | true | false | false |
| API_BASE_URL | 空 | 空 | https://api.domain.com | http://service:4000 |
| 部署复杂度 | 简单 | 中等 | 简单 | 中等 |
| 性能 | 良好 | 最佳 | 良好 | 良好 |
| 扩展性 | 低 | 中等 | 高 | 高 |

## 迁移指南

### 从硬编码到环境配置

**Before (需要手动修改)**:
```javascript
// 😰 每次部署都要改
const response = await fetch('http://localhost:4000/api/videos');
```

**After (自动适配)**:
```javascript
// 😍 一次配置，处处运行
import { api } from './api-config';
const response = await api.get('/api/videos');
```

### 部署流程

1. **选择部署方案** (单域名/分离域名/容器化)
2. **配置环境变量** (复制对应的.env示例)
3. **构建应用** (`npm run build`)
4. **部署** (无需修改代码)

### 验证配置

运行测试脚本验证配置：
```bash
# 测试当前环境配置
node test-proxy-api.js

# 测试生产环境配置
NODE_ENV=production node test-proxy-api.js
``` 