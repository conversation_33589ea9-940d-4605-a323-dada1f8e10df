#!/usr/bin/env node\n\n/**\n * 批量为所有Next.js页面启用SSR\n * 基于Next.js 15 App Router SSR最佳实践\n * 参考: https://nextjs.org/docs/pages/building-your-application/rendering/server-side-rendering\n */\n\nconst fs = require('fs');\nconst path = require('path');\nconst { execSync } = require('child_process');\n\n// SSR配置模板\nconst SSR_CONFIG = `// Route Segment Config for Server-Side Rendering (SSR)\nexport const dynamic = 'force-dynamic'; // Enable SSR - render on each request\nexport const fetchCache = 'default-no-store'; // Ensure fresh data on each request\nexport const runtime = 'nodejs'; // Use Node.js runtime for better performance\nexport const preferredRegion = 'auto'; // Auto-select optimal region\nexport const revalidate = false; // Disable revalidation for true SSR`;\n\n// 需要启用SSR的页面列表（排除客户端组件和已处理的页面）\nconst TARGET_PAGES = [\n  'src/app/[locale]/search/page.tsx',\n  'src/app/[locale]/category/page.tsx',\n  'src/app/[locale]/actress/[name]/page.tsx',\n  'src/app/[locale]/director/[name]/page.tsx',\n  'src/app/[locale]/studio/[name]/page.tsx',\n  'src/app/[locale]/publisher/[name]/page.tsx',\n  'src/app/[locale]/series/[name]/page.tsx',\n  'src/app/[locale]/popular/weekly/page.tsx',\n  'src/app/[locale]/auth/login/page.tsx',\n  'src/app/[locale]/auth/register/page.tsx',\n  'src/app/[locale]/auth/signup/page.tsx',\n  'src/app/[locale]/profile/page.tsx',\n  'src/app/[locale]/profile/favorites/page.tsx'\n];\n\n// 处理单个页面文件\nfunction processPageFile(filePath) {\n  const fullPath = path.join('/Users/<USER>/Desktop/JAVFLIX.TV/javflix', filePath);\n  \n  if (!fs.existsSync(fullPath)) {\n    console.log(`⚠️  文件不存在: ${filePath}`);\n    return false;\n  }\n  \n  try {\n    let content = fs.readFileSync(fullPath, 'utf8');\n    \n    // 检查是否是客户端组件\n    if (content.includes(\"'use client'\")) {\n      console.log(`⏭️  跳过客户端组件: ${filePath}`);\n      return false;\n    }\n    \n    // 检查是否已经有Route Segment Config\n    if (content.includes('export const dynamic')) {\n      console.log(`✅ 已有配置: ${filePath}`);\n      return true;\n    }\n    \n    // 查找合适的插入位置（在imports之后，在函数之前）\n    const lines = content.split('\\n');\n    let insertIndex = -1;\n    \n    // 找到最后一个import语句的位置\n    for (let i = 0; i < lines.length; i++) {\n      const line = lines[i].trim();\n      if (line.startsWith('import ') || line.startsWith('import{') || line.startsWith('import*')) {\n        insertIndex = i + 1;\n      } else if (line.startsWith('export') && !line.includes('import')) {\n        break;\n      }\n    }\n    \n    if (insertIndex === -1) {\n      // 如果没有找到import，就在文件开头插入\n      insertIndex = 0;\n    }\n    \n    // 插入SSR配置\n    lines.splice(insertIndex, 0, '', SSR_CONFIG, '');\n    \n    // 写回文件\n    fs.writeFileSync(fullPath, lines.join('\\n'), 'utf8');\n    console.log(`✅ 已启用SSR: ${filePath}`);\n    return true;\n    \n  } catch (error) {\n    console.error(`❌ 处理失败 ${filePath}:`, error.message);\n    return false;\n  }\n}\n\n// 主函数\nfunction main() {\n  console.log('🚀 开始为所有页面启用SSR...');\n  console.log(`📊 目标页面数量: ${TARGET_PAGES.length}`);\n  \n  let processedCount = 0;\n  let successCount = 0;\n  \n  TARGET_PAGES.forEach(pagePath => {\n    processedCount++;\n    console.log(`\\n[${processedCount}/${TARGET_PAGES.length}] 处理: ${pagePath}`);\n    \n    if (processPageFile(pagePath)) {\n      successCount++;\n    }\n  });\n  \n  console.log('\\n' + '='.repeat(60));\n  console.log('🎉 SSR启用完成！');\n  console.log(`📊 处理总数: ${processedCount}`);\n  console.log(`✅ 成功启用: ${successCount}`);\n  console.log(`❌ 失败数量: ${processedCount - successCount}`);\n  \n  if (successCount > 0) {\n    console.log('\\n🔄 正在重启开发服务器以应用更改...');\n    try {\n      // 这里可以添加重启逻辑，但通常不需要，Next.js会自动检测更改\n      console.log('✅ 配置已应用，Next.js将自动重新编译');\n    } catch (error) {\n      console.log('⚠️  请手动重启开发服务器以应用更改');\n    }\n  }\n}\n\n// 运行主函数\nif (require.main === module) {\n  main();\n}\n\nmodule.exports = { processPageFile, SSR_CONFIG };"