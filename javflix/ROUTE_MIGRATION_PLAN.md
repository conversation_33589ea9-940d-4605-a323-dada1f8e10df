# 路由迁移计划\n\n## 📋 概述\n\n为了统一用户体验和简化代码维护，需要将旧版非国际化路由迁移到新的国际化路由系统。\n\n## 🎯 迁移状态\n\n### ✅ 已完成\n\n#### 女优页面增强\n- ✅ **后台采集功能**: 修改 `javbus-api` 采集逻辑，保存完整女优信息（生日、三围、身高、出生地、爱好等）\n- ✅ **数据库结构**: 为 `stars` 表添加新字段支持完整女优资料\n- ✅ **API接口**: 更新 `/api/actress/[name]` 返回完整女优信息\n- ✅ **国际化页面**: `/[locale]/actress/[name]` 支持显示完整女优资料\n- ✅ **重定向配置**: 在 `next.config.js` 中添加旧版页面重定向\n\n### 🔄 当前路由对照表\n\n| 旧版路由 (已弃用) | 新版路由 (推荐) | 状态 |\n|------------------|----------------|------|\n| `/actress/[id]` | `/[locale]/actress/[name]` | 🔄 重定向中 |\n| `/actress` | `/[locale]/actress` | ✅ 已迁移 |\n\n## 📊 使用情况分析\n\n### 非国际化路由 `/actress/[id]`\n- ❌ **问题**: 使用硬编码假数据\n- ❌ **问题**: 不支持新的女优信息字段\n- ❌ **问题**: 与当前设计系统不一致\n- ✅ **解决**: 已添加自动重定向到国际化版本\n\n### 国际化路由 `/[locale]/actress/[name]`\n- ✅ **优势**: 支持完整女优信息显示\n- ✅ **优势**: 与最新API对接\n- ✅ **优势**: 符合项目整体国际化策略\n- ✅ **优势**: 现代化UI设计\n\n## 🗑️ 删除计划\n\n### 阶段1: 重定向（当前阶段）\n- [x] 添加 `next.config.js` 重定向规则\n- [x] 修改 `/actress/[id]/page.tsx` 为重定向页面\n- [x] 添加日志记录跟踪访问情况\n\n### 阶段2: 监控期（建议1-2个月）\n- [ ] 监控旧版路由访问频率\n- [ ] 收集用户反馈\n- [ ] 确认外部链接更新情况\n- [ ] 搜索引擎索引更新\n\n### 阶段3: 完全删除（未来版本）\n**删除文件清单**:\n```\nsrc/app/actress/[id]/\n├── page.tsx                    # 删除重定向页面\n├── ActressDetailClient.tsx     # 删除旧版客户端组件\n└── 整个目录可删除\n\nsrc/app/actress/\n├── ActressClient.tsx           # 保留（国际化版本仍需要）\n└── page.tsx                    # 保留（国际化版本仍需要）\n```\n\n## 📈 迁移收益\n\n### 开发维护\n- 🎯 **代码简化**: 减少重复代码和维护负担\n- 🔧 **功能统一**: 所有女优相关功能使用同一套代码\n- 🐛 **Bug减少**: 消除数据不一致问题\n\n### 用户体验\n- 🌍 **国际化**: 统一的多语言支持\n- 📱 **现代化**: 最新的UI设计和交互\n- 📊 **数据完整**: 显示完整的女优资料信息\n\n### SEO优化\n- 🔗 **URL统一**: 避免重复内容问题\n- 📍 **链接集中**: 集中权重到国际化URL\n- 🗂️ **结构化**: 更好的网站结构\n\n## 🚨 注意事项\n\n### 外部链接\n- 确认社交媒体分享链接已更新\n- 检查搜索引擎收录情况\n- 通知合作伙伴更新链接\n\n### 用户收藏\n- 用户收藏的旧链接会自动重定向\n- 考虑在用户界面提示链接已更新\n\n### 分析数据\n- Google Analytics 等工具需要更新跟踪设置\n- 确保重定向不影响访问统计\n\n## 🔍 检查清单\n\n### 迁移完成检查\n- [x] 数据库字段已添加\n- [x] API接口已更新\n- [x] 前端页面已适配\n- [x] 采集功能已增强\n- [x] 重定向已配置\n- [ ] 监控数据收集\n- [ ] 用户反馈收集\n- [ ] SEO影响评估\n\n### 删除前检查\n- [ ] 旧版路由访问量 < 5%\n- [ ] 用户反馈积极\n- [ ] 搜索引擎已重新索引\n- [ ] 外部链接已更新\n- [ ] 备份已创建\n\n---\n\n**最后更新**: 2024-12-19  \n**负责人**: AI Assistant  \n**状态**: 迁移阶段1已完成，进入监控期\n"