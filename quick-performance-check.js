#!/usr/bin/env node

/**
 * JAVFLIX.TV 快速性能检查工具
 * 无需额外依赖的性能测试脚本
 */

const http = require('http');
const https = require('https');
const { performance } = require('perf_hooks');

// 测试配置
const config = {
  baseUrl: 'http://localhost:3001',
  timeout: 10000,
  userAgent: 'JAVFLIX-Performance-Tester/1.0'
};

// 性能测试函数
async function testPagePerformance(url, pageName) {
  return new Promise((resolve) => {
    const startTime = performance.now();
    const urlObj = new URL(url);
    const client = urlObj.protocol === 'https:' ? https : http;
    
    const options = {
      hostname: urlObj.hostname,
      port: urlObj.port || (urlObj.protocol === 'https:' ? 443 : 80),
      path: urlObj.pathname + urlObj.search,
      method: 'GET',
      headers: {
        'User-Agent': config.userAgent,
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate',
        'Cache-Control': 'no-cache'
      },
      timeout: config.timeout
    };
    
    let responseStartTime;
    let firstByteTime;
    let dataReceived = 0;
    let statusCode;
    let contentType;
    
    const req = client.request(options, (res) => {
      responseStartTime = performance.now();
      statusCode = res.statusCode;
      contentType = res.headers['content-type'] || 'unknown';
      
      res.on('data', (chunk) => {
        if (!firstByteTime) {
          firstByteTime = performance.now();
        }
        dataReceived += chunk.length;
      });
      
      res.on('end', () => {
        const endTime = performance.now();
        
        const result = {
          pageName,
          url,
          timestamp: new Date().toISOString(),
          success: true,
          statusCode,
          contentType,
          metrics: {
            totalTime: Math.round(endTime - startTime),
            responseStartTime: Math.round(responseStartTime - startTime),
            firstByteTime: Math.round((firstByteTime || responseStartTime) - startTime),
            downloadTime: Math.round(endTime - (firstByteTime || responseStartTime)),
            dataSize: dataReceived,
            downloadSpeed: Math.round(dataReceived / ((endTime - startTime) / 1000))
          }
        };
        
        resolve(result);
      });
    });
    
    req.on('error', (error) => {
      const endTime = performance.now();
      resolve({
        pageName,
        url,
        timestamp: new Date().toISOString(),
        success: false,
        error: error.message,
        metrics: {
          totalTime: Math.round(endTime - startTime)
        }
      });
    });
    
    req.on('timeout', () => {
      req.destroy();
      resolve({
        pageName,
        url,
        timestamp: new Date().toISOString(),
        success: false,
        error: 'Request timeout',
        metrics: {
          totalTime: config.timeout
        }
      });
    });
    
    req.end();
  });
}

// 并发测试函数
async function runConcurrentTest(url, concurrency = 10) {
  console.log(`🚀 运行并发测试: ${concurrency} 个并发请求`);
  
  const promises = Array(concurrency).fill().map((_, index) => 
    testPagePerformance(url, `并发请求-${index + 1}`)
  );
  
  const startTime = performance.now();
  const results = await Promise.all(promises);
  const endTime = performance.now();
  
  const successful = results.filter(r => r.success);
  const failed = results.filter(r => !r.success);
  
  return {
    totalTime: Math.round(endTime - startTime),
    totalRequests: concurrency,
    successful: successful.length,
    failed: failed.length,
    averageResponseTime: successful.length > 0 ? 
      Math.round(successful.reduce((sum, r) => sum + r.metrics.totalTime, 0) / successful.length) : 0,
    throughput: Math.round(successful.length / ((endTime - startTime) / 1000))
  };
}

// 生成性能评分
function calculatePerformanceScore(metrics) {
  let score = 100;
  
  // 响应时间评分 (权重: 40%)
  if (metrics.totalTime > 3000) score -= 40;
  else if (metrics.totalTime > 2000) score -= 25;
  else if (metrics.totalTime > 1000) score -= 15;
  else if (metrics.totalTime > 500) score -= 5;
  
  // 首字节时间评分 (权重: 30%)
  if (metrics.firstByteTime > 1000) score -= 30;
  else if (metrics.firstByteTime > 600) score -= 20;
  else if (metrics.firstByteTime > 300) score -= 10;
  else if (metrics.firstByteTime > 150) score -= 5;
  
  // 下载速度评分 (权重: 20%)
  if (metrics.downloadSpeed < 10000) score -= 20;
  else if (metrics.downloadSpeed < 50000) score -= 15;
  else if (metrics.downloadSpeed < 100000) score -= 10;
  else if (metrics.downloadSpeed < 500000) score -= 5;
  
  // 内容大小评分 (权重: 10%)
  if (metrics.dataSize > 5000000) score -= 10; // 5MB
  else if (metrics.dataSize > 2000000) score -= 7; // 2MB
  else if (metrics.dataSize > 1000000) score -= 5; // 1MB
  else if (metrics.dataSize > 500000) score -= 2; // 500KB
  
  return Math.max(0, Math.round(score));
}

// 生成优化建议
function generateRecommendations(results) {
  const recommendations = [];
  const avgMetrics = calculateAverageMetrics(results);
  
  if (avgMetrics.totalTime > 2000) {
    recommendations.push('🚨 页面加载时间过长，建议实施静态页面生成(SSG)');
  }
  
  if (avgMetrics.firstByteTime > 600) {
    recommendations.push('⚡ 服务器响应时间过慢，建议优化后端API和数据库查询');
  }
  
  if (avgMetrics.dataSize > 1000000) {
    recommendations.push('📦 页面大小过大，建议压缩图片和代码分割');
  }
  
  if (avgMetrics.downloadSpeed < 100000) {
    recommendations.push('🔄 下载速度较慢，建议启用CDN和压缩');
  }
  
  // 根据内容类型给出建议
  const hasLargeContent = results.some(r => r.metrics && r.metrics.dataSize > 500000);
  if (hasLargeContent) {
    recommendations.push('🖼️ 检测到大型资源，建议实施图片懒加载和WebP格式');
  }
  
  if (recommendations.length === 0) {
    recommendations.push('✅ 性能表现良好，可考虑进一步的高级优化');
  }
  
  return recommendations;
}

// 计算平均指标
function calculateAverageMetrics(results) {
  const validResults = results.filter(r => r.success && r.metrics);
  if (validResults.length === 0) return {};
  
  const metrics = validResults.reduce((acc, result) => {
    Object.keys(result.metrics).forEach(key => {
      acc[key] = (acc[key] || 0) + result.metrics[key];
    });
    return acc;
  }, {});
  
  Object.keys(metrics).forEach(key => {
    metrics[key] = Math.round(metrics[key] / validResults.length);
  });
  
  return metrics;
}

// 格式化文件大小
function formatFileSize(bytes) {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 主测试函数
async function runPerformanceTest() {
  console.log('🎯 JAVFLIX.TV 快速性能检查');
  console.log('================================\n');
  
  const testPages = [
    { name: '首页', url: '/' },
    { name: '搜索页', url: '/zh-CN/search?q=test' },
    { name: 'API-首页数据', url: '/api/videos/popular?limit=10' },
    { name: 'API-搜索数据', url: '/api/search?q=test&limit=5' }
  ];
  
  const results = [];
  
  // 单页面测试
  for (const page of testPages) {
    const url = config.baseUrl + page.url;
    console.log(`📊 测试 ${page.name}...`);
    
    const result = await testPagePerformance(url, page.name);
    results.push(result);
    
    if (result.success) {
      const score = calculatePerformanceScore(result.metrics);
      console.log(`✅ ${page.name} 完成`);
      console.log(`   性能评分: ${score}/100`);
      console.log(`   响应时间: ${result.metrics.totalTime}ms`);
      console.log(`   首字节时间: ${result.metrics.firstByteTime}ms`);
      console.log(`   内容大小: ${formatFileSize(result.metrics.dataSize)}`);
      console.log(`   下载速度: ${formatFileSize(result.metrics.downloadSpeed)}/s`);
    } else {
      console.log(`❌ ${page.name} 失败: ${result.error}`);
    }
    console.log('');
    
    // 测试间隔
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  // 并发测试
  const concurrentResult = await runConcurrentTest(config.baseUrl, 5);
  
  // 生成报告
  console.log('📋 性能测试报告');
  console.log('================\n');
  
  const validResults = results.filter(r => r.success);
  const avgMetrics = calculateAverageMetrics(validResults);
  const avgScore = validResults.length > 0 ? 
    Math.round(validResults.reduce((sum, r) => sum + calculatePerformanceScore(r.metrics), 0) / validResults.length) : 0;
  
  console.log(`📊 总体性能评分: ${avgScore}/100`);
  console.log(`⏱️ 平均响应时间: ${avgMetrics.totalTime || 0}ms`);
  console.log(`⚡ 平均首字节时间: ${avgMetrics.firstByteTime || 0}ms`);
  console.log(`📦 平均内容大小: ${formatFileSize(avgMetrics.dataSize || 0)}`);
  console.log(`🚀 平均下载速度: ${formatFileSize(avgMetrics.downloadSpeed || 0)}/s`);
  
  console.log('\n🔄 并发性能测试:');
  console.log(`   并发请求数: ${concurrentResult.totalRequests}`);
  console.log(`   成功请求: ${concurrentResult.successful}`);
  console.log(`   失败请求: ${concurrentResult.failed}`);
  console.log(`   平均响应时间: ${concurrentResult.averageResponseTime}ms`);
  console.log(`   吞吐量: ${concurrentResult.throughput} 请求/秒`);
  
  console.log('\n💡 优化建议:');
  const recommendations = generateRecommendations(validResults);
  recommendations.forEach(rec => console.log(`   ${rec}`));
  
  console.log('\n🎯 性能等级:');
  if (avgScore >= 90) {
    console.log('   🏆 A级 (优秀) - 性能表现出色');
  } else if (avgScore >= 80) {
    console.log('   🥈 B级 (良好) - 性能良好，有优化空间');
  } else if (avgScore >= 70) {
    console.log('   🥉 C级 (一般) - 需要性能优化');
  } else if (avgScore >= 60) {
    console.log('   ⚠️ D级 (较差) - 急需性能改进');
  } else {
    console.log('   🚨 F级 (差) - 严重性能问题');
  }
  
  console.log('\n✅ 性能检查完成！');
  
  return {
    averageScore: avgScore,
    results: validResults,
    concurrentTest: concurrentResult,
    recommendations
  };
}

// 运行测试
if (require.main === module) {
  runPerformanceTest().catch(console.error);
}

module.exports = { runPerformanceTest, testPagePerformance };