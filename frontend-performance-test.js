/**
 * JAVFLIX.TV Next.js前端页面性能测试工具
 * 基于Next.js缓存和性能优化最佳实践
 * 参考: https://nextjs.org/docs/app/deep-dive/caching
 */

const http = require('http');
const https = require('https');

const TEST_CONFIG = {
  baseUrl: 'http://localhost:3001',
  timeout: 15000,
  concurrent: 10
};

// 测试页面列表
const FRONTEND_PAGES = [
  {
    path: '/zh-CN/new',
    name: '最新页面',
    description: '最新影片列表页面'
  },
  {
    path: '/zh-CN/popular', 
    name: '热门页面',
    description: '热门影片列表页面'
  },
  {
    path: '/zh-CN/actress',
    name: '演员页面', 
    description: '演员列表页面'
  },
  {
    path: '/zh-CN',
    name: '首页',
    description: '网站首页'
  }
];

function makeRequest(url) {
  return new Promise((resolve, reject) => {
    const startTime = Date.now();
    
    const req = http.request(`${TEST_CONFIG.baseUrl}${url}`, {
      method: 'GET',
      timeout: TEST_CONFIG.timeout,
      headers: {
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
      }
    }, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        const endTime = Date.now();
        const responseTime = endTime - startTime;
        
        // 分析Next.js相关的响应头
        const nextjsHeaders = {
          cacheControl: res.headers['cache-control'] || 'none',
          etag: res.headers['etag'] || 'none',
          lastModified: res.headers['last-modified'] || 'none',
          xNextjsCache: res.headers['x-nextjs-cache'] || 'unknown',
          xVercelCache: res.headers['x-vercel-cache'] || 'none',
          contentEncoding: res.headers['content-encoding'] || 'none',
          contentLength: res.headers['content-length'] || data.length,
          server: res.headers['server'] || 'unknown'
        };
        
        // 分析页面内容
        const isSSR = data.includes('__NEXT_DATA__');
        const hasHydration = data.includes('_app');
        const hasPreloading = data.includes('rel="preload"');
        const hasImages = data.includes('<img') || data.includes('next/image');
        const scriptCount = (data.match(/<script/g) || []).length;
        const linkCount = (data.match(/<link/g) || []).length;
        
        resolve({
          success: res.statusCode === 200,
          statusCode: res.statusCode,
          responseTime,
          dataSize: data.length,
          nextjsHeaders,
          pageAnalysis: {
            isSSR,
            hasHydration,
            hasPreloading,
            hasImages,
            scriptCount,
            linkCount
          }
        });
      });
    });
    
    req.on('error', (error) => {
      reject({
        success: false,
        error: error.message,
        responseTime: Date.now() - startTime
      });
    });
    
    req.on('timeout', () => {
      req.destroy();
      reject({
        success: false,
        error: 'Request timeout'
      });
    });
    
    req.end();
  });
}

// 测试Next.js缓存效果
async function testNextjsCaching(path, description) {
  console.log(`\\n💾 Next.js缓存测试: ${description}`);
  console.log('------------------------------------------');
  
  // 第一次请求
  console.log('🔸 第一次请求 (冷启动)...');
  const first = await makeRequest(path);
  
  if (!first.success) {
    console.log(`   ❌ 第一次请求失败: ${first.error}`);
    return null;
  }
  
  console.log(`   📈 响应时间: ${first.responseTime}ms`);
  console.log(`   💾 缓存状态: ${first.nextjsHeaders.xNextjsCache}`);
  console.log(`   📦 压缩: ${first.nextjsHeaders.contentEncoding}`);
  console.log(`   📄 数据大小: ${Math.round(first.dataSize/1024)}KB`);
  
  // 等待1秒后第二次请求
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  console.log('🔸 第二次请求 (预期缓存命中)...');
  const second = await makeRequest(path);
  
  if (!second.success) {
    console.log(`   ❌ 第二次请求失败`);
    return null;
  }
  
  console.log(`   📈 响应时间: ${second.responseTime}ms`);
  console.log(`   💾 缓存状态: ${second.nextjsHeaders.xNextjsCache}`);
  
  const improvement = ((first.responseTime - second.responseTime) / first.responseTime * 100);
  
  if (second.responseTime < first.responseTime) {
    console.log(`   ✅ 性能提升: ${improvement.toFixed(1)}%`);
  } else {
    console.log(`   ⚠️  未检测到明显的缓存效果`);
  }
  
  return {
    firstLoad: first.responseTime,
    secondLoad: second.responseTime,
    cacheEffective: second.responseTime < first.responseTime,
    improvement,
    cacheStatus: second.nextjsHeaders.xNextjsCache,
    pageSize: first.dataSize,
    pageAnalysis: first.pageAnalysis
  };
}

// 并发测试
async function testConcurrency(path, count = 5) {
  console.log(`\\n🔥 并发测试: ${count}个并发请求`);
  console.log('------------------------------------------');
  
  const startTime = Date.now();
  const promises = [];
  
  for (let i = 0; i < count; i++) {
    promises.push(makeRequest(path));
  }
  
  try {
    const results = await Promise.all(promises);
    const endTime = Date.now();
    const totalTime = endTime - startTime;
    
    const successCount = results.filter(r => r.success).length;
    const avgResponseTime = results.reduce((sum, r) => sum + r.responseTime, 0) / results.length;
    const throughput = Math.round((count / totalTime) * 1000);
    
    console.log(`📊 并发测试结果:`);
    console.log(`   - 总耗时: ${totalTime}ms`);
    console.log(`   - 平均响应时间: ${Math.round(avgResponseTime)}ms`);
    console.log(`   - 成功率: ${Math.round((successCount/count)*100)}%`);
    console.log(`   - 吞吐量: ${throughput} 请求/秒`);
    
    return {
      successRate: (successCount/count)*100,
      avgResponseTime,
      throughput
    };
  } catch (error) {
    console.log(`❌ 并发测试失败: ${error.message}`);
    return null;
  }
}

async function runFrontendPerformanceTest() {
  console.log('🎬 JAVFLIX.TV Next.js前端性能测试');
  console.log('基于Next.js缓存和性能优化最佳实践');
  console.log('参考: https://nextjs.org/docs/app/deep-dive/caching');
  console.log('============================================================');
  
  // 检查前端服务器
  console.log('🔍 检查前端服务器连通性...');
  try {
    const health = await makeRequest('/zh-CN');
    if (health.success) {
      console.log('✅ 前端服务器连接正常');
      console.log(`   📄 Server: ${health.nextjsHeaders.server}`);
    } else {
      console.log('❌ 前端服务器连接失败');
      return;
    }
  } catch (error) {
    console.log(`❌ 前端服务器连接超时: ${error.error}`);
    return;
  }
  
  const testResults = [];
  
  // 第一阶段：基础页面性能测试
  console.log('\\n📋 第一阶段: Next.js页面性能分析');
  console.log('=========================================');
  
  for (const page of FRONTEND_PAGES) {
    const cacheResult = await testNextjsCaching(page.path, page.name);
    if (cacheResult) {
      testResults.push({
        page: page.name,
        path: page.path,
        ...cacheResult
      });
    }
  }
  
  // 第二阶段：并发性能测试
  console.log('\\n📋 第二阶段: 并发性能测试');
  console.log('=========================================');
  
  const concurrentResults = [];
  
  for (const page of FRONTEND_PAGES.slice(0, 2)) { // 只测试前两个页面
    console.log(`\\n🚀 测试页面: ${page.name}`);
    const result = await testConcurrency(page.path, 8);
    if (result) {
      concurrentResults.push({
        page: page.name,
        ...result
      });
    }
  }
  
  // 生成性能报告
  console.log('\\n============================================================');
  console.log('📊 JAVFLIX.TV Next.js前端性能报告');
  console.log('============================================================');
  
  if (testResults.length > 0) {
    const avgFirstLoad = testResults.reduce((sum, r) => sum + r.firstLoad, 0) / testResults.length;
    const avgSecondLoad = testResults.reduce((sum, r) => sum + r.secondLoad, 0) / testResults.length;
    const avgPageSize = testResults.reduce((sum, r) => sum + r.pageSize, 0) / testResults.length;
    const cacheEffectiveCount = testResults.filter(r => r.cacheEffective).length;
    
    console.log(`🎯 页面性能统计:`);
    console.log(`   - 测试页面数: ${testResults.length}`);
    console.log(`   - 平均首次加载: ${Math.round(avgFirstLoad)}ms`);
    console.log(`   - 平均二次加载: ${Math.round(avgSecondLoad)}ms`);
    console.log(`   - 平均页面大小: ${Math.round(avgPageSize/1024)}KB`);
    console.log(`   - 缓存有效页面: ${cacheEffectiveCount}/${testResults.length}`);
    
    // 分析页面特征
    console.log(`\\n🔍 页面技术分析:`);
    testResults.forEach(result => {
      console.log(`   📄 ${result.page}:`);
      console.log(`      - SSR渲染: ${result.pageAnalysis.isSSR ? '✅' : '❌'}`);
      console.log(`      - 预加载资源: ${result.pageAnalysis.hasPreloading ? '✅' : '❌'}`);
      console.log(`      - 图片优化: ${result.pageAnalysis.hasImages ? '✅' : '❌'}`);
      console.log(`      - 脚本数量: ${result.pageAnalysis.scriptCount}`);
      console.log(`      - 链接数量: ${result.pageAnalysis.linkCount}`);
    });
  }
  
  if (concurrentResults.length > 0) {
    const avgThroughput = concurrentResults.reduce((sum, r) => sum + r.throughput, 0) / concurrentResults.length;
    const avgSuccessRate = concurrentResults.reduce((sum, r) => sum + r.successRate, 0) / concurrentResults.length;
    const avgConcurrentResponse = concurrentResults.reduce((sum, r) => sum + r.avgResponseTime, 0) / concurrentResults.length;
    
    console.log(`\\n🚀 并发性能统计:`);
    console.log(`   - 平均吞吐量: ${Math.round(avgThroughput)} 请求/秒`);
    console.log(`   - 平均成功率: ${avgSuccessRate.toFixed(1)}%`);
    console.log(`   - 平均并发响应: ${Math.round(avgConcurrentResponse)}ms`);
  }
  
  // 性能评级
  let frontendScore = 0;
  let grade = 'F';
  
  if (testResults.length > 0) {
    const avgFirstLoad = testResults.reduce((sum, r) => sum + r.firstLoad, 0) / testResults.length;
    const avgSecondLoad = testResults.reduce((sum, r) => sum + r.secondLoad, 0) / testResults.length;
    const cacheEffectiveRate = testResults.filter(r => r.cacheEffective).length / testResults.length;
    
    // 首次加载时间评分 (30分)
    if (avgFirstLoad <= 1000) frontendScore += 30;
    else if (avgFirstLoad <= 2000) frontendScore += 25;
    else if (avgFirstLoad <= 3000) frontendScore += 20;
    else if (avgFirstLoad <= 5000) frontendScore += 15;
    else frontendScore += 10;
    
    // 缓存效果评分 (25分)
    if (cacheEffectiveRate >= 0.8) frontendScore += 25;
    else if (cacheEffectiveRate >= 0.6) frontendScore += 20;
    else if (cacheEffectiveRate >= 0.4) frontendScore += 15;
    else if (cacheEffectiveRate >= 0.2) frontendScore += 10;
    else frontendScore += 5;
    
    // 二次加载时间评分 (20分)
    if (avgSecondLoad <= 300) frontendScore += 20;
    else if (avgSecondLoad <= 500) frontendScore += 18;
    else if (avgSecondLoad <= 800) frontendScore += 15;
    else if (avgSecondLoad <= 1000) frontendScore += 12;
    else frontendScore += 8;
    
    // 页面大小评分 (15分)
    const avgPageSize = testResults.reduce((sum, r) => sum + r.pageSize, 0) / testResults.length;
    if (avgPageSize <= 100 * 1024) frontendScore += 15; // 100KB
    else if (avgPageSize <= 300 * 1024) frontendScore += 12; // 300KB
    else if (avgPageSize <= 500 * 1024) frontendScore += 10; // 500KB
    else if (avgPageSize <= 1024 * 1024) frontendScore += 8; // 1MB
    else frontendScore += 5;
  }
  
  // 并发性能评分 (10分)
  if (concurrentResults.length > 0) {
    const avgThroughput = concurrentResults.reduce((sum, r) => sum + r.throughput, 0) / concurrentResults.length;
    if (avgThroughput >= 100) frontendScore += 10;
    else if (avgThroughput >= 50) frontendScore += 8;
    else if (avgThroughput >= 30) frontendScore += 6;
    else if (avgThroughput >= 20) frontendScore += 4;
    else frontendScore += 2;
  }
  
  if (frontendScore >= 90) grade = 'A+';
  else if (frontendScore >= 85) grade = 'A';
  else if (frontendScore >= 80) grade = 'B+';
  else if (frontendScore >= 75) grade = 'B';
  else if (frontendScore >= 70) grade = 'C+';
  else if (frontendScore >= 65) grade = 'C';
  else if (frontendScore >= 60) grade = 'D';
  
  console.log(`\\n🏆 前端性能评级: ${frontendScore}/100 (${grade}级)`);
  
  // 优化建议
  console.log(`\\n💡 基于Next.js缓存机制的优化建议:`);
  
  if (frontendScore < 90) {
    if (testResults.length > 0) {
      const avgFirstLoad = testResults.reduce((sum, r) => sum + r.firstLoad, 0) / testResults.length;
      const avgPageSize = testResults.reduce((sum, r) => sum + r.pageSize, 0) / testResults.length;
      const cacheEffectiveRate = testResults.filter(r => r.cacheEffective).length / testResults.length;
      
      if (avgFirstLoad > 2000) {
        console.log(`   🔧 首次加载优化 (当前${Math.round(avgFirstLoad)}ms):`);
        console.log(`      - 启用 generateStaticParams 预渲染动态路由`);
        console.log(`      - 配置 Full Route Cache 静态生成`);
        console.log(`      - 使用 next/image 优化图片`);
        console.log(`      - 启用代码分割和懒加载`);
      }
      
      if (cacheEffectiveRate < 0.8) {
        console.log(`   💾 缓存优化 (当前命中率${(cacheEffectiveRate*100).toFixed(1)}%):`);
        console.log(`      - 配置适当的 revalidate 时间`);
        console.log(`      - 使用 fetch 的 cache 和 next.revalidate 选项`);
        console.log(`      - 实施 Router Cache 预取策略`);
        console.log(`      - 配置静态生成模式`);
      }
      
      if (avgPageSize > 500 * 1024) {
        console.log(`   📦 页面大小优化 (当前${Math.round(avgPageSize/1024)}KB):`);
        console.log(`      - 启用 gzip/brotli 压缩`);
        console.log(`      - 优化图片格式和大小`);
        console.log(`      - 移除未使用的代码`);
        console.log(`      - 使用 dynamic imports 代码分割`);
      }
    }
    
    console.log(`\\n🔗 Next.js性能优化参考资料:`);
    console.log(`   - Next.js缓存机制: https://nextjs.org/docs/app/deep-dive/caching`);
    console.log(`   - 静态生成和ISR: https://nextjs.org/docs/pages/building-your-application/rendering/static-site-generation`);
    console.log(`   - 图片优化: https://nextjs.org/docs/pages/building-your-application/optimizing/images`);
  } else {
    console.log(`   ✅ 前端性能已达到优秀水平！`);
    console.log(`   🎯 继续保持当前的优化策略`);
  }
  
  console.log(`\\n🎉 前端性能测试完成！`);
}

runFrontendPerformanceTest().catch(console.error);