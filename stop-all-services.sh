#!/bin/bash

echo "🛑 停止JAVFLIX.TV所有服务..."

# 停止Go服务器
if [ -f /tmp/go.pid ]; then
    GO_PID=$(cat /tmp/go.pid)
    echo "停止Go视频处理服务器 (PID: $GO_PID)..."
    kill $GO_PID 2>/dev/null && echo "✅ Go服务器已停止" || echo "⚠️ Go服务器可能已经停止"
    rm -f /tmp/go.pid
fi

# 停止API服务器
if [ -f /tmp/api.pid ]; then
    API_PID=$(cat /tmp/api.pid)
    echo "停止Node.js API服务器 (PID: $API_PID)..."
    kill $API_PID 2>/dev/null && echo "✅ API服务器已停止" || echo "⚠️ API服务器可能已经停止"
    rm -f /tmp/api.pid
fi

# 停止JavBus API
if [ -f /tmp/javbus.pid ]; then
    JAVBUS_PID=$(cat /tmp/javbus.pid)
    echo "停止JavBus API服务器 (PID: $JAVBUS_PID)..."
    kill $JAVBUS_PID 2>/dev/null && echo "✅ JavBus API已停止" || echo "⚠️ JavBus API可能已经停止"
    rm -f /tmp/javbus.pid
fi

# 强制杀死可能残留的进程
echo "🧹 清理残留进程..."
pkill -f "node.*server.js" 2>/dev/null
pkill -f "video-processor" 2>/dev/null
pkill -f "javbus-api" 2>/dev/null

echo "✅ 所有服务已停止"
