{"mcpServers": {"javflix-server": {"command": "node", "args": ["/Users/<USER>/Desktop/JAVFLIX.TV/mcp-servers/javflix-server/index.js"], "description": "JAVFLIX.TV专用MCP服务器 - 视频管理、API调用、数据库查询", "env": {"DATABASE_URL": "postgresql://longgedemacminim4:Yshe3O9RgmjKgitUJedpdCzj@localhost:5432/javflix", "NODE_ENV": "production"}}, "filesystem": {"command": "npx", "args": ["@modelcontextprotocol/server-filesystem", "/Users/<USER>/Desktop/JAVFLIX.TV"], "description": "访问JAVFLIX.TV项目文件系统"}, "postgres": {"command": "npx", "args": ["@modelcontextprotocol/server-postgres", "postgresql://longgedemacminim4:Yshe3O9RgmjKgitUJedpdCzj@localhost:5432/javflix"], "description": "连接到JAVFLIX.TV PostgreSQL数据库", "env": {"POSTGRES_CONNECTION_STRING": "postgresql://longgedemacminim4:Yshe3O9RgmjKgitUJedpdCzj@localhost:5432/javflix"}}, "desktop-commander": {"command": "npx", "args": ["-y", "@smithery/cli@latest", "run", "@wonderwhy-er/desktop-commander", "--key", "dac69fb0-23cc-43a1-8af5-1cb442d92264"]}, "mcp-shrimp-task-manager": {"command": "npx", "args": ["-y", "@smithery/cli@latest", "run", "@cjo4m06/mcp-shrimp-task-manager", "--key", "dac69fb0-23cc-43a1-8af5-1cb442d92264", "--profile", "unchanged-locust-0nXYEA"]}}, "globalShortcut": "CommandOrControl+Shift+."}