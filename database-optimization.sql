-- JAVFLIX.TV 数据库性能优化脚本
-- 基于专业API优化最佳实践

-- 🚀 创建核心索引以提升查询性能

-- 1. 主要查询索引
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_movies_title_gin 
ON movies USING gin(to_tsvector('simple', title));

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_movies_movie_id 
ON movies(movie_id);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_movies_view_count 
ON movies(view_count DESC NULLS LAST);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_movies_created_at 
ON movies(created_at DESC);

-- 2. 复合索引用于热门视频查询
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_movies_title_movie_id_not_null 
ON movies(title, movie_id) WHERE title IS NOT NULL AND movie_id IS NOT NULL;

-- 3. 搜索优化索引
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_movies_search_fields 
ON movies(title, movie_id, description) WHERE title IS NOT NULL;

-- 4. magnet表索引（用于热门度计算）
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_magnets_movie_id 
ON magnets(movie_id);

-- 5. 统计查询索引
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_movies_stats 
ON movies(view_count, created_at) WHERE title IS NOT NULL AND movie_id IS NOT NULL;

-- 🔥 创建物化视图以加速热门视频查询
DROP MATERIALIZED VIEW IF EXISTS mv_popular_videos;
CREATE MATERIALIZED VIEW mv_popular_videos AS
SELECT 
  m.id, 
  m.title, 
  m.image_url, 
  m.cached_image_url, 
  m.description, 
  m.duration, 
  m.view_count as views, 
  m.created_at, 
  m.movie_id,
  COALESCE(mc.magnet_count, 0) as magnet_count
FROM movies m
LEFT JOIN (
  SELECT movie_id, COUNT(*) as magnet_count 
  FROM magnets 
  GROUP BY movie_id
) mc ON mc.movie_id = m.id
WHERE m.title IS NOT NULL 
  AND m.movie_id IS NOT NULL
ORDER BY COALESCE(mc.magnet_count, 0) DESC, m.view_count DESC;

-- 为物化视图创建索引
CREATE UNIQUE INDEX ON mv_popular_videos(id);
CREATE INDEX ON mv_popular_videos(magnet_count DESC, views DESC);

-- 🚀 创建自动刷新物化视图的函数
CREATE OR REPLACE FUNCTION refresh_popular_videos_mv()
RETURNS void AS $$
BEGIN
  REFRESH MATERIALIZED VIEW CONCURRENTLY mv_popular_videos;
END;
$$ LANGUAGE plpgsql;

-- 📊 创建表统计信息更新脚本
CREATE OR REPLACE FUNCTION update_table_statistics()
RETURNS void AS $$
BEGIN
  ANALYZE movies;
  ANALYZE magnets;
  ANALYZE mv_popular_videos;
END;
$$ LANGUAGE plpgsql;

-- ⚡ 优化PostgreSQL配置建议（需要管理员权限）
/*
在postgresql.conf中添加或修改以下配置：

# 内存配置
shared_buffers = '256MB'          # 至少25%的可用RAM
effective_cache_size = '1GB'      # 75%的可用RAM
work_mem = '16MB'                 # 每个连接的工作内存
maintenance_work_mem = '128MB'    # 维护操作内存

# 查询优化
random_page_cost = 1.1            # SSD优化
effective_io_concurrency = 200    # SSD并发IO

# 连接和并发
max_connections = 200             # 最大连接数
max_worker_processes = 8          # 工作进程数
max_parallel_workers_per_gather = 4  # 并行查询工作进程

# 检查点和WAL
checkpoint_completion_target = 0.9
wal_buffers = '16MB'
*/

-- 🔍 性能监控查询
-- 查看慢查询
SELECT 
  query,
  mean_time,
  calls,
  total_time,
  mean_time/calls as avg_time_per_call
FROM pg_stat_statements 
WHERE mean_time > 100 -- 超过100ms的查询
ORDER BY mean_time DESC;

-- 查看表统计信息
SELECT 
  schemaname,
  tablename,
  n_tup_ins,
  n_tup_upd,
  n_tup_del,
  n_live_tup,
  n_dead_tup,
  last_vacuum,
  last_autovacuum,
  last_analyze,
  last_autoanalyze
FROM pg_stat_user_tables 
WHERE tablename IN ('movies', 'magnets');

-- 查看索引使用情况
SELECT 
  indexrelname,
  idx_tup_read,
  idx_tup_fetch,
  idx_scan
FROM pg_stat_user_indexes 
WHERE schemaname = 'public'
ORDER BY idx_scan DESC;

-- 🚀 定期维护脚本
-- 建议每天执行一次
DO $$
BEGIN
  -- 更新统计信息
  PERFORM update_table_statistics();
  
  -- 刷新物化视图（如果数据变化较多）
  PERFORM refresh_popular_videos_mv();
  
  -- 记录维护时间
  INSERT INTO maintenance_log (operation, executed_at) 
  VALUES ('daily_optimization', NOW())
  ON CONFLICT DO NOTHING;
END $$;

-- 创建维护日志表
CREATE TABLE IF NOT EXISTS maintenance_log (
  id SERIAL PRIMARY KEY,
  operation VARCHAR(255) NOT NULL,
  executed_at TIMESTAMP DEFAULT NOW()
);

-- 📈 性能基准测试查询
-- 测试热门视频查询性能
EXPLAIN (ANALYZE, BUFFERS) 
SELECT 
  m.id, m.title, m.image_url, m.cached_image_url, m.description, m.duration, 
  m.view_count as views, m.created_at, m.movie_id,
  COALESCE(mc.magnet_count, 0) as magnet_count
FROM movies m
LEFT JOIN (
  SELECT movie_id, COUNT(*) as magnet_count 
  FROM magnets 
  GROUP BY movie_id
) mc ON mc.movie_id = m.id
WHERE m.title IS NOT NULL 
  AND m.movie_id IS NOT NULL
ORDER BY COALESCE(mc.magnet_count, 0) DESC, m.view_count DESC
LIMIT 8;

-- 测试搜索查询性能
EXPLAIN (ANALYZE, BUFFERS) 
SELECT 
  m.id, m.title, m.image_url, m.cached_image_url, m.description, m.duration, 
  m.view_count as views, m.created_at, m.movie_id
FROM movies m
WHERE (
  m.title ILIKE '%test%' OR 
  m.movie_id ILIKE '%test%' OR
  m.description ILIKE '%test%'
)
AND m.title IS NOT NULL 
AND m.movie_id IS NOT NULL
ORDER BY 
  CASE 
    WHEN m.title ILIKE '%test%' THEN 1
    WHEN m.movie_id ILIKE '%test%' THEN 2
    WHEN m.description ILIKE '%test%' THEN 3
    ELSE 4
  END,
  COALESCE(m.view_count, 0) DESC,
  m.created_at DESC
LIMIT 20;