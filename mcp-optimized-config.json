{"mcpServers": {"interactive-feedback": {"command": "uv", "args": ["--directory", "/Users/<USER>/Desktop/JAVFLIX.TV/mcp-servers/interactive-feedback-mcp", "run", "server.py"], "timeout": 600, "autoApprove": ["interactive_feedback"], "description": "交互式反馈MCP服务器 - 允许AI暂停并请求用户澄清"}, "javflix-test-server": {"command": "node", "args": ["/Users/<USER>/Desktop/JAVFLIX.TV/mcp-servers/javflix-server/test-server.js"], "description": "JAVFLIX测试服务器 - 用于验证MCP连接"}, "javflix-server": {"command": "node", "args": ["/Users/<USER>/Desktop/JAVFLIX.TV/mcp-servers/javflix-server/index.js"], "description": "JAVFLIX.TV专用MCP服务器 - 视频管理、API调用、数据库查询", "env": {"DATABASE_URL": "postgresql://longgedemacminim4:Yshe3O9RgmjKgitUJedpdCzj@localhost:5432/javflix", "NODE_ENV": "production"}}, "filesystem": {"command": "npx", "args": ["@modelcontextprotocol/server-filesystem", "/Users/<USER>/Desktop/JAVFLIX.TV"], "description": "访问JAVFLIX.TV项目文件系统"}, "postgres": {"command": "npx", "args": ["@modelcontextprotocol/server-postgres", "postgresql://longgedemacminim4:Yshe3O9RgmjKgitUJedpdCzj@localhost:5432/javflix"], "description": "连接到JAVFLIX.TV PostgreSQL数据库", "env": {"POSTGRES_CONNECTION_STRING": "postgresql://longgedemacminim4:Yshe3O9RgmjKgitUJedpdCzj@localhost:5432/javflix"}}, "memory": {"command": "npx", "args": ["@modelcontextprotocol/server-memory"], "description": "AI助手的持久化记忆存储"}}, "globalShortcut": "CommandOrControl+Shift+."}