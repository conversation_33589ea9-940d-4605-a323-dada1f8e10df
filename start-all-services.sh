#!/bin/bash

echo "🚀 启动JAVFLIX.TV所有服务..."

# 创建必要的目录
mkdir -p /www/wwwroot/javflix.tv/javflix/public/images/javbus/{cover,actress,sample}
mkdir -p /tmp/javflix-processing

# 启动JavBus API服务器 (端口3000)
echo "📡 启动JavBus API服务器..."
cd /www/wwwroot/javflix.tv/javbus-api
npm run dev &
JAVBUS_PID=$!
echo "JavBus API PID: $JAVBUS_PID"

# 等待JavBus API启动
sleep 5

# 启动Node.js API服务器 (端口4000)
echo "🔧 启动Node.js API服务器..."
cd /www/wwwroot/javflix.tv/javflix-api
npm start &
API_PID=$!
echo "API服务器 PID: $API_PID"

# 等待API服务器启动
sleep 5

# 启动Go视频处理服务器 (端口8080)
echo "🎬 启动Go视频处理服务器..."
cd /www/wwwroot/javflix.tv/video-processor
./video-processor &
GO_PID=$!
echo "Go服务器 PID: $GO_PID"

# 等待Go服务器启动
sleep 3

# 检查服务状态
echo "🔍 检查服务状态..."
curl -s http://localhost:3000/api/health && echo "✅ JavBus API服务器运行正常" || echo "❌ JavBus API服务器未响应"
curl -s http://localhost:4000/api/health && echo "✅ Node.js API服务器运行正常" || echo "❌ Node.js API服务器未响应"
curl -s http://localhost:8080/health && echo "✅ Go视频处理服务器运行正常" || echo "❌ Go视频处理服务器未响应"

echo "🎉 所有服务启动完成！"
echo "📊 管理面板: http://localhost:8001"
echo "🔧 API服务器: http://localhost:4000"
echo "📡 JavBus API: http://localhost:3000"
echo "🎬 视频处理: http://localhost:8080"

# 保存PID到文件
echo "$JAVBUS_PID" > /tmp/javbus.pid
echo "$API_PID" > /tmp/api.pid
echo "$GO_PID" > /tmp/go.pid

echo "💡 使用 ./stop-all-services.sh 停止所有服务"
