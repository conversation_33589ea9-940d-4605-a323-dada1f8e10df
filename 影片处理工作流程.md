# JAVFLIX.TV 影片处理工作流程

## 🔄 完整工作流程

### 1. 影片采集阶段
```
JavBus采集 → 保存为draft状态 → 等待管理员处理
```

**技术实现:**
- 采集的影片自动保存为 `status = 'draft'`
- 包含基本信息：标题、封面、磁力链接等
- **用户前端不显示draft状态的影片**

### 2. 管理员选择处理
```
管理后台 → 影片处理管理 → 选择影片 → 配置处理参数 → 开始处理
```

**操作步骤:**
1. 登录管理后台
2. 进入"影片处理管理"页面
3. 查看待处理影片列表（draft状态）
4. 选择要处理的影片（支持批量选择）
5. 配置处理参数：
   - 优先级（1-10）
   - 水印设置
   - 视频质量（1080p/720p/480p）
6. 点击"开始处理"

### 3. 视频处理阶段
```
创建任务 → Go服务器处理 → 实时进度推送 → 处理完成
```

**处理流程:**
1. **状态变更**: `draft` → `processing`
2. **任务创建**: 在video_processing_tasks表创建任务记录
3. **发送到Go服务器**: 通过HTTP API发送处理任务
4. **Go服务器处理**:
   - 下载视频（磁力链接）
   - 添加水印
   - HLS切片（多分辨率）
   - 上传到CDN
5. **实时进度**: WebSocket推送进度到管理后台
6. **处理完成**: 状态变更为 `processed`

### 4. 影片发布阶段
```
处理完成 → 管理员确认 → 发布影片 → 用户可观看
```

**发布步骤:**
1. 处理完成后，影片状态为 `processed`
2. 管理员在后台查看处理结果
3. 确认视频质量和链接正常
4. 点击"发布影片"
5. 状态变更为 `published`
6. **用户前端开始显示该影片**

## 📊 状态管理

### 影片状态说明
| 状态 | 说明 | 用户可见 | 管理员操作 |
|------|------|----------|------------|
| `draft` | 刚采集的草稿 | ❌ | 可开始处理 |
| `processing` | 正在处理中 | ❌ | 可查看进度/取消 |
| `processed` | 处理完成 | ❌ | 可发布 |
| `published` | 已发布 | ✅ | 可下架 |
| `failed` | 处理失败 | ❌ | 可重新处理 |

### 数据库字段
```sql
-- movies表新增字段
status VARCHAR(20) DEFAULT 'draft'           -- 影片状态
video_urls JSONB DEFAULT '{}'               -- 处理后的视频链接
processing_priority INTEGER DEFAULT 5        -- 处理优先级
published_at TIMESTAMP NULL                 -- 发布时间
processing_started_at TIMESTAMP NULL        -- 开始处理时间
processing_completed_at TIMESTAMP NULL      -- 处理完成时间
```

## 🔧 API接口

### 管理员接口
```javascript
// 获取待处理影片
GET /api/movie-processing/pending?page=1&limit=20&status=draft

// 开始处理影片
POST /api/movie-processing/start
{
  "movieIds": [1, 2, 3],
  "config": {
    "priority": 8,
    "watermark": { "enabled": true, "text": "JAVFLIX.TV" },
    "slice": { "qualities": ["1080p", "720p"] }
  }
}

// 发布影片
POST /api/movie-processing/publish
{
  "movieIds": [1, 2, 3]
}

// 获取处理统计
GET /api/movie-processing/stats
```

### 用户前端接口
```javascript
// 获取已发布影片（替换原有的影片接口）
GET /api/published-videos?page=1&limit=20

// 获取影片详情（只返回已发布的）
GET /api/published-videos/:id

// 搜索已发布影片
GET /api/published-videos/search?q=关键词

// 热门影片
GET /api/published-videos/popular

// 最新影片
GET /api/published-videos/recent
```

## 🎯 前端实现

### 管理后台页面
- **影片处理管理**: `/admin/movie-processing`
- **实时进度监控**: WebSocket连接
- **批量操作**: 支持批量处理和发布
- **统计面板**: 各状态影片数量统计

### 用户前端调整
```javascript
// 原来的API调用
// GET /api/videos

// 现在改为
// GET /api/published-videos

// 确保只显示已发布的影片
```

## 📱 WebSocket实时通信

### 事件类型
```javascript
// 处理开始
socket.on('movie-processing-started', (data) => {
  // 更新界面显示处理开始
});

// 进度更新
socket.on('video-task-progress', (data) => {
  // 更新进度条
});

// 处理完成
socket.on('movie-processing-completed', (data) => {
  // 显示处理完成通知
});

// 处理失败
socket.on('movie-processing-failed', (data) => {
  // 显示错误信息
});

// 影片发布
socket.on('movie-published', (data) => {
  // 显示发布成功通知
});
```

## 🔒 安全考虑

### 权限控制
- **管理员权限**: 只有管理员可以处理和发布影片
- **用户权限**: 用户只能看到已发布的影片
- **API限流**: 防止恶意请求

### 数据验证
- **状态验证**: 严格的状态转换验证
- **参数验证**: 处理配置参数验证
- **文件验证**: 上传文件安全检查

## 📈 监控和统计

### 处理统计
- 各状态影片数量
- 处理成功率
- 平均处理时间
- 队列长度

### 用户统计
- 观看次数
- 热门影片排行
- 搜索关键词统计

## 🚀 部署步骤

### 1. 数据库迁移
```bash
psql -d javflix -f database-movie-status-migration.sql
```

### 2. 后端部署
```bash
# 安装新依赖
npm install uuid ws socket.io-client

# 添加新路由到app.js
app.use('/api/movie-processing', movieProcessingRoutes);
app.use('/api/published-videos', publishedVideoRoutes);

# 重启服务
pm2 restart javflix-api
```

### 3. 前端部署
```bash
# 管理后台添加新页面
# 用户前端修改API调用
```

### 4. Go服务器部署
```bash
# 按照双服务器架构方案部署Go服务器
```

## ✅ 验证清单

### 功能验证
- [ ] 采集的影片保存为draft状态
- [ ] 管理员可以看到待处理影片列表
- [ ] 可以成功开始处理影片
- [ ] 实时进度推送正常工作
- [ ] 处理完成后状态正确更新
- [ ] 可以成功发布影片
- [ ] 用户前端只显示已发布影片
- [ ] 搜索功能正常工作

### 性能验证
- [ ] 大量影片列表加载速度
- [ ] 批量处理性能
- [ ] WebSocket连接稳定性
- [ ] 数据库查询性能

### 安全验证
- [ ] 权限控制正确
- [ ] API限流生效
- [ ] 数据验证完整
- [ ] 状态转换安全

---

**🎬 通过这个工作流程，确保用户只能看到真正可播放的高质量影片！**
