#!/bin/bash

# 磁力链接下载进度修复部署脚本
# 使用方法: ./deploy-download-progress-fix.sh

set -e

echo "🚀 开始部署磁力链接下载进度修复..."

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查必要的工具
check_requirements() {
    log_info "检查系统要求..."
    
    if ! command -v psql &> /dev/null; then
        log_error "PostgreSQL客户端未安装"
        exit 1
    fi
    
    if ! command -v go &> /dev/null; then
        log_error "Go未安装"
        exit 1
    fi
    
    if ! command -v node &> /dev/null; then
        log_error "Node.js未安装"
        exit 1
    fi
    
    if ! command -v npm &> /dev/null; then
        log_error "npm未安装"
        exit 1
    fi
    
    log_success "系统要求检查通过"
}

# 数据库迁移
migrate_database() {
    log_info "执行数据库迁移..."
    
    # 检查数据库连接配置
    if [ -z "$DB_HOST" ]; then
        DB_HOST="localhost"
    fi
    
    if [ -z "$DB_PORT" ]; then
        DB_PORT="5432"
    fi
    
    if [ -z "$DB_NAME" ]; then
        DB_NAME="javflix"
    fi
    
    if [ -z "$DB_USER" ]; then
        DB_USER="postgres"
    fi
    
    log_info "数据库配置: $DB_USER@$DB_HOST:$DB_PORT/$DB_NAME"
    
    # 执行迁移脚本
    if [ -f "video-processor/migrations/add_download_progress_fields.sql" ]; then
        log_info "执行数据库迁移脚本..."
        PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -f video-processor/migrations/add_download_progress_fields.sql
        log_success "数据库迁移完成"
    else
        log_error "迁移脚本不存在: video-processor/migrations/add_download_progress_fields.sql"
        exit 1
    fi
}

# 构建Go服务器
build_go_server() {
    log_info "构建Go视频处理服务器..."
    
    cd video-processor
    
    # 下载依赖
    log_info "下载Go依赖..."
    go mod tidy
    
    # 构建
    log_info "构建Go应用..."
    go build -o video-processor ./cmd/main.go
    
    if [ $? -eq 0 ]; then
        log_success "Go服务器构建成功"
    else
        log_error "Go服务器构建失败"
        exit 1
    fi
    
    cd ..
}

# 重启Go服务器
restart_go_server() {
    log_info "重启Go视频处理服务器..."
    
    # 查找并停止现有进程
    if pgrep -f "video-processor" > /dev/null; then
        log_info "停止现有Go服务器进程..."
        pkill -f "video-processor"
        sleep 2
    fi
    
    # 启动新进程
    cd video-processor
    log_info "启动Go服务器..."
    nohup ./video-processor > video-processor.log 2>&1 &
    
    # 等待服务器启动
    sleep 5
    
    if pgrep -f "video-processor" > /dev/null; then
        log_success "Go服务器启动成功"
    else
        log_error "Go服务器启动失败，请检查日志: video-processor/video-processor.log"
        exit 1
    fi
    
    cd ..
}

# 重启Node.js API服务器
restart_nodejs_api() {
    log_info "重启Node.js API服务器..."
    
    cd javflix-api
    
    # 安装依赖（如果需要）
    if [ ! -d "node_modules" ]; then
        log_info "安装Node.js依赖..."
        npm install
    fi
    
    # 查找并停止现有进程
    if pgrep -f "javflix-api" > /dev/null; then
        log_info "停止现有Node.js API进程..."
        pkill -f "javflix-api"
        sleep 2
    fi
    
    # 启动新进程
    log_info "启动Node.js API服务器..."
    nohup npm start > api.log 2>&1 &
    
    # 等待服务器启动
    sleep 5
    
    if pgrep -f "javflix-api" > /dev/null; then
        log_success "Node.js API服务器启动成功"
    else
        log_error "Node.js API服务器启动失败，请检查日志: javflix-api/api.log"
        exit 1
    fi
    
    cd ..
}

# 重启前端管理面板
restart_admin_panel() {
    log_info "重启前端管理面板..."
    
    cd admin-panel
    
    # 安装依赖（如果需要）
    if [ ! -d "node_modules" ]; then
        log_info "安装前端依赖..."
        npm install
    fi
    
    # 查找并停止现有进程
    if pgrep -f "admin-panel" > /dev/null; then
        log_info "停止现有前端进程..."
        pkill -f "admin-panel"
        sleep 2
    fi
    
    # 启动新进程
    log_info "启动前端管理面板..."
    nohup npm run serve > admin-panel.log 2>&1 &
    
    # 等待服务器启动
    sleep 10
    
    if pgrep -f "admin-panel" > /dev/null; then
        log_success "前端管理面板启动成功"
    else
        log_error "前端管理面板启动失败，请检查日志: admin-panel/admin-panel.log"
        exit 1
    fi
    
    cd ..
}

# 验证部署
verify_deployment() {
    log_info "验证部署状态..."
    
    # 检查Go服务器
    if curl -s http://localhost:8080/health > /dev/null 2>&1; then
        log_success "Go服务器运行正常"
    else
        log_warning "Go服务器健康检查失败"
    fi
    
    # 检查Node.js API
    if curl -s http://localhost:3001/api/health > /dev/null 2>&1; then
        log_success "Node.js API运行正常"
    else
        log_warning "Node.js API健康检查失败"
    fi
    
    # 检查前端
    if curl -s http://localhost:3000 > /dev/null 2>&1; then
        log_success "前端管理面板运行正常"
    else
        log_warning "前端管理面板健康检查失败"
    fi
}

# 显示部署结果
show_deployment_info() {
    echo ""
    log_success "🎉 磁力链接下载进度修复部署完成！"
    echo ""
    echo "服务状态:"
    echo "  - Go视频处理服务器: http://localhost:8080"
    echo "  - Node.js API服务器: http://localhost:3001"
    echo "  - 前端管理面板: http://localhost:3000"
    echo ""
    echo "测试步骤:"
    echo "  1. 访问 http://localhost:3000/video-processing"
    echo "  2. 检查WebSocket连接状态"
    echo "  3. 启动一个视频处理任务"
    echo "  4. 观察下载进度实时更新"
    echo ""
    echo "日志文件:"
    echo "  - Go服务器: video-processor/video-processor.log"
    echo "  - Node.js API: javflix-api/api.log"
    echo "  - 前端面板: admin-panel/admin-panel.log"
    echo ""
    log_info "详细测试指南请参考: test-download-progress.md"
}

# 主函数
main() {
    echo "🔧 磁力链接下载进度修复部署脚本"
    echo "=================================="
    
    check_requirements
    migrate_database
    build_go_server
    restart_go_server
    restart_nodejs_api
    restart_admin_panel
    verify_deployment
    show_deployment_info
}

# 执行主函数
main "$@"
