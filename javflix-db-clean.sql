--
-- PostgreSQL database dump
--

-- Dumped from database version 14.18 (Homebrew)
-- Dumped by pg_dump version 14.18 (Homebrew)

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

SET default_tablespace = '';

SET default_table_access_method = heap;

--
-- Name: categories; Type: TABLE; Schema: public; Owner: longgedemacminim4
--

CREATE TABLE public.categories (
    id integer NOT NULL,
    name text NOT NULL,
    slug text NOT NULL,
    image_url text,
    count integer DEFAULT 0,
    description text,
    color text,
    icon text,
    is_featured boolean DEFAULT false,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.categories OWNER TO longgedemacminim4;

--
-- Name: categories_id_seq; Type: SEQUENCE; Schema: public; Owner: longgedemacminim4
--

CREATE SEQUENCE public.categories_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.categories_id_seq OWNER TO longgedemacminim4;

--
-- Name: categories_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: longgedemacminim4
--

ALTER SEQUENCE public.categories_id_seq OWNED BY public.categories.id;


--
-- Name: directors; Type: TABLE; Schema: public; Owner: longgedemacminim4
--

CREATE TABLE public.directors (
    id integer NOT NULL,
    director_id character varying(50) NOT NULL,
    name character varying(100) NOT NULL,
    created_at timestamp without time zone NOT NULL,
    updated_at timestamp without time zone NOT NULL
);


ALTER TABLE public.directors OWNER TO longgedemacminim4;

--
-- Name: directors_id_seq; Type: SEQUENCE; Schema: public; Owner: longgedemacminim4
--

CREATE SEQUENCE public.directors_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.directors_id_seq OWNER TO longgedemacminim4;

--
-- Name: directors_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: longgedemacminim4
--

ALTER SEQUENCE public.directors_id_seq OWNED BY public.directors.id;


--
-- Name: genres; Type: TABLE; Schema: public; Owner: longgedemacminim4
--

CREATE TABLE public.genres (
    id integer NOT NULL,
    name character varying(100) NOT NULL,
    created_at timestamp without time zone NOT NULL,
    updated_at timestamp without time zone NOT NULL
);


ALTER TABLE public.genres OWNER TO longgedemacminim4;

--
-- Name: genres_id_seq; Type: SEQUENCE; Schema: public; Owner: longgedemacminim4
--

CREATE SEQUENCE public.genres_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.genres_id_seq OWNER TO longgedemacminim4;

--
-- Name: genres_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: longgedemacminim4
--

ALTER SEQUENCE public.genres_id_seq OWNED BY public.genres.id;


--
-- Name: magnets; Type: TABLE; Schema: public; Owner: longgedemacminim4
--

CREATE TABLE public.magnets (
    id integer NOT NULL,
    movie_id integer,
    magnet_id character varying(100),
    link text NOT NULL,
    title text,
    size character varying(50),
    is_hd boolean DEFAULT false,
    has_subtitle boolean DEFAULT false,
    share_date character varying(50),
    created_at timestamp without time zone NOT NULL,
    updated_at timestamp without time zone NOT NULL
);


ALTER TABLE public.magnets OWNER TO longgedemacminim4;

--
-- Name: magnets_id_seq; Type: SEQUENCE; Schema: public; Owner: longgedemacminim4
--

CREATE SEQUENCE public.magnets_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.magnets_id_seq OWNER TO longgedemacminim4;

--
-- Name: magnets_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: longgedemacminim4
--

ALTER SEQUENCE public.magnets_id_seq OWNED BY public.magnets.id;


--
-- Name: movie_directors; Type: TABLE; Schema: public; Owner: longgedemacminim4
--

CREATE TABLE public.movie_directors (
    movie_id integer NOT NULL,
    director_id integer NOT NULL
);


ALTER TABLE public.movie_directors OWNER TO longgedemacminim4;

--
-- Name: movie_genres; Type: TABLE; Schema: public; Owner: longgedemacminim4
--

CREATE TABLE public.movie_genres (
    movie_id integer NOT NULL,
    genre_id integer NOT NULL
);


ALTER TABLE public.movie_genres OWNER TO longgedemacminim4;

--
-- Name: movie_stars; Type: TABLE; Schema: public; Owner: longgedemacminim4
--

CREATE TABLE public.movie_stars (
    movie_id integer NOT NULL,
    star_id integer NOT NULL
);


ALTER TABLE public.movie_stars OWNER TO longgedemacminim4;

--
-- Name: movies; Type: TABLE; Schema: public; Owner: longgedemacminim4
--

CREATE TABLE public.movies (
    id integer NOT NULL,
    movie_id character varying(50) NOT NULL,
    title text NOT NULL,
    image_url text,
    cover_image text,
    release_date character varying(50),
    duration character varying(50),
    description text,
    director_id integer,
    producer_id integer,
    publisher_id integer,
    series_id integer,
    created_at timestamp without time zone NOT NULL,
    updated_at timestamp without time zone NOT NULL,
    cached_image_url text
);


ALTER TABLE public.movies OWNER TO longgedemacminim4;

--
-- Name: movies_id_seq; Type: SEQUENCE; Schema: public; Owner: longgedemacminim4
--

CREATE SEQUENCE public.movies_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.movies_id_seq OWNER TO longgedemacminim4;

--
-- Name: movies_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: longgedemacminim4
--

ALTER SEQUENCE public.movies_id_seq OWNED BY public.movies.id;


--
-- Name: producers; Type: TABLE; Schema: public; Owner: longgedemacminim4
--

CREATE TABLE public.producers (
    id integer NOT NULL,
    producer_id character varying(50) NOT NULL,
    name character varying(100) NOT NULL,
    created_at timestamp without time zone NOT NULL,
    updated_at timestamp without time zone NOT NULL
);


ALTER TABLE public.producers OWNER TO longgedemacminim4;

--
-- Name: producers_id_seq; Type: SEQUENCE; Schema: public; Owner: longgedemacminim4
--

CREATE SEQUENCE public.producers_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.producers_id_seq OWNER TO longgedemacminim4;

--
-- Name: producers_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: longgedemacminim4
--

ALTER SEQUENCE public.producers_id_seq OWNED BY public.producers.id;


--
-- Name: publishers; Type: TABLE; Schema: public; Owner: longgedemacminim4
--

CREATE TABLE public.publishers (
    id integer NOT NULL,
    publisher_id character varying(50) NOT NULL,
    name character varying(100) NOT NULL,
    created_at timestamp without time zone NOT NULL,
    updated_at timestamp without time zone NOT NULL
);


ALTER TABLE public.publishers OWNER TO longgedemacminim4;

--
-- Name: publishers_id_seq; Type: SEQUENCE; Schema: public; Owner: longgedemacminim4
--

CREATE SEQUENCE public.publishers_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.publishers_id_seq OWNER TO longgedemacminim4;

--
-- Name: publishers_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: longgedemacminim4
--

ALTER SEQUENCE public.publishers_id_seq OWNED BY public.publishers.id;


--
-- Name: samples; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.samples (
    id integer NOT NULL,
    movie_id integer,
    sample_id character varying(50),
    image_url text,
    thumbnail_url text,
    cached_image_url text,
    cached_thumbnail_url text,
    alt text,
    created_at timestamp without time zone NOT NULL,
    updated_at timestamp without time zone NOT NULL
);


ALTER TABLE public.samples OWNER TO postgres;

--
-- Name: samples_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.samples_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.samples_id_seq OWNER TO postgres;

--
-- Name: samples_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.samples_id_seq OWNED BY public.samples.id;


--
-- Name: series; Type: TABLE; Schema: public; Owner: longgedemacminim4
--

CREATE TABLE public.series (
    id integer NOT NULL,
    series_id character varying(50) NOT NULL,
    name character varying(200) NOT NULL,
    created_at timestamp without time zone NOT NULL,
    updated_at timestamp without time zone NOT NULL
);


ALTER TABLE public.series OWNER TO longgedemacminim4;

--
-- Name: series_id_seq; Type: SEQUENCE; Schema: public; Owner: longgedemacminim4
--

CREATE SEQUENCE public.series_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.series_id_seq OWNER TO longgedemacminim4;

--
-- Name: series_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: longgedemacminim4
--

ALTER SEQUENCE public.series_id_seq OWNED BY public.series.id;


--
-- Name: similar_movies; Type: TABLE; Schema: public; Owner: longgedemacminim4
--

CREATE TABLE public.similar_movies (
    id integer NOT NULL,
    movie_id integer,
    similar_movie_id character varying(50) NOT NULL,
    similar_title text,
    similar_image text,
    created_at timestamp without time zone NOT NULL,
    updated_at timestamp without time zone NOT NULL
);


ALTER TABLE public.similar_movies OWNER TO longgedemacminim4;

--
-- Name: similar_movies_id_seq; Type: SEQUENCE; Schema: public; Owner: longgedemacminim4
--

CREATE SEQUENCE public.similar_movies_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.similar_movies_id_seq OWNER TO longgedemacminim4;

--
-- Name: similar_movies_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: longgedemacminim4
--

ALTER SEQUENCE public.similar_movies_id_seq OWNED BY public.similar_movies.id;


--
-- Name: stars; Type: TABLE; Schema: public; Owner: longgedemacminim4
--

CREATE TABLE public.stars (
    id integer NOT NULL,
    star_id character varying(50) NOT NULL,
    name character varying(100) NOT NULL,
    image_url text,
    created_at timestamp without time zone NOT NULL,
    updated_at timestamp without time zone NOT NULL,
    cached_image_url text
);


ALTER TABLE public.stars OWNER TO longgedemacminim4;

--
-- Name: stars_id_seq; Type: SEQUENCE; Schema: public; Owner: longgedemacminim4
--

CREATE SEQUENCE public.stars_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.stars_id_seq OWNER TO longgedemacminim4;

--
-- Name: stars_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: longgedemacminim4
--

ALTER SEQUENCE public.stars_id_seq OWNED BY public.stars.id;


--
-- Name: users; Type: TABLE; Schema: public; Owner: longgedemacminim4
--

CREATE TABLE public.users (
    id integer NOT NULL,
    username character varying(50) NOT NULL,
    email character varying(100) NOT NULL,
    password character varying(255) NOT NULL,
    is_admin boolean DEFAULT false,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    "lastLoginAt" timestamp without time zone,
    updated_at timestamp without time zone,
    "updatedAt" timestamp without time zone,
    "createdAt" timestamp without time zone,
    is_active boolean DEFAULT true
);


ALTER TABLE public.users OWNER TO longgedemacminim4;

--
-- Name: users_id_seq; Type: SEQUENCE; Schema: public; Owner: longgedemacminim4
--

CREATE SEQUENCE public.users_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.users_id_seq OWNER TO longgedemacminim4;

--
-- Name: users_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: longgedemacminim4
--

ALTER SEQUENCE public.users_id_seq OWNED BY public.users.id;


--
-- Name: video_categories; Type: TABLE; Schema: public; Owner: longgedemacminim4
--

CREATE TABLE public.video_categories (
    video_id integer NOT NULL,
    category_id integer NOT NULL
);


ALTER TABLE public.video_categories OWNER TO longgedemacminim4;

--
-- Name: categories id; Type: DEFAULT; Schema: public; Owner: longgedemacminim4
--

ALTER TABLE ONLY public.categories ALTER COLUMN id SET DEFAULT nextval('public.categories_id_seq'::regclass);


--
-- Name: directors id; Type: DEFAULT; Schema: public; Owner: longgedemacminim4
--

ALTER TABLE ONLY public.directors ALTER COLUMN id SET DEFAULT nextval('public.directors_id_seq'::regclass);


--
-- Name: genres id; Type: DEFAULT; Schema: public; Owner: longgedemacminim4
--

ALTER TABLE ONLY public.genres ALTER COLUMN id SET DEFAULT nextval('public.genres_id_seq'::regclass);


--
-- Name: magnets id; Type: DEFAULT; Schema: public; Owner: longgedemacminim4
--

ALTER TABLE ONLY public.magnets ALTER COLUMN id SET DEFAULT nextval('public.magnets_id_seq'::regclass);


--
-- Name: movies id; Type: DEFAULT; Schema: public; Owner: longgedemacminim4
--

ALTER TABLE ONLY public.movies ALTER COLUMN id SET DEFAULT nextval('public.movies_id_seq'::regclass);


--
-- Name: producers id; Type: DEFAULT; Schema: public; Owner: longgedemacminim4
--

ALTER TABLE ONLY public.producers ALTER COLUMN id SET DEFAULT nextval('public.producers_id_seq'::regclass);


--
-- Name: publishers id; Type: DEFAULT; Schema: public; Owner: longgedemacminim4
--

ALTER TABLE ONLY public.publishers ALTER COLUMN id SET DEFAULT nextval('public.publishers_id_seq'::regclass);


--
-- Name: samples id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.samples ALTER COLUMN id SET DEFAULT nextval('public.samples_id_seq'::regclass);


--
-- Name: series id; Type: DEFAULT; Schema: public; Owner: longgedemacminim4
--

ALTER TABLE ONLY public.series ALTER COLUMN id SET DEFAULT nextval('public.series_id_seq'::regclass);


--
-- Name: similar_movies id; Type: DEFAULT; Schema: public; Owner: longgedemacminim4
--

ALTER TABLE ONLY public.similar_movies ALTER COLUMN id SET DEFAULT nextval('public.similar_movies_id_seq'::regclass);


--
-- Name: stars id; Type: DEFAULT; Schema: public; Owner: longgedemacminim4
--

ALTER TABLE ONLY public.stars ALTER COLUMN id SET DEFAULT nextval('public.stars_id_seq'::regclass);


--
-- Name: users id; Type: DEFAULT; Schema: public; Owner: longgedemacminim4
--

ALTER TABLE ONLY public.users ALTER COLUMN id SET DEFAULT nextval('public.users_id_seq'::regclass);


--
-- Data for Name: categories; Type: TABLE DATA; Schema: public; Owner: longgedemacminim4
--

COPY public.categories (id, name, slug, image_url, count, description, color, icon, is_featured, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: directors; Type: TABLE DATA; Schema: public; Owner: longgedemacminim4
--

COPY public.directors (id, director_id, name, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: genres; Type: TABLE DATA; Schema: public; Owner: longgedemacminim4
--

COPY public.genres (id, name, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: magnets; Type: TABLE DATA; Schema: public; Owner: longgedemacminim4
--

COPY public.magnets (id, movie_id, magnet_id, link, title, size, is_hd, has_subtitle, share_date, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: movie_directors; Type: TABLE DATA; Schema: public; Owner: longgedemacminim4
--

COPY public.movie_directors (movie_id, director_id) FROM stdin;
\.


--
-- Data for Name: movie_genres; Type: TABLE DATA; Schema: public; Owner: longgedemacminim4
--

COPY public.movie_genres (movie_id, genre_id) FROM stdin;
\.


--
-- Data for Name: movie_stars; Type: TABLE DATA; Schema: public; Owner: longgedemacminim4
--

COPY public.movie_stars (movie_id, star_id) FROM stdin;
\.


--
-- Data for Name: movies; Type: TABLE DATA; Schema: public; Owner: longgedemacminim4
--

COPY public.movies (id, movie_id, title, image_url, cover_image, release_date, duration, description, director_id, producer_id, publisher_id, series_id, created_at, updated_at, cached_image_url) FROM stdin;
\.


--
-- Data for Name: producers; Type: TABLE DATA; Schema: public; Owner: longgedemacminim4
--

COPY public.producers (id, producer_id, name, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: publishers; Type: TABLE DATA; Schema: public; Owner: longgedemacminim4
--

COPY public.publishers (id, publisher_id, name, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: samples; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.samples (id, movie_id, sample_id, image_url, thumbnail_url, cached_image_url, cached_thumbnail_url, alt, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: series; Type: TABLE DATA; Schema: public; Owner: longgedemacminim4
--

COPY public.series (id, series_id, name, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: similar_movies; Type: TABLE DATA; Schema: public; Owner: longgedemacminim4
--

COPY public.similar_movies (id, movie_id, similar_movie_id, similar_title, similar_image, created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: stars; Type: TABLE DATA; Schema: public; Owner: longgedemacminim4
--

COPY public.stars (id, star_id, name, image_url, created_at, updated_at, cached_image_url) FROM stdin;
\.


--
-- Data for Name: users; Type: TABLE DATA; Schema: public; Owner: longgedemacminim4
--

COPY public.users (id, username, email, password, is_admin, created_at, "lastLoginAt", updated_at, "updatedAt", "createdAt", is_active) FROM stdin;
2	admin	<EMAIL>	$2b$10$EztSPiVHZ3Me3xh7x4aRievZHLqL8YY8uqpHCVgneDJ60u8h0RL42	t	2025-05-19 20:20:24.801167	2025-05-19 20:20:49.372	2025-05-19 20:20:44.538624	2025-05-19 20:20:44.538624	2025-05-19 20:20:44.538624	t
\.


--
-- Data for Name: video_categories; Type: TABLE DATA; Schema: public; Owner: longgedemacminim4
--

COPY public.video_categories (video_id, category_id) FROM stdin;
1	77
2	77
3	77
4	77
5	77
6	77
7	77
8	77
9	77
10	77
11	77
12	77
13	77
14	77
15	77
16	77
17	77
18	77
19	77
20	77
1	83
2	83
3	83
29	83
31	83
32	83
33	83
34	83
40	83
41	83
42	83
43	83
44	83
45	83
4	83
5	83
6	83
\.


--
-- Name: categories_id_seq; Type: SEQUENCE SET; Schema: public; Owner: longgedemacminim4
--

SELECT pg_catalog.setval('public.categories_id_seq', 83, true);


--
-- Name: directors_id_seq; Type: SEQUENCE SET; Schema: public; Owner: longgedemacminim4
--

SELECT pg_catalog.setval('public.directors_id_seq', 119, true);


--
-- Name: genres_id_seq; Type: SEQUENCE SET; Schema: public; Owner: longgedemacminim4
--

SELECT pg_catalog.setval('public.genres_id_seq', 1741, true);


--
-- Name: magnets_id_seq; Type: SEQUENCE SET; Schema: public; Owner: longgedemacminim4
--

SELECT pg_catalog.setval('public.magnets_id_seq', 32, true);


--
-- Name: movies_id_seq; Type: SEQUENCE SET; Schema: public; Owner: longgedemacminim4
--

SELECT pg_catalog.setval('public.movies_id_seq', 10, true);


--
-- Name: producers_id_seq; Type: SEQUENCE SET; Schema: public; Owner: longgedemacminim4
--

SELECT pg_catalog.setval('public.producers_id_seq', 214, true);


--
-- Name: publishers_id_seq; Type: SEQUENCE SET; Schema: public; Owner: longgedemacminim4
--

SELECT pg_catalog.setval('public.publishers_id_seq', 189, true);


--
-- Name: samples_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.samples_id_seq', 136, true);


--
-- Name: series_id_seq; Type: SEQUENCE SET; Schema: public; Owner: longgedemacminim4
--

SELECT pg_catalog.setval('public.series_id_seq', 130, true);


--
-- Name: similar_movies_id_seq; Type: SEQUENCE SET; Schema: public; Owner: longgedemacminim4
--

SELECT pg_catalog.setval('public.similar_movies_id_seq', 1272, true);


--
-- Name: stars_id_seq; Type: SEQUENCE SET; Schema: public; Owner: longgedemacminim4
--

SELECT pg_catalog.setval('public.stars_id_seq', 228, true);


--
-- Name: users_id_seq; Type: SEQUENCE SET; Schema: public; Owner: longgedemacminim4
--

SELECT pg_catalog.setval('public.users_id_seq', 2, true);


--
-- Name: categories categories_pkey; Type: CONSTRAINT; Schema: public; Owner: longgedemacminim4
--

ALTER TABLE ONLY public.categories
    ADD CONSTRAINT categories_pkey PRIMARY KEY (id);


--
-- Name: categories categories_slug_key; Type: CONSTRAINT; Schema: public; Owner: longgedemacminim4
--

ALTER TABLE ONLY public.categories
    ADD CONSTRAINT categories_slug_key UNIQUE (slug);


--
-- Name: directors directors_director_id_key; Type: CONSTRAINT; Schema: public; Owner: longgedemacminim4
--

ALTER TABLE ONLY public.directors
    ADD CONSTRAINT directors_director_id_key UNIQUE (director_id);


--
-- Name: directors directors_pkey; Type: CONSTRAINT; Schema: public; Owner: longgedemacminim4
--

ALTER TABLE ONLY public.directors
    ADD CONSTRAINT directors_pkey PRIMARY KEY (id);


--
-- Name: genres genres_name_key; Type: CONSTRAINT; Schema: public; Owner: longgedemacminim4
--

ALTER TABLE ONLY public.genres
    ADD CONSTRAINT genres_name_key UNIQUE (name);


--
-- Name: genres genres_pkey; Type: CONSTRAINT; Schema: public; Owner: longgedemacminim4
--

ALTER TABLE ONLY public.genres
    ADD CONSTRAINT genres_pkey PRIMARY KEY (id);


--
-- Name: magnets magnets_pkey; Type: CONSTRAINT; Schema: public; Owner: longgedemacminim4
--

ALTER TABLE ONLY public.magnets
    ADD CONSTRAINT magnets_pkey PRIMARY KEY (id);


--
-- Name: movie_directors movie_directors_pkey; Type: CONSTRAINT; Schema: public; Owner: longgedemacminim4
--

ALTER TABLE ONLY public.movie_directors
    ADD CONSTRAINT movie_directors_pkey PRIMARY KEY (movie_id, director_id);


--
-- Name: movie_genres movie_genres_pkey; Type: CONSTRAINT; Schema: public; Owner: longgedemacminim4
--

ALTER TABLE ONLY public.movie_genres
    ADD CONSTRAINT movie_genres_pkey PRIMARY KEY (movie_id, genre_id);


--
-- Name: movie_stars movie_stars_pkey; Type: CONSTRAINT; Schema: public; Owner: longgedemacminim4
--

ALTER TABLE ONLY public.movie_stars
    ADD CONSTRAINT movie_stars_pkey PRIMARY KEY (movie_id, star_id);


--
-- Name: movies movies_movie_id_key; Type: CONSTRAINT; Schema: public; Owner: longgedemacminim4
--

ALTER TABLE ONLY public.movies
    ADD CONSTRAINT movies_movie_id_key UNIQUE (movie_id);


--
-- Name: movies movies_pkey; Type: CONSTRAINT; Schema: public; Owner: longgedemacminim4
--

ALTER TABLE ONLY public.movies
    ADD CONSTRAINT movies_pkey PRIMARY KEY (id);


--
-- Name: producers producers_pkey; Type: CONSTRAINT; Schema: public; Owner: longgedemacminim4
--

ALTER TABLE ONLY public.producers
    ADD CONSTRAINT producers_pkey PRIMARY KEY (id);


--
-- Name: producers producers_producer_id_key; Type: CONSTRAINT; Schema: public; Owner: longgedemacminim4
--

ALTER TABLE ONLY public.producers
    ADD CONSTRAINT producers_producer_id_key UNIQUE (producer_id);


--
-- Name: publishers publishers_pkey; Type: CONSTRAINT; Schema: public; Owner: longgedemacminim4
--

ALTER TABLE ONLY public.publishers
    ADD CONSTRAINT publishers_pkey PRIMARY KEY (id);


--
-- Name: publishers publishers_publisher_id_key; Type: CONSTRAINT; Schema: public; Owner: longgedemacminim4
--

ALTER TABLE ONLY public.publishers
    ADD CONSTRAINT publishers_publisher_id_key UNIQUE (publisher_id);


--
-- Name: samples samples_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.samples
    ADD CONSTRAINT samples_pkey PRIMARY KEY (id);


--
-- Name: series series_pkey; Type: CONSTRAINT; Schema: public; Owner: longgedemacminim4
--

ALTER TABLE ONLY public.series
    ADD CONSTRAINT series_pkey PRIMARY KEY (id);


--
-- Name: series series_series_id_key; Type: CONSTRAINT; Schema: public; Owner: longgedemacminim4
--

ALTER TABLE ONLY public.series
    ADD CONSTRAINT series_series_id_key UNIQUE (series_id);


--
-- Name: similar_movies similar_movies_pkey; Type: CONSTRAINT; Schema: public; Owner: longgedemacminim4
--

ALTER TABLE ONLY public.similar_movies
    ADD CONSTRAINT similar_movies_pkey PRIMARY KEY (id);


--
-- Name: stars stars_pkey; Type: CONSTRAINT; Schema: public; Owner: longgedemacminim4
--

ALTER TABLE ONLY public.stars
    ADD CONSTRAINT stars_pkey PRIMARY KEY (id);


--
-- Name: stars stars_star_id_key; Type: CONSTRAINT; Schema: public; Owner: longgedemacminim4
--

ALTER TABLE ONLY public.stars
    ADD CONSTRAINT stars_star_id_key UNIQUE (star_id);


--
-- Name: users users_email_key; Type: CONSTRAINT; Schema: public; Owner: longgedemacminim4
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_email_key UNIQUE (email);


--
-- Name: users users_pkey; Type: CONSTRAINT; Schema: public; Owner: longgedemacminim4
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_pkey PRIMARY KEY (id);


--
-- Name: users users_username_key; Type: CONSTRAINT; Schema: public; Owner: longgedemacminim4
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_username_key UNIQUE (username);


--
-- Name: video_categories video_categories_pkey; Type: CONSTRAINT; Schema: public; Owner: longgedemacminim4
--

ALTER TABLE ONLY public.video_categories
    ADD CONSTRAINT video_categories_pkey PRIMARY KEY (video_id, category_id);


--
-- Name: idx_movies_cached_image_url; Type: INDEX; Schema: public; Owner: longgedemacminim4
--

CREATE INDEX idx_movies_cached_image_url ON public.movies USING btree (cached_image_url);


--
-- Name: idx_stars_cached_image_url; Type: INDEX; Schema: public; Owner: longgedemacminim4
--

CREATE INDEX idx_stars_cached_image_url ON public.stars USING btree (cached_image_url);


--
-- Name: magnets magnets_movie_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: longgedemacminim4
--

ALTER TABLE ONLY public.magnets
    ADD CONSTRAINT magnets_movie_id_fkey FOREIGN KEY (movie_id) REFERENCES public.movies(id) ON DELETE CASCADE;


--
-- Name: movie_directors movie_directors_director_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: longgedemacminim4
--

ALTER TABLE ONLY public.movie_directors
    ADD CONSTRAINT movie_directors_director_id_fkey FOREIGN KEY (director_id) REFERENCES public.directors(id) ON DELETE CASCADE;


--
-- Name: movie_directors movie_directors_movie_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: longgedemacminim4
--

ALTER TABLE ONLY public.movie_directors
    ADD CONSTRAINT movie_directors_movie_id_fkey FOREIGN KEY (movie_id) REFERENCES public.movies(id) ON DELETE CASCADE;


--
-- Name: movie_genres movie_genres_genre_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: longgedemacminim4
--

ALTER TABLE ONLY public.movie_genres
    ADD CONSTRAINT movie_genres_genre_id_fkey FOREIGN KEY (genre_id) REFERENCES public.genres(id) ON DELETE CASCADE;


--
-- Name: movie_genres movie_genres_movie_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: longgedemacminim4
--

ALTER TABLE ONLY public.movie_genres
    ADD CONSTRAINT movie_genres_movie_id_fkey FOREIGN KEY (movie_id) REFERENCES public.movies(id) ON DELETE CASCADE;


--
-- Name: movie_stars movie_stars_movie_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: longgedemacminim4
--

ALTER TABLE ONLY public.movie_stars
    ADD CONSTRAINT movie_stars_movie_id_fkey FOREIGN KEY (movie_id) REFERENCES public.movies(id) ON DELETE CASCADE;


--
-- Name: movie_stars movie_stars_star_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: longgedemacminim4
--

ALTER TABLE ONLY public.movie_stars
    ADD CONSTRAINT movie_stars_star_id_fkey FOREIGN KEY (star_id) REFERENCES public.stars(id) ON DELETE CASCADE;


--
-- Name: movies movies_director_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: longgedemacminim4
--

ALTER TABLE ONLY public.movies
    ADD CONSTRAINT movies_director_id_fkey FOREIGN KEY (director_id) REFERENCES public.directors(id) ON DELETE SET NULL;


--
-- Name: movies movies_producer_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: longgedemacminim4
--

ALTER TABLE ONLY public.movies
    ADD CONSTRAINT movies_producer_id_fkey FOREIGN KEY (producer_id) REFERENCES public.producers(id) ON DELETE SET NULL;


--
-- Name: movies movies_publisher_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: longgedemacminim4
--

ALTER TABLE ONLY public.movies
    ADD CONSTRAINT movies_publisher_id_fkey FOREIGN KEY (publisher_id) REFERENCES public.publishers(id) ON DELETE SET NULL;


--
-- Name: movies movies_series_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: longgedemacminim4
--

ALTER TABLE ONLY public.movies
    ADD CONSTRAINT movies_series_id_fkey FOREIGN KEY (series_id) REFERENCES public.series(id) ON DELETE SET NULL;


--
-- Name: samples samples_movie_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.samples
    ADD CONSTRAINT samples_movie_id_fkey FOREIGN KEY (movie_id) REFERENCES public.movies(id) ON DELETE CASCADE;


--
-- Name: similar_movies similar_movies_movie_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: longgedemacminim4
--

ALTER TABLE ONLY public.similar_movies
    ADD CONSTRAINT similar_movies_movie_id_fkey FOREIGN KEY (movie_id) REFERENCES public.movies(id) ON DELETE CASCADE;


--
-- PostgreSQL database dump complete
--

