#!/bin/bash

echo "🔍 JAVFLIX.TV 完整流程诊断..."

# 1. 检查必要目录
echo "📁 检查目录结构..."
mkdir -p /www/wwwroot/javflix.tv/javflix/public/images/javbus/{cover,actress,sample}
mkdir -p /tmp/javflix-processing
echo "✅ 目录结构已创建"

# 2. 检查数据库连接
echo "🗄️ 检查数据库连接..."
cd /www/wwwroot/javflix.tv/javflix-api
node -e "
const { Pool } = require('pg');
const pool = new Pool({
  host: 'localhost',
  port: 5432,
  database: 'javflix',
  user: 'longgedemacminim4',
  password: ''
});
pool.query('SELECT 1').then(() => {
  console.log('✅ PostgreSQL连接成功');
  process.exit(0);
}).catch(err => {
  console.log('❌ PostgreSQL连接失败:', err.message);
  process.exit(1);
});
"

# 3. 检查Redis连接
echo "📦 检查Redis连接..."
redis-cli ping && echo "✅ Redis连接成功" || echo "❌ Redis连接失败"

# 4. 停止所有可能的进程
echo "🛑 清理现有进程..."
pkill -f "node.*server" 2>/dev/null
pkill -f "video-processor" 2>/dev/null
pkill -f "tsx.*server" 2>/dev/null

# 5. 启动JavBus API (端口3000)
echo "🚀 启动JavBus API..."
cd /www/wwwroot/javflix.tv/javbus-api
npm run dev > /tmp/javbus.log 2>&1 &
JAVBUS_PID=$!
echo "JavBus API PID: $JAVBUS_PID"

# 6. 启动Node.js API (端口4000)
echo "🚀 启动Node.js API..."
cd /www/wwwroot/javflix.tv/javflix-api
npm start > /tmp/api.log 2>&1 &
API_PID=$!
echo "API服务器 PID: $API_PID"

# 7. 启动Go视频处理 (端口8080)
echo "🚀 启动Go视频处理..."
cd /www/wwwroot/javflix.tv/video-processor
./video-processor > /tmp/go.log 2>&1 &
GO_PID=$!
echo "Go服务器 PID: $GO_PID"

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 10

# 8. 检查端口
echo "🔍 检查端口状态..."
netstat -tlnp | grep -E ":(3000|4000|8080)" || echo "⚠️ 某些端口未监听"

# 9. 测试API端点
echo "🧪 测试API端点..."

# 测试JavBus API
curl -s http://localhost:3000/api/movies?page=1 > /dev/null && echo "✅ JavBus API响应正常" || echo "❌ JavBus API无响应"

# 测试Node.js API
curl -s http://localhost:4000/api/javbus-admin/import-status > /dev/null && echo "✅ Node.js API响应正常" || echo "❌ Node.js API无响应"

# 测试Go服务器
curl -s http://localhost:8080/health > /dev/null && echo "✅ Go服务器响应正常" || echo "❌ Go服务器无响应"

# 10. 显示日志
echo "📋 服务日志摘要..."
echo "--- JavBus API日志 ---"
tail -5 /tmp/javbus.log 2>/dev/null || echo "无日志"
echo "--- Node.js API日志 ---"
tail -5 /tmp/api.log 2>/dev/null || echo "无日志"
echo "--- Go服务器日志 ---"
tail -5 /tmp/go.log 2>/dev/null || echo "无日志"

echo "🎉 诊断完成！"
echo "📊 管理面板: http://localhost:8001"
echo "🔧 API服务器: http://localhost:4000"
echo "📡 JavBus API: http://localhost:3000"
echo "🎬 视频处理: http://localhost:8080"

# 保存PID
echo "$JAVBUS_PID" > /tmp/javbus.pid
echo "$API_PID" > /tmp/api.pid
echo "$GO_PID" > /tmp/go.pid
