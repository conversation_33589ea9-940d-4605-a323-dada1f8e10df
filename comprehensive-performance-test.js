#!/usr/bin/env node

/**
 * JAVFLIX.TV 全面性能测试工具
 * 基于 Pieces.app 和 Sematext 的性能优化最佳实践
 * 测试播放页和所有关键页面的性能
 */

const http = require('http');
const https = require('https');
const { performance } = require('perf_hooks');
const fs = require('fs');

// 测试配置
const config = {
  baseUrl: 'http://localhost:3001',
  timeout: 15000,
  userAgent: 'JAVFLIX-Comprehensive-Performance-Tester/1.0',
  concurrentRequests: 3
};

// 全面测试页面列表
const testPages = [
  // 核心页面
  { name: '🏠 首页', url: '/', category: 'core', priority: 'high' },
  { name: '🔍 搜索页', url: '/zh-CN/search?q=TOTTE-218', category: 'core', priority: 'high' },
  { name: '📂 分类页', url: '/zh-CN/categories', category: 'core', priority: 'medium' },
  
  // 播放相关页面
  { name: '🎬 播放页 - 热门视频', url: '/zh-CN/video/SSIS-016', category: 'video', priority: 'high' },
  { name: '🎬 播放页 - 搜索结果', url: '/zh-CN/video/TOTTE-218', category: 'video', priority: 'high' },
  { name: '🎬 视频详情页', url: '/zh-CN/movie/test-movie-id', category: 'video', priority: 'medium' },
  
  // API端点测试
  { name: '📡 API-首页数据', url: '/api/popular-videos?locale=zh-CN&limit=8', category: 'api', priority: 'high' },
  { name: '📡 API-搜索数据', url: '/api/search?q=test&locale=zh-CN', category: 'api', priority: 'high' },
  { name: '📡 API-分类数据', url: '/api/categories', category: 'api', priority: 'medium' },
  { name: '📡 API-视频详情', url: '/api/video/details?id=test', category: 'api', priority: 'medium' },
  
  // 用户相关页面
  { name: '👤 用户中心', url: '/zh-CN/profile', category: 'user', priority: 'low' },
  { name: '⭐ 收藏页面', url: '/zh-CN/favorites', category: 'user', priority: 'low' },
  { name: '📜 历史记录', url: '/zh-CN/history', category: 'user', priority: 'low' },
  
  // 静态资源测试
  { name: '🖼️ 静态图片', url: '/images/logo.png', category: 'static', priority: 'medium' },
  { name: '📄 样式文件', url: '/_next/static/css/app.css', category: 'static', priority: 'medium' },
  { name: '⚡ JS文件', url: '/_next/static/chunks/main.js', category: 'static', priority: 'medium' }
];

// 性能测试结果存储
let testResults = [];
let categorySummary = {};

// 性能测试函数
async function testPagePerformance(page) {
  return new Promise((resolve) => {
    const startTime = performance.now();
    const url = `${config.baseUrl}${page.url}`;
    const urlObj = new URL(url);
    const client = urlObj.protocol === 'https:' ? https : http;
    
    let responseSize = 0;
    let ttfb = 0;
    let statusCode = 0;
    
    const options = {
      hostname: urlObj.hostname,
      port: urlObj.port || (urlObj.protocol === 'https:' ? 443 : 80),
      path: urlObj.pathname + urlObj.search,
      method: 'GET',
      headers: {
        'User-Agent': config.userAgent,
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        'Accept-Language': 'zh-CN,zh;q=0.8,en;q=0.5',
        'Accept-Encoding': 'gzip, deflate',
        'Connection': 'keep-alive',
        'Cache-Control': 'no-cache'
      },
      timeout: config.timeout
    };

    const req = client.request(options, (res) => {
      statusCode = res.statusCode;
      ttfb = performance.now() - startTime;
      
      res.on('data', (chunk) => {
        responseSize += chunk.length;
      });
      
      res.on('end', () => {
        const totalTime = performance.now() - startTime;
        
        // 性能评分算法（基于 Pieces.app 的优化指标）
        let score = 100;
        if (totalTime > 3000) score -= 40;      // 3秒以上严重扣分
        else if (totalTime > 1000) score -= 25; // 1-3秒中等扣分
        else if (totalTime > 500) score -= 15;  // 500ms-1秒轻度扣分
        else if (totalTime > 200) score -= 5;   // 200-500ms微扣分
        
        if (ttfb > 1000) score -= 15;          // TTFB过长扣分
        if (statusCode !== 200) score -= 30;   // 非200状态码扣分
        if (responseSize < 100) score -= 10;   // 内容过小可能有问题
        
        // 根据页面类型调整评分标准
        if (page.category === 'api' && totalTime > 300) score -= 10;
        if (page.category === 'video' && totalTime > 2000) score -= 15;
        if (page.category === 'static' && totalTime > 500) score -= 20;
        
        const result = {
          name: page.name,
          url: page.url,
          category: page.category,
          priority: page.priority,
          statusCode,
          responseTime: Math.round(totalTime),
          ttfb: Math.round(ttfb),
          responseSize,
          downloadSpeed: responseSize > 0 ? Math.round((responseSize / (totalTime / 1000))) : 0,
          score: Math.max(0, Math.round(score)),
          timestamp: new Date().toISOString()
        };
        
        resolve(result);
      });
    });
    
    req.on('timeout', () => {
      req.destroy();
      resolve({
        name: page.name,
        url: page.url,
        category: page.category,
        priority: page.priority,
        statusCode: 0,
        responseTime: config.timeout,
        ttfb: config.timeout,
        responseSize: 0,
        downloadSpeed: 0,
        score: 0,
        error: 'Timeout',
        timestamp: new Date().toISOString()
      });
    });
    
    req.on('error', (error) => {
      resolve({
        name: page.name,
        url: page.url,
        category: page.category,
        priority: page.priority,
        statusCode: 0,
        responseTime: performance.now() - startTime,
        ttfb: 0,
        responseSize: 0,
        downloadSpeed: 0,
        score: 0,
        error: error.message,
        timestamp: new Date().toISOString()
      });
    });
    
    req.end();
  });
}

// 批量并发测试
async function runBatchTest(pages, batchSize = 3) {
  const results = [];
  
  for (let i = 0; i < pages.length; i += batchSize) {
    const batch = pages.slice(i, i + batchSize);
    console.log(`\n🚀 测试批次 ${Math.floor(i / batchSize) + 1}/${Math.ceil(pages.length / batchSize)}`);
    
    const batchPromises = batch.map(async (page, index) => {
      console.log(`📊 正在测试 ${page.name}...`);
      const result = await testPagePerformance(page);
      
      // 实时显示结果
      const status = result.score >= 80 ? '✅' : result.score >= 60 ? '⚠️' : '❌';
      const responseTimeColor = result.responseTime < 200 ? '🟢' : 
                                result.responseTime < 500 ? '🟡' : 
                                result.responseTime < 1000 ? '🟠' : '🔴';
      
      console.log(`${status} ${page.name} 完成`);
      console.log(`   性能评分: ${result.score}/100`);
      console.log(`   响应时间: ${responseTimeColor} ${result.responseTime}ms`);
      console.log(`   首字节时间: ${result.ttfb}ms`);
      console.log(`   内容大小: ${(result.responseSize / 1024).toFixed(2)} KB`);
      console.log(`   下载速度: ${(result.downloadSpeed / 1024).toFixed(2)} KB/s`);
      if (result.error) console.log(`   ❌ 错误: ${result.error}`);
      
      return result;
    });
    
    const batchResults = await Promise.all(batchPromises);
    results.push(...batchResults);
    
    // 批次间稍作延迟，避免服务器压力过大
    if (i + batchSize < pages.length) {
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
  }
  
  return results;
}

// 生成分类统计
function generateCategorySummary(results) {
  const categories = {};
  
  results.forEach(result => {
    if (!categories[result.category]) {
      categories[result.category] = {
        name: result.category,
        count: 0,
        totalScore: 0,
        avgResponseTime: 0,
        avgTtfb: 0,
        totalResponseTime: 0,
        totalTtfb: 0,
        highPriorityIssues: 0,
        issues: []
      };
    }
    
    const cat = categories[result.category];
    cat.count++;
    cat.totalScore += result.score;
    cat.totalResponseTime += result.responseTime;
    cat.totalTtfb += result.ttfb;
    
    // 识别高优先级问题
    if (result.priority === 'high' && result.score < 70) {
      cat.highPriorityIssues++;
      cat.issues.push({
        name: result.name,
        score: result.score,
        responseTime: result.responseTime,
        issue: result.responseTime > 1000 ? '响应时间过长' : 
               result.score < 50 ? '严重性能问题' : '性能需要优化'
      });
    }
  });
  
  // 计算平均值
  Object.values(categories).forEach(cat => {
    cat.avgScore = Math.round(cat.totalScore / cat.count);
    cat.avgResponseTime = Math.round(cat.totalResponseTime / cat.count);
    cat.avgTtfb = Math.round(cat.totalTtfb / cat.count);
  });
  
  return categories;
}

// 生成性能优化建议
function generateOptimizationSuggestions(results, categories) {
  const suggestions = [];
  
  // 按优先级分析问题
  const highPriorityIssues = results.filter(r => r.priority === 'high' && r.score < 70);
  const apiIssues = results.filter(r => r.category === 'api' && r.responseTime > 300);
  const videoPageIssues = results.filter(r => r.category === 'video' && r.responseTime > 2000);
  const staticResourceIssues = results.filter(r => r.category === 'static' && r.responseTime > 500);
  
  if (highPriorityIssues.length > 0) {
    suggestions.push({
      priority: '🔥 紧急',
      category: '核心页面优化',
      description: `发现 ${highPriorityIssues.length} 个高优先级性能问题`,
      issues: highPriorityIssues.map(issue => ({
        page: issue.name,
        problem: `响应时间 ${issue.responseTime}ms，评分 ${issue.score}/100`,
        solution: issue.responseTime > 1000 ? '需要立即优化API查询和缓存' : '需要代码和查询优化'
      }))
    });
  }
  
  if (apiIssues.length > 0) {
    suggestions.push({
      priority: '⚡ 高优先级',
      category: 'API性能优化',
      description: `${apiIssues.length} 个API端点需要优化`,
      solutions: [
        '实施Redis缓存策略',
        '优化数据库查询和索引',
        '启用API响应压缩',
        '实施分页和数据限制'
      ]
    });
  }
  
  if (videoPageIssues.length > 0) {
    suggestions.push({
      priority: '🎬 专项优化',
      category: '播放页面优化',
      description: `${videoPageIssues.length} 个播放页面需要优化`,
      solutions: [
        '实施播放页面静态生成(SSG)',
        '优化视频元数据查询',
        '启用播放页面缓存',
        '分离播放统计异步加载',
        '优化图片懒加载'
      ]
    });
  }
  
  if (staticResourceIssues.length > 0) {
    suggestions.push({
      priority: '📦 资源优化',
      category: '静态资源优化',
      description: `${staticResourceIssues.length} 个静态资源需要优化`,
      solutions: [
        '启用CDN加速',
        '实施文件压缩',
        '优化图片格式(WebP)',
        '启用浏览器缓存',
        '代码分割和懒加载'
      ]
    });
  }
  
  return suggestions;
}

// 生成详细报告
function generateDetailedReport(results, categories, suggestions) {
  const overallScore = Math.round(results.reduce((sum, r) => sum + r.score, 0) / results.length);
  const avgResponseTime = Math.round(results.reduce((sum, r) => sum + r.responseTime, 0) / results.length);
  
  console.log('\n' + '='.repeat(60));
  console.log('📊 JAVFLIX.TV 全面性能测试报告');
  console.log('='.repeat(60));
  
  console.log(`\n🎯 总体性能概况:`);
  console.log(`   📈 整体评分: ${overallScore}/100 ${overallScore >= 80 ? '🟢 优秀' : overallScore >= 60 ? '🟡 良好' : '🔴 需要改进'}`);
  console.log(`   ⏱️  平均响应时间: ${avgResponseTime}ms`);
  console.log(`   📊 测试页面数: ${results.length}`);
  console.log(`   🕐 测试时间: ${new Date().toLocaleString('zh-CN')}`);
  
  console.log(`\n📂 分类性能分析:`);
  Object.values(categories).forEach(cat => {
    const icon = cat.name === 'core' ? '🏠' : 
                 cat.name === 'video' ? '🎬' : 
                 cat.name === 'api' ? '📡' : 
                 cat.name === 'user' ? '👤' : '📦';
    console.log(`   ${icon} ${cat.name.toUpperCase()}:`);
    console.log(`      评分: ${cat.avgScore}/100, 响应时间: ${cat.avgResponseTime}ms`);
    console.log(`      页面数: ${cat.count}, 高优先级问题: ${cat.highPriorityIssues}`);
  });
  
  console.log(`\n🚨 关键问题页面:`);
  const criticalIssues = results.filter(r => r.score < 60).sort((a, b) => a.score - b.score);
  criticalIssues.forEach(issue => {
    console.log(`   ❌ ${issue.name}: ${issue.score}/100 (${issue.responseTime}ms)`);
  });
  
  console.log(`\n🔧 优化建议:`);
  suggestions.forEach((suggestion, index) => {
    console.log(`   ${index + 1}. ${suggestion.priority} ${suggestion.category}`);
    console.log(`      ${suggestion.description}`);
    if (suggestion.solutions) {
      suggestion.solutions.forEach(solution => {
        console.log(`      • ${solution}`);
      });
    }
    if (suggestion.issues) {
      suggestion.issues.forEach(issue => {
        console.log(`      • ${issue.page}: ${issue.problem} → ${issue.solution}`);
      });
    }
  });
  
  console.log(`\n⭐ 性能等级:`);
  if (overallScore >= 85) {
    console.log(`   🏆 A级 (优秀) - 性能表现卓越`);
  } else if (overallScore >= 70) {
    console.log(`   🥈 B级 (良好) - 性能表现良好，有优化空间`);
  } else if (overallScore >= 55) {
    console.log(`   🥉 C级 (一般) - 需要性能优化`);
  } else {
    console.log(`   💔 D级 (较差) - 急需性能优化`);
  }
  
  console.log('\n✅ 全面性能测试完成！');
}

// 保存测试结果
function saveTestResults(results, categories, suggestions) {
  const report = {
    timestamp: new Date().toISOString(),
    summary: {
      totalPages: results.length,
      overallScore: Math.round(results.reduce((sum, r) => sum + r.score, 0) / results.length),
      avgResponseTime: Math.round(results.reduce((sum, r) => sum + r.responseTime, 0) / results.length),
      criticalIssues: results.filter(r => r.score < 60).length
    },
    results,
    categories,
    suggestions
  };
  
  try {
    fs.writeFileSync('./comprehensive-performance-report.json', JSON.stringify(report, null, 2));
    console.log('\n💾 详细报告已保存到: comprehensive-performance-report.json');
  } catch (error) {
    console.log('\n❌ 保存报告失败:', error.message);
  }
}

// 主测试函数
async function runComprehensiveTest() {
  console.log('🚀 JAVFLIX.TV 全面性能测试');
  console.log('================================');
  console.log(`📊 将测试 ${testPages.length} 个页面的性能`);
  console.log(`🔗 基础URL: ${config.baseUrl}`);
  console.log(`⏱️  开始时间: ${new Date().toLocaleString('zh-CN')}`);
  
  const startTime = performance.now();
  
  // 运行批量测试
  testResults = await runBatchTest(testPages, config.concurrentRequests);
  
  // 生成分析报告
  categorySummary = generateCategorySummary(testResults);
  const optimizationSuggestions = generateOptimizationSuggestions(testResults, categorySummary);
  
  // 输出详细报告
  generateDetailedReport(testResults, categorySummary, optimizationSuggestions);
  
  // 保存测试结果
  saveTestResults(testResults, categorySummary, optimizationSuggestions);
  
  const totalTime = performance.now() - startTime;
  console.log(`\n🕐 总测试耗时: ${Math.round(totalTime)}ms`);
}

// 运行测试
if (require.main === module) {
  runComprehensiveTest().catch(console.error);
}

module.exports = { runComprehensiveTest, testPagePerformance, testPages };