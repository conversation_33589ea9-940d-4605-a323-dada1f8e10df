{"env": {"es2021": true, "node": true}, "extends": ["eslint:recommended", "plugin:@typescript-eslint/recommended", "plugin:prettier/recommended"], "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaVersion": "latest", "sourceType": "module"}, "plugins": ["@typescript-eslint"], "rules": {"@typescript-eslint/no-unused-vars": ["warn", {"argsIgnorePattern": "^_", "caughtErrors": "none"}], "prettier/prettier": "warn"}}