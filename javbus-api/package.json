{"name": "javbus-api", "version": "2.1.2", "description": "JavBus API", "main": "index.js", "homepage": "https://github.com/ovnrain/javbus-api#readme", "scripts": {"start": "node -r dotenv/config dist/server.js", "dev": "tsx watch --clear-screen=false -r dotenv/config api/server.ts", "build": "tsc", "vercel-build": "tsc", "format": "prettier --write \"api/**/*.ts\"", "lint": "eslint --config .eslintrc.commit.json \"api/**/*.ts\"", "prepare": "husky"}, "keywords": ["javbus", "api"], "author": {"name": "ovnrain", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "https://github.com/ovnrain/javbus-api.git"}, "license": "MIT", "devDependencies": {"@types/bytes": "^3.1.4", "@types/express": "^4.17.21", "@types/express-session": "^1.18.0", "@types/http-errors": "^2.0.4", "@types/node": "^22.0.1", "@types/probe-image-size": "^7.2.5", "@types/qs": "^6.9.15", "@typescript-eslint/eslint-plugin": "^7.17.0", "@typescript-eslint/parser": "^7.17.0", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.1", "husky": "^9.1.4", "lint-staged": "^15.2.7", "prettier": "^3.3.3", "tsx": "^4.19.4", "typescript": "^5.5.4"}, "dependencies": {"axios": "^1.9.0", "bytes": "^3.1.2", "dotenv": "^16.4.5", "express": "^4.19.2", "express-session": "^1.18.0", "express-validator": "^7.1.0", "got": "^14.4.2", "http-errors": "^2.0.0", "https-proxy-agent": "^7.0.5", "memorystore": "^1.6.7", "node-html-parser": "^6.1.13", "pg": "^8.16.0", "probe-image-size": "^7.2.3", "qs": "^6.12.3", "socks-proxy-agent": "^8.0.4", "znv": "^0.4.0", "zod": "^3.23.8"}, "type": "module", "lint-staged": {"*.ts": ["eslint --config .eslintrc.commit.json"], "*.html": ["prettier --write"]}}