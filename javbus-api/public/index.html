<!doctype html>
<html lang="zh-<PERSON>">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>JavBus API</title>
    <link rel="icon" href="/favicon.png" sizes="32x32" />
    <link rel="icon" href="/favicon.svg" type="image/svg+xml" />
    <link rel="stylesheet" href="/css/normalize.css" />
    <link rel="stylesheet" href="/css/base.css" />
    <style>
      .tip {
        padding: 0 32px;
      }

      .tip,
      .try {
        margin-top: 16px;
      }

      .list {
        margin: 8px 0 0 0;
        padding: 0 16px 0 32px;
      }

      .list li:not(:last-child) {
        margin-bottom: 8px;
      }

      .list li a {
        line-height: 24px;
      }

      .tag {
        background-color: #e6e6e6;
        display: inline-block;
        border-radius: 4px;
        padding: 0px 4px;
      }

      .user-wrapper {
        position: absolute;
        top: 16px;
        right: 16px;
      }

      .login {
        display: none;
      }

      .login.show {
        display: flex;
      }

      .login-link {
        border-radius: 4px;
        background-color: #008cc4;
        color: #ffffff;
        cursor: pointer;
        font-size: 14px;
        width: 48px;
        height: 24px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
      }

      .login-link:hover {
        text-decoration: none;
      }

      .user {
        display: none;
        align-items: center;
      }

      .user.show {
        display: flex;
      }

      .username {
        margin-right: 8px;
      }

      .logout {
        border: none;
        border-radius: 4px;
        background-color: #008cc4;
        color: #ffffff;
        cursor: pointer;
        font-size: 14px;
        width: 48px;
        height: 24px;
        box-sizing: border-box;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        padding: 0;
      }
    </style>
  </head>

  <body>
    <div class="container">
      <h1 class="title">JavBus API</h1>
      <p class="tip">如果你看到这个页面，说明服务已经成功运行起来！</p>
      <p>
        <a href="https://github.com/ovnrain/javbus-api#readme" target="_blank">查看文档</a>
      </p>
      <p class="try">你可以尝试访问以下 API：</p>
      <ol class="list">
        <li>
          <a href="/api/movies">返回有磁力链接的第一页影片</a>
        </li>
        <li>
          <a href="/api/movies?filterType=star&filterValue=qs6&magnet=all">
            返回演员 ID 为
            <span class="tag">qs6</span>
            的影片的第一页，包含有磁力链接和无磁力链接的影片
          </a>
        </li>
        <li>
          <a href="/api/movies/search?keyword=三上">
            搜索关键词为
            <span class="tag">三上</span>
            的影片的第一页，只返回有磁力链接的影片
          </a>
        </li>
        <li>
          <p>影片详情</p>
          <ul class="list">
            <li><a href="/api/movies/IPX-585">IPX-585</a></li>
            <li><a href="/api/movies/IPZZ-023">IPZZ-023</a></li>
            <li><a href="/api/movies/IPX-451">IPX-451</a></li>
          </ul>
        </li>
        <li>
          <p>磁力链接</p>
          <ul class="list">
            <li><a href="/api/magnets/JUQ-434?gid=56326057355&uc=0">JUQ-434</a></li>
            <li><a href="/api/magnets/IPZZ-135?gid=56327757424&uc=0">IPZZ-135</a></li>
            <li>
              <a href="/api/magnets/SSNI-730?gid=42785257471&uc=0">SSNI-730</a>
              <ul>
                <li>
                  <a href="/api/magnets/SSNI-730?gid=42785257471&uc=0&sortBy=date&sortOrder=desc">按照日期降序排序</a>
                </li>
                <li>
                  <a href="/api/magnets/SSNI-730?gid=42785257471&uc=0&sortBy=size&sortOrder=asc">按照大小升序排序</a>
                </li>
              </ul>
            </li>
          </ul>
        </li>
      </ol>
      <div class="user-wrapper">
        <div class="login">
          <a class="login-link" href="/login.html">登录</a>
        </div>
        <div class="user">
          <span class="username"></span>
          <button class="logout">退出</button>
        </div>
      </div>
    </div>

    <script>
      (function () {
        const user = document.querySelector('.user');
        const username = document.querySelector('.username');
        const login = document.querySelector('.login');
        const logout = document.querySelector('.logout');

        fetch('/api/user')
          .then((res) => res.json())
          .then((res) => {
            if (res.username) {
              username.textContent = res.username;
              user.classList.add('show');
              login.classList.remove('show');
            } else if (res.useCredentials) {
              login.classList.add('show');
              user.classList.remove('show');
            }
          });

        logout.addEventListener('click', () => {
          fetch('/api/logout', {
            method: 'POST',
          }).then(() => {
            window.location.reload();
          });
        });
      })();
    </script>
  </body>
</html>
