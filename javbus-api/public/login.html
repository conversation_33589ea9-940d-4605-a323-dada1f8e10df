<!doctype html>
<html lang="zh-<PERSON>">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>登录</title>
    <link rel="icon" href="/favicon.png" sizes="32x32" />
    <link rel="icon" href="/favicon.svg" type="image/svg+xml" />
    <link rel="stylesheet" href="/css/normalize.css" />
    <link rel="stylesheet" href="/css/base.css" />
    <style>
      .form {
        margin-top: 16px;
        width: 280px;
      }

      .item {
        display: flex;
        flex-direction: column;
        margin-bottom: 16px;
        align-items: flex-start;
      }

      .label {
        margin-bottom: 8px;
      }

      .input {
        height: 36px;
        border: 1px solid #dddddd;
        border-radius: 4px;
        padding: 0 8px;
        width: 100%;
        box-sizing: border-box;
      }

      .login-button {
        width: 100%;
        height: 36px;
        border: none;
        border-radius: 4px;
        background-color: rgb(0, 140, 196);
        color: #ffffff;
        cursor: pointer;
        font-size: 16px;
      }

      .login-button[disabled] {
        background-color: rgba(0, 140, 196, 0.7);
        cursor: not-allowed;
      }

      .message {
        color: red;
        margin-bottom: 16px;
        display: none;
        font-size: 14px;
      }

      .message.show {
        display: block;
      }

      .return-index {
        margin-top: 100px;
        text-align: center;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <h1 class="title">登录</h1>
      <form class="form" action="">
        <div class="item">
          <label class="label" for="username">用户名:</label>
          <input class="input" id="username" type="text" name="username" autofocus />
        </div>
        <div class="item">
          <label class="label" for="password">密码:</label>
          <input class="input" id="password" type="password" name="password" />
        </div>
        <div class="message"></div>
        <div>
          <button class="login-button" type="submit">登录</button>
        </div>
      </form>
      <div class="return-index">
        <a href="/">回到首页</a>
      </div>
    </div>
    <script>
      (function () {
        const redirectUrl = new URLSearchParams(window.location.search).get('redirect');

        const form = document.querySelector('.form');
        const message = document.querySelector('.message');
        const button = document.querySelector('.login-button');

        function showMessage(text) {
          message.textContent = text;
          message.classList.add('show');
        }

        function hideMessage() {
          message.textContent = '';
          message.classList.remove('show');
        }

        form.addEventListener('submit', (e) => {
          e.preventDefault();

          hideMessage();

          if (!form.username.value || !form.password.value) {
            showMessage('请输入用户名和密码');
            return;
          }

          button.setAttribute('disabled', true);
          button.textContent = '登录中...';

          fetch('/api/login', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              username: form.username.value,
              password: form.password.value,
            }),
          })
            .then((res) => res.json())
            .then((res) => {
              if (!res.success) {
                showMessage('用户名或密码错误');
                button.removeAttribute('disabled');
                button.textContent = '登录';
              } else {
                window.location.replace(redirectUrl || '/');
              }
            })
            .catch((e) => {
              console.log(e);
            });
        });
      })();
    </script>
  </body>
</html>
