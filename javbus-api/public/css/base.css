body {
  display: flex;
  justify-content: center;
  color: #333333;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Helvetica, Arial, sans-serif,
    'Apple Color Emoji', 'Segoe UI Emoji';
}

p {
  margin: 8px 0;
}

a {
  text-decoration: none;
  color: #008cc4;
}

a:hover {
  text-decoration: underline;
}

.container {
  padding-top: 80px;
  display: flex;
  flex-direction: column;
  align-items: center;
  max-width: 600px;
  position: relative;
}

.title {
  margin: 0;
}
